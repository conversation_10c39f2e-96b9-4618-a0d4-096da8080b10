namespace ShinKenShinKun.DataAccess;

public class ButtonActionModel
{
    public ButtonActionType ActionType { get; set; }
    public string ActionValue { get; set; } = string.Empty;
    public List<string> TargetComponentIds { get; set; } = new();
    public string TargetAreaId { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

public class NavigateActionModel : ButtonActionModel
{
    public string PageRoute { get; set; } = string.Empty;
    public Dictionary<string, object> NavigationParameters { get; set; } = new();
}

public class ShowDialogActionModel : ButtonActionModel
{
    public string DialogTitle { get; set; } = string.Empty;
    public string DialogMessage { get; set; } = string.Empty;
    public string DialogType { get; set; } = "Info"; // Info, Warning, Error, Confirm
}
