using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class ConciergeTimeGroupList
    {
        private const int MEDICAL_CHECK_NO_POS = 0;
        private const int MEDICAL_CHECK_NAME_POS = 1;
        private const int VIEWORDER_POS = 2;
        private string FldConciergeTimeGroupId;
        private string FldConciergeTimeGroupName;
        private string FldViewOrder;

        public ConciergeTimeGroupList(string[] dbData)
        {
            this.ConciergeTimeGroupId = dbData[MEDICAL_CHECK_NO_POS];
            this.ConciergeTimeGroupName = dbData[MEDICAL_CHECK_NAME_POS];
            this.VierOrder = dbData[VIEWORDER_POS];
        }

        public string ConciergeTimeGroupId
        {
            get
            {
                return this.FldConciergeTimeGroupId;
            }
            set
            {
                this.FldConciergeTimeGroupId = value;
            }
        }

        public string ConciergeTimeGroupName
        {
            get
            {
                return this.FldConciergeTimeGroupName;
            }
            set
            {
                this.FldConciergeTimeGroupName = value;
            }
        }

        public string VierOrder
        {
            get
            {
                return this.FldViewOrder;
            }
            set
            {
                this.FldViewOrder = value;
            }
        }
    }
}
