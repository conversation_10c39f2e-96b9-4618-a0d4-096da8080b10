﻿namespace ShinKenShinKun.Utils;

///_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/
/// <summary>
/// MyComparerクラス
/// </summary>
/// <remarks>
/// ArrayList.Sort用のクラスです。
/// その後メインスレッドを起動します。
/// </remarks>
///_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/
public class BackUpComparer : System.Collections.IComparer
{
    //xがyより小さいときはマイナスの数、大きいときはプラスの数、
    //同じときは0を返す
    public int Compare(object x, object y)
    {
        int result = 0;

        //日付(yyyymmdd形式)をDateTime型に変換する
        DateTime dx = DateTime.ParseExact(x.ToString().Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
        DateTime dy = DateTime.ParseExact(y.ToString().Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

        //Messageで並び替える
        result = DateTime.Compare(dx, dy);

        //結果を返す
        return result;
    }
}