﻿namespace ShinKenShinKun.Utils;

public class DateTimeToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        string defaultLanguage = Thread.CurrentThread.CurrentUICulture.ToString().ToLower();
        string formattedDate = defaultLanguage switch
        {
            "ja-jp" => ((DateTime)value).ToString("yyyy年MM月dd日"),
            _ => ((DateTime)value).ToString("dd MMMM yyyy"),
        };
        return formattedDate;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}