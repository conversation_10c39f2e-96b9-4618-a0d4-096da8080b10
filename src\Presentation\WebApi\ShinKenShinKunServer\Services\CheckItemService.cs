﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Globalization;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// CheckItemService の概要の説明です

    /// </summary>
    public class CheckItemService
    {
        private MedicalDataSet MedicalDataSet = new MedicalDataSet();
        private Service srv;
        private Logger logger = null;

        public CheckItemService()
        {
            srv = new Service();
        }

        /// <summary>
        /// 健診者の検査種別番号ごとの、検査項目番号情報を取得する。

        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <returns></returns>
        public string[] StrGetCheckItem(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            try
            {
                //動的配列を用意する。

                ArrayList checkItemArray = new ArrayList();

                DateTime conMedicalCheckDate = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
                //termIDを元にmedicalCheckNoを導く
                short medicalCheckNo = Convert.ToInt16(srv.TerminalData[termID].ToString());// 検査種別番号

                //データセットの関数をコールする。

                MedicalDataSet = srv.GetCheckItem(conMedicalCheckDate, sClientID, sDivision, medicalCheckNo);

                //日付・当日ID・区分をキーに検索し、その行を取得する。

                DataRow row = MedicalDataSet.Tables["MedicalCheckItem"].Rows.Find(new object[] { conMedicalCheckDate, sClientID, sDivision, medicalCheckNo });

                if (row != null)
                {
                    // 検索した情報を配列に追加していく
                    checkItemArray.Add(row["MedicalCheckDate"].ToString().PadLeft(1, ' '));
                    checkItemArray.Add(row["ClientID"].ToString().PadLeft(1, ' '));
                    checkItemArray.Add(row["Division"].ToString().PadLeft(1, ' '));
                    checkItemArray.Add(row["MedicalCheckNo"].ToString().PadLeft(1, ' '));

                    // 検索した情報を配列に追加していく
                    for (int i = 1; i <= 50; i++)
                    {
                        if (row["CheckItemNo" + i.ToString()].ToString() == "")
                        {
                            checkItemArray.Add(null);
                        }
                        else
                        {
                            checkItemArray.Add(row["CheckItemNo" + i.ToString()].ToString());
                        }
                    }
                    checkItemArray.Add(row["ExecDate"].ToString().PadLeft(1, ' '));
                    checkItemArray.Add(row["RecordHeader"].ToString().PadLeft(1, ' '));

                    // ArrayListからString配列に変換
                    string[] checkItemlist = new string[checkItemArray.Count];
                    Array.Copy(checkItemArray.ToArray(), checkItemlist, checkItemArray.Count);
                    return checkItemlist;
                }
                else
                {
                    return null;
                }
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        /// <summary>
        /// 健診者のすべての、検査項目番号情報を取得する。

        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        public PluralRecord[] StrGetCheckItemsPlural(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            try
            {
                //動的配列を用意する。

                ArrayList checkItemArray = new ArrayList();

                DateTime conMedicalCheckDate = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                //データセットの関数をコールする。

                MedicalDataSet = srv.GetCheckItemAll(conMedicalCheckDate, sClientID, sDivision);

                // 取得したレコード数をチェックする。

                int recordCount = MedicalDataSet.Tables["MedicalCheckItem"].Rows.Count;
                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = MedicalDataSet.Tables["MedicalCheckItem"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        checkItemArray.Add(row["MedicalCheckDate"].ToString().PadLeft(1, ' '));
                        checkItemArray.Add(row["ClientID"].ToString().PadLeft(1, ' '));
                        checkItemArray.Add(row["Division"].ToString().PadLeft(1, ' '));
                        checkItemArray.Add(row["MedicalCheckNo"].ToString().PadLeft(1, ' '));

                        // 検索した情報を配列に追加していく
                        for (int i = 1; i <= 50; i++)
                        {
                            if (row["CheckItemNo" + i.ToString()].ToString() == "")
                            {
                                checkItemArray.Add(null);
                            }
                            else
                            {
                                checkItemArray.Add(row["CheckItemNo" + i.ToString()].ToString());
                            }
                        }
                        checkItemArray.Add(row["ExecDate"].ToString().PadLeft(1, ' '));
                        checkItemArray.Add(row["RecordHeader"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] checkItemlist = new string[checkItemArray.Count];
                    Array.Copy(checkItemArray.ToArray(), checkItemlist, checkItemArray.Count);
                    pRecord[j] = new PluralRecord(checkItemlist);
                    checkItemArray.Clear();
                }
                return pRecord;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

    }
}