﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ShadowStyle => CreateStyle<Shadow>()
        .Set(Shadow.BrushProperty, AppColors.Black)
        .Set(Shadow.RadiusProperty, 15)
        .Set(<PERSON>.OpacityProperty, 1)
        .Set(Shadow.OffsetProperty, new Point(10, 10));

    public static Style CustomMediumShadowStyle => CreateStyle<Shadow>()
    .Set(Shadow.BrushProperty, AppColors.Black)
    .Set(Shadow.OpacityProperty, Dimens.OpacityMedium);

    public static Style NumericPadButtonShadowStyle => CreateStyle<Shadow>()
            .Set(Shadow.OpacityProperty, 1)
            .Set(Shadow.RadiusProperty, 1)
            .Set(Shadow.OffsetProperty, new Point(2, 2));

    public static Shadow NoShadow => new() { Opacity = 0, Offset = new(0, 0) };
}