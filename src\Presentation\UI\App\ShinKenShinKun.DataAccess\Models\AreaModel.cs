﻿namespace ShinKenShinKun.DataAccess;

public class AreaModel
{
    public string AreaId { get; set; } = null!;
    public string ColumnRatios { get; set; } = null!;
    public List<int> ColumnRatiosDisplay => [.. ColumnRatios.SplitToList().Select(int.Parse)];
    public List<ComponentLayoutCombineModel> ComponentLayouts { get; set; } = [];
    public double Height { get; set; }
    public LayoutOptions HorizontalOptions { get; set; }
    public string Margin { get; set; } = null!;
    public Thickness MarginDisplay
    {
        get
        {
            var margins = Margin.SplitToList();
            if (margins.Count == 4 &&
                double.TryParse(margins[0], out double left) &&
                double.TryParse(margins[1], out double top) &&
                double.TryParse(margins[2], out double right) &&
                double.TryParse(margins[3], out double bottom))
            {
                return new Thickness(left, top, right, bottom);
            }
            return new Thickness(0);
        }
    }

    public int Order { get; set; }
    public string RowRatios { get; set; } = null!;
    public List<int> RowRatiosDisplay => [.. RowRatios.SplitToList().Select(int.Parse)];
    public string ScreenId { get; set; } = null!;
    public LayoutOptions VerticalOptions { get; set; }
    public double? Width { get; set; }
}

// TODO: After having the database, I will delete it and read data from the database using Entity Framework and Mapping.
public partial class ComponentLayoutCombineModel : ComponentLayoutModel
{
    [ObservableProperty] private string categories;
    [ObservableProperty] private ButtonState currentState;
    [ObservableProperty] private DisplayMode displayMode;
    [ObservableProperty] private string examinationItemId;
    [ObservableProperty] private string examinationTypeId;
    [ObservableProperty] private string inputFormat;
    [ObservableProperty] private bool isAverageResultDisplay;
    [ObservableProperty] private bool isHorizontalLayout;
    [ObservableProperty] private bool isOldResultDisplay;
    [ObservableProperty] private ObservableCollection<MultiSelectTableRowDataModel> items;
    [ObservableProperty] private int settingNumberPadId;
    [ObservableProperty] private LabelResultType labelResultType;
    [ObservableProperty] private int numberOfMeasurements;
    [ObservableProperty] private int numberOfSelection;
    [ObservableProperty] private Color statusColor;
    [ObservableProperty] private ObservableCollection<ButtonState> statusList;
    [ObservableProperty] private string statusName;
    [ObservableProperty] private string switchPressSettingId;
    [ObservableProperty] private string text;
    [ObservableProperty] private string title;
    [ObservableProperty] private string value;
    [ObservableProperty] private string labelColor;
    [ObservableProperty] private string textColor;
    [ObservableProperty] private bool isReadOnly;
    [ObservableProperty] private bool useDirectInput;
    [ObservableProperty] private TextAlignment textAlignment;
    [ObservableProperty] private int minLength;
    [ObservableProperty] private int maxLength;
    [ObservableProperty] private string backgroundColor;
    [ObservableProperty] private ButtonActionModel buttonAction;
    public IList<string> CategoryDisplays => [.. Categories.SplitToList()];
}