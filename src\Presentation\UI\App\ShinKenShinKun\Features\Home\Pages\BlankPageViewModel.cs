﻿
namespace ShinKenShinKun;

public class BlankPageViewModel : NavigationAwareBaseViewModel
{
    private readonly IDispatcher dispatcher;

    public BlankPageViewModel(IAppNavigator appNavigator, IDispatcher dispatcher) : base(appNavigator)
    {
        this.dispatcher = dispatcher;
    }

    public override async Task OnAppearingAsync()
    {
        await Task.Delay(1000);
        await dispatcher.DispatchAsync(async () =>
        {
            await AppNavigator.ShowNotificationDialog(AppResources.AppSettingErrorMessage, AppResources.AppSettingErrorTitle);
            Application.Current?.Quit();
        });
    }
}
