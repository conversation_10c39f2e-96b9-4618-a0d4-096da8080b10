﻿using Microsoft.Win32.SafeHandles;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Services;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Globalization;
using System.IO;
using System.Runtime.ConstrainedExecution;
using System.Runtime.InteropServices;
using System.Security;
using System.Security.Principal;
using System.Text;
using System.Threading;
using System.Web.Services;

namespace ShinKenShinKunServer
{
    [WebService(Namespace = "http://localhost/AR1000Server/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class PublicService : WebService
    {
        //public static Hashtable TerminalData = new Hashtable();
        /// <summary>
        /// データ読み書き用クラス
        /// </summary>
        private ClientService strCSrv;
        private OldClientService strOldCsSrv;
        private GetDataStateService getDsSrv;
        private SetDataStateService setDsSrv;
        private NextService strNeSrv;
        private IpTermSetService strITSrv;
        private MachineService strMaSrv;
        private MeMachinesService strMeSrv;
        private NeedCheckupService strNeedSrv;
        private IndividualControl strIndSrv;
        private CheckItemService strCitSrv;
        private ConciergeControlService strCcSrv;
        private ConciergeMemberListService strCmSrv;
        private ConciergeTimeGroupListService strCgSrv;
        private LiteTerminalService strLiteSrv;

        #region ==========標準化用に追加==========
        private ClickSwitchSettingService clickSrv;
        private DataTransformedService transSrv;
        private DispChangeSettingService dispChangeSrv;
        private MedicalItemListService itemListSrv;
        private MedicalItemSettingService itemSettingSrv;
        private FormModeDispService formModeSrv;
        private PluralDispSettingService pluralDispSetSrv;
        private PluralSetting pluralSetSrv;
        private TransformedSettingService transSetSrv;
        private FormDispSettingService formDispSetSrv;
        private ClientRemarksService clientRemarksService;
        private RemarksItemListService remarksItemListService;
        private EyeControlService eyeControlService;
        private EyeInitialSelectService eyeInitialService;
        private EyeGroupSelectService eyeGroupService;
        //private ReceiveOrderSettingService receiveOrderService;
        private MeDataRelationalService meDataRelationalService;
        private FontSettingService fontSettingService;
        private CustomizeButtonService customizeButtonService;
        private SoundSetService soundSetService;
        #endregion

        private UserService UserService;
        private MessageService messageService;

        #region ==========ヘルゼアNext用に追加==========
        private ClientAddInformationService addInforService;
        private ClientAddInformation2Service addInfor2Service;
        private MedicalCheckReasonDataService checkReasonDataService;
        private MedicalCheckItemStateService checkItemStateService;
        private NeedCheckupItemService needCheckupItemService;
        #endregion

        #region ==========誘導支援用に追加==========
        private GuideSettingService guideSettingSrv;
        private GuideGroupService guideGroupSrv;
        #endregion

        #region ==========案内表示用に追加==========
        private CallMonitorSettingService callMonitorSettingSrv;
        #endregion

        #region コンストラクタ
        /// <summary>
        /// コンストラクタ
        /// 内部用に端末情報格納用テーブルを構成する。
        /// </summary>
        public PublicService()
        {
            //デザインされたコンポーネントを使用する場合、次の行をコメントを解除してください 
            //InitializeComponent();
            //strCSrv = new ClientService();
            //strOldCsSrv = new OldClientService();
            //getDsSrv = new GetDataStateService();
            //setDsSrv = new SetDataStateService();
            //strNeSrv = new NextService();
            //strITSrv = new IpTermSetService();
            //strMaSrv = new MachineService();
            //strMeSrv = new MeMachinesService();
            //strNeedSrv = new NeedCheckupService();
            //strIndSrv = new IndividualControl();
            //strCitSrv = new CheckItemService();
            //strCcSrv = new ConciergeControlService();
            //strCmSrv = new ConciergeMemberListService();
            //strCgSrv = new ConciergeTimeGroupListService();

            //clickSrv = new ClickSwitchSettingService();
            //transSrv = new DataTransformedService();
            //dispChangeSrv = new DispChangeSettingService();
            //itemListSrv = new MedicalItemListService();
            //itemSettingSrv = new MedicalItemSettingService();
            //formModeSrv = new FormModeDispService();
            //pluralDispSetSrv = new PluralDispSettingService();
            //pluralSetSrv = new PluralSetting();
            //transSetSrv = new TransformedSettingService();
            //formDispSetSrv = new FormDispSettingService();
            //clientRemarksService = new ClientRemarksService();
            //remarksItemListService = new RemarksItemListService();
            //eyeControlService = new EyeControlService();
            //eyeGroupService = new EyeGroupSelectService();
            //eyeInitialService = new EyeInitialSelectService();
            ////receiveOrderService = new ReceiveOrderSettingService();
            //meDataRelationalService = new MeDataRelationalService();
            //fontSettingService = new FontSettingService();
            //customizeButtonService = new CustomizeButtonService();
            //soundSetService = new SoundSetService();

            //strLiteSrv = new LiteTerminalService();

            //// ヘルゼアNext対策
            //addInforService = new ClientAddInformationService();
            //checkReasonDataService = new MedicalCheckReasonDataService();
            //checkItemStateService = new MedicalCheckItemStateService();
            //needCheckupItemService = new NeedCheckupItemService();

            //// 誘導支援用
            //guideSettingSrv = new GuideSettingService();
            //guideGroupSrv = new GuideGroupService();
        }
        #endregion

        #region HelloWorld
        /// <summary>
        /// テスト用関数
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public string HelloWorld()
        {
            return "Hello World";
        }
        #endregion

        #region HostDataDef用の部分

        /// <summary>
        /// HostDataDef用の部分
        /// データセットにホストデータを読み込み、
        /// 戻り値としてStringの配列でホストデータを返却する。
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetHostData()
        {
            strMaSrv = new MachineService();
            string[] hostDatalist = strMaSrv.StrGetHostData();
            return hostDatalist;
        }
        #endregion

        #region MedicalMachinesの部分

        /// <summary>
        /// MedicalMachinesの部分
        /// データセットに検査機器情報を読み込む
        /// 戻り値としてStringの配列で検査機器情報を返却する。
        /// </summary>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMachines(string termID)
        {
            strMaSrv = new MachineService();
            string[] medicalMachineslist = strMaSrv.StrGetMachines(termID);
            return medicalMachineslist;
        }

        /// <summary>
        /// MedicalMachinesの部分
        /// データセットに検査機器情報を読み込む
        /// 戻り値としてStringの配列で検査機器情報を返却する。
        /// </summary>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMachinesAllkeys()
        {
            strMaSrv = new MachineService();
            string[] medicalMachineslist = strMaSrv.StrGetMachinesall();
            return medicalMachineslist;
        }

        /// <summary>
        /// MedicalMachinesの部分
        /// データセットに検査機器情報を読み込む
        /// 戻り値としてStringの配列で検査機器情報を返却する。
        /// </summary>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMachinesAllPlural()
        {
            strMaSrv = new MachineService();
            PluralRecord[] medicalMachineslist = strMaSrv.StrGetMachinesAllPlural();
            return medicalMachineslist;
        }
        #endregion

        #region ClientInformationの部分

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientData(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientData(sMedicalCheckDate, sClientID, sDivision);
            return medicalClientlist;
        }
        /// <summary>
        /// ClientInformationの部分
        /// 健診者ID（ユニークな値）を検索し、
        /// 戻り値としてStringで健診者IDを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetClientRegistrationNo(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            strOldCsSrv = new OldClientService();
            string strRegistrationNo = strOldCsSrv.StrGetClientRegistrationNo(sMedicalCheckDate, sClientID, sDivision);
            return strRegistrationNo;
        }

        /// <summary>
        /// ClientInformationの部分
        /// 健診者ID（ユニークな値）から、当日以外で一番新しい受診日の
        /// 受診日付と当日IDを戻り値としてStringの配列で返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="RegistrationNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetBeforeClientIDDate(string sMedicalCheckDate, string RegistrationNo)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientIDDate(sMedicalCheckDate, RegistrationNo);
            return beforeClientList;
        }

        /// <summary>
        /// 受診日時と、当日IDと、区分から
        /// 健診者ID（ユニークな値）を導き出し、当日以外で一番新しい受診日の
        /// 受診日付と当日IDを取得する。戻り値としてStringの配列で返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetBeforeClientData(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientData(sMedicalCheckDate, sClientID, sDivision);
            return beforeClientList;
        }
        /// <summary>
        /// ClientInformationの部分
        /// 健診者ID（ユニークな値）を検索し、
        /// 戻り値としてStringで健診者IDを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetClientRegistrationNoNoDiv(string sMedicalCheckDate, string sClientID)
        {
            strOldCsSrv = new OldClientService();
            string strRegistrationNo = strOldCsSrv.StrGetClientRegistrationNoNoDiv(sMedicalCheckDate, sClientID);
            return strRegistrationNo;
        }

        /// <summary>
        /// 受診日時と、当日IDと、区分から
        /// 健診者ID（ユニークな値）を導き出し、当日以外で一番新しい受診日の
        /// 受診日付と当日IDを取得する。戻り値としてStringの配列で返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetBeforeClientDataNoDiv(string sMedicalCheckDate, string sClientID)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientData(sMedicalCheckDate, sClientID);
            return beforeClientList;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClientDataPlural(string sMedicalCheckDate, string termID)
        {
            strCSrv = new ClientService();
            PluralRecord[] medicalClientlist = strCSrv.StrGetClientDataPlural(sMedicalCheckDate, termID);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClientDataPluralCheckNo(string sMedicalCheckDate, string sMedicalCheckNo)
        {
            strCSrv = new ClientService();
            PluralRecord[] medicalClientlist = strCSrv.StrGetClientDataPluralCheckNo(sMedicalCheckDate, sMedicalCheckNo);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClientDataPluralAllState(string sMedicalCheckDate)
        {
            strCSrv = new ClientService();
            PluralRecord[] medicalClientlist = strCSrv.StrGetClientDataPlural(sMedicalCheckDate);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientDataNoDiv(string sMedicalCheckDate, string sClientID)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientDataNoDiv(sMedicalCheckDate, sClientID);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分(健診者ID)
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientDataRegist(string sMedicalCheckDate, string sRegistNo)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientDataRegist(sMedicalCheckDate, sRegistNo);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分(健診者ID)
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。

        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientDataRegistDiv(string sMedicalCheckDate, string sRegistNo, string sDivision)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientDataRegistDiv(sMedicalCheckDate, sRegistNo, sDivision);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientDataSeachDiv(string sMedicalCheckDate, string sClientID)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientDataSearchDiv(sMedicalCheckDate, sClientID);
            return medicalClientlist;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientDataSeachDivRegist(string sMedicalCheckDate, string sRegistNo)
        {
            strCSrv = new ClientService();
            string[] medicalClientlist = strCSrv.StrGetClientDataSearchDivRegist(sMedicalCheckDate, sRegistNo);
            return medicalClientlist;
        }

        #endregion

        #region MedicalCheckData用の部分

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            getDsSrv = new GetDataStateService();
            string[] medicalDatalist = getDsSrv.StrGetData(sMedicalCheckDate, sClientID, sDivision, termID);
            return medicalDatalist;
        }

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetDataCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            getDsSrv = new GetDataStateService();
            string[] medicalDatalist = getDsSrv.StrGetDataCheckNo(sMedicalCheckDate, sClientID, sDivision, sMedicalCheckNo);
            return medicalDatalist;
        }

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="medicalData"></param>
        /// <param name="iDataCount"></param>
        /// <param name="sStartMedicalCheckDate"></param>
        /// <param name="sEndMedicalCheckDate"></param>
        [WebMethod]
        public void StrSetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg)
        {
            //日付(yyyymmddHHmmss形式)をDateTime型に変換する
            string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
            DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
            setDsSrv = new SetDataStateService();
            setDsSrv.StrSetData(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg, nowTime);
        }

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="medicalData"></param>
        /// <param name="iDataCount"></param>
        /// <param name="sStartMedicalCheckDate"></param>
        /// <param name="sEndMedicalCheckDate"></param>
        [WebMethod]
        public void StrSetDataTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg, DateTime nowTime)
        {
            setDsSrv = new SetDataStateService();
            setDsSrv.StrSetData(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg, nowTime);
        }

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDataPlural(string sMedicalCheckDate, string termID)
        {
            getDsSrv = new GetDataStateService();
            PluralRecord[] medicalDatalist = getDsSrv.StrGetDataPlural(sMedicalCheckDate, termID);
            return medicalDatalist;
        }

        /// <summary>
        /// MedicalCheckData用の部分
        /// データセットに健診データを読み込む
        /// 戻り値としてStringの配列で健診データを返却する。全データ返却
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDataAll(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            getDsSrv = new GetDataStateService();
            PluralRecord[] medicalDatalist = getDsSrv.StrGetDataAll(sMedicalCheckDate, sClientID, sDivision);
            return medicalDatalist;
        }

        #endregion

        #region MedicalCheckState用の部分

        /// <summary>
        /// MedicalCheckState用の部分
        /// データセットに健診状況を読み込む
        /// 戻り値としてStringの配列で健診状況を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetState(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            getDsSrv = new GetDataStateService();
            string[] medicalStatelist = getDsSrv.StrGetState(sMedicalCheckDate, sClientID, sDivision);
            return medicalStatelist;
        }

        /// <summary>
        /// 次検査用の部分
        /// データセットに健診状況を読み込む。
        /// MedicalCheckStateテーブルのStateの中に"O(オー)"があるかを検索する。
        /// 無い場合は設定範囲内で、見つかるまで検索　→　待ち　→　ループを繰り返す。
        /// 設定値はWeb.config内に記述してある。デフォルトは待ち500ms、ループ3回としてある。
        /// Oが見つかったカウントをキー(MedicalCheckNo)に、MedicalCheckListテーブルから
        /// 受診項目を検索し、戻り値として返却する。
        /// 見つからなかった場合は、""を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetStateNext(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            strNeSrv = new NextService();
            //次検査項目格納用
            string nextCheckItem = strNeSrv.StrGetStateNext(sMedicalCheckDate, sClientID, sDivision);
            return nextCheckItem;
        }

        /// <summary>
        /// 健診状況を更新する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        [WebMethod]
        public void StrSetState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg)
        {
            //日付(yyyymmddHHmmss形式)をDateTime型に変換する
            string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
            DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
            setDsSrv = new SetDataStateService();
            setDsSrv.StrSetState(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg, nowTime);
        }

        /// <summary>
        /// 健診状況を更新する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        [WebMethod]
        public void StrSetStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg, DateTime nowTime)
        {
            setDsSrv = new SetDataStateService();
            setDsSrv.StrSetState(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg, nowTime);
        }

        #endregion

        #region MedicalCheckListの部分

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetMedicalCheckList(short iMedicalCheckNo)
        {
            strNeSrv = new NextService();
            string MedicalCheckName = strNeSrv.StrGetMedicalCheckList(iMedicalCheckNo);
            return MedicalCheckName;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetMedicalCheckListNo(string sMedicalCheckName)
        {
            strNeSrv = new NextService();
            string sMedicalCheckNo = strNeSrv.StrGetMedicalCheckListNo(sMedicalCheckName);
            return sMedicalCheckNo;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMedicalCheckListAll()
        {
            strNeSrv = new NextService();
            string[] MedicalCheckName = strNeSrv.StrGetMedicalCheckListAll();
            return MedicalCheckName;
        }

        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalCheckListPlural()
        {
            strNeSrv = new NextService();
            PluralRecord[] MedicalCheckName = strNeSrv.StrGetMedicalCheckListPlural();
            return MedicalCheckName;
        }

        #endregion

        #region データ更新(状況・データ両方)をトランザクション付き
        /// <summary>
        /// 開始時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state)
        {
            int timeFlg = 0;
            string[] medicalData = null;

            //日付(yyyymmddHHmmss形式)をDateTime型に変換する
            string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
            DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData)
        {
            int timeFlg = 1;

            //日付(yyyymmddHHmmss形式)をDateTime型に変換する
            string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
            DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        /// <summary>
        /// 開始時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, DateTime nowTime)
        {
            int timeFlg = 0;
            string[] medicalData = null;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, DateTime nowTime)
        {
            int timeFlg = 1;

            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        /// <summary>
        /// 開始時のデータ更新(状況・データ両方)をトランザクション付きで行う。複数レコード用
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, DateTime nowTime)
        {
            int timeFlg = 0;
            string[] medicalData = null;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。複数レコード用
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, DateTime nowTime)
        {
            int timeFlg = 1;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, nowTime);
            return updateResult;
        }

        #region 時間管理用に追加

        /// <summary>
        /// 開始時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateDataStateTimeTrans(
            string sMedicalCheckDate, string sClientID, string sDivision, string termID,
            string state, DateTime startTime, DateTime endTime, bool hostTransFlg)
        {
            int timeFlg = 0;
            string[] medicalData = null;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(
                sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, startTime, endTime, hostTransFlg);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataStateTimeTrans(
            string sMedicalCheckDate, string sClientID, string sDivision, string termID,
            string state, string[] medicalData, DateTime startTime, DateTime endTime, bool hostTransFlg)
        {
            int timeFlg = 1;

            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(
                sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg, startTime, endTime, hostTransFlg);
            return updateResult;
        }

        /// <summary>
        /// 開始時のデータ更新(状況・データ両方)をトランザクション付きで行う。複数レコード用
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateDataStateTimeTransMore(
            string sMedicalCheckDate, string sClientID, string sDivision, string[] termID,
            string[] state, DateTime startTime, DateTime endTime, bool hostTransFlg)
        {
            int timeFlg = 0;
            string[] medicalData = null;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(
                sMedicalCheckDate, sClientID, sDivision, termID,
                state, medicalData, timeFlg, startTime, endTime, hostTransFlg);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。複数レコード用
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataStateTimeTransMore(
            string sMedicalCheckDate, string sClientID, string sDivision, string[] termID,
            string[] state, string[] medicalData, DateTime startTime, DateTime endTime, bool hostTransFlg)
        {
            int timeFlg = 1;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(
                sMedicalCheckDate, sClientID, sDivision, termID,
                state, medicalData, timeFlg, startTime, endTime, hostTransFlg);
            return updateResult;
        }

        /// <summary>
        /// 終了時のデータ更新(状況・データ両方)をトランザクション付きで行う。（時間更新は行わない）
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <param name="medicalData"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrEndUpdateDataStateNoTimeTrans(
            string sMedicalCheckDate, string sClientID, string sDivision, string termID,
            string state, string[] medicalData, bool hostTransFlg)
        {
            int timeFlg = 0;

            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(
                sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, timeFlg,
                DateTime.MinValue, DateTime.MinValue, hostTransFlg);
            return updateResult;
        }

        #endregion

        #endregion

        #region PermissionIPの部分

        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPをキーに、MedicalCheckNo、TermIDを返却する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetPermissionIP(string sIp)
        {
            System.Diagnostics.Debug.WriteLine("StrGetPermissionIP Start");                    // ★
            strITSrv = new IpTermSetService();
            string[] permIpList = strITSrv.StrGetPermissionIP(sIp);
            System.Diagnostics.Debug.WriteLine("StrGetPermissionIP End");                      // ★
            return permIpList;
        }

        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPをキーに、MedicalCheckNo、TermIDを返却する(複数レコード)
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPermissionIP_Plural(string sIp)
        {
            strITSrv = new IpTermSetService();
            PluralRecord[] permIpList = strITSrv.StrGetPermissionIP_Plural(sIp);
            return permIpList;
        }

        /// <summary>
        /// PermissionIPの部分
        /// テーブルに設定されている、すべてのIPを返却する。
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetPermissionIPallkeys()
        {
            strITSrv = new IpTermSetService();
            String[] permIpList = strITSrv.StrGetPermissionIPall();
            return permIpList;
        }

        /// <summary>
        /// PermissionIPの部分
        /// テーブルに設定されている、すべてのIPを返却する。
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPermissionIPallPlural()
        {
            strITSrv = new IpTermSetService();
            PluralRecord[] permIpList = strITSrv.StrGetPermissionIPallPlural();
            return permIpList;
        }

        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPとtermIDをキーに、MedicalCheckNo、TermIDを登録する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetPermissionIP(string sIp, string termID, string beforeTermID)
        {
            strITSrv = new IpTermSetService();
            bool updateResult = strITSrv.StrSetPermissionIP(sIp, termID, beforeTermID);
            return updateResult;
        }

        /// <summary>
        /// PermissionIPを削除する。
        /// IPを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeletePermissionIP(string sIp)
        {
            strITSrv = new IpTermSetService();
            bool deleteResult = strITSrv.DeletePermissionIP(sIp);
            return deleteResult;
        }

        #endregion

        #region TerminalSettingの部分

        /// <summary>
        /// TerminalSettingの部分
        /// 渡されたIPとSettingIDをキーに、設定値を返却する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetTerminalSetting(string sIp, short iSettingId)
        {
            strITSrv = new IpTermSetService();
            string[] permIpList = strITSrv.StrGetTerminalSetting(sIp, iSettingId);
            return permIpList;
        }
        /// <summary>
        /// TerminalSettingの部分
        /// 渡されたIPとSettingIDをキーに、設定値を返却する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetTerminalSettingPlural(string sIp)
        {
            strITSrv = new IpTermSetService();
            PluralRecord[] permIpList = strITSrv.StrGetTerminalSettingPlural(sIp);
            return permIpList;
        }
        /// <summary>
        /// TerminalSettingの部分
        /// 渡されたIPとSettingIDと設定値を登録する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetTerminalSetting(string sIp, short iSettingId, string iContent)
        {
            strITSrv = new IpTermSetService();
            bool updateResult = strITSrv.StrSetTerminalSetting(sIp, iSettingId, iContent);
            return updateResult;
        }

        /// <summary>
        /// TerminalSettingを削除する。
        /// IPとSettingIdを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteTerminalSetting(string sIp, short iSettingId)
        {
            strITSrv = new IpTermSetService();
            bool deleteResult = strITSrv.DeleteTerminalSetting(sIp, iSettingId);
            return deleteResult;
        }

        #endregion

        #region 端末データ更新
        /// <summary>
        /// 開始時の端末データの更新を行う
        /// </summary>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StartUpdateTermStatus(string termID)
        {
            string state = "1";
            strMaSrv = new MachineService();
            bool result = strMaSrv.UpdateTermStatus(termID, state);
            return result;
        }

        /// <summary>
        /// 終了時の端末データの更新を行う
        /// </summary>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool EndUpdateTermStatus(string termID)
        {
            string state = "0";
            strMaSrv = new MachineService();
            bool result = strMaSrv.UpdateTermStatus(termID, state);
            return result;
        }
        #endregion

        #region 過去データ取得分
        /// <summary>
        /// 受診日時と、当日IDと、区分から
        /// 健診者ID（ユニークな値）を導き出し、当日以外で一番新しい受診日の
        /// 受診日付と当日IDを取得する。その後、端末IDから過去データを求める。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetBeforeOldData(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientData(sMedicalCheckDate, sClientID, sDivision);
            string[] medicalDatalist = null;
            if (beforeClientList != null)
            {
                getDsSrv = new GetDataStateService();
                medicalDatalist = getDsSrv.StrGetData(beforeClientList[0].ToString(), beforeClientList[1].ToString(),
                    beforeClientList[2].ToString(), termID);
            }
            return medicalDatalist;
        }

        /// <summary>
        /// 受診日時と、当日IDと、区分から
        /// 健診者ID（ユニークな値）を導き出し、当日以外で一番新しい受診日の
        /// 受診日付と当日IDを取得する。その後、端末IDから過去データを求める。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetBeforeOldDataNoDiv(string sMedicalCheckDate, string sClientID, string termID)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientData(sMedicalCheckDate, sClientID);
            string[] medicalDatalist = null;
            if (beforeClientList != null)
            {
                getDsSrv = new GetDataStateService();
                medicalDatalist = getDsSrv.StrGetData(beforeClientList[0].ToString(), beforeClientList[1].ToString(),
                    beforeClientList[2].ToString(), termID);
            }
            return medicalDatalist;
        }

        [WebMethod]
        public String[] StrGetBeforeOldCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            strOldCsSrv = new OldClientService();
            string[] beforeClientList = strOldCsSrv.StrGetBeforeClientData(sMedicalCheckDate, sClientID, sDivision);
            string[] medicalDatalist = null;
            if (beforeClientList != null)
            {
                getDsSrv = new GetDataStateService();
                medicalDatalist = getDsSrv.StrGetDataCheckNo(beforeClientList[0].ToString(), beforeClientList[1].ToString(), beforeClientList[2].ToString(), sMedicalCheckNo);
            }
            return medicalDatalist;
        }

        #endregion

        #region データベース期限管理処理
        /// <summary>
        /// データベース期限管理処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DatabaseLimitControl()
        {
            strCSrv = new ClientService();
            bool limitResult = strCSrv.DatabaseLimitControl();
            return limitResult;
        }
        #endregion

        #region GetDataName取得処理
        /// <summary>
        /// GetDataName取得処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetDataName(string termID)
        {
            strMaSrv = new MachineService();
            string[] dataNameList = strMaSrv.StrGetDataName(termID);
            return dataNameList;
        }
        #endregion

        #region ME機器部分

        /// <summary>
        /// MedicalEngineeringCheckUpName取得処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMeCheckUpName(string termID)
        {
            strMeSrv = new MeMachinesService();
            string[] dataNameList = strMeSrv.StrGetMeCheckUpName(termID);
            return dataNameList;
        }

        /// <summary>
        /// MedicalEngineeringCheckUpName取得処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMeCheckUpNameNo(string sMedicalCheckNo)
        {
            strMeSrv = new MeMachinesService();
            string[] dataNameList = strMeSrv.StrGetMeCheckUpNameNo(sMedicalCheckNo);
            return dataNameList;
        }

        /// <summary>
        /// MedicalEngineeringCheckUpName取得処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMeCheckUpNameKindNo(string sKindNo)
        {
            strMeSrv = new MeMachinesService();
            string[] dataNameList = strMeSrv.StrGetMeCheckUpNameKindNo(sKindNo);
            return dataNameList;
        }

        /// <summary>
        /// MedicalEngineeringConnectSetting取得処理
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetMeConnectSetting(string sDll, short iMeId)
        {
            strMeSrv = new MeMachinesService();
            string[] dataNameList = strMeSrv.StrGetMeConnectSetting(sDll, iMeId);
            return dataNameList;
        }

        /// <summary>
        /// ME接続仕様を更新する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        [WebMethod]
        public bool StrSetMeConnectSetting(string sDll, short iMeId, string[] setting, string[] medicalData)
        {
            strMeSrv = new MeMachinesService();
            bool updateResult = strMeSrv.StrSetMeConnectSetting(sDll, iMeId, setting, medicalData);
            return updateResult;
        }

        #endregion

        #region 必須検査項目用
        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetNeedCheckup(string termID)
        {
            strNeedSrv = new NeedCheckupService();
            string[] NeedCheckup = strNeedSrv.StrGetNeedCheckup(termID);
            return NeedCheckup;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetNeedCheckupPlural()
        {
            strNeedSrv = new NeedCheckupService();
            PluralRecord[] NeedCheckup = strNeedSrv.StrGetNeedCheckupPlural();
            return NeedCheckup;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを設定する。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetNeedCheckup(string termID, string[] checkObjectNos)
        {
            strNeedSrv = new NeedCheckupService();
            return strNeedSrv.StrSetNeedCheckup(termID, checkObjectNos);
        }

        /// <summary>
        /// NeedCheckupを削除する。
        /// sMedicalCheckNoを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteNeedCheckup(string termID)
        {
            strNeedSrv = new NeedCheckupService();
            bool deleteResult = strNeedSrv.DeleteNeedCheckup(termID);
            return deleteResult;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetNeedCheckupMedicalNo(string sMedicalCheckNo)
        {
            strNeedSrv = new NeedCheckupService();
            string[] NeedCheckup = strNeedSrv.StrGetNeedCheckupMedicalNo(sMedicalCheckNo);
            return NeedCheckup;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを設定する。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetNeedCheckupMedicalNo(string sMedicalCheckNo, string[] checkObjectNos)
        {
            strNeedSrv = new NeedCheckupService();
            return strNeedSrv.StrSetNeedCheckupMedicalNo(sMedicalCheckNo, checkObjectNos);
        }

        /// <summary>
        /// NeedCheckupを削除する。
        /// sMedicalCheckNoを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteNeedCheckupMedicalNo(string sMedicalCheckNo)
        {
            strNeedSrv = new NeedCheckupService();
            bool deleteResult = strNeedSrv.DeleteNeedCheckupMedicalNo(sMedicalCheckNo);
            return deleteResult;
        }

        #endregion

        #region 個別管理画面用

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetIndividualControlPlural()
        {
            strIndSrv = new IndividualControl();
            PluralRecord[] IndividualControl = strIndSrv.StrGetIndividualControlPlural();
            return IndividualControl;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetIndividualControl(string iPAddress)
        {
            strIndSrv = new IndividualControl();
            string[] IndividualControl = strIndSrv.StrGetIndividualControl(iPAddress);
            return IndividualControl;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを設定する。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetIndividualControl(string iPAddress, string[] dispCheckNos, string[] checkObjectNos)
        {
            strIndSrv = new IndividualControl();
            return strIndSrv.StrSetIndividualControl(iPAddress, dispCheckNos, checkObjectNos);
        }

        /// <summary>
        /// NeedCheckupを削除する。
        /// sMedicalCheckNoを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteIndividualControl(string iPAddress)
        {
            strIndSrv = new IndividualControl();
            bool deleteResult = strIndSrv.DeleteIndividualControl(iPAddress);
            return deleteResult;
        }

        #endregion

        #region 検査項目取得用

        /// <summary>
        /// MedicalCheckNoをキーに、検査項目レコードを導き出す。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetCheckItem(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            strCitSrv = new CheckItemService();
            string[] medicalItemlist = strCitSrv.StrGetCheckItem(sMedicalCheckDate, sClientID, sDivision, termID);
            return medicalItemlist;
        }

        [WebMethod]
        public PluralRecord[] StrGetCheckItemPlural(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            strCitSrv = new CheckItemService();
            PluralRecord[] medicalItemlist = strCitSrv.StrGetCheckItemsPlural(sMedicalCheckDate, sClientID, sDivision);
            return medicalItemlist;
        }

        #endregion

        #region コンシェルジュ用
        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetConciergeControl(string sGroupId)
        {
            strCcSrv = new ConciergeControlService();
            string[] ConciergeControl = strCcSrv.StrGetConciergeControl(sGroupId);
            return ConciergeControl;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを導き出す。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetConciergeControlPlural()
        {
            strCcSrv = new ConciergeControlService();
            PluralRecord[] ConciergeControl = strCcSrv.StrGetConciergeControlPlural();
            return ConciergeControl;
        }

        /// <summary>
        /// MedicalCheckNoをキーに、未検査チェックNoを設定する。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetConciergeControl(string sGroupId, string sGroupName, string sShortGroupName, string[] checkObjectNos)
        {
            strCcSrv = new ConciergeControlService();
            return strCcSrv.StrSetConciergeControl(sGroupId, sGroupName, sShortGroupName, checkObjectNos);
        }

        /// <summary>
        /// ConciergeControlを削除する。
        /// sMedicalCheckNoを元に削除処理を行う。
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="iSettingId"></param>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteConciergeControl(string sGroupId)
        {
            strCcSrv = new ConciergeControlService();
            bool deleteResult = strCcSrv.DeleteConciergeControl(sGroupId);
            return deleteResult;
        }

        #endregion

        #region 健診者情報の更新
        /// <summary>
        /// 健診者情報の更新
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetClientData(string sMedicalCheckDate, string sClientID, string sDivision, string[] regData, int regPos)
        {
            strCSrv = new ClientService();
            return strCSrv.StrSetClientData(sMedicalCheckDate, sClientID, sDivision, regData, regPos);
        }

        /// <summary>
        /// 開始時のデータ更新(状況)を行う。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="termID"></param>
        /// <param name="state"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrStartUpdateStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, DateTime nowTime)
        {
            int timeFlg = 0;
            bool updateResult = false;
            try
            {
                setDsSrv = new SetDataStateService();
                setDsSrv.StrSetState(sMedicalCheckDate, sClientID, sDivision, termID, state, timeFlg, nowTime);
                updateResult = true;
            }
            catch (Exception)
            {
                updateResult = false;
            }
            return updateResult;
        }

        #endregion

        #region 健診者情報をまとめて取得する。
        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClientDataPluralCheckNoCustom(string sMedicalCheckDate, string[] sMedicalCheckNo)
        {
            strCSrv = new ClientService();
            PluralRecord[] medicalClientlist = strCSrv.StrGetClientDataPluralCheckNo(sMedicalCheckDate, sMedicalCheckNo);
            return medicalClientlist;
        }
        #endregion

        #region 健診者情報をまとめて取得する(DataTable)。
        /// <summary>
        /// ClientInformationの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public DataTable StrGetClientDataPluralCheckNoTable(string sql)
        {
            strCSrv = new ClientService();
            DateTime dCheckDate;
            if (string.IsNullOrEmpty(sql))
            {
                DataTable getTabel = strCSrv.StrGetClientDataPluralCheckNoTable(DateTime.Now.ToString("yyyyMMdd"));
                return getTabel.Copy();
            }
            else if (DateTime.TryParseExact(sql, "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None, out dCheckDate))
            {
                DataTable getTabel = strCSrv.StrGetClientDataPluralCheckNoTable(sql);
                return getTabel.Copy();
            }
            else
            {
                DataTable getTabel = strCSrv.StrGetClientDataCheckNoTable(sql);
                return getTabel.Copy();
            }
        }
        #endregion

        #region ConciergeMemberListの部分

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetConciergeMemberList(short iMedicalCheckNo)
        {
            strCmSrv = new ConciergeMemberListService();
            return strCmSrv.StrGetConciergeMemberList(iMedicalCheckNo);
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetConciergeMemberListNo(string sMedicalCheckName)
        {
            strCmSrv = new ConciergeMemberListService();
            return strCmSrv.StrGetConciergeMemberListNo(sMedicalCheckName);
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetConciergeMemberListAll()
        {
            strCmSrv = new ConciergeMemberListService();
            return strCmSrv.StrGetConciergeMemberListAll();
        }

        /// <summary>
        /// ConciergeMemberListの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetConciergeMemberListPlural()
        {
            strCmSrv = new ConciergeMemberListService();
            return strCmSrv.StrGetConciergeMemberListPlural();
        }

        #endregion

        #region ConciergeTimeGroupListの部分

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetConciergeTimeGroupList(short iMedicalCheckNo)
        {
            strCgSrv = new ConciergeTimeGroupListService();
            return strCgSrv.StrGetConciergeTimeGroupList(iMedicalCheckNo);
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String StrGetConciergeTimeGroupListNo(string sMedicalCheckName)
        {
            strCgSrv = new ConciergeTimeGroupListService();
            return strCgSrv.StrGetConciergeTimeGroupListNo(sMedicalCheckName);
        }

        /// <summary>
        /// MedicalCheckNoをキーに、MedicalCheckNameを導き出す。
        /// </summary>
        /// <param name="iMedicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetConciergeTimeGroupListAll()
        {
            strCgSrv = new ConciergeTimeGroupListService();
            return strCgSrv.StrGetConciergeTimeGroupListAll();
        }

        /// <summary>
        /// ConciergeMemberListの部分
        /// データセットに受信者情報を読み込む
        /// 戻り値としてStringの配列で受信者情報を返却する。
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetConciergeTimeGroupListPlural()
        {
            strCgSrv = new ConciergeTimeGroupListService();
            return strCgSrv.StrGetConciergeTimeGroupListPlural();
        }

        #endregion

        #region ==========標準化用追加==========

        #region ClickSwitchSetting全取得
        /// <summary>
        /// ClickSwitchSettingの情報を全て取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClickSwitchSettingTableAll()
        {
            clickSrv = new ClickSwitchSettingService();
            return clickSrv.GetClickSwitchSettingAll();
        }
        #endregion

        #region ClickSwitchSettingの情報取得(Key指定）
        /// <summary>
        /// ClickSwitchSettingの情報を取得(Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClickSwitchSettingTable
            (string medicalCheckNo, string itemNo)
        {
            clickSrv = new ClickSwitchSettingService();
            return this.clickSrv.GetClickSwitchSetting(medicalCheckNo, itemNo);
        }
        #endregion

        #region DataTransformedSettingの情報を全て取得
        /// <summary>
        /// DataTransformedSettingの情報を全て取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDataTransformedSettingAll()
        {
            transSrv = new DataTransformedService();
            return this.transSrv.GetDataTransformedSettingAll();
        }
        #endregion

        #region DataTransformedSettingの情報を取得（Key指定）
        /// <summary>
        /// DataTransformedSettingの情報を取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDataTransformedSetting(
            string medicalCheckNo,
            string itemNo)
        {
            transSrv = new DataTransformedService();
            return this.transSrv.GetDataTransformedSetting(medicalCheckNo, itemNo);
        }
        #endregion

        #region DispChangeSettingの情報を全取得
        /// <summary>
        /// DispChangeSettingの情報を全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDispChangeSettingAll()
        {
            dispChangeSrv = new DispChangeSettingService();
            return this.dispChangeSrv.GetDispChangeSettingAll();
        }
        #endregion

        #region DispChangeSettingの情報取得（Key指定）
        /// <summary>
        /// DispChangeSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetDispChangeSetting(
            string medicalCheckNo,
            string itemNo)
        {
            dispChangeSrv = new DispChangeSettingService();
            return this.dispChangeSrv.GetdispChangeSetting(medicalCheckNo, itemNo);
        }
        #endregion

        #region MedicalItemListの情報の全取得
        /// <summary>
        /// MedicalItemListの情報の全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemListAll()
        {
            itemListSrv = new MedicalItemListService();
            return this.itemListSrv.GetMedicalItemListAll();
        }
        #endregion

        #region MedicalItemListの情報取得（Key指定）
        /// <summary>
        /// MedicalItemListの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemList(
            string medicalCheckNo,
            string itemNo)
        {
            itemListSrv = new MedicalItemListService();
            return this.itemListSrv.GetMedicalItemList(medicalCheckNo, itemNo);
        }
        #endregion

        #region MedicalItemListの情報取得（Key指定）
        /// <summary>
        /// MedicalItemListの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemListNo(string medicalCheckNo)
        {
            itemListSrv = new MedicalItemListService();
            return this.itemListSrv.GetMedicalItemListNo(medicalCheckNo);
        }
        #endregion

        #region MedicalItemSettingの情報全取得
        /// <summary>
        /// MedicalItemSettingの情報全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemSettingAll()
        {
            //return this.itemSettingSrv.GetMedicalItemSettingAll();
            formDispSetSrv = new FormDispSettingService();
            return this.formDispSetSrv.GetFormDispSettingAll();
        }
        #endregion

        #region MedicalItemSettingの情報取得（Key指定）
        /// <summary>
        /// MedicalItemSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemSettingNo(string medicalCheckNo)
        {
            //return this.itemSettingSrv.GetMedicalItemSettingNo(medicalCheckNo);
            formDispSetSrv = new FormDispSettingService();
            return this.formDispSetSrv.GetFormDispSettingNo(medicalCheckNo);
        }
        #endregion

        #region MedicalItemSettingの情報取得（Key指定）
        /// <summary>
        /// MedicalItemSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemSetting(
            string medicalCheckNo,
            string itemNo,
            string dataType)
        {
            //return this.itemSettingSrv.GetMedicalItemSetting(medicalCheckNo, itemNo, dataType);
            formDispSetSrv = new FormDispSettingService();
            return this.formDispSetSrv.GetFormDispSettingNo(medicalCheckNo, itemNo, dataType);
        }
        #endregion

        [WebMethod]
        public PluralRecord[] StrGetMedicalItemSettingDataType(
            string medicalCheckNo,
            string dataType)
        {
            formDispSetSrv = new FormDispSettingService();
            return this.formDispSetSrv.GetFormDispSettingNo(medicalCheckNo, dataType);
        }

        #region MedicalItemSettingの情報取得（設定画面用）
        /// <summary>
        /// MedicalItemSettingの情報取得（設定画面用）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalItemSettingMenu(string medicalCheckNo)
        {
            itemSettingSrv = new MedicalItemSettingService();
            return this.itemSettingSrv.GetMedicalItemSettingMenu(medicalCheckNo);
        }
        #endregion

        #region MedicalItemSettingの情報取得（設定画面用）
        /// <summary>
        /// MedicalItemSettingの情報取得（設定画面用）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetMedicalItemSettingMenu(string medicalCheckNo, List<string[]> updateValue, int updateType)
        {
            itemSettingSrv = new MedicalItemSettingService();
            return this.itemSettingSrv.SetMedicalItemSettingMenu(medicalCheckNo, updateValue, updateType);
        }
        #endregion

        #region FormModeDispの全取得
        /// <summary>
        /// FormModeDispの情報全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetFormModeDispAll()
        {
            formModeSrv = new FormModeDispService();
            return this.formModeSrv.GetFormDispModeAll();
        }
        #endregion

        #region FormModeDispの情報取得（Key指定）
        /// <summary>
        /// FormModeDispの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetFormModeDisp(
            string medicalCheckNo)
        {
            formModeSrv = new FormModeDispService();
            return this.formModeSrv.GetFormDispMode(medicalCheckNo);
        }
        #endregion

        #region PluralDispSettingの情報全取得
        /// <summary>
        /// PluralDispSettinの情報全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPluralDispSettingAll()
        {
            pluralDispSetSrv = new PluralDispSettingService();
            return this.pluralDispSetSrv.GetPluralDispSettingAll();
        }
        #endregion

        #region PluralDispSettingの情報取得（Key指定）
        /// <summary>
        /// PluralDispSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPluralDispSettingNo(string medicalCheckNo)
        {
            pluralDispSetSrv = new PluralDispSettingService();
            return this.pluralDispSetSrv.GetPluralDispSettingNo(medicalCheckNo);
        }
        #endregion

        #region PluralDispSettingの情報取得（Key指定）
        /// <summary>
        /// PluralDispSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPluralDispSetting(
            string medicalCheckNo,
            string itemNo)
        {
            pluralDispSetSrv = new PluralDispSettingService();
            return this.pluralDispSetSrv.GetPluralDispSetting(medicalCheckNo, itemNo);
        }
        #endregion

        #region PluralSettingの全取得
        /// <summary>
        /// PluralSettingの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPluralSettingAll()
        {
            pluralSetSrv = new PluralSetting();
            return this.pluralSetSrv.GetPluralSettingAll();
        }
        #endregion

        #region PluralSettingの情報取得（Key指定）
        /// <summary>
        /// PluralSettingの情報取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetPluralSetting(
            string medicalCheckNo)
        {
            pluralSetSrv = new PluralSetting();
            return this.pluralSetSrv.GetPlurakSetting(medicalCheckNo);
        }
        #endregion

        #region TransformedSettingの全取得
        /// <summary>
        /// TransformedSettingの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetTransformedSettingAll()
        {
            transSetSrv = new TransformedSettingService();
            return this.transSetSrv.GetTransformedSettingAll();
        }
        #endregion

        #region TransformedSettingの取得（Key指定）
        /// <summary>
        /// TransformedSettingの取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetTransformedSetting(
            string medicalCheckNo)
        {
            transSetSrv = new TransformedSettingService();
            return this.transSetSrv.GetTransformdSetting(medicalCheckNo);
        }
        #endregion

        #region ClientRemarksInformationの取得（Key指定）
        /// <summary>
        /// ClientRemarksInformationの取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public string[] StrGetClientRemarksData(
            string sMedicalCheckDate, string sClientID, string sDivision)
        {
            clientRemarksService = new ClientRemarksService();
            return this.clientRemarksService.StrGetClientRemarksData(sMedicalCheckDate, sClientID, sDivision);
        }
        #endregion

        #region ClientRemarksInformationの登録（Key指定）
        /// <summary>
        /// ClientRemarksInformationの登録
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetClientRemarksInformation(string sMedicalCheckDate, string sClientID, string sDivision, string[] commentData)
        {
            clientRemarksService = new ClientRemarksService();
            return this.clientRemarksService.StrSetClientRemarksData(sMedicalCheckDate, sClientID, sDivision, commentData);
        }
        #endregion

        #region RemarksItemListの情報の全取得
        /// <summary>
        /// RemarksItemListの情報の全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetRemarksItemListAll()
        {
            remarksItemListService = new RemarksItemListService();
            return this.remarksItemListService.GetRemarksItemListAll();
        }
        #endregion

        #region EyeGroupSelectの情報の全取得
        /// <summary>
        /// EyeGroupSelectの情報の全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeGroupSelectAll()
        {
            eyeGroupService = new EyeGroupSelectService();
            return this.eyeGroupService.GetEyeGroupSelectAll();
        }
        #endregion

        #region EyeGroupSelectの情報の取得（Key指定）
        /// <summary>
        /// EyeGroupSelectの情報の取得（Key指定）
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeGroupSelectId(string groupId)
        {
            eyeGroupService = new EyeGroupSelectService();
            return this.eyeGroupService.GetEyeGroupSelectId(groupId);
        }
        [WebMethod]
        public PluralRecord[] StrGetEyeGroupSelectMedicalCheckNo(string formId)
        {
            eyeGroupService = new EyeGroupSelectService();
            return this.eyeGroupService.GetEyeGroupMedicalCheckNo(formId);
        }
        #endregion

        #region EyeControlの情報の全取得
        /// <summary>
        /// EyeControlの情報の全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeControlAll()
        {
            eyeControlService = new EyeControlService();
            return this.eyeControlService.GetEyeControlAll();
        }
        #endregion

        #region EyeControlの情報の取得（Key指定）
        /// <summary>
        /// EyeControlの情報の取得（Key指定）
        /// </summary>
        /// <param name="controlId"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeControlId(string controlId)
        {
            eyeControlService = new EyeControlService();
            return this.eyeControlService.GetEyeControlId(controlId);
        }
        [WebMethod]
        public PluralRecord[] StrGetEyeControlMedicalcheckNo(string formId)
        {
            eyeControlService = new EyeControlService();
            return this.eyeControlService.GetEyeControlMedicalCheckNo(formId);
        }
        #endregion

        #region EyeInitialSelectの全取得
        /// <summary>
        /// EyeInitialSelectの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeInitialSelectAll()
        {
            eyeInitialService = new EyeInitialSelectService();
            return this.eyeInitialService.GetEyeInitialSelectAll();
        }
        #endregion

        #region EyeInitialSelectの取得（Key指定）
        /// <summary>
        /// EyeInitialSelectの取得（Key指定）
        /// </summary>
        /// <param name="initialId"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetEyeInitialSelectId(string initialId)
        {
            eyeInitialService = new EyeInitialSelectService();
            return this.eyeInitialService.GetEyeInitialSelectId(initialId);
        }
        [WebMethod]
        public PluralRecord[] StrGetEyeInitialSelectMedicalCheckNo(string formId)
        {
            eyeInitialService = new EyeInitialSelectService();
            return this.eyeInitialService.GetEyeInitialSelectMedicalCheckNo(formId);
        }
        #endregion

        //#region ReceiveOrderSettingテーブルの全取得
        ///// <summary>
        ///// ReceiveOrderSettingテーブルの全取得
        ///// </summary>
        ///// <returns></returns>
        //[WebMethod]
        //public PluralRecord[] StrGetReceiveOrderSettingAll()
        //{
        //    return this.receiveOrderService.GetReceiveOrderSettingAll();
        //}
        //#endregion

        //#region ReceiveOrderSettingテーブルの取得(Key指定）
        ///// <summary>
        ///// ReceiveOrderSettingテーブルの取得(Key指定）
        ///// </summary>
        ///// <param name="pMedicalCheckNo"></param>
        ///// <returns></returns>
        //[WebMethod]
        //public PluralRecord[] StrGetRecieveOrderSettingNo(string pMedicalCheckNo)
        //{
        //    return this.receiveOrderService.GetReceiveOrderSettingNo(pMedicalCheckNo);
        //}
        //#endregion

        #region MeDataRelationalテーブルの全取得
        /// <summary>
        /// MeDataRelationalテーブルの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMeDataRelationalAll()
        {
            meDataRelationalService = new MeDataRelationalService();
            return this.meDataRelationalService.GetMeDataRelationalAll();
        }
        #endregion

        #region MeDataRelationalテーブルの取得（Key指定）
        /// <summary>
        /// MeDataRelationalテーブルの取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMeDataRelationalNo(string medicalCheckNo)
        {
            meDataRelationalService = new MeDataRelationalService();
            return this.meDataRelationalService.GetMeDataRelationalNo(medicalCheckNo);
        }
        #endregion

        #region MeDataRelationalテーブルの取得（Key指定）
        /// <summary>
        /// MeDataRelationalテーブルの取得（Key指定）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMeDataRelationalItemNo(string medicalCheckNo, string itemNo)
        {
            meDataRelationalService = new MeDataRelationalService();
            return this.meDataRelationalService.GetMeDataRelationalItemNo(medicalCheckNo, itemNo);
        }
        #endregion

        #region MeDataRelationalテーブルの取得(Key指定）
        /// <summary>
        /// MeDataRelationalテーブルの取得（Key指定）
        /// </summary>
        /// <param name="meCode"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMeDataRelationalMeCode(string meCode)
        {
            meDataRelationalService = new MeDataRelationalService();
            return this.meDataRelationalService.GetMeDataRelationalMeCode(meCode);
        }
        #endregion

        #region FontSettingの全取得
        /// <summary>
        /// FontSettingの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetFontSettingAll()
        {
            fontSettingService = new FontSettingService();
            return this.fontSettingService.GetFontSettingAll();
        }
        #endregion

        #region FontSettingの取得(Key指定)
        /// <summary>
        /// FontSettingの取得(Key指定)
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetFontSettingFormId(string formId)
        {
            fontSettingService = new FontSettingService();
            return this.fontSettingService.GetFontSettingFormId(formId);
        }
        #endregion

        #region FontSettingの全取得
        /// <summary>
        /// FontSettingの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetFontSettingFormLabel(string formId, string label)
        {
            fontSettingService = new FontSettingService();
            return this.fontSettingService.GetFontSettingFormLabel(formId, label);
        }
        #endregion

        #region CustomizeButtonSettingの全取得
        /// <summary>
        /// CustomizeButtonSettingの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCustomizeButtonSettingAll()
        {
            customizeButtonService = new CustomizeButtonService();
            return this.customizeButtonService.GetCustomizeButtonSettingAll();
        }
        #endregion

        #region CustomizeButtonDetailの全取得
        /// <summary>
        /// CustomizeButtonDetailの全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCustomizeButtonDetailAll()
        {
            customizeButtonService = new CustomizeButtonService();
            return this.customizeButtonService.GetCustomizeButtonDetailAll();
        }
        #endregion

        #region CustomizeButtonDetailの取得(Key指定)
        /// <summary>
        /// CustomizeButtonDetailの取得(Key指定)
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCustomizeButtonDetailbyFormId(string formId)
        {
            customizeButtonService = new CustomizeButtonService();
            return this.customizeButtonService.GetCustomizeButtonDetailFormId(formId);
        }
        #endregion

        #endregion

        #region ==========AR1000L用追加==========

        /// <summary>
        /// LiteTerminalSettings取得処理（AR1000L用）
        /// 端末IDを元に端末情報１レコードを取得する
        /// </summary>
        /// <param name="liteTerminalID">端末ID</param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetLiteTerminalData(string liteTerminalID)
        {
            strLiteSrv = new LiteTerminalService();
            return strLiteSrv.StrGetLiteTerminalData(liteTerminalID);
        }

        /// <summary>
        /// LiteTerminalSettings取得処理（AR1000L用）
        /// 端末情報全レコードと検査種別を取得する
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetLiteTerminalDataAll()
        {
            strLiteSrv = new LiteTerminalService();
            return strLiteSrv.StrGetLiteTerminalDataAll();
        }

        /// <summary>
        /// LiteMachineSettings設定処理（AR1000L用）
        /// </summary>
        /// <param name="lmSingleData"></param>
        /// <param name="befClientID"></param>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetLiteTerminalData(string befClientID)
        {
            strLiteSrv = new LiteTerminalService();
            return strLiteSrv.StrSetLiteTerminalData(befClientID, DateTime.Now);
        }

        /// <summary>
        /// LiteMachineSettings設定処理（AR1000L用）～～～不要～～～
        /// IPを元に該当するレコードを削除する
        /// </summary>
        /// <param name="clientID"></param>
        /// <returns></returns>
        //[WebMethod]
        //public bool DeleteLiteMachineData(string clientID)
        //{
        //    bool deleteResult = strLiteSrv.DeleteLiteMachineSingleData(clientID);
        //    return deleteResult;
        //}

        #endregion

        #region ==========ヘルゼアNext用追加==========

        #region ClientAddInformationの取得（Key指定）
        /// <summary>
        /// ClientAddInformationの取得（Key指定）
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            addInforService = new ClientAddInformationService();
            return addInforService.StrGetClientAddInformation(sMedicalCheckDate, sClientID, sDivision);
        }
        #endregion

        #region ClientAddInformation2の取得（Key指定）
        /// <summary>
        /// ClientAddInformationの取得（Key指定）
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            addInfor2Service = new ClientAddInformation2Service();
            return addInfor2Service.StrGetClientAddInformation2(sMedicalCheckDate, sClientID, sDivision);
        }
        #endregion

        [WebMethod]
        public bool StrSetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            addInforService = new ClientAddInformationService();
            return addInforService.StrSetClientAddInfoData(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }

        [WebMethod]
        public bool StrSetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            addInfor2Service = new ClientAddInformation2Service();
            return addInfor2Service.StrSetClientAddInfo2Data(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }
        #region MedicalCheckReasonDataの取得（Key指定）
        /// <summary>
        /// MedicalCheckReasonDataの取得（Key指定）
        /// </summary>
        /// <param name="sMedialCheckDate"></param>
        /// <param name="sClientId"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalCheckReasonData(string sMedialCheckDate, string sClientId, string sDivision)
        {
            checkReasonDataService = new MedicalCheckReasonDataService();
            return checkReasonDataService.GetMedicalCheckReasonDataClientId(sMedialCheckDate, sClientId, sDivision);
        }
        #endregion

        #region MedicalCheckItemStateの取得（Key指定）
        /// <summary>
        /// MedicalCheckItemStateの取得（Key指定）
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientId"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetMedicalCheckItemState(string sMedicalCheckDate, string sClientId, string sDivision)
        {
            checkItemStateService = new MedicalCheckItemStateService();
            return checkItemStateService.GetMedicalCheckItemStateClientId(sMedicalCheckDate, sClientId, sDivision);
        }
        #endregion

        [WebMethod]
        public bool StrEndUpdateEndTimeAll(
            string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state,
            string[] medicalCheckData, string[] reasonData, string[] itemState, DateTime nowTime)
        {
            int timeFlg = 1;
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state,
                medicalCheckData, reasonData, itemState, timeFlg, nowTime);
            return updateResult;
        }

        #region 必須検査項目（項目毎）の全設定取得
        /// <summary>
        /// 必須検査項目（項目毎）の全設定取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetNeedCheckupItemAll()
        {
            needCheckupItemService = new NeedCheckupItemService();
            return this.needCheckupItemService.GetNeedCheckupItemAll();
        }
        #endregion

        [WebMethod]
        public PluralRecord[] StrGetNeedCheckupItem(string medicalCheckNo)
        {
            needCheckupItemService = new NeedCheckupItemService();
            return this.needCheckupItemService.GetNeedCheckupItem(medicalCheckNo);
        }
        [WebMethod]
        public PluralRecord[] StrGetNeedCheckupItemItemNo(string medicalCheckNo, string itemNo)
        {
            needCheckupItemService = new NeedCheckupItemService();
            return this.needCheckupItemService.GetNeedCheckupItem(medicalCheckNo, itemNo);
        }

        [WebMethod]
        public bool StrSetNeedCheckupItem(DataTable tbl)
        {
            needCheckupItemService = new NeedCheckupItemService();
            return this.needCheckupItemService.SetNeedCheckupItem(tbl);
        }

        #endregion

        #region ==========誘導支援用追加==========

        #region IniGuideMaster取得（Key指定）
        /// <summary>
        /// IniGuideMaster取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetIniGuideMaster(string ipAddress)
        {
            guideSettingSrv = new GuideSettingService();
            return guideSettingSrv.StrGetIniGuideMaster(ipAddress);
        }
        #endregion

        #region IniGuideMaster全取得
        /// <summary>
        /// IniGuideMaster全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetIniGuideMasterAll()
        {
            guideSettingSrv = new GuideSettingService();
            return guideSettingSrv.StrGetIniGuideMasterAll();
        }
        #endregion

        #region TermGuideData取得（Key指定）
        /// <summary>
        /// TermGuideData取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetTermGuideData(string ipAddress)
        {
            guideSettingSrv = new GuideSettingService();
            return guideSettingSrv.StrGetTermGuideData(ipAddress);
        }
        #endregion

        #region TermGuideData全取得
        /// <summary>
        /// TermGuideData全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetTermGuideDataAll()
        {
            guideSettingSrv = new GuideSettingService();
            return guideSettingSrv.StrGetTermGuideDataAll();
        }
        #endregion

        #region TermGuideData更新
        /// <summary>
        /// IniGuideMaster全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetTermGuideData(string ipAddress, string GuideFlg, string TermFlg)
        {
            guideSettingSrv = new GuideSettingService();
            bool updateResult = guideSettingSrv.StrSetTermGuideData(ipAddress, GuideFlg, TermFlg);
            return updateResult;
        }
        #endregion

        #region ClientGuideData（& ChildGroupMaster）取得（Key指定）
        /// <summary>
        /// ClientGuideData（& ChildGroupMaster）取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetClientGuideDataWithChildGroup(DateTime dateTime, string clientId, string division)
        {
            System.Diagnostics.Debug.WriteLine(DateTime.Now.ToString("HH:mm:ss.fff"));
            guideGroupSrv = new GuideGroupService();
            guideGroupSrv.StrGetClientGuideDataWithChildGroup(dateTime, clientId, division);
            System.Diagnostics.Debug.WriteLine(DateTime.Now.ToString("HH:mm:ss.fff"));
            return guideGroupSrv.StrGetClientGuideDataWithChildGroup(dateTime, clientId, division);
        }
        #endregion

        #region ChildGroupDetailMaster取得（Key指定）
        /// <summary>
        /// ChildGroupDetailMaster取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetChildGroupDetailMaster(string childGroupId)
        {
            short id;
            if (short.TryParse(childGroupId, out id))
            {
                guideGroupSrv = new GuideGroupService();
                return guideGroupSrv.StrGetChildGroupDetailMaster(id);
            }
            return null;
        }
        #endregion

        #region ChildGroupTermMaster取得（Key指定）
        /// <summary>
        /// ChildGroupTermMaster取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetChildGroupTermMaster(string childGroupId)
        {
            short id;
            if (short.TryParse(childGroupId, out id))
            {
                guideGroupSrv = new GuideGroupService();
                return guideGroupSrv.strGetChildGroupTermMaster(id);
            }
            return null;
        }
        #endregion

        #region ExamSettingMaster取得（Key指定）
        /// <summary>
        /// ExamSettingMaster取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetExamSettingMaster(string medicalCheckNo)
        {
            short id;
            if (short.TryParse(medicalCheckNo, out id))
            {
                guideSettingSrv = new GuideSettingService();
                return guideSettingSrv.StrGetExamSettingMaster(id);
            }
            return null;
        }
        #endregion

        #region 指定したステータスの人数を取得
        /// <summary>
        /// MedicalCheckStateの部分
        /// 指定したステータスの対象の人数を取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public DataTable StrGetTargetStateNumber(string sql)
        {
            getDsSrv = new GetDataStateService();
            DataTable table = getDsSrv.StrGetTargetStateNumber(sql);
            return table.Copy();
        }
        #endregion

        #region 受信日から全ステータスを取得
        /// <summary>
        /// MedicalCheckStateの部分
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetAllStateFromExecDate(string execDate)
        {
            getDsSrv = new GetDataStateService();
            return getDsSrv.StrGetAllStateFromExecDate(execDate);
        }
        #endregion

        #region ClientNextData取得（Key指定）
        /// <summary>
        /// ClientGuideData（& ChildGroupMaster）取得（Key指定）
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetNextGuideData(DateTime dateTime, string clientId, string division)
        {
            guideGroupSrv = new GuideGroupService();
            return guideGroupSrv.StrGetNextGuideData(dateTime, clientId, division);
        }
        #endregion

        #region NextGuideData削除
        /// <summary>
        /// NextGuideData削除。
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteNextGuideData(DateTime dateTime, string clientId, string division)
        {
            guideGroupSrv = new GuideGroupService();
            bool deleteResult = guideGroupSrv.DeleteNextGuideData(dateTime, clientId, division);
            return deleteResult;
        }
        #endregion

        #region NextGuideData更新
        /// <summary>
        /// NextGuideData更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetNextGuideData(DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            guideGroupSrv = new GuideGroupService();
            bool updateResult = guideGroupSrv.StrSetNextGuideData(dateTime, clientId, division, childGroupFlg, value);
            return updateResult;
        }
        #endregion

        #region NextGuideData更新
        /// <summary>
        /// NextGuideData更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetNextGuideDataHoldTime(DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            guideGroupSrv = new GuideGroupService();
            bool updateResult = guideGroupSrv.StrSetNextGuideDataHoldTime(dateTime, clientId, division, childGroupFlg, value);
            return updateResult;
        }
        #endregion

        #region 進捗状況のみを更新する
        /// <summary>
        /// 健診状況を更新する。
        /// </summary>
        [WebMethod]
        public bool StrSetStateOnly(string sMedicalCheckDate, string sClientID, string sDivision, string medicalCheckNo, string state)
        {
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetStateOnly(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, state);
            return updateResult;
        }
        #endregion

        #region 進捗状況のみを更新する
        /// <summary>
        /// 健診状況を更新する。
        /// </summary>
        [WebMethod]
        public bool StrSetStateOnlyPlural(string sMedicalCheckDate, string sClientID, string sDivision, string[] medicalCheckNo, string[] checkState)
        {
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetStateOnly(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, checkState);
            return updateResult;
        }
        #endregion

        #region MedicalCheckStateのStartTimeとEndTimeを削除する（誘導支援用）
        /// <summary>
        /// MedicalCheckStateのStartTimeとEndTimeを削除する（誘導支援用）
        /// </summary>
        [WebMethod]
        public bool StrSetStateTimeToNULL(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            setDsSrv = new SetDataStateService();
            bool updateResult = setDsSrv.StrSetStateTimeToNULL(sMedicalCheckDate, sClientID, sDivision);
            return updateResult;
        }
        #endregion

        #endregion

        #region ==========案内表示用追加==========

        #region CallClientData全取得
        /// <summary>
        /// CallClientData全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCallClientDataAll()
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallClientDataAll();
        }
        #endregion

        #region CallClientData１レコード取得
        /// <summary>
        /// CallClientData１レコード取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallClientData(sMedicalCheckDate, sClientId, sDivision, iExamId);
        }
        #endregion

        #region CallClientData更新
        /// <summary>
        /// CallClientData更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId, int iCallOrder, int iStatus)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrSetCallClientData(sMedicalCheckDate, sClientId, sDivision, iExamId, iCallOrder, iStatus);
        }
        #endregion

        #region CallClientData削除
        /// <summary>
        /// CallClientData削除
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteCallClientData(string sMedicalCheckData, string sClientId, string sDivision, int iExamId)
        {
            return callMonitorSettingSrv.DeleteCallClientData(sMedicalCheckData, sClientId, sDivision, iExamId);
        }
        #endregion

        #region CallExamMaster全取得
        /// <summary>
        /// CallExamMaster全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCallExamMasterAll()
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallExamMasterAll();
        }
        #endregion

        #region CallExamMaster１レコード取得
        /// <summary>
        /// CallExamMaster１レコード取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetCallExamMaster(short iExamId)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallExamMaster(iExamId);
        }
        #endregion

        #region CallExamMaster更新
        /// <summary>
        /// CallExamMaster更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetCallExamMaster(short iExamId, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string Param1, string Param2, string Param3)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrSetCallExamMaster(iExamId, sTitle, bStatus, sStartTime, sEndTime, iCapacity, iBackColor, sMessage, sHeader1, sHeader2, iMedicalCheckNos, iPanelType, Param1, Param2, Param3);
        }
        #endregion

        #region CallExamMaster削除
        /// <summary>
        /// CallExamMaster削除
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteCallExamMaster(short iExamId)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.DeleteCallExamMaster(iExamId);
        }
        #endregion

        #region CallStatus全取得
        /// <summary>
        /// CallStatus全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCallStatusAll()
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallStatusAll();
        }
        #endregion

        #region CallStatus更新
        /// <summary>
        /// CallStatus更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetCallStatus(int iExamId, int iMaxWaitTime)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrSetCallStatus(iExamId, iMaxWaitTime);
        }
        #endregion

        #region CallStatus削除
        /// <summary>
        /// CallStatus削除
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteCallStatus(int iExamId)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.DeleteCallStatus(iExamId);
        }
        #endregion

        #region CallTerminalMaster全取得
        /// <summary>
        /// CallTerminalMaster全取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] StrGetCallTerminalMasterAll()
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallTerminalMasterAll();
        }
        #endregion

        #region CallTerminalMaster１レコード取得
        /// <summary>
        /// CallTerminalMaster１レコード取得
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public String[] StrGetCallTerminalMaster(string sTermID)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrGetCallTerminalMaster(sTermID);
        }
        #endregion

        #region CallTerminalMaster更新
        /// <summary>
        /// CallTerminalMaster更新
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool StrSetCallTerminalMaster(string sTermID, string sIPAddress, int iIntervalTime, int[] iExamIds)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.StrSetCallTerminalMaster(sTermID, sIPAddress, iIntervalTime, iExamIds);
        }
        #endregion

        #region CallTerminalMaster削除
        /// <summary>
        /// CallTerminalMaster削除
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        public bool DeleteCallTerminalMaster(string sTermID)
        {
            callMonitorSettingSrv = new CallMonitorSettingService();
            return callMonitorSettingSrv.DeleteTerminalMaster(sTermID);
        }
        #endregion

        #endregion

        #region PermissionIPの部分
        #region GetSoundSettingPlural
        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPをキーに、MedicalCheckNo、TermIDを返却する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public PluralRecord[] GetSoundSettingPlural(string sIp)
        {
            soundSetService = new SoundSetService();
            PluralRecord[] soundSettingList = soundSetService.GetSoundSettingPlural(sIp);
            return soundSettingList;
        }
        #endregion
        #region GetSoundSettingOne
        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPをキーに、MedicalCheckNo、TermIDを返却する(複数レコード)
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public String[] GetSoundSettingOne(string sIp, string patternId)
        {
            soundSetService = new SoundSetService();
            String[] soundSettingList = soundSetService.GetSoundSettingOne(sIp, patternId);
            return soundSettingList;
        }
        #endregion
        #region SetSoundSetting
        /// <summary>
        /// PermissionIPの部分
        /// 渡されたIPとtermIDをキーに、MedicalCheckNo、TermIDを登録する
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        [WebMethod]
        public bool SetSoundSetting(string sIp, string patternId, string soundFileName,
            short? repetitionCount, string snoozeTime, short? volume)
        {
            soundSetService = new SoundSetService();
            bool updateResult = soundSetService.SetSoundSetting(sIp, patternId, soundFileName,
                repetitionCount, snoozeTime, volume);
            return updateResult;
        }
        #endregion
        #region PermissionIPを削除する
        /// <summary>
        /// PermissionIPを削除する。
        /// IPを元に削除処理を行う。
        /// </summary>
        [WebMethod]
        public bool DeleteSoundSetting(string sIp, string patternId)
        {
            soundSetService = new SoundSetService();
            bool deleteResult = soundSetService.DeleteSoundSetting(sIp, patternId);
            return deleteResult;
        }
        #endregion

        #region ファイルを作成する。
        /// <summary>
        /// 指定のパスにファイルを作成する。
        /// </summary>
        [WebMethod]
        public bool SFCreateServerFile(string sServerPath, string fileName, string msg, bool bOverWrite)
        {
            bool result = false;
            try
            {
                if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

                using (NetCredentialProc(sServerPath))
                {
                    //ファイル作成
                    string ext = Path.GetExtension(fileName);
                    string preFileName = Path.GetFileNameWithoutExtension(fileName);

                    string createFilePath = sServerPath + preFileName + ".tmp";
                    string moveFilePath = sServerPath + fileName;

                    bool bExist = File.Exists(moveFilePath);
                    if (!bOverWrite && bExist)
                    {
                        //ファイルが存在する。
                        return false;
                    }
                    else if (bOverWrite && bExist)
                    {
                        try
                        {
                            File.Delete(moveFilePath);
                        }
                        catch (Exception e)
                        {
                        }
                    }

                    //tmp拡張子でファイル作成
                    //File.WriteAllText(createFilePath, msg);
                    //2020.05.21 Base64変換で修正↓↓↓
                    //File.WriteAllText(createFilePath, msg, Encoding.GetEncoding(932));
                    byte[] fileBytes = Convert.FromBase64String(msg);
                    File.WriteAllText(createFilePath, Encoding.GetEncoding(932).GetString(fileBytes), Encoding.GetEncoding(932));
                    //2020.05.21 Base64変換で修正↑↑↑
                    //移動
                    File.Move(createFilePath, moveFilePath);
                }
                result = true;
            }
            catch (Exception e)
            {
                Logger.GetInstance().logWrite("ERROR SFCreateServerFile() Param[" + sServerPath + "][" + fileName + "][" + bOverWrite.ToString() + "][" + msg + "]");
                Logger.GetInstance().logWrite("ERROR SFCreateServerFile():" + e.Message + " " + e.StackTrace);
                throw e;
            }
            return result;
        }
        #endregion


        #region ファイルを作成する。（文字エンコード指定）
        /// <summary>
        /// 指定のパスにファイルを作成する。
        /// </summary>
        [WebMethod]
        public bool SFCreateServerFileForEncode(string sServerPath, string fileName, string msg, bool bOverWrite, string encode)
        {
            bool result = false;
            try
            {
                if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

                using (NetCredentialProc(sServerPath))
                {
                    //ファイル作成
                    string ext = Path.GetExtension(fileName);
                    string preFileName = Path.GetFileNameWithoutExtension(fileName);

                    string createFilePath = sServerPath + preFileName + ".tmp";
                    string moveFilePath = sServerPath + fileName;

                    bool bExist = File.Exists(moveFilePath);
                    if (!bOverWrite && bExist)
                    {
                        //ファイルが存在する。
                        return false;
                    }
                    else if (bOverWrite && bExist)
                    {
                        try
                        {
                            File.Delete(moveFilePath);
                        }
                        catch (Exception e)
                        {
                        }
                    }

                    //tmp拡張子でファイル作成
                    //File.WriteAllText(createFilePath, msg);
                    //2020.05.21 Base64変換で修正↓↓↓
                    //File.WriteAllText(createFilePath, msg, Encoding.GetEncoding(932));
                    byte[] fileBytes = Convert.FromBase64String(msg);
                    File.WriteAllText(createFilePath, Encoding.GetEncoding(encode).GetString(fileBytes), Encoding.GetEncoding(encode));
                    //2020.05.21 Base64変換で修正↑↑↑
                    //移動
                    File.Move(createFilePath, moveFilePath);
                }
                result = true;
            }
            catch (Exception e)
            {
                Logger.GetInstance().logWrite("ERROR SFCreateServerFileForEncode() Param[" + sServerPath + "][" + fileName + "][" + msg + "][" + bOverWrite.ToString() + "][" + encode + "]");
                Logger.GetInstance().logWrite("ERROR SFCreateServerFileForEncode():" + e.Message + " " + e.StackTrace);
                throw e;
            }
            return result;
        }
        #endregion

        #region ファイル存在チェックする。
        /// <summary>
        /// 指定のパスのファイルを取得する。
        /// </summary>
        [WebMethod]
        public bool SFGetFileExist(string sServerPath, string sFileName)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            //Logger.GetInstance().logWrite("SFGetFileExist()");

            using (NetCredentialProc(sServerPath))
            {
                string filePath = sServerPath + sFileName;

                //存在チェック
                if (File.Exists(filePath)) return true;
            }
            return false;
        }
        #endregion


        #region フォルダ存在チェックする。
        /// <summary>
        /// フォルダ存在チェックを取得する。
        /// </summary>
        [WebMethod]
        public bool SFGetDirectoryExist(string sServerPath)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            //Logger.GetInstance().logWrite("SFGetDirectoryExist()");

            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (Directory.Exists(sServerPath)) return true;
            }
            return false;
        }
        #endregion

        #region ファイルを取得する。
        /// <summary>
        /// 指定のパスのファイルを取得する。
        /// </summary>
        [WebMethod]
        public String SFGetFileContent(string sServerPath, string sFileName)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            string filePath = sServerPath + sFileName;

            byte[] ret = null;
            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (!File.Exists(filePath)) throw new FileNotFoundException("ファイルが存在しません。", filePath);

                //ファイルコンテンツ読み込み
                ret = File.ReadAllBytes(filePath);
            }
            //バイナリデータをBase64文字に変換
            return Convert.ToBase64String(ret, 0, ret.Length, Base64FormattingOptions.InsertLineBreaks);
        }
        #endregion

        #region ファイル作成日付取得
        /// <summary>
        /// ファイル作成日付取得
        /// </summary>
        [WebMethod]
        public DateTime SFGetFileCreateDate(string sServerPath, string sFileName)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            string filePath = sServerPath + sFileName;

            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (!File.Exists(filePath)) throw new FileNotFoundException("ファイルが存在しません。", filePath);

                //作成日付取得
                return File.GetCreationTime(filePath);
            }

            return DateTime.MinValue;
        }
        #endregion

        #region ファイル更新日付取得
        /// <summary>
        /// ファイル更新日付取得
        /// </summary>
        [WebMethod]
        public DateTime SFGetFileUpdateDate(string sServerPath, string sFileName)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            string filePath = sServerPath + sFileName;

            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (!File.Exists(filePath)) throw new FileNotFoundException("ファイルが存在しません。", filePath);

                //作成日付取得
                return File.GetLastWriteTime(filePath);
            }
            return DateTime.MinValue;
        }
        #endregion


        #region ファイルリスト取得。
        /// <summary>
        /// ファイルを削除する。
        /// </summary>
        [WebMethod]
        public String[] SFGetFileList(string sServerPath)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            String[] fileList = null;
            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (!Directory.Exists(sServerPath)) throw new FileNotFoundException("フォルダが存在しません。", sServerPath);

                //ファイルコンテンツ読み込み
                fileList = Directory.GetFiles(sServerPath);
            }
            //バイナリデータをBase64文字に変換
            return fileList;
        }
        #endregion


        #region フォルダリスト取得。
        /// <summary>
        /// フォルダリスト取得する。
        /// </summary>
        [WebMethod]
        public String[] SFGetDirectoryList(string sServerPath)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            String[] dirList = null;
            using (NetCredentialProc(sServerPath))
            {
                //存在チェック
                if (!Directory.Exists(sServerPath)) throw new FileNotFoundException("フォルダが存在しません。", sServerPath);

                //ディレクトリリスト
                dirList = Directory.GetDirectories(sServerPath);
            }
            return dirList;
        }
        #endregion

        #region ファイル削除
        /// <summary>
        /// ファイル削除
        /// </summary>
        [WebMethod]
        public bool SFDeleteFile(string sServerPath, string sFileName, bool bRestrictFlag)
        {
            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            string filePath = sServerPath + sFileName;

            bool result = false;
            using (NetCredentialProc(sServerPath))
            {
                //存在チェック（厳密チェックフラグTrueの場合）
                if (bRestrictFlag && !File.Exists(filePath)) throw new FileNotFoundException("ファイルが存在しません。", filePath);

                result = false;
                //存在チェック
                try
                {
                    File.Delete(filePath);
                    result = true;
                }
                catch (Exception e)
                {
                    Logger.GetInstance().logWrite("ERROR SFDeleteFile() Param[" + sServerPath + "][" + sFileName + "][" + bRestrictFlag.ToString() + "]");
                    Logger.GetInstance().logWrite("ERROR SFDeleteFile():" + e.Message + " " + e.StackTrace);
                }
            }
            return result;
        }
        #endregion


        #region 条件一致ファイル取得
        /// <summary>
        /// 条件一致ファイル取得
        /// </summary>
        [WebMethod]
        public string[] SFGetConditionCsvFile(string sServerPath, string sPatternName, int colIndex, string conditionStr)
        {
            Logger.GetInstance().logWrite("SFGetConditionCsvFile() start");
            string[] resultArray = null;
            byte[] csvContentBytes = null;

            if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

            Logger.GetInstance().logWrite("NetCredentialProc()");
            using (NetCredentialProc(sServerPath))
            {
                //フォルダチェック
                Logger.GetInstance().logWrite("SFGetConditionCsvFile() フォルダチェック:");
                if (!Directory.Exists(sServerPath))
                {
                    throw new Exception("指定フォルダ無し：[" + sServerPath + "]");
                }
                Logger.GetInstance().logWrite("SFGetConditionCsvFile() フォルダチェック OK");

                lock (this)
                {
                    string[] files = Directory.GetFiles(sServerPath, sPatternName);

                    foreach (string filename in files)
                    {
                        Logger.GetInstance().logWrite("SFGetConditionCsvFile() filename:" + filename);
                        //ファイル取得
                        string csvContent = null;
                        try
                        {
                            //ファイルコンテンツ読み込み

                            csvContentBytes = File.ReadAllBytes(filename);

                            //csvContent = File.ReadAllText(filename, Encoding.GetEncoding(932));
                            csvContent = Encoding.GetEncoding(932).GetString(csvContentBytes);
                        }
                        catch (FileNotFoundException fnex)
                        {
                            //リスト取得からファイルが既に無い状況。
                            continue;
                        }
                        catch (IOException ioex)
                        {
                            //別プロセスで使用等
                            continue;
                        }
                        Logger.GetInstance().logWrite("SFGetConditionCsvFile() ファイル取得：[" + csvContent + "]");
                        string[] cols = csvContent.Split(',');

                        if (cols.Length > colIndex)
                        {
                            if (cols[colIndex] == conditionStr)
                            {
                                //ファイル削除（リトライ3回）
                                int retry = 0;

                                while (true)
                                {
                                    try
                                    {
                                        Logger.GetInstance().logWrite("SFGetConditionCsvFile() ファイル削除:[" + filename + "]");
                                        File.Delete(filename);
                                        break;
                                    }
                                    catch (Exception ex0)
                                    {
                                        Logger.GetInstance().logWrite("SFGetConditionCsvFile() ファイル削除エラー:" + ex0.Message + " " + ex0.StackTrace);
                                    }
                                    retry++;

                                    if (retry > 3) break;

                                    Thread.Sleep(80);
                                }
                                if (File.Exists(filename))
                                {
                                    //ファイル存在の為エラー
                                    Logger.GetInstance().logWrite("SFGetConditionCsvFile() ファイル削除エラー (s存在している為。)");
                                    throw new IOException("ファイル削除エラー");
                                }
                                else
                                {
                                    Logger.GetInstance().logWrite("SFGetConditionCsvFile() 正常処理として結果を設定");
                                    //ファイルが無い場合、正常処理として結果を設定
                                    resultArray = new string[2];
                                    //ファイル名設定
                                    resultArray[0] = filename;
                                    //ファイル内容
                                    resultArray[1] = csvContent;
                                    //resultArray[1] = Convert.ToBase64String(csvContentBytes, 0, csvContentBytes.Length, Base64FormattingOptions.InsertLineBreaks);

                                }
                                break;
                            }
                        }
                    }
                }
            }
            Logger.GetInstance().logWrite("SFGetConditionCsvFile() end");
            return resultArray;
        }
        #endregion


        #region フォルダを作成する。
        /// <summary>
        /// 指定のパスにフォルダを作成する。
        /// </summary>
        [WebMethod]
        public void SFCreateServerFolder(string sServerPath)
        {
            try
            {
                using (NetCredentialProc(sServerPath))
                {
                    if (!sServerPath.EndsWith(@"\")) sServerPath += @"\";

                    if (!Directory.Exists(sServerPath))
                    {
                        //フォルダ作成
                        Directory.CreateDirectory(sServerPath);
                    }
                }
            }
            catch (Exception e)
            {
                Logger.GetInstance().logWrite("ERROR SFCreateServerFolder() Param[" + sServerPath + "]");
                Logger.GetInstance().logWrite("ERROR SFDeleteFile([" + sServerPath + "]):" + e.Message + " " + e.StackTrace);
                throw e;
            }
        }
        #endregion


        [DllImport("advapi32.dll", SetLastError = true, CharSet = CharSet.Unicode)]
        public static extern bool LogonUser(String lpszUsername, String lpszDomain, String lpszPassword,
            int dwLogonType, int dwLogonProvider, out SafeTokenHandle phToken);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        public extern static bool CloseHandle(IntPtr handle);


        public static WindowsImpersonationContext GetImpersonationContext(string userName, string domainName, string password)
        {
            const int LOGON32_PROVIDER_DEFAULT = 0;
            const int LOGON32_LOGON_INTERACTIVE = 2;
            const int LOGON32_LOGON_NETWORK = 3;
            const int LOGON32_LOGON_BATCH = 4;
            const int LOGON32_LOGON_SERVICE = 5;
            const int LOGON32_LOGON_UNLOCK = 7;
            const int LOGON32_LOGON_NETWORK_CLEARTEXT = 8;
            const int LOGON32_LOGON_NEW_CREDENTIALS = 9;

            try
            {
                SafeTokenHandle safeTokenHandle;
                bool returnValue = LogonUser(userName, domainName, password, LOGON32_LOGON_NEW_CREDENTIALS, LOGON32_PROVIDER_DEFAULT, out safeTokenHandle);

                if (false == returnValue) throw new ArgumentException(string.Format("ログインユーザの偽装に失敗しました。 ErrorCode:{0}", Marshal.GetLastWin32Error()));

                using (safeTokenHandle)
                {
                    WindowsIdentity newId = new WindowsIdentity(safeTokenHandle.DangerousGetHandle());
                    return newId.Impersonate();
                }
            }
            catch (Exception e)
            {
                Logger.GetInstance().logWrite("ERROR GetImpersonationContext() Param[" + userName + "][" + domainName + "][" + password + "]");
                Logger.GetInstance().logWrite("ERROR GetImpersonationContext():" + e.Message + " " + e.StackTrace);
                throw e;
            }
        }


        private static Hashtable CredentialPathInfo = null;
        public void InitCredentialInfoOnlyOneceLoad()
        {
            //Logger.GetInstance().logWrite("InitCredentialInfoOnlyOneceLoad()");

            if (CredentialPathInfo == null)
            {
                //Logger.GetInstance().logWrite("InitCredentialInfoOnlyOneceLoad()");

                CredentialPathInfo = new Hashtable();

                for (int i = 0; i < 100; i++)
                {
                    string key = "CredentialPathInfo" + (i + 1).ToString("00");
                    //Logger.GetInstance().logWrite("InitCredentialInfoOnlyOneceLoad() load:"+key);
                    if (ConfigurationManager.AppSettings[key] != null)
                    {
                        string tmp = ConfigurationManager.AppSettings[key].ToString();
                        string[] tmpList = tmp.Split(',');
                        string path = tmpList[0].ToLower();
                        if (!path.EndsWith(@"\")) path += @"\";
                        string userId = tmpList[1];
                        string domain = tmpList[2];
                        string password = tmpList[3];
                        if (!CredentialPathInfo.ContainsKey(path))
                        {
                            //Logger.GetInstance().logWrite("InitCredentialInfoOnlyOneceLoad() set:" + path);
                            CredentialPathInfo.Add(path, new string[] { userId, domain, password });
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
        //フォルダパスによって、認証が必要かどうか判定
        public WindowsImpersonationContext NetCredentialProc(string folderPath)
        {
            //初期設定読み込み
            InitCredentialInfoOnlyOneceLoad();

            if (!folderPath.EndsWith(@"\")) folderPath += @"\";

            //Logger.GetInstance().logWrite("NetCredentialProc() :"+folderPath.ToLower());

            if (CredentialPathInfo.ContainsKey(folderPath.ToLower()))
            {
                //Logger.GetInstance().logWrite("NetCredentialProc() hashtable in");
                //認証情報
                string[] idList = (string[])CredentialPathInfo[folderPath.ToLower()];
                return GetImpersonationContext(idList[0], idList[1], idList[2]);
            }
            //Logger.GetInstance().logWrite("NetCredentialProc() hashtable null");
            return null;
        }

        #endregion

        #region Authentication
        /// <summary>
        /// Authenticates a user based on the provided login ID and password.
        /// </summary>
        /// <remarks>This method delegates the authentication process to the underlying <see
        /// cref="UserService"/>. Ensure that the <see cref="UserService"/> is properly configured before calling this
        /// method.</remarks>
        /// <param name="loginId">The unique identifier for the user attempting to log in. Cannot be null or empty.</param>
        /// <param name="password">The password associated with the specified login ID. Cannot be null or empty.</param>
        /// <returns>A <see cref="LoginRecord"/> object containing details of the authenticated user if the login is successful.
        /// Returns <c>null</c> if authentication fails.</returns>
        [WebMethod]
        public LoginRecord StrLogin(string loginId, string password)
        {
            UserService = new UserService();
            return UserService.StrLogin(loginId, password);
        }

        /// <summary>
        /// Registers a new user with the specified details.
        /// </summary>
        /// <remarks>This method is exposed as a web service and delegates the registration process to the
        /// underlying <see cref="UserService" />. Ensure that all input parameters are valid and meet the system's
        /// requirements to avoid errors.</remarks>
        /// <param name="loginId">The unique identifier for the user's login.</param>
        /// <param name="userName">The name of the user to be registered.</param>
        /// <param name="email">The email address of the user. Must be a valid email format.</param>
        /// <param name="roleId">The role identifier for the user. Valid values depend on the system's role configuration.</param>
        /// <param name="password">The password for the user account. Must meet the system's password complexity requirements.</param>
        /// <returns>A byte value indicating the result of the registration operation.  Typically, 0 represents success, while
        /// other values may indicate specific error codes.</returns>
        [WebMethod]
        public byte StrRegister(string loginId, string userName, string email, byte roleId, string password)
        {
            UserService = new UserService();
            return UserService.StrRegister(loginId, userName, email, roleId, password);
        }
        #endregion

        #region Message
        [WebMethod]
        public DataTable StrGetMessage()
        {
            messageService = new MessageService();
            return messageService.GetAllMessage();
        }
        #endregion
    }

    public sealed class SafeTokenHandle : SafeHandleZeroOrMinusOneIsInvalid
    {
        private SafeTokenHandle() : base(true) { }

        [DllImport("kernel32.dll")]
        [ReliabilityContract(Consistency.WillNotCorruptState, Cer.Success)]
        [SuppressUnmanagedCodeSecurity]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CloseHandle(IntPtr handle);

        protected override bool ReleaseHandle()
        {
            return CloseHandle(handle);
        }
    }

}
