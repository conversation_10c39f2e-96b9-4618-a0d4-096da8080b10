namespace ShinKenShinKun.Utils;

public class PropertyHelpers
{
    public static T GetPropertyValue<T>(object? obj, string propertyName)
    {
        var property = obj?.GetType().GetProperty(propertyName);
        return property != null ? (T)property.GetValue(obj) : default;
    }

    public static object? Resolve(IServiceProvider services, string absolutePath)
    {
        if (string.IsNullOrWhiteSpace(absolutePath))
        {
            return default;
        }

        if (!TryParsePath(absolutePath, out var typeName, out var memberName, out var isMethod, out var assemblyName))
        {
            return default;
        }

        var type = ResolveType(typeName, assemblyName);
        if (type == null)
        {
            return default;
        }

        var instance = ResolveInstance(services, type);

        return isMethod
            ? ResolveAsCommand(type, instance, memberName)
            : ResolveAsProperty(type, instance, memberName);
    }

    public static TProperty? Resolve<TProperty>(IServiceProvider services, string absolutePath)
    {
        var value = Resolve(services, absolutePath);
        if (value is TProperty casted)
        {
            return casted;
        }

        try
        {
            if (value != null)
                return (TProperty)Convert.ChangeType(value, typeof(TProperty));
        }
        catch (Exception ex)
        {
            Log.Error(ex?.StackTrace);
        }

        return default;
    }

    public static void SetPropertyValue<T>(object? obj, string propertyName, T value)
    {
        var property = obj?.GetType().GetProperty(propertyName);
        if (property != null && property.CanWrite)
        {
            property.SetValue(obj, value);
        }
    }

    private static object? ResolveAsCommand(Type type, object? instance, string methodName)
    {
        var method = type.GetMethod(methodName);
        if (method == null)
        {
            return default;
        }

        var parameters = method.GetParameters();

        return parameters.Length switch
        {
            0 => new Command(() => method.Invoke(instance, null)),
            1 => new Command<object?>(param => method.Invoke(instance, new[] { param })),
            _ => null // Not supported: more than 1 parameter
        };
    }

    private static object? ResolveAsProperty(Type type, object? instance, string propertyName)
    {
        var prop = type.GetProperty(propertyName);
        return prop?.GetValue(instance);
    }

    private static object? ResolveInstance(IServiceProvider services, Type type)
    {
        // Support for static classes: return null instance
        return type.IsAbstract && type.IsSealed
            ? null
            : services.GetService(type);
    }

    private static Type? ResolveType(string typeName, string assemblyName)
    {
        var fullTypeName = $"{typeName}, {assemblyName}";
        return Type.GetType(fullTypeName);
    }

    private static bool TryParsePath(string absolutePath, out string typeName, out string memberName, out bool isMethod, out string assemblyName)
    {
        typeName = memberName = assemblyName = string.Empty;
        isMethod = false;

        var parts = absolutePath.Split(';');
        if (parts.Length != 2)
        {
            return false;
        }

        var fullPath = parts[0];
        assemblyName = parts[1];

        isMethod = fullPath.EndsWith("()");
        var cleanPath = isMethod ? fullPath[..^2] : fullPath;

        var lastDot = cleanPath.LastIndexOf('.');
        if (lastDot == -1)
        {
            return false;
        }

        typeName = cleanPath[..lastDot];
        memberName = cleanPath[(lastDot + 1)..];

        return true;
    }
}