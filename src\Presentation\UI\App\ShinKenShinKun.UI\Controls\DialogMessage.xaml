<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePopup
    x:Class="ShinKenShinKun.UI.DialogMessage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:Name="this"
    x:DataType="ui:DialogMessageViewModel"
    CanBeDismissedByTappingOutsideOfPopup="True">
    <telerik:RadBorder
        x:Name="parent"
        Padding="{Static ui:Dimens.Width20}"
        BackgroundColor="{Static ui:AppColors.White}"
        CornerRadius="16"
        MaximumHeightRequest="200"
        MaximumWidthRequest="500">
        <Grid RowDefinitions="*,auto" RowSpacing="{Static ui:Dimens.Width20}">
            <Grid ColumnDefinitions="auto,*" ColumnSpacing="{Static ui:Dimens.Width20}">
                <Image
                    Aspect="Fill"
                    HeightRequest="{Static ui:Dimens.Height80}"
                    Source="{Binding Icon}"
                    VerticalOptions="Start"
                    WidthRequest="{Static ui:Dimens.Width80}" />
                <Grid Grid.Column="1" RowDefinitions="auto,*">
                    <Label Style="{Static ui:Styles.MessageTitleStyle}" Text="{Binding DialogMessage.MessageTitle}" />
                    <ScrollView
                        Grid.Row="1"
                        MaximumWidthRequest="{Binding MaximumWidthRequest, Source={Reference parent}}"
                        Orientation="Vertical">
                        <Label Style="{Static ui:Styles.MessageBodyStyle}" Text="{Binding DialogMessage.MessageBody}" />
                    </ScrollView>
                </Grid>
            </Grid>
            <Grid
                x:Name="Action"
                Grid.Row="1"
                ColumnSpacing="{Static ui:Dimens.Width20}"
                MaximumWidthRequest="{Binding MaximumWidthRequest, Source={Reference parent}}" />
        </Grid>
    </telerik:RadBorder>
</mvvm:BasePopup>