namespace ShinKenShinKun;

public partial class ChangeGuidanceButtonView : RadBorder
{
    public ChangeGuidanceButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty ChangeGuidanceCommandProperty = BindableProperty.Create(
        nameof(ChangeGuidanceCommand),
        typeof(IRelayCommand),
        typeof(ChangeGuidanceButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand ChangeGuidanceCommand
    {
        get => (IRelayCommand)GetValue(ChangeGuidanceCommandProperty);
        set => SetValue(ChangeGuidanceCommandProperty, value);
    }

    public static readonly BindableProperty ClientSelectedProperty = BindableProperty.Create(
        nameof(ClientSelected),
        typeof(ClientInformationModel),
        typeof(ChangeGuidanceButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public ClientInformationModel ClientSelected
    {
        get => (ClientInformationModel)GetValue(ClientSelectedProperty);
        set => SetValue(ClientSelectedProperty, value);
    }
}