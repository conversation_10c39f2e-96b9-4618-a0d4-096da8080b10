﻿namespace ShinKenShinKun.Utils;

public class CompomentStatusModel
{
    public bool IsTitleDisplay { get; set; }
    public bool IsHomeDisplay { get; set; }
    public bool IsSwitchMaskDisplay { get; set; }
    public bool IsUserLoginDisplay { get; set; }
    public bool IsSwitchModeDisplay { get; set; }
    public bool IsFinishDisplay { get; set; }
    public bool IsLogoutDisplay { get; set; }
    public bool IsBackDisplay { get; set; }
    public bool IsEnterIdDisplay { get; set; }
    public bool IsHoldDisplay { get; set; }
    public bool IsPauseDisplay { get; set; }
    public bool IsRegisterDisplay { get; set; }
    public bool IsBulkHoldPatientsDisplay { get; set; }
    public bool IsChangeGuidanceDisplay { get; set; }
    public bool IsBulkRegisterHoldPatientsDisplay { get; set; }
    public bool IsBulkReleaseHoldPatientsDisplay { get; set; }
    public bool IsBulkHoldTestsDisplay { get; set; }
    public bool IsBulkReleaseHoldTestsDisplay { get; set; }
    public bool IsBulkPauseTestsDisplay { get; set; }
    public bool IsBulkCancelPauseTestsDisplay { get; set; }
}
