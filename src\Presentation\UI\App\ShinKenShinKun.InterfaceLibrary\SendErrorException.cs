﻿namespace ShinKenShinKun.InterfaceLibrary;
public class SendErrorException : Exception
{
    #region 変数
    private int FldErrorCode;
    private string FldMsg;
    #endregion

    #region  SendErrorException
    public SendErrorException()
    {
    }
    #endregion

    #region  SendErrorException
    public SendErrorException(string msg)
    {
        FldMsg = msg;
    }
    #endregion

    #region Msg
    public string Msg
    {
        get
        {
            return FldMsg;
        }
    }
    #endregion

    #region SendErrorException
    public SendErrorException(int errorCode)
    {
        ErrorCode = errorCode;
    }
    #endregion

    #region ErrorCode
    public int ErrorCode
    {
        set
        {
            FldErrorCode = value;
        }
        get
        {
            return FldErrorCode;
        }
    }
    #endregion

}
