﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Globalization;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MedicalCheckReasonDataService の概要の説明です
    /// </summary>
    public class MedicalCheckReasonDataService
    {
        private MedicalDataSet MedicalDataSet = new MedicalDataSet();
        private Service srv;
        private Logger logger = null;

        private readonly string MEDICAL_CHECK_DATE_COLUMN = "MedicalCheckDate";
        private readonly string CLIENT_ID_COLUMN = "ClientID";
        private readonly string DIVISION_COLUMN = "Division";
        private readonly string MEDICAL_CHECK_NO_COLUMN = "MedicalCheckNo";
        private readonly string START_TIME_COLUMN = "StartTime";
        private readonly string END_TIME_COLUMN = "EndTime";
        private readonly string DATA_COLUMN = "Data";
        private readonly int DATA_COUNT = 50;

        public MedicalCheckReasonDataService()
        {
            srv = new Service();
        }

        public PluralRecord[] GetMedicalCheckReasonDataClientId(string sMedicalCheckDate, string clientId, string division)
        {
            try
            {
                // 動的配列を用意する。
                ArrayList reasonArray = new ArrayList();

                DateTime date = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                // データセットの関数をコールする
                MedicalDataSet = srv.GetMedicalCheckReasonData(date, clientId, division);
                // 取得したレコード数のチェック
                int recCount = MedicalDataSet.MedicalCheckReasonData.Rows.Count;
                // 複数レコード返却クラスの配列を作成
                PluralRecord[] pRecord = new PluralRecord[recCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recCount; j++)
                {
                    DataRow row = MedicalDataSet.MedicalCheckReasonData.Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        reasonArray.Add(row[MEDICAL_CHECK_DATE_COLUMN].ToString().PadLeft(1, ' '));
                        reasonArray.Add(row[CLIENT_ID_COLUMN].ToString().PadLeft(1, ' '));
                        reasonArray.Add(row[DIVISION_COLUMN].ToString().PadLeft(1, ' '));
                        reasonArray.Add(row[MEDICAL_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        reasonArray.Add(row[START_TIME_COLUMN].ToString().PadLeft(1, ' '));
                        reasonArray.Add(row[END_TIME_COLUMN].ToString().PadLeft(1, ' '));
                        for (int i = 1; i <= DATA_COUNT; i++)
                        {
                            reasonArray.Add(row[DATA_COLUMN + i.ToString()].ToString().PadLeft(1, ' '));
                        }
                    }
                    else
                    {
                    }
                    //ArrayListからstring配列に変換
                    string[] reasonList = new string[reasonArray.Count];
                    Array.Copy(reasonArray.ToArray(), reasonList, reasonArray.Count);
                    pRecord[j] = new PluralRecord(reasonList);
                    reasonArray.Clear();
                }
                return pRecord;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
    }
}