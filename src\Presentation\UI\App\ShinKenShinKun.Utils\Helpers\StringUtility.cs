﻿namespace ShinKenShinKun.Utils;

/// <summary>
/// 文字列操作ユーティリティクラス
/// </summary>
public class StringUtility
{
    #region データフォーマット変換

    /// <summary>
    /// 指定のデータフォーマットに変換する
    /// </summary>
    /// <param name="data">元データ</param>
    /// <param name="formats">データフォーマット</param>
    /// <returns>変換後のデータ</returns>
    public static string ConvertToFormat(string beforeData, string format)
    {
        string retData = beforeData;

        if (!string.IsNullOrEmpty(beforeData)
            && !string.IsNullOrEmpty(format))
        {
            try
            {
                double convData = 0;
                convData = double.Parse(beforeData);
                retData = convData.ToString(format);
            }
            catch (Exception) { }
        }
        return retData;
    }

    #endregion データフォーマット変換

    #region データフォーマット変換

    /// <summary>
    /// 指定のデータフォーマットに変換する
    /// </summary>
    /// <param name="data">元データ</param>
    /// <param name="formats">データフォーマット</param>
    /// <returns>変換後のデータ</returns>
    public static string ConvertToFormat(string beforeData, string format, int dispFormat)
    {
        string retData = beforeData;

        if (!string.IsNullOrEmpty(beforeData)
            && !string.IsNullOrEmpty(format))
        {
            try
            {
                double convData = 0;
                convData = double.Parse(beforeData);
                decimal dConvData = ParseUtility.TryParseDecimal(convData).Value;

                int digits = 0;

                if (format.IndexOf(".") != -1)
                {
                    string subStr = format.Substring(format.IndexOf(".") + 1, format.Length - (format.IndexOf(".") + 1));
                    digits = subStr.Length;
                }
                // 切捨て
                if (dispFormat == DispDataFormatConst.ROUND_DOWN)
                {
                    decimal tmpData = CalculateUtility.ToRoundDown(dConvData, digits);
                    convData = ParseUtility.TryParseDouble(tmpData).Value;
                }
                // 切り上げ
                else if (dispFormat == DispDataFormatConst.ROUND_UP)
                {
                    decimal tmpData = CalculateUtility.ToRoundUp(dConvData, digits);
                    convData = ParseUtility.TryParseDouble(tmpData).Value;
                }
                // 四捨五入
                else
                {
                    decimal tmpData = CalculateUtility.ToHalfAdjust(dConvData, digits);
                    convData = ParseUtility.TryParseDouble(tmpData).Value;
                }
                retData = convData.ToString(format);
            }
            catch (Exception) { }
        }
        return retData;
    }

    #endregion データフォーマット変換
}