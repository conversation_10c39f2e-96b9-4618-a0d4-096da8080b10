﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.GuideSupportDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Transactions;

namespace ShinKenShinKunServer.Services
{
    public class CallMonitorSettingService
    {
        GuideSupportDataSet GuideDataSet = new GuideSupportDataSet();

        private CallMonitorService srv;
        private Logger logger = null;

        public CallMonitorSettingService()
        {
            srv = new CallMonitorService();
        }

        #region CallClientData　全レコード取得
        /// <summary>
        /// CallClientData　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetCallClientDataAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallClientDataAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["CallClientData"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["CallClientData"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        array.Add(row["MedicalCheckDate"].ToString());
                        array.Add(row["ClientID"].ToString());
                        array.Add(row["Division"].ToString());
                        array.Add(row["ExamID"].ToString());
                        array.Add(row["CallOrder"].ToString());
                        array.Add(row["Status"].ToString());
                        array.Add(row["LastUpdate"].ToString());
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[array.Count];
                    Array.Copy(array.ToArray(), ipList, array.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    array.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallClientData　１レコード取得
        /// <summary>
        /// CallClientData　１レコード取得
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="iExamID"></param>
        /// <returns></returns>
        public string[] StrGetCallClientData(string sMedicalCheckDate, string sClientID, string sDivision, int iExamID)
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                DateTime date = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                GuideDataSet = srv.GetCallClientData(date, sClientID, sDivision, iExamID);

                if (GuideDataSet.Tables["CallClientData"].Rows.Count != 0)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。
                    DataRow row_cdata = GuideDataSet.Tables["CallClientData"].Rows[0];

                    if (row_cdata != null)
                    {
                        array.Add(row_cdata["MedicalCheckDate"].ToString());
                        array.Add(row_cdata["ClientID"].ToString());
                        array.Add(row_cdata["Division"].ToString());
                        array.Add(row_cdata["ExamID"].ToString());
                        array.Add(row_cdata["CallOrder"].ToString());
                        array.Add(row_cdata["Status"].ToString());
                        array.Add(row_cdata["LastUpdate"].ToString());
                    }
                    else
                    {
                        return null;
                    }

                    // ArrayListからString配列に変換
                    string[] datalist = new string[array.Count];
                    Array.Copy(array.ToArray(), datalist, array.Count);
                    return datalist;
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallClientData　更新
        /// <summary>
        /// CallClientData　更新
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="sClientID"></param>
        /// <param name="sDivision"></param>
        /// <param name="addInfo"></param>
        /// <returns></returns>
        public bool StrSetCallClientData(string sMedicalCheckDate, string sClientID, string sDivision, int iExamID, int iCallOrder, int iStatus)
        {
            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    // 日付(yyyyMMdd形式）をDateTime型に変換する
                    DateTime conMedicalCheckDate = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
                    // 現在日付(yyyymmddHHmmss形式)をDateTime型に変換する
                    string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                    DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                    // データセットの関数をコール
                    GuideDataSet = srv.GetCallClientData(conMedicalCheckDate, sClientID, sDivision, iExamID);
                    DataRow row = GuideDataSet.CallClientData.Rows
                        .Find(new object[] { conMedicalCheckDate, sClientID, sDivision, iStatus });
                    if (row == null)
                    {
                        row = GuideDataSet.CallClientData.NewCallClientDataRow();
                    }

                    row.BeginEdit();
                    row["MedicalCheckDate"] = conMedicalCheckDate;
                    row["ClientID"] = sClientID;
                    row["Division"] = sDivision;
                    row["ExamID"] = iExamID;
                    row["CallOrder"] = iCallOrder;
                    row["Status"] = iStatus;
                    row["LastUpdate"] = nowTime;
                    row.EndEdit();

                    if (GuideDataSet.CallClientData.Rows.Count == 0)
                    {
                        GuideDataSet.CallClientData.AddCallClientDataRow(row as GuideSupportDataSet.CallClientDataRow);
                    }

                    CallClientDataTableAdapter adapter
                        = new CallClientDataTableAdapter();
                    adapter.Update(GuideDataSet.CallClientData);
                    tx.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region CallClientData削除
        /// <summary>
        /// CallClientData削除を削除する
        /// </summary>
        /// <returns></returns>
        public bool DeleteCallClientData(string sMedicalCheckDate, string sClientID, string sDivision, int iExamID)
        {
            try
            {
                // 日付(yyyyMMdd形式）をDateTime型に変換する
                DateTime conMedicalCheckDate = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                CallClientDataTableAdapter adapter
                    = new CallClientDataTableAdapter();
                adapter.Delete(conMedicalCheckDate, sClientID, sDivision, Convert.ToInt16(iExamID));

                return true;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
        #endregion

        #region CallExamMaster　全レコード取得
        /// <summary>
        /// CallExamMaster　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetCallExamMasterAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallExamMasterAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["CallExamMaster"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["CallExamMaster"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        array.Add(row["ExamID"].ToString());
                        array.Add(row["Title"].ToString());
                        array.Add(row["Status"].ToString());
                        array.Add(row["DispStartTime"].ToString());
                        array.Add(row["DispEndTime"].ToString());
                        array.Add(row["Capacity"].ToString());
                        array.Add(row["BackgroundColor"].ToString());
                        array.Add(row["Message"].ToString());
                        array.Add(row["Header1"].ToString());
                        array.Add(row["Header2"].ToString());
                        for (int i = 1; i <= 10; i++)
                        {
                            array.Add(row["MedicalCheckNo" + i.ToString()].ToString());
                        }
                        array.Add(row["PanelType"].ToString());
                        array.Add(row["Param1"].ToString());
                        array.Add(row["Param2"].ToString());
                        array.Add(row["Param3"].ToString());
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[array.Count];
                    Array.Copy(array.ToArray(), ipList, array.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    array.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallExamMaster　１レコード取得
        /// <summary>
        /// CallExamMaster　１レコード取得
        /// </summary>
        /// <returns></returns>
        public string[] StrGetCallExamMaster(short iExamID)
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallExamMaster(iExamID);

                if (GuideDataSet.Tables["CallExamMaster"].Rows.Count != 0)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。
                    DataRow row = GuideDataSet.Tables["CallExamMaster"].Rows[0];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        array.Add(row["ExamID"].ToString());
                        array.Add(row["Title"].ToString());
                        array.Add(row["Status"].ToString());
                        array.Add(row["DispStartTime"].ToString());
                        array.Add(row["DispEndTime"].ToString());
                        array.Add(row["Capacity"].ToString());
                        array.Add(row["BackgroundColor"].ToString());
                        array.Add(row["Message"].ToString());
                        array.Add(row["Header1"].ToString());
                        array.Add(row["Header2"].ToString());
                        for (int i = 1; i <= 10; i++)
                        {
                            array.Add(row["MedicalCheckNo" + i.ToString()].ToString());
                        }
                        array.Add(row["PanelType"].ToString());
                        array.Add(row["Param1"].ToString());
                        array.Add(row["Param2"].ToString());
                        array.Add(row["Param3"].ToString());
                    }
                    else
                    {
                        return null;
                    }

                    // ArrayListからString配列に変換
                    string[] datalist = new string[array.Count];
                    Array.Copy(array.ToArray(), datalist, array.Count);
                    return datalist;
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallExamMaster　更新
        /// <summary>
        /// CallExamMaster　更新
        /// </summary>
        /// <param name="iExamID"></param>
        /// <param name="sTitle"></param>
        /// <param name="bStatus"></param>
        /// <param name="sStartTime"></param>
        /// <param name="sEndTime"></param>
        /// <param name="iCapacity"></param>
        /// <param name="iBackColor"></param>
        /// <param name="sMessage"></param>
        /// <param name="iMedicalCheckNos"></param>
        /// <returns></returns>
        public bool StrSetCallExamMaster(short iExamID, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string sParam1, string sParam2, string sParam3)
        {
            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    // データセットの関数をコール
                    GuideDataSet = srv.GetCallExamMaster(iExamID);
                    DataRow row = GuideDataSet.CallExamMaster.Rows.Find(new object[] { iExamID });
                    if (row == null)
                    {
                        row = GuideDataSet.CallExamMaster.NewCallExamMasterRow();
                    }

                    row.BeginEdit();
                    row["ExamID"] = iExamID;
                    row["Title"] = sTitle;
                    row["Status"] = bStatus;
                    row["DispStartTime"] = sStartTime;
                    row["DispEndTime"] = sEndTime;
                    row["Capacity"] = iCapacity;
                    row["BackgroundColor"] = iBackColor;
                    row["Message"] = sMessage;
                    row["Header1"] = sHeader1;
                    row["Header2"] = sHeader2;
                    if (iMedicalCheckNos != null)
                    {
                        for (int i = 0; i < 10; i++)
                        {
                            if (i < iMedicalCheckNos.Length)
                            {
                                row["MedicalCheckNo" + Convert.ToString(i + 1)] = iMedicalCheckNos[i];
                            }
                            else
                            {
                                row["MedicalCheckNo" + Convert.ToString(i + 1)] = DBNull.Value;
                            }
                        }
                    }
                    row["PanelType"] = iPanelType;
                    row["Param1"] = sParam1;
                    row["Param2"] = sParam2;
                    row["Param3"] = sParam3;
                    row.EndEdit();

                    if (GuideDataSet.CallExamMaster.Rows.Count == 0)
                    {
                        GuideDataSet.CallExamMaster.AddCallExamMasterRow(row as GuideSupportDataSet.CallExamMasterRow);
                    }

                    CallExamMasterTableAdapter adapter
                        = new CallExamMasterTableAdapter();
                    adapter.Update(GuideDataSet.CallExamMaster);
                    tx.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region CallExamMaster削除
        /// <summary>
        /// CallExamMaterを削除する
        /// </summary>
        /// <returns></returns>
        public bool DeleteCallExamMaster(short iExamID)
        {
            try
            {
                CallExamMasterTableAdapter adapter
                    = new CallExamMasterTableAdapter();
                adapter.Delete(iExamID);

                return true;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
        #endregion

        #region CallStatus　全レコード取得
        /// <summary>
        /// CallStatus　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetCallStatusAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallStatusAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["CallStatus"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["CallStatus"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        array.Add(row["ExamID"].ToString());
                        array.Add(row["MaxWaitTime"].ToString());
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[array.Count];
                    Array.Copy(array.ToArray(), ipList, array.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    array.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallStatus　更新
        /// <summary>
        /// CallStatus　更新
        /// </summary>
        /// <param name="iExamID"></param>
        /// <param name="iMaxWaitTime"></param>
        /// <returns></returns>
        public bool StrSetCallStatus(int iExamID, int iMaxWaitTime)
        {
            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    // データセットの関数をコール
                    GuideDataSet = srv.GetCallStatus(iExamID);
                    DataRow row = GuideDataSet.CallStatus.Rows.Find(new object[] { iExamID });
                    if (row == null)
                    {
                        row = GuideDataSet.CallStatus.NewCallStatusRow();
                    }

                    row.BeginEdit();
                    row["ExamID"] = iExamID;
                    row["MaxWaitTime"] = iMaxWaitTime;
                    row.EndEdit();

                    if (GuideDataSet.CallStatus.Rows.Count == 0)
                    {
                        GuideDataSet.CallStatus.AddCallStatusRow(row as GuideSupportDataSet.CallStatusRow);
                    }

                    CallStatusTableAdapter adapter
                        = new CallStatusTableAdapter();
                    adapter.Update(GuideDataSet.CallStatus);
                    tx.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region CallStatus削除
        /// <summary>
        /// CallStatusを削除する
        /// </summary>
        /// <returns></returns>
        public bool DeleteCallStatus(int iExamID)
        {
            try
            {
                CallStatusTableAdapter adapter
                    = new CallStatusTableAdapter();
                adapter.Delete(Convert.ToInt16(iExamID));

                return true;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
        #endregion

        #region CallTerminalMaster　全レコード取得
        /// <summary>
        /// CallTerminalMaster　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetCallTerminalMasterAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallTerminalMasterAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["CallTerminalMaster"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["CallTerminalMaster"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        array.Add(row["TermID"].ToString());
                        array.Add(row["IPAddress"].ToString());
                        array.Add(row["ChangeSecond"].ToString());
                        for (int i = 1; i <= 10; i++)
                        {
                            array.Add(row["ExamID" + i.ToString()].ToString());
                        }
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[array.Count];
                    Array.Copy(array.ToArray(), ipList, array.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    array.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallTerminalMaster　１レコード取得
        /// <summary>
        /// CallTerminalMaster　１レコード取得
        /// </summary>
        /// <returns></returns>
        public string[] StrGetCallTerminalMaster(string sTermID)
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetCallTerminalMaster(sTermID);

                if (GuideDataSet.Tables["CallTerminalMaster"].Rows.Count != 0)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。
                    DataRow row = GuideDataSet.Tables["CallTerminalMaster"].Rows[0];
                    if (row != null)
                    {                    // 検索した情報を配列に追加していく
                        array.Add(row["TermID"].ToString());
                        array.Add(row["IPAddress"].ToString());
                        array.Add(row["ChangeSecond"].ToString());
                        for (int i = 1; i <= 10; i++)
                        {
                            array.Add(row["ExamID" + i.ToString()].ToString());
                        }
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] datalist = new string[array.Count];
                    Array.Copy(array.ToArray(), datalist, array.Count);
                    return datalist;
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region CallTerminalMaster　更新
        /// <summary>
        /// CallTerminalMaster　更新
        /// </summary>
        /// <param name="sIPAddress"></param>
        /// <param name="iChangeSecond"></param>
        /// <param name="iExamID"></param>
        /// <returns></returns>
        public bool StrSetCallTerminalMaster(string sTermID, string sIPAddress, int iChangeSecond, int[] iExamID)
        {
            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    //IPを補正
                    string[] s = sIPAddress.Split('.');
                    sIPAddress = s[0].PadLeft(3, '0').ToString() + "." +
                                 s[1].PadLeft(3, '0').ToString() + "." +
                                 s[2].PadLeft(3, '0').ToString() + "." +
                                 s[3].PadLeft(3, '0').ToString();

                    // データセットの関数をコール
                    GuideDataSet = srv.GetCallTerminalMaster(sTermID);
                    DataRow row = GuideDataSet.CallTerminalMaster.Rows.Find(new object[] { sTermID });
                    if (row == null)
                    {
                        row = GuideDataSet.CallTerminalMaster.NewCallTerminalMasterRow();
                    }

                    row.BeginEdit();
                    row["TermID"] = sTermID;
                    row["IPAddress"] = sIPAddress;
                    row["ChangeSecond"] = iChangeSecond;
                    if (iExamID != null)
                    {
                        for (int i = 0; i < 10; i++)
                        {
                            if (i < iExamID.Length)
                            {
                                row["ExamID" + Convert.ToString(i + 1)] = iExamID[i];
                            }
                            else
                            {
                                row["ExamID" + Convert.ToString(i + 1)] = DBNull.Value;
                            }
                        }
                    }
                    else
                    {
                        for (int i = 0; i < 10; i++)
                        {
                            row["ExamID" + Convert.ToString(i + 1)] = DBNull.Value;
                        }
                    }
                    row.EndEdit();

                    if (GuideDataSet.CallTerminalMaster.Rows.Count == 0)
                    {
                        GuideDataSet.CallTerminalMaster.AddCallTerminalMasterRow(row as GuideSupportDataSet.CallTerminalMasterRow);
                    }

                    CallTerminalMasterTableAdapter adapter
                        = new CallTerminalMasterTableAdapter();
                    adapter.Update(GuideDataSet.CallTerminalMaster);
                    tx.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        #endregion

        #region CallTerminalMaster削除
        /// <summary>
        /// CallStatusを削除する
        /// </summary>
        /// <returns></returns>
        public bool DeleteTerminalMaster(string sTermID)
        {
            try
            {
                CallTerminalMasterTableAdapter adapter
                    = new CallTerminalMasterTableAdapter();
                adapter.Delete(sTermID);

                return true;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
        #endregion
    }
}