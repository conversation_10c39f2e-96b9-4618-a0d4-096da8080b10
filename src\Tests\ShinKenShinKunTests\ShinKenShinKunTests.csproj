﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
	  <TargetFrameworks>net9.0</TargetFrameworks>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <Nullable>enable</Nullable>
	  <IsTestProject>true</IsTestProject>
	  <!-- xUnit: Add this for .NET MAUI API access -->
	  <!-- <UseMaui>true</UseMaui> -->
	  <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" />
    <PackageReference Include="coverlet.collector">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Moq.AutoMock" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Presentation\UI\App\ShinKenShinKun\ShinKenShinKun.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Factories\" />
  </ItemGroup>

</Project>
