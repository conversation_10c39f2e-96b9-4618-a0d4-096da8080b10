<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="ShinKenShinKun.PatientPendingTableView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:utils="clr-namespace:ShinKenShinKun.Utils;assembly=ShinKenShinKun.Utils"
    x:Name="this">
    <Grid>
        <telerik:RadDataGrid
            IsVisible="{Binding ClientInformations, Source={Reference this}, Converter={x:Static ui:AppConverters.IsListNotNullOrEmpty}}"
            ItemsSource="{Binding ClientInformations, Source={Reference this}}"
            Style="{x:Static ui:Styles.PatientPendingListDataGridStyle}">
            <telerik:RadDataGrid.Columns>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width40}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <telerik:RadCheckBox
                                BackgroundColor="{Static ui:AppColors.White}"
                                Command="{Binding CheckAllClientCommand, Source={Reference this}}"
                                HorizontalOptions="Center"
                                IsChecked="{Binding IsSelectAllClient, Source={Reference this}}"
                                VerticalOptions="Center" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <telerik:RadCheckBox
                                x:DataType="app:ClientInformationCheckedModel"
                                BackgroundColor="{Static ui:AppColors.Transparent}"
                                Command="{Binding CheckClientInformationCommand, Source={Reference this}}"
                                HorizontalOptions="Center"
                                IsChecked="{Binding IsChecked}"
                                VerticalOptions="Center" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Id}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                Text="{Binding ClientInformation.ClientId}"
                                TextColor="{Binding ClientInformation.ContentCellColor}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width320}"
                    SizeMode="Stretch">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                MinimumWidthRequest="{x:Static ui:Dimens.Width320}"
                                Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}"
                                Text="{x:Static resources:AppResources.KanaName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                Text="{Binding ClientInformation.Name}"
                                TextColor="{Binding ClientInformation.ContentCellColor}">
                                <Label.Triggers>
                                    <DataTrigger
                                        Binding="{Binding IsMaskMode, Source={Reference this}}"
                                        TargetType="Label"
                                        Value="True">
                                        <Setter Property="Text" Value="{x:Static resources:AppResources.MaskText}" />
                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Age}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                Text="{Binding ClientInformation.Age}"
                                TextColor="{Binding ClientInformation.ContentCellColor}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Gender}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                Text="{Binding ClientInformation.SexDisplay.DisplayName}"
                                TextColor="{Binding ClientInformation.ContentCellColor}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[0]}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                BackgroundColor="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[0].TestStatus.ProgressColor}"
                                Style="{x:Static ui:Styles.ContentCellLabelFillStyle}"
                                Text="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[0].TestStatus.DisplayName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[1]}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                BackgroundColor="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[1].TestStatus.ProgressColor}"
                                Style="{x:Static ui:Styles.ContentCellLabelFillStyle}"
                                Text="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[1].TestStatus.DisplayName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.PatientPendingListDataGridHeaderStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[2]}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationCheckedModel"
                                BackgroundColor="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[2].TestStatus.ProgressColor}"
                                Style="{x:Static ui:Styles.ContentCellLabelFillStyle}"
                                Text="{Binding ClientInformation.MedicalCheckProgressesSubDisplays[2].TestStatus.DisplayName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
            </telerik:RadDataGrid.Columns>
        </telerik:RadDataGrid>
        <ScrollView IsVisible="{Binding ClientInformations, Source={Reference this}, Converter={x:Static ui:AppConverters.IsListNullOrEmpty}}" Orientation="Horizontal">
            <HorizontalStackLayout VerticalOptions="Start">
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Id}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width320}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.KanaName}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Age}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{x:Static resources:AppResources.Gender}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[0]}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[1]}" />
                </telerik:RadBorder>
                <telerik:RadBorder MinimumWidthRequest="{x:Static ui:Dimens.Width160}" Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label Style="{x:Static ui:Styles.HeaderPatientPendingLabelStyle}" Text="{Binding Source={Static utils:AppConstants.MedicalCheckNames}, Path=[2]}" />
                </telerik:RadBorder>
            </HorizontalStackLayout>
        </ScrollView>
    </Grid>
</ContentView>