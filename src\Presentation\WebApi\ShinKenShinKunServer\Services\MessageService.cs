﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.MessageDataSetTableAdapters;
using ShinKenShinKunServer.Utils;
using System.Data;

namespace ShinKenShinKunServer.Services
{
    public class MessageService
    {
        public MessageDataSet MessageDataSet = new MessageDataSet();
        private Logger logger = null;
        private Service srv;

        public MessageService()
        {
            srv = new Service();
        }

        public DataTable GetAllMessage()
        {
            logger = Logger.GetInstance();
            try
            {
                var messagesTableAdapter = new MessagesTableAdapter();
                var messagesDataTable = messagesTableAdapter.GetData();
                return messagesDataTable;

            }
            catch (System.Exception exception)
            {
                logger.logWrite($"ERROR GetAllMessage(): {exception.Message} {exception.StackTrace}");
                throw exception;
            }
        }
    }
}