﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// CustomizeButtonService の概要の説明です
    /// </summary>
    public class CustomizeButtonService
    {
        private DispSettingService service;
        private Logger logger = null;

        public CustomizeButtonService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetCustomizeButtonSettingAll()
        {
            try
            {
                DispSettingDataSet.CustomizeButtonSettingDataTable tmpTable
                = service.GetCustomizeButtonSettingAll().CustomizeButtonSetting;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();

                    foreach (DispSettingDataSet.CustomizeButtonSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row["FormId"].ToString());
                        rList.Add(row["ButtonID"].ToString());
                        rList.Add(row["PageNo"].ToString());
                        rList.Add(row["DispName"].ToString());
                        rList.Add(row["DispShape"].ToString());
                        rList.Add(row["DispPosition"].ToString());
                        rList.Add(row["DispSize"].ToString());
                        rList.Add(row["DispVisible"].ToString());
                        rList.Add(row["DispColor"].ToString());
                        rList.Add(row["AutoAction"].ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetCustomizeButtonDetailAll()
        {
            try
            {
                DispSettingDataSet.CustomizeButtonDetailDataTable tmpTable
                = service.GetCustomizeButtonDetailAll().CustomizeButtonDetail;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();

                    foreach (DispSettingDataSet.CustomizeButtonDetailRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row["FunctionID"].ToString());
                        rList.Add(row["FormID"].ToString());
                        rList.Add(row["ButtonID"].ToString());
                        rList.Add(row["Priority"].ToString());
                        rList.Add(row["FunctionType"].ToString());
                        rList.Add(row["DispData"].ToString());
                        rList.Add(row["DispItemIndex"].ToString());
                        rList.Add(row["CalcItemFlg1"].ToString());
                        rList.Add(row["CalcItemIndex1"].ToString());
                        rList.Add(row["CalcItemFlg2"].ToString());
                        rList.Add(row["CalcItemIndex2"].ToString());
                        rList.Add(row["CalcItemFlg3"].ToString());
                        rList.Add(row["CalcItemIndex3"].ToString());
                        rList.Add(row["CalcItemFlg4"].ToString());
                        rList.Add(row["CalcItemIndex4"].ToString());
                        rList.Add(row["CalcItemFlg5"].ToString());
                        rList.Add(row["CalcItemIndex5"].ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetCustomizeButtonDetailFormId(string FormId)
        {
            try
            {
                short formIdShr = Convert.ToInt16(FormId);

                DispSettingDataSet.CustomizeButtonDetailDataTable tmpTable
                    = service.GetCustomizeButtonDetailbyFormId(formIdShr).CustomizeButtonDetail;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();

                    foreach (DispSettingDataSet.CustomizeButtonDetailRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row["FunctionID"].ToString());
                        rList.Add(row["FormID"].ToString());
                        rList.Add(row["ButtonID"].ToString());
                        rList.Add(row["Priority"].ToString());
                        rList.Add(row["FunctionType"].ToString());
                        rList.Add(row["DispData"].ToString());
                        rList.Add(row["DispItemIndex"].ToString());
                        rList.Add(row["CalcItemFlg1"].ToString());
                        rList.Add(row["CalcItemIndex1"].ToString());
                        rList.Add(row["CalcItemFlg2"].ToString());
                        rList.Add(row["CalcItemIndex2"].ToString());
                        rList.Add(row["CalcItemFlg3"].ToString());
                        rList.Add(row["CalcItemIndex3"].ToString());
                        rList.Add(row["CalcItemFlg4"].ToString());
                        rList.Add(row["CalcItemIndex4"].ToString());
                        rList.Add(row["CalcItemFlg5"].ToString());
                        rList.Add(row["CalcItemIndex5"].ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

    }
}