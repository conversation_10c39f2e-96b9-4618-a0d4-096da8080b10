﻿namespace ShinKenShinKun.UI;

public partial class FunctionButtonControl : ContentView
{
    public static readonly BindableProperty CommandParameterProperty =
        BindableProperty.Create(
            nameof(CommandParameter),
            typeof(object),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty CommandProperty =
            BindableProperty.Create(
            nameof(Command),
            typeof(Command),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty TextProperty =
        BindableProperty.Create(
            nameof(Text),
            typeof(string),
            typeof(FunctionButtonControl),
            defaultValue: string.Empty,
            defaultBindingMode: BindingMode.TwoWay);

    public FunctionButtonControl()
    {
        InitializeComponent();
    }

    public Command Command
    {
        get => (Command)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    public object CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }
}