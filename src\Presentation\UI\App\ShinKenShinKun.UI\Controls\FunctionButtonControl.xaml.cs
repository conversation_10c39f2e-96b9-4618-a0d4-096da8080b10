﻿using Serilog;

namespace ShinKenShinKun.UI;

public partial class FunctionButtonControl : ContentView
{
    public static readonly BindableProperty CommandParameterProperty =
        BindableProperty.Create(
            nameof(CommandParameter),
            typeof(object),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty CommandProperty =
            BindableProperty.Create(
            nameof(Command),
            typeof(Command),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty TextProperty =
        BindableProperty.Create(
            nameof(Text),
            typeof(string),
            typeof(FunctionButtonControl),
            defaultValue: string.Empty,
            defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BackgroundColorProperty =
        BindableProperty.Create(
            nameof(BackgroundColor),
            typeof(string),
            typeof(FunctionButtonControl),
            defaultValue: string.Empty,
            defaultBindingMode: BindingMode.OneWay,
            propertyChanged: OnBackgroundColorChanged);

    public static readonly BindableProperty StyleProperty =
        BindableProperty.Create(
            nameof(Style),
            typeof(Style),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.OneWay);

    public static readonly BindableProperty ButtonActionProperty =
        BindableProperty.Create(
            nameof(ButtonAction),
            typeof(ButtonActionModel),
            typeof(FunctionButtonControl),
            defaultValue: null,
            defaultBindingMode: BindingMode.OneWay);

    public static readonly BindableProperty AreaIdProperty =
        BindableProperty.Create(
            nameof(AreaId),
            typeof(string),
            typeof(FunctionButtonControl),
            defaultValue: string.Empty,
            defaultBindingMode: BindingMode.OneWay);

    public FunctionButtonControl()
    {
        InitializeComponent();
        // Initialize with default style
        Style = Styles.FunctionButtonDefaultStyle;

        // Set up command for button action
        Command = new Command(async () => await ExecuteButtonActionAsync());
    }

    private static void OnBackgroundColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is FunctionButtonControl control && newValue is string colorValue)
        {
            control.ApplyBackgroundColor(colorValue);
        }
    }

    public Command Command
    {
        get => (Command)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    public object CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    public string BackgroundColor
    {
        get => (string)GetValue(BackgroundColorProperty);
        set => SetValue(BackgroundColorProperty, value);
    }

    public Style Style
    {
        get => (Style)GetValue(StyleProperty);
        set => SetValue(StyleProperty, value);
    }

    public ButtonActionModel ButtonAction
    {
        get => (ButtonActionModel)GetValue(ButtonActionProperty);
        set => SetValue(ButtonActionProperty, value);
    }

    public string AreaId
    {
        get => (string)GetValue(AreaIdProperty);
        set => SetValue(AreaIdProperty, value);
    }

    private void ApplyBackgroundColor(string colorValue)
    {
        if (!string.IsNullOrEmpty(colorValue))
        {
            try
            {
                // Create a custom style with the specified background color
                var customStyle = new Style(typeof(RadButton));
                customStyle.BasedOn = Styles.FunctionButtonDefaultStyle;
                customStyle.Setters.Add(new Setter
                {
                    Property = VisualElement.BackgroundColorProperty,
                    Value = Color.FromArgb(colorValue)
                });

                // Set the Style property which will be bound to the RadButton
                Style = customStyle;
            }
            catch
            {
                // Fallback to default style if color parsing fails
                Style = Styles.FunctionButtonDefaultStyle;
            }
        }
        else
        {
            // Use default style when no background color is specified
            Style = Styles.FunctionButtonDefaultStyle;
        }
    }

    private async Task ExecuteButtonActionAsync()
    {
        if (ButtonAction == null) return;

        try
        {
            var actionService = Application.Current?.Handler?.MauiContext?.Services?.GetService<IButtonActionService>();
            if (actionService != null)
            {
                await actionService.ExecuteActionAsync(ButtonAction, AreaId);
            }
        }
        catch (Exception ex)
        {
            Log.Error(nameof(FunctionButtonControl), $"{nameof(ExecuteButtonActionAsync)}: Error executing button action: {ex.Message}");
        }
    }
}