using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class ChildGroupTermMasterRecord
    {
        private const int CHILDGROUPTERM_ID_POS = 0;
        private const int CHILDGROUP_ID_POS = 1;
        private const int IPADDRESS_POS = 2;
        private const int LASTUPDATE_POS = 3;

        private string FldChildGroupTermId;
        private string FldChildGroupId;
        private string FldIpAddress;
        private string FldLastUpdate;
        private string FldGuideFlg;
        private string FldTermFlg;

        public ChildGroupTermMasterRecord(string[] dbData)
        {
            this.ChildGroupTermId = dbData[CHILDGROUPTERM_ID_POS];
            this.ChildGroupId = dbData[CHILDGROUP_ID_POS];
            this.IpAddress = dbData[IPADDRESS_POS];
            this.LastUpdate = dbData[LASTUPDATE_POS];
        }

        public ChildGroupTermMasterRecord(
            string childGroupTermId,
            string childGroupId,
            string ipAddress,
            string lastUpdate,
            string guideFlg,
            string termFlg)
        {
            this.ChildGroupTermId = childGroupTermId;
            this.ChildGroupId = childGroupId;
            this.IpAddress = ipAddress;
            this.LastUpdate = lastUpdate;
            this.GuideFlg = guideFlg;
            this.TermFlg = termFlg;
        }

        public string ChildGroupTermId
        {
            get
            {
                return this.FldChildGroupTermId;
            }
            set
            {
                this.FldChildGroupTermId = value;
            }
        }

        public string ChildGroupId
        {
            get
            {
                return this.FldChildGroupId;
            }
            set
            {
                this.FldChildGroupId = value;
            }
        }

        public string IpAddress
        {
            get
            {
                return this.FldIpAddress;
            }
            set
            {
                this.FldIpAddress = value;
            }
        }

        public string LastUpdate
        {
            get
            {
                return this.FldLastUpdate;
            }
            set
            {
                this.FldLastUpdate = value;
            }
        }

        public string GuideFlg
        {
            get
            {
                return this.FldGuideFlg;
            }
            set
            {
                this.FldGuideFlg = value;
            }
        }

        public string TermFlg
        {
            get
            {
                return this.FldTermFlg;
            }
            set
            {
                this.FldTermFlg = value;
            }
        }
    }
}
