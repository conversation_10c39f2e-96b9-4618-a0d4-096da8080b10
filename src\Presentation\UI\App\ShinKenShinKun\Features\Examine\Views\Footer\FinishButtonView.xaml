<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    Style="{x:Static ui:Styles.DarkBlueWidth80ButtonBorderStyle}"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.FinishButtonView"
    x:Name="this">
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Aspect="Fill"
            Source="{x:Static ui:Icons.Finish}"
            Style="{x:Static ui:Styles.ImageFooterXlSizeButtonStyle}" />
        <Label
            Style="{x:Static ui:Styles.FinishLabelStyle}" />
    </VerticalStackLayout>
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding FinishCommand, Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
</telerik:RadBorder>
