﻿namespace ShinKenShinKun.UI;

public class UserNameRadEntryBehavior : Behavior<RadEntry>
{
    private static readonly Regex UsernameRegex = new(@"^[a-zA-Z0-9._]*$", RegexOptions.Compiled);

    protected override void OnAttachedTo(RadEntry radEntry)
    {
        base.OnAttachedTo(radEntry);
        radEntry.TextChanged += OnTextChanged;
    }

    protected override void OnDetachingFrom(RadEntry radEntry)
    {
        base.OnDetachingFrom(radEntry);
        radEntry.TextChanged -= OnTextChanged;
    }

    private void OnTextChanged(object sender, TextChangedEventArgs e)
    {
        if (sender is RadEntry radEntry && !string.IsNullOrEmpty(e.NewTextValue))
        {
            // Allow only Latin characters
            string filteredText = new([.. e.NewTextValue.Where(c => UsernameRegex.IsMatch(c.ToString()))]);

            if (radEntry.Text != filteredText)
            {
                radEntry.Text = filteredText;
            }
        }
    }
}