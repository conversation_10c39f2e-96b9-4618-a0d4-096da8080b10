<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualPatientDetail"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:DataType="app:IndividualProgressViewModel"
    x:Name="this">
    <Grid
        Padding="{x:Static ui:Dimens.SpacingSm2}"
        RowDefinitions="*,auto"
        RowSpacing="{x:Static ui:Dimens.SpacingMd}">
        <!--Spec-->
        <telerik:RadBorder
            Grid.Row="0"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingS}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <Grid
                RowSpacing="{x:Static ui:Dimens.SpacingMd}"
                Padding="{ui:EdgeInsets Bottom={x:Static ui:Dimens.SpacingMd}}"
                RowDefinitions="auto,*">
                <telerik:RadButton
                    Grid.Row="0"
                    Text="{x:Static resources:AppResources.TestResult}"
                    TextColor="{x:Static ui:AppColors.White}"
                    Grid.Column="0"
                    HorizontalOptions="Start"
                    Style="{Static ui:Styles.NormalButton}" />
                <ScrollView
                    Grid.Row="1"
                    Padding="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingXxl}}">
                    <VerticalStackLayout
                        Spacing="{x:Static ui:Dimens.SpacingMd}">
                        <app:IndividualSectionView
                            ItemSource="{Binding IndividualPatientInfo.IndividualStats}"
                            SectionName="{x:Static resources:AppResources.Measurement}"
                            SpanCount="3"/>
                        <app:IndividualSectionView
                            ItemSource="{Binding IndividualPatientInfo.IndividualSight}"
                            SectionName="{x:Static resources:AppResources.Sight}"
                            SpanCount="2" />
                        <app:IndividualSectionView
                            ItemSource="{Binding IndividualPatientInfo.IndividualBloodPressure}"
                            SectionName="{x:Static resources:AppResources.BloodPressure}"
                            SpanCount="2" />
                        <app:IndividualSectionView
                            ItemSource="{Binding IndividualPatientInfo.IndividualHearing}"
                            SectionName="{x:Static resources:AppResources.HearingSelection}"
                            SpanCount="2" />
                        <app:IndividualSectionView
                            ItemSource="{Binding IndividualPatientInfo.IndividualHearingThreshold}"
                            SectionName="{x:Static resources:AppResources.HearingThreshold}"
                            SpanCount="2" />
                    </VerticalStackLayout>
                   
                </ScrollView>
            </Grid>
        </telerik:RadBorder>
        <!--Medical check-->
        <telerik:RadBorder
            Grid.Row="1"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingS}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <VerticalStackLayout
                Spacing="{x:Static ui:Dimens.SpacingXs}">
                <telerik:RadButton
                    Grid.Row="0"
                    Text="{x:Static resources:AppResources.Progress}"
                    TextColor="{x:Static ui:AppColors.White}"
                    Grid.Column="0"
                    HorizontalOptions="Start"
                    Style="{Static ui:Styles.NormalButton}" />
                <app:PatientListView
                    Grid.Row="1"
                    HorizontalOptions="CenterAndExpand"
                    ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
            </VerticalStackLayout>
        </telerik:RadBorder>
    </Grid>
</ContentView>
