﻿namespace ShinKenShinKun.CoreMVVM;

public abstract class BasePage : ContentPage
{
    protected override void OnAppearing()
    {
        base.OnAppearing();

        if (BindingContext is BaseViewModel vm)
        {
            vm.OnAppearingAsync().FireAndForget();
        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();

        if (BindingContext is BaseViewModel vm)
        {
            vm.OnDisappearingAsync().FireAndForget();
        }
    }
}