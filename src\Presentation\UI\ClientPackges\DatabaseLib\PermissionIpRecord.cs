using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class PermissionIpRecord
    {
        private const int IPADRESS_POS = 0;
        private const int MEDICAL_CHECK_NO_POS = 1;
        private const int TERM_ID_POS = 2;
        private const int LAST_UPDATE_POS = 3;
        private string FldIpAdress;
        private string FldMedicalCheckNo;
        private string FldTermId;
        private string FldLastUpDate;

        public PermissionIpRecord(string[] dbData)
        {
            this.IpAdress = dbData[IPADRESS_POS];
            this.MedicalCheckNo = dbData[MEDICAL_CHECK_NO_POS];
            this.TermId = dbData[TERM_ID_POS];
            this.LastUpDate = dbData[LAST_UPDATE_POS];
        }

        public string IpAdress
        {
            get
            {
                return this.FldIpAdress;
            }
            set
            {
                this.FldIpAdress = value;
            }
        }

        public string MedicalCheckNo
        {
            get
            {
                return this.FldMedicalCheckNo;
            }
            set
            {
                this.FldMedicalCheckNo = value;
            }
        }

        public string TermId
        {
            get
            {
                return this.FldTermId;
            }
            set
            {
                this.FldTermId = value;
            }
        }

        public string LastUpDate
        {
            get
            {
                return this.FldLastUpDate;
            }
            set
            {
                this.FldLastUpDate = value;
            }
        }
    }
}
