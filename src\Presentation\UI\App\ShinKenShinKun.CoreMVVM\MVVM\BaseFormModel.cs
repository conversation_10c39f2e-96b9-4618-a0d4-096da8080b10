﻿namespace ShinKenShinKun.CoreMVVM;

public abstract partial class BaseFormModel: ObservableValidator
{
    protected virtual string[] ValidatableAndSupportPropertyNames => new string[0];

    public virtual bool IsValid()
    {
        ValidateAllProperties();

        foreach (var propertyName in ValidatableAndSupportPropertyNames)
        {
            OnPropertyChanged(propertyName);
        }

        return !HasErrors;
    }

}
