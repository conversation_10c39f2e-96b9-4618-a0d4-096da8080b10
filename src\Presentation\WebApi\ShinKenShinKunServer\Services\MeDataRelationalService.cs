﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MeDataRelationalService の概要の説明です
    /// </summary>
    public class MeDataRelationalService
    {
        private DispSettingService service;
        private Logger logger = null;

        public MeDataRelationalService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }
        public PluralRecord[] GetMeDataRelationalAll()
        {
            try
            {
                DispSettingDataSet.MeDataRelationalDataTable tmpTable =
                    service.GetMeDataRelationalAll().MeDataRelational;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MeDataRelationalRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.MeItemCode);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMeDataRelationalNo(string medicalCheckNo)
        {
            try
            {
                short medicalNo = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.MeDataRelationalDataTable tmpTable =
                    service.GetMeDataRelationalNo(medicalNo).MeDataRelational;
                if (tmpTable != null)
                {
                    List<PluralRecord> pList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MeDataRelationalRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.MeItemCode);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pList.Add(pRec);
                    }
                    if (pList.Count != 0)
                    {
                        return pList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMeDataRelationalItemNo(string medicalCheckNo, string itemNo)
        {
            try
            {
                short sNo = Convert.ToInt16(medicalCheckNo);
                short sItmNo = Convert.ToInt16(itemNo);
                DispSettingDataSet.MeDataRelationalDataTable tmpTable =
                    service.GetMeDataRelationalItemNo(sNo, sItmNo).MeDataRelational;
                if (tmpTable != null)
                {
                    List<PluralRecord> pList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MeDataRelationalRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.MeItemCode);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pList.Add(pRec);
                    }
                    if (pList.Count != 0)
                    {
                        return pList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMeDataRelationalMeCode(string meItemCode)
        {
            try
            {
                DispSettingDataSet.MeDataRelationalDataTable tmpTable =
                    service.GetMeDataRelationalMeCode(meItemCode).MeDataRelational;
                if (tmpTable != null)
                {
                    List<PluralRecord> pList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MeDataRelationalRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.MeItemCode);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pList.Add(pRec);
                    }
                    if (pList.Count != 0)
                    {
                        return pList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}