﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// TransformedSettingService の概要の説明です

    /// </summary>
    public class TransformedSettingService
    {
        private DispSettingService service;
        private Logger logger = null;

        public TransformedSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetTransformedSettingAll()
        {
            try
            {
                DispSettingDataSet.TransformedSettingDataTable tmpTable
                = service.GetTransformedSettingAll().TransformedSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.TransformedSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        for (int i = 1; i <= 50; i++)
                        {
                            if (row["TransformedItemNo" + i.ToString()] == null)
                            {
                                rList.Add(null);
                            }
                            else
                            {
                                rList.Add(row["TransformedItemNo" + i.ToString()].ToString());
                            }
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetTransformdSetting(
            string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.TransformedSettingDataTable tmpTable
                    = service.GetTransformedSetting(medicalCheckNoSht).TransformedSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.TransformedSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        for (int i = 1; i <= 50; i++)
                        {
                            if (row["TransformedItemNo" + i.ToString()] == null)
                            {
                                rList.Add(null);
                            }
                            else
                            {
                                rList.Add(row["TransformedItemNo" + i.ToString()].ToString());
                            }
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}