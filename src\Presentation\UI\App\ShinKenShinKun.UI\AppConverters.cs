﻿namespace ShinKenShinKun.UI;

public class AppConverters
{
    public static readonly IsEqualConverter IsEqual = new();
    public static readonly EnumToBoolConverter EnumToBool = new();
    public static readonly IsListNullOrEmptyConverter IsListNullOrEmpty = new();
    public static readonly IsListNotNullOrEmptyConverter IsListNotNullOrEmpty = new();
    public static readonly StringToBoolConverter StringToBoolConverter = new();
    public static readonly ObjectToBoolConverter ObjectToBoolConverter = new();
    public static readonly IsNullConverter IsNullConverter = new();
    public static readonly DateToJapaneseFormatConverter DateToJapaneseFormat = new();
    public static readonly SexConverter Sex = new();
    public static readonly SizeMultipleConverter SizeMultipleConverter = new();
    public static readonly ResponsiveFontSizeConverter ResponsiveFontSizeConverter = new();
    public static readonly IndividualFontSizeConverter IndividualFontSizeConverter = new();
    public static readonly LabelLengthToWidthConverter LabelLengthToWidthConverter = new();
    public static readonly TimeSpanToStringConverter TimeSpanToStringConverter = new();
}
