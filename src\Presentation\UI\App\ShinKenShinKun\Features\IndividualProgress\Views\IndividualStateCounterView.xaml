<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualStateCounterView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
   
    <HorizontalStackLayout
        VerticalOptions="Center">
        <VerticalStackLayout>
            <Label
                x:Name="LabelRef"
                Text="{Binding Label, Source={x:Reference this}}"
                TextColor="{Binding LabelColor, Source={x:Reference this}}"
                Style="{x:Static ui:Styles.PatientStatTitleLabel}" />
            <BoxView
                Margin="{ui:EdgeInsets 
                Top={x:Static ui:Dimens.Spacing0}}"
                WidthRequest="{Binding Label, Source={x:Reference this}, Converter={x:Static ui:AppConverters.LabelLengthToWidthConverter}}"
                BackgroundColor="{Binding LabelColor, Source={x:Reference this}}"
                Style="{x:Static ui:Styles.IndividualProgressStatusCounterRule}" />
        </VerticalStackLayout>
        <Grid
            RowDefinitions="auto,auto">
            <HorizontalStackLayout
                Grid.Row="0">
                <Label
                    Text="{Binding Value, Source={x:Reference this}}"
                    TextColor="{Binding ValueColor, Source={x:Reference this}}"
                    VerticalOptions="Center"
                    Style="{x:Static ui:Styles.PatientStatDetailLabel}" />
                <Label
                    Text="{x:Static resources:AppResources.Person}"
                    TextColor="{Binding ValueColor, Source={x:Reference this}}"
                    VerticalOptions="Center"
                    Style="{x:Static ui:Styles.PatientStatDetailLabel}" />
            </HorizontalStackLayout>
            <BoxView
                Grid.Row="1"
                Margin="{ui:EdgeInsets
                Top={x:Static ui:Dimens.Spacing0}}"
                BackgroundColor="{Binding ValueColor, Source={x:Reference this}}"
                Style="{x:Static ui:Styles.HorizontalRule}" />
        </Grid>

    </HorizontalStackLayout>
</ContentView>
