﻿namespace ShinKenShinKun.UI;

public static partial class Styles
{
    public static Style BaseOn(this Style style, Style basedOn)
    {
        style.BasedOn = basedOn;
        return style;
    }

    public static Style BindTrigger(this Style style, Microsoft.Maui.Controls.Binding binding, object value, params (BindableProperty p, object value)[] setters)
    {
        var dataTrigger = new DataTrigger(style.TargetType)
        {
            Binding = binding,
            Value = value
        };

        for (int i = 0; i < setters.Length; i++)
        {
            dataTrigger.Setters.Add(new Setter
            {
                Property = setters[i].p,
                Value = setters[i].value
            });
        }

        style.Triggers.Add(dataTrigger);

        return style;
    }

    public static Style CreateStyle<T>()
    {
        return new Style(typeof(T));
    }

    public static Style Set(this Style style, BindableProperty property, object value)
    {
        style.Setters.Add(new Setter
        {
            Property = property,
            Value = value
        });
        return style;
    }
}