﻿<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.LoginPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:vm="clr-namespace:ShinKenShinKun"
    x:Name="this"
    xmlns:app="clr-namespace:ShinKenShinKun"
    Title="LoginPage"
    x:DataType="vm:LoginPageViewModel">
    <Grid
        BackgroundColor="{Static ui:AppColors.Platinum}"
        HorizontalOptions="Fill"
        RowDefinitions="80,*,auto"
        VerticalOptions="Fill">
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.LoginScreenTitle}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <Grid
            Grid.Row="1"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">
            <Grid
                HorizontalOptions="Center"
                RowDefinitions="auto,auto,auto"
                RowSpacing="{Static ui:Dimens.Spacing20}"
                VerticalOptions="Center">
                <Label
                    x:Name="loginLabel"
                    Style="{Static ui:Styles.LoginSystemNameLabelStyle}"
                    Text="{Static resources:AppResources.SystemName}" />
                <Button
                    Grid.ColumnSpan="1"
                    MinimumHeightRequest="0"
                    MinimumWidthRequest="0"
                    Opacity="0"
                    ZIndex="-1" />
                <Grid
                    Grid.Row="1"
                    RowDefinitions="auto,auto"
                    RowSpacing="{Static ui:Dimens.Spacing5}"
                    WidthRequest="{Static ui:Dimens.Width350}">
                    <telerik:RadBorder
                        Style="{Static ui:Styles.UserIdPasswordRadBorderStyle}">
                        <Grid
                            ColumnDefinitions="27*,73*"
                            ColumnSpacing="{Static ui:Dimens.Spacing5}">
                            <Label
                                Style="{Static ui:Styles.UserIdPasswordLabelStyle}"
                                Text="{Static resources:AppResources.UserId}" />
                            <telerik:RadEntry
                                Grid.Column="1"
                                BorderThickness="0"
                                HorizontalOptions="Fill"
                                Text="{Binding LoginForm.LoginId}" />
                        </Grid>
                    </telerik:RadBorder>
                    <telerik:RadBorder
                        Grid.Row="1"
                        Style="{Static ui:Styles.UserIdPasswordRadBorderStyle}">
                        <Grid
                            ColumnDefinitions="27*,73*"
                            ColumnSpacing="5">
                            <Label
                                Style="{Static ui:Styles.UserIdPasswordLabelStyle}"
                                Text="{Static resources:AppResources.Password}" />
                            <Grid
                                Grid.Column="1"
                                ColumnDefinitions="*,auto">
                                <telerik:RadEntry
                                    x:Name="passwordEntry"
                                    Grid.ColumnSpan="2"
                                    Padding="{ui:EdgeInsets Right={Static ui:Dimens.Spacing24},
                                                            Left={Static ui:Dimens.SpacingSm}}"
                                    BorderThickness="0"
                                    ClearButtonVisibility="Never"
                                    HorizontalOptions="Fill"
                                    IsPassword="True"
                                    Text="{Binding LoginForm.Password}" />
                                <Image
                                    x:Name="showHidePassWord"
                                    Grid.Column="1"
                                    Margin="{ui:EdgeInsets Right={Static ui:Dimens.SpacingXs}}"
                                    BackgroundColor="{Static ui:AppColors.Transparent}"
                                    HorizontalOptions="End"
                                    Source="{Static ui:Icons.Visibility}"
                                    WidthRequest="{Static ui:Dimens.Spacing20}">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer
                                            Tapped="TapGestureRecognizer_Tapped" />
                                    </Image.GestureRecognizers>
                                </Image>
                            </Grid>
                        </Grid>
                    </telerik:RadBorder>
                </Grid>
                <telerik:RadButton
                    Grid.Row="2"
                    Command="{Binding LoginCommand}"
                    Style="{Static ui:Styles.LoginButtonStyle}"
                    Text="{Static resources:AppResources.Login}"
                    WidthRequest="{Binding Source={Reference loginLabel}, Path=Width}" />
            </Grid>
        </Grid>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            ClientSelected="{Binding ClientSelected}"
            FinishCommand="{Binding OpenQuitAppPopupCommand}" />
    </Grid>
</mvvm:BasePage>