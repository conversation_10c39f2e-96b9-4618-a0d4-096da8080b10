﻿namespace ShinKenShinKun.Utils;

public static class ReflectionHelper
{
    public static object GetPropertyValue(object source, string propertyName)
    {
        Type type = source.GetType();
        PropertyInfo propertyInfo = type.GetProperty(propertyName);
        object value = propertyInfo.GetValue(source, null);
        return value;
    }

    public static void SetPropertyValue(object source, string propertyName, object value)
    {
        Type type = source.GetType();
        PropertyInfo propertyInfo = type.GetProperty(propertyName);
        propertyInfo.SetValue(source, value);
    }
}