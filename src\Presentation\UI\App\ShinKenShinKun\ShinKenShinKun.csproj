﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
        The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
        When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
        The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
        either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->
		<RootNamespace>ShinKenShinKun</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>ShinKenShinKun</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.shinkenshinkun</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>

        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
        <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
        <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <OutputType Condition="'$(TargetFramework)' != 'net9.0'">Exe</OutputType>
        <OutputType Condition="'$(TargetFramework)' == 'net9.0'">Library</OutputType>
    </PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('Release')) ">
		<DebugType>none</DebugType>
	</PropertyGroup>
	<ItemGroup>
        <!-- App Icon -->
        <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
		<MauiAsset Include="appsettings - Copy (2).json" />
		<MauiAsset Include="appsettings - Copy.json" />
		<MauiAsset Include="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AdamE.MemoryToolkit.Maui" />
		<PackageReference Include="FFImageLoading.Maui" />
		<PackageReference Include="Microsoft.Extensions.Configuration" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" />
		<PackageReference Include="Microsoft.Maui.Controls" />
		<PackageReference Include="Microsoft.UI.Xaml" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Serilog.Extensions.Logging" />
		<PackageReference Include="Serilog.Sinks.Console" />
		<PackageReference Include="Serilog.Sinks.File" />
		<PackageReference Include="System.Diagnostics.EventLog" />
		<PackageReference Include="Telerik.UI.for.Maui" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ShinKenShinKun.CoreMVVM\ShinKenShinKun.CoreMVVM.csproj" />
		<ProjectReference Include="..\ShinKenShinKun.DataAccess\ShinKenShinKun.DataAccess.csproj" />
		<ProjectReference Include="..\ShinKenShinKun.UI\ShinKenShinKun.UI.csproj" />
		<ProjectReference Include="..\ShinKenShinKun.Utils\ShinKenShinKun.Utils.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Features\Examine\Views\NoteEditPopup.xaml.cs">
	    <DependentUpon>NoteEditPopup.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\IndividualProgress\Views\IndividualPatientLabelView.xaml.cs">
	    <DependentUpon>IndividualPatientLabelView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\IndividualProgress\Views\IndividualStateDisplayView.xaml.cs">
	    <DependentUpon>IndividualStateDisplayView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\IndividualProgress\Views\IndividualStateCounterView.xaml.cs">
	    <DependentUpon>IndividualStateCounterView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\IndividualProgress\Views\IndividualSectionView.xaml.cs">
	    <DependentUpon>IndividualSectionView.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\PatientPending\Pages\PatientPendingPage.xaml.cs">
	    <DependentUpon>PatientPendingPage.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\PatientGuidanceAdjustment\Pages\GuidanceAdjustmentPage.xaml.cs">
	    <DependentUpon>GuidanceAdjustmentPage.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\TestSelection\Views\SelectionTestStateListView.xaml.cs">
	    <DependentUpon>SelectionTestStateListView.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkCancelPauseTestsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkHoldPatientsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkHoldTestsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkPauseTestsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkRegisterHoldPatientsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkReleaseHoldPatientsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BulkReleaseHoldTestsButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\ChangeGuidanceButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\EnterIdButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\FinishButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\FooterView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\HoldButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\LogoutButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\PauseButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\RegisterButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\HeaderView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\NoteEditPopup.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\Footer\BackButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\HomeButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\LoginUserView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\MaskButtonView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\SwitchModeView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Pages\GuidanceSelectionPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Home\Pages\BlankPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Pages\IndividualProgressPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualProgessPatientsInfo.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualProgressPatientDetail.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualPatientLabelView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualSectionView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualStateCounterView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualStateDisplayView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\IndividualProgress\Views\IndividualTableView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Pages\GuidanceSettingPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Views\GuidanceSelectionListView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Login\Pages\LoginPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Home\Pages\HomePage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Pages\ExamineBeforeAuthPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\ExaminationBarView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\ExaminationTableView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\PatientInfoView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\PatientListMonitorView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Examine\Views\PatientListView.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Pages\GuidanceAdjustmentPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Views\PatientGuidanceFilterView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\Guidance\Views\PatientGuidanceTableView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\StopExamine\Pages\StopExaminePage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\PatientPending\Pages\PatientPendingPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\PatientPending\Views\PatientPendingTableView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\TestSelection\Pages\PendingTestSelectionPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\TestSelection\Pages\StopTestSelectionPage.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\TestSelection\Pages\TestSelectionPage.xaml">
			<Generator>MSBuild:Compile</Generator>
		</MauiXaml>
		<MauiXaml Update="Features\TestSelection\Views\SelectionTestStateListView.xaml">
		  <Generator>MSBuild:Compile</Generator>
		</MauiXaml>
	</ItemGroup>
</Project>