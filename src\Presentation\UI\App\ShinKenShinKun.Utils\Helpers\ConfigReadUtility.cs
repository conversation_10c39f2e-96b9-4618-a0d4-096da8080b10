﻿namespace ShinKenShinKun.Utils;

public class ConfigReadUtility
{
    private const string LOG_TITLE = "【ConfigReadUtility】";

    /// <summary>
    /// Configファイルの読み込み格納先
    /// </summary>
    private static Dictionary<string, string> ConfigData = new Dictionary<string, string>();

    #region Configファイルの読み込み

    public static void InitializeConfigData()
    {
        // 実行パス以下のMessage.xmlを取得しにいきます。
        Assembly asm = Assembly.GetExecutingAssembly();
        // -------------------------------------------------- //
        OperatingSystem osInfo = Environment.OSVersion;
        bool? IsXpModeFlg = null;
        // 使用マシンがＸＰの場合
        if (osInfo.Version.Major == 5 && osInfo.Version.Minor == 1)
        {
            IsXpModeFlg = true;
        }
        // 使用マシンがWin7の場合
        else if (osInfo.Version.Major == 6 && osInfo.Version.Minor >= 1)
        {
            IsXpModeFlg = true;
        }
        else
        {
            IsXpModeFlg = false;
        }

        //string configFilePath = System.Reflection.Assembly.GetCallingAssembly().GetName().CodeBase;
        string configFilePath = Assembly.GetEntryAssembly().GetName().CodeBase;
        if (IsXpModeFlg.HasValue && IsXpModeFlg.Value)
        {
            //configFilePath = Path.Combine(Directory.GetCurrentDirectory(),
            //    Path.GetFileName(System.Reflection.Assembly.GetCallingAssembly().GetName().CodeBase));
            configFilePath = Path.Combine(Directory.GetCurrentDirectory(),
                Path.GetFileName(Assembly.GetEntryAssembly().GetName().CodeBase));
        }
        configFilePath = configFilePath.Replace(".exe", ".exe.config");
        configFilePath = configFilePath.Replace(".EXE", ".exe.config");
        CommonInit(configFilePath);
    }

    #endregion Configファイルの読み込み

    #region InitializeConfigData

    public static void InitializeConfigData(string filePath)
    {
        CommonInit(filePath);
    }

    #endregion InitializeConfigData

    #region CommonInit

    private static void CommonInit(string configFilePath)
    {
        try
        {
            //XMLファイルを開く
            FileStream fStream = new FileStream(configFilePath, FileMode.Open, FileAccess.Read);
            XmlTextReader reader = new XmlTextReader(fStream);
            //XMLファイルを1ノードずつ読み込む
            while (reader.Read())
            {
                reader.MoveToContent();
                //ノードに属性がある場合(例：<chr animal="熊">フィロ</chr>の「animal」)
                if (reader.NodeType == XmlNodeType.Element && reader.HasAttributes)
                {
                    //ノード名がある場合
                    if (reader.Name == "add")
                    {
                        string strKey = string.Empty;
                        string strValue = string.Empty;
                        //ノードの属性の数だけループ
                        for (int i = 0; i < reader.AttributeCount; i++)
                        {
                            //インデックスを属性に移動します
                            reader.MoveToAttribute(i);
                            if (reader.Name == "key")
                            {
                                //属性データを取得(例：フィロの「熊」)
                                strKey = reader.Value;
                            }
                            else if (reader.Name == "value")
                            {
                                //属性データを取得(例：フィロの「熊」)
                                strValue = reader.Value;
                            }
                        }
                        if (strKey != string.Empty)
                        {
                            ConfigData.Add(strKey, strValue);
                        }
                    }
                    //ノード要素がある場合(例：<chr animal="熊">フィロ</chr>の「chr」)
                    else if (reader.LocalName.Equals("setting"))
                    {
                        string strKey = string.Empty;
                        string strValue = string.Empty;
                        //インデックスを属性に移動します
                        reader.MoveToAttribute(0);
                        //属性データを取得(例：フィロの「熊」)
                        strKey = reader.Value;
                        if (strKey.Contains("EyeBotnNaked"))
                        {
                            var r = 0;
                        }
                        // 処理的に２度読む必要がある。
                        strValue = reader.ReadString();
                        if (strValue.Trim(new char[] { '\r', '\n', ' ', '\t' }) == string.Empty)
                        {
                            strValue = reader.ReadString();
                        }
                        if (strValue == string.Empty)
                        {
                            reader.MoveToContent();
                            strValue = reader.ReadString();
                        }
                        if (strKey != string.Empty)
                        {
                            ConfigData.Add(strKey, strValue);
                        }
                    }
                    //インデックスを要素に移動します
                    reader.MoveToElement();
                }
            }
            //XMLファイルを閉じる
            reader.Close();
            fStream.Close();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }
    }

    #endregion CommonInit

    #region Configデータの取得

    public static string GetConfigData(string key)
    {
        try
        {
            if (ConfigData.ContainsKey(key))
            {
                string data = ConfigData[key];
                if (data.Trim() == "")
                {
                }
                else
                {
                }
                return data;
            }
            else
            {
                return null;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return null;
        }
    }

    #endregion Configデータの取得
}