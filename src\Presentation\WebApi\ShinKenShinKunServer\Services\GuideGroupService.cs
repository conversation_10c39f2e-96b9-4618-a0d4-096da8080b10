﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.GuideSupportDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;

namespace ShinKenShinKunServer.Services
{
    public class GuideGroupService
    {
        GuideSupportDataSet GuideDataSet = new GuideSupportDataSet();

        private GuideSupportService srv;
        private Logger logger = null;

        public GuideGroupService()
        {
            srv = new GuideSupportService();
        }

        #region ClientGuideData（& ChildGroupMaster）取得
        /// <summary>
        /// ClientGuideData（& ChildGroupMaster）取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetClientGuideDataWithChildGroup(DateTime dateTime, string clientId, string division)
        {
            try
            {
                //動的配列を用意する。
                ArrayList dataArray = new ArrayList();

                GuideDataSet = srv.GetClientGuideDataWithChildGroup(dateTime, clientId, division);

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["ClientGuideData"].Rows.Count;
                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int i = 0; i < recordCount; i++)
                {
                    if (GuideDataSet.Tables["ClientGuideData"].Rows.Count != 0)
                    {
                        //日付・当日ID・区分をキーに検索し、その行を取得する。
                        DataRow row_cdata = GuideDataSet.Tables["ClientGuideData"].Rows[i];

                        if (row_cdata != null)
                        {
                            dataArray.Add(row_cdata["MedicalCheckDate"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ClientID"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["Division"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["RegistrationNo"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["GuideMasterId"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["LastUpdate"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["GroupId"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ChildGroupId"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ChildGroupName"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ChildGuideType"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["GuidePriority"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["GroupPriority"].ToString().PadLeft(1, ' '));
                        }
                        else
                        {
                            return null;
                        }

                        // ArrayListからString配列に変換
                        string[] datalist = new string[dataArray.Count];
                        Array.Copy(dataArray.ToArray(), datalist, dataArray.Count);
                        pRecord[i] = new PluralRecord(datalist);
                        dataArray.Clear();
                    }
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region ChildGroupDetailMaster取得
        /// <summary>
        /// ChildGroupDetailMaster取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetChildGroupDetailMaster(short childGroupId)
        {
            try
            {
                //動的配列を用意する。
                ArrayList dataArray = new ArrayList();

                GuideDataSet = srv.GetChildGroupDetailMaster(childGroupId);

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["ChildGroupDetailMaster"].Rows.Count;
                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int i = 0; i < recordCount; i++)
                {
                    if (GuideDataSet.Tables["ChildGroupDetailMaster"].Rows.Count != 0)
                    {
                        //日付・当日ID・区分をキーに検索し、その行を取得する。
                        DataRow row_cdata = GuideDataSet.Tables["ChildGroupDetailMaster"].Rows[i];

                        if (row_cdata != null)
                        {
                            dataArray.Add(row_cdata["ChildGroupDetailId"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ChildGroupId"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["ChildGroupPriority"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["MedicalCheckNo"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["LastUpdate"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["MedicalCheckName"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo1"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo2"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo3"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo4"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo5"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo6"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo7"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo8"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo9"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo10"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo11"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo12"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo13"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo14"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo15"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo16"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo17"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo18"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo19"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo20"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo21"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo22"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo23"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo24"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo25"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo26"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo27"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo28"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo29"].ToString().PadLeft(1, ' '));
                            dataArray.Add(row_cdata["CheckObjectNo30"].ToString().PadLeft(1, ' '));
                        }
                        else
                        {
                            return null;
                        }

                        // ArrayListからString配列に変換
                        string[] datalist = new string[dataArray.Count];
                        Array.Copy(dataArray.ToArray(), datalist, dataArray.Count);
                        pRecord[i] = new PluralRecord(datalist);
                        dataArray.Clear();
                    }
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region ChildGroupTermMaster取得
        /// <summary>
        /// ChildGroupTermMaster取得
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public string[] strGetChildGroupTermMaster(short medicalCheckNo)
        {
            try
            {
                //動的配列を用意する。
                ArrayList dataArray = new ArrayList();

                GuideDataSet = srv.GetChildGroupTermMaster(medicalCheckNo);

                if (GuideDataSet.Tables["ChildGroupTermMaster"].Rows.Count != 0)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。
                    DataRow row_cdata = GuideDataSet.Tables["ChildGroupTermMaster"].Rows[0];

                    if (row_cdata != null)
                    {
                        dataArray.Add(row_cdata["ChildGroupTermId"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["ChildGroupId"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["IPAddress"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["LastUpdate"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["GuideFlg"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["TermFlg"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }

                    // ArrayListからString配列に変換
                    string[] datalist = new string[dataArray.Count];
                    Array.Copy(dataArray.ToArray(), datalist, dataArray.Count);
                    return datalist;
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region NextGuideData取得
        /// <summary>
        /// NextGuideData取得
        /// </summary>
        /// <returns></returns>
        public string[] StrGetNextGuideData(DateTime dateTime, string clientId, string division)
        {
            try
            {
                //動的配列を用意する。
                ArrayList dataArray = new ArrayList();

                GuideDataSet = srv.GetNextGuideDataMaster(dateTime, clientId, division);

                if (GuideDataSet.Tables["NextGuideData"].Rows.Count != 0)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。
                    DataRow row_cdata = GuideDataSet.Tables["NextGuideData"].Rows[0];

                    if (row_cdata != null)
                    {
                        dataArray.Add(row_cdata["MedicalCheckDate"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["ClientId"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["Division"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["ChildGroupFlg"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["Value"].ToString().PadLeft(1, ' '));
                        dataArray.Add(row_cdata["LastUpdate"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }

                    // ArrayListからString配列に変換
                    string[] datalist = new string[dataArray.Count];
                    Array.Copy(dataArray.ToArray(), datalist, dataArray.Count);
                    return datalist;
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region NextGuideData削除
        /// <summary>
        /// NextGuideData削除を削除する。
        /// </summary>
        /// <returns></returns>
        public bool DeleteNextGuideData(DateTime dateTime, string clientId, string division)
        {
            try
            {
                //動的配列を用意する。
                ArrayList termArray = new ArrayList();

                NextGuideDataTableAdapter adapterDelete
                    = new NextGuideDataTableAdapter();
                adapterDelete.DeleteQuery(dateTime, clientId, division);

                return true;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
        #endregion

        #region NextGuideData　更新
        /// <summary>
        /// NextGuideData　更新
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="termID"></param>
        /// <param name="beforeTermID"></param>
        /// <returns></returns>
        public bool StrSetNextGuideData(DateTime dateTime, string clientId, string division, bool childGuideFlg, string value)
        {
            try
            {
                DataRow row = null;

                //日付(yyyymmddHHmmss形式)をDateTime型に変換する
                string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                //各データを、更新する行に上書きしていく。行がない場合は新規作成する。
                GuideDataSet = srv.GetNextGuideDataMaster(dateTime, clientId, division);

                if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 1)
                {
                    row = GuideDataSet.Tables["NextGuideData"].Rows[0];
                }
                else if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 0)
                {
                    row = GuideDataSet.Tables["NextGuideData"].NewRow();
                }
                row["MedicalCheckDate"] = dateTime;
                row["ClientId"] = clientId;
                row["Division"] = division;
                row["ChildGroupFlg"] = childGuideFlg;
                row["Value"] = value;
                row["LastUpdate"] = nowTime;

                // 2017/10/18 新規追加できないので修正
                if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 0)
                {
                    GuideDataSet.Tables["NextGuideData"].Rows.Add(row);
                }

                //健診状況の更新を行う。
                NextGuideDataTableAdapter adpt
                    = new NextGuideDataTableAdapter();
                adpt.Update(GuideDataSet.NextGuideData);
                return true;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                return false;
            }
        }
        #endregion

        #region NextGuideData　更新
        /// <summary>
        /// NextGuideData　更新
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="termID"></param>
        /// <param name="beforeTermID"></param>
        /// <returns></returns>
        public bool StrSetNextGuideDataHoldTime(DateTime dateTime, string clientId, string division, bool childGuideFlg, string value)
        {
            try
            {
                DataRow row = null;

                //日付(yyyymmddHHmmss形式)をDateTime型に変換する
                string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                //各データを、更新する行に上書きしていく。行がない場合は新規作成する。
                GuideDataSet = srv.GetNextGuideDataMaster(dateTime, clientId, division);

                if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 1)
                {
                    row = GuideDataSet.Tables["NextGuideData"].Rows[0];
                }
                else if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 0)
                {
                    row = GuideDataSet.Tables["NextGuideData"].NewRow();
                    row["LastUpdate"] = nowTime;
                }
                row["MedicalCheckDate"] = dateTime;
                row["ClientId"] = clientId;
                row["Division"] = division;
                row["ChildGroupFlg"] = childGuideFlg;
                row["Value"] = value;
                //row["LastUpdate"] = nowTime;

                // 2017/10/18 新規追加できないので修正
                if (GuideDataSet.Tables["NextGuideData"].Rows.Count == 0)
                {
                    GuideDataSet.Tables["NextGuideData"].Rows.Add(row);
                }

                //健診状況の更新を行う。
                NextGuideDataTableAdapter adpt
                    = new NextGuideDataTableAdapter();
                adpt.Update(GuideDataSet.NextGuideData);
                return true;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                return false;
            }
        }
        #endregion
    }
}