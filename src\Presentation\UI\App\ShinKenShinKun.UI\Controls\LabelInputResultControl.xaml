<?xml version="1.0" encoding="utf-8" ?>
<Grid
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:Class="ShinKenShinKun.UI.LabelInputResultControl"
    x:Name="this"
    RowDefinitions="*, Auto"
    RowSpacing="{x:Static ui:Dimens.SpacingXs}">
    <Grid
        VerticalOptions="End"
        Margin="{ui:EdgeInsets
                Horizontal={x:Static ui:Dimens.SpacingS}}"
        ColumnDefinitions="Auto, *, Auto">
        <Label
            x:Name="title"
            Grid.Column="0"
            Text="{Binding Title,Source={x:Reference this}}"
            TextColor="{x:Static ui:AppColors.LightBlue}"
            VerticalOptions="Center"
            Style="{x:Static ui:Styles.PatientStatTitleLabel}" />
        <Grid
            x:Name="grid"
            Grid.Column="1"
            VerticalOptions="Fill"
            HorizontalOptions="Fill">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer
                    Tapped="TapGestureRecognizer_Tapped" />
            </Grid.GestureRecognizers>
        </Grid>
        <Image
            x:Name="image"
            Grid.Column="2"
            HeightRequest="{x:Static ui:Dimens.IconSize26}"
            Source="{x:Static ui:Icons.ArrowChevronRightIcon}"
            HorizontalOptions="End"
            VerticalOptions="Center" />
    </Grid>
    <BoxView
        x:Name="boxView"
        Grid.Row="1"
        Grid.ColumnSpan="3"
        BackgroundColor="{x:Static ui:AppColors.LightBlue}"
        Style="{x:Static ui:Styles.HorizontalRule}"
        VerticalOptions="End" />
</Grid>