using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class ExamSetingMasterRecord
    {
        private const int EXAMSETTING_ID_POS = 0;
        private const int MECHECK_NO_POS = 1;
        private const int EXAMTIME_POS = 2;
        private const int WAITTIME_POS = 3;
        private const int WAITPEOPLE_POS = 4;
        private const int LASTUPDATE_POS = 5;
        private string FldExamSettingId;
        private string FldMeCheckNo;
        private string FldExamTime;
        private string FldWaitTime;
        private string FldWaitPeople;
        private string FldLastUpdate;

        public ExamSetingMasterRecord(string[] dbData)
        {
            this.ExamSettingId = dbData[EXAMSETTING_ID_POS];
            this.MedicalCheckNo = dbData[MECHECK_NO_POS];
            this.ExamTime = dbData[EXAMTIME_POS];
            this.WaitTime = dbData[WAITTIME_POS];
            this.WaitPeople = dbData[WAITPEOPLE_POS];
            this.LastUpdate = dbData[LASTUPDATE_POS];
        }

        public string ExamSettingId
        {
            get
            {
                return this.FldExamSettingId;
            }
            set
            {
                this.FldExamSettingId = value;
            }
        }

        public string MedicalCheckNo
        {
            get
            {
                return this.FldMeCheckNo;
            }
            set
            {
                this.FldMeCheckNo = value;
            }
        }

        public string ExamTime
        {
            get
            {
                return this.FldExamTime;
            }
            set
            {
                this.FldExamTime = value;
            }
        }

        public string WaitTime
        {
            get
            {
                return this.FldWaitTime;
            }
            set
            {
                this.FldWaitTime = value;
            }
        }

        public string WaitPeople
        {
            get
            {
                return this.FldWaitPeople;
            }
            set
            {
                this.FldWaitPeople = value;
            }
        }

        public string LastUpdate
        {
            get
            {
                return this.FldLastUpdate;
            }
            set
            {
                this.FldLastUpdate = value;
            }
        }
    }
}
