﻿namespace ShinKenShinKun;

public partial class IndividualProgressViewModel(
    IAppNavigator appNavigator,
    IAppSettingServices appSettingServices,
    IIndividualProgressServices individualProgressService,
    ISessionServices sessionServices) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObervableProperty
    [ObservableProperty] private ObservableCollection<ClientInformationModel> clientInformations;
    [ObservableProperty] private ClientInformationModel clientSelected;
    [ObservableProperty] private ObservableCollection<MedicalCheckModel> medicalCheckClients;
    [ObservableProperty] private IndividualPatientInfoDto individualPatientInfo;
    [ObservableProperty] private ObservableCollection<string> stateCounter;
    [ObservableProperty] private MedicalCheckModel medicalCheckSelected;
    [ObservableProperty] private string medicalMachineName;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string userName;

    #endregion

    #region Override
    protected override void OnInit(IDictionary<string, object> query)
    {
        IsLogin = appSettingServices.AppSettings.IsLogin;
        UserName = sessionServices.UserName;
        IsMaskMode = sessionServices.IsMaskMode;
        ClientSelected = ClientInformations?.FirstOrDefault();
        MedicalCheckClients = individualProgressService.GetMedicalCheckClients();
        MedicalCheckSelected = MedicalCheckClients.First();
        IndividualPatientInfo = individualProgressService.PatientInfoGenerate();
        ClientInformations = individualProgressService.LoadDataClientInformations(MedicalCheckSelected);
        StatusCount(MedicalCheckSelected);
    }


    partial void OnMedicalCheckSelectedChanged(MedicalCheckModel? oldValue, MedicalCheckModel newValue)
    {
        if(oldValue == null || MedicalCheckSelected == null)
        {
            return;
        }
        ClientInformations = individualProgressService.LoadDataClientInformations(MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
        StatusCount(MedicalCheckSelected);
    }

    #endregion

    #region Private

    private List<string> OrderedStates = new List<string>
            {
                AppResources.Done,
                AppResources.NotYet,
                AppResources.StopButton,
                AppResources.PendingButton
            };
    private void StatusCount(MedicalCheckModel specificChecklist)
    {
        var counts = new int[OrderedStates.Count];

        foreach(var patient in ClientInformations)
        {
            for(int i = 0; i < patient.MedicalChecklists.Count; i++)
            {
                if(patient.MedicalChecklists[i].Contains(specificChecklist.DisplayName))
                {
                    var state = patient.MedicalCheckProgresses[i];
                    patient.IndividualState = state;

                    var index = OrderedStates.FindIndex(s => s.Contains(state) || state.Contains(s));

                    if(index != -1)
                    {
                        counts[index]++;
                    }
                }
            }
        }

        StateCounter = counts.Select(c => c.ToString()).ToObservableCollection();
    }


    #endregion

    #region Relay Commands

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    #endregion
}
