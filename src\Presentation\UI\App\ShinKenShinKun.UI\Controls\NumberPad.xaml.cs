﻿namespace ShinKenShinKun.UI;
public partial class NumberPad : BasePopup
{
    private readonly NumberPadViewModel viewModel;

    public NumberPad(NumberPadViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
        this.viewModel = viewModel;
    }

    public event Action<string>? Comfirm;

    public ICommand CancelCommand => new RelayCommand(Cancel);
    public ICommand BackSpaceCommand => new RelayCommand(Backspace);
    public bool AllowZeroAtHead { get; set; }
    public ICommand DeleteCommand => new RelayCommand(Delete);
    public ICommand DeleteAndEnterCommand => new RelayCommand(DeleteAndEnter);
    public ICommand EnterCommand => new RelayCommand(Enter);

    private void Cancel()
    {
        Close();
    }

    private void Enter()
    {
        if(viewModel.Text is AppConstants.Add or AppConstants.Subtract)
            return;
        viewModel.Comfirm?.Execute(ConvertToFormat(viewModel.Text, viewModel.InputFormat));
        Close();
    }

    public void Backspace()
    {
        if(viewModel.Text.Length > 0) {
            viewModel.Text = viewModel.Text[..^1];
        }
    }

    public void Delete()
    {
        viewModel.Text = string.Empty;
    }

    public void DeleteAndEnter()
    {
        viewModel.Text = string.Empty;
        viewModel.Comfirm?.Execute(viewModel.Text);
        Close();
    }

    protected override void OnAppearing(object? sender, PopupOpenedEventArgs e)
    {
        base.OnAppearing(sender, e);
        viewModel.StartListening();
        GenerateNumberPad();
        if(viewModel.IsStandard) {
            viewModel.KeyboardHandlerServices.KeyDown += KeyboardHandlerServices_KeyDown;
        }
    }

    private void KeyboardHandlerServices_KeyDown(string key)
    {
        switch(key) {
            case AppConstants.Enter:
                Enter();
                break;

            case AppConstants.Backspace:
                Backspace();
                break;

            case AppConstants.Delete:
                Delete();
                break;

            default:
                var standardKey =
                    viewModel.SettingNumberPad.StandarMasterKeySettings.FirstOrDefault(x =>
                        x.StandardMasterKey.Value == key
                    );
                if(standardKey == default) {
                    return;
                }
                HandleStandardKeyPress(standardKey.StandardMasterKey);
                break;
        }
    }

    protected override void OnDisappearing(object? sender, PopupClosedEventArgs e)
    {
        base.OnDisappearing(sender, e);
        viewModel.StopListening();
    }

    private void GenerateNumberPad()
    {
        var numberKeys = viewModel.IsStandard
            ? GenerateStandard(viewModel.SettingNumberPad)
            : GenerateCustom(viewModel.SettingNumberPad);
        Grid.SetRow(numberKeys, 1);
        MainThread.BeginInvokeOnMainThread(() =>
        {
            numberPad.Children.Add(numberKeys);
        });
    }

    private Grid GenerateStandard(SettingNumberPadDto settingNumberPad)
    {
        AllowZeroAtHead = settingNumberPad.AllowZeroAtHead;
        var grid = new Grid
        {
            ColumnSpacing = 5,
            RowSpacing = 5,
            Margin = new Thickness(0),
        };
        for(int i = 0; i < settingNumberPad.Row; i++) {
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Star });
        }

        for(int i = 0; i < settingNumberPad.Column; i++) {
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
        }

        settingNumberPad.StandarMasterKeySettings.ForEach(x =>
        {
            var button = new RadBorder
            {
                BackgroundColor = Color.FromArgb(x.BackgroundColor),
                BorderColor = AppColors.Gray82,
                BorderThickness = 1,
                CornerRadius = Dimens.Thickness4,
                HorizontalOptions = LayoutOptions.Fill,
                IsEnabled = x.IsActive,
                VerticalOptions = LayoutOptions.Fill,
            };
            var label = new Label
            {
                FontSize = Dimens.FontSizeT3,
                HorizontalOptions = LayoutOptions.Center,
                Text = x.StandardMasterKey.Text,
                TextColor = Color.FromArgb(x.TextColor),
                VerticalOptions = LayoutOptions.Center,
            };
            button.GestureRecognizers.Add(
                new TapGestureRecognizer { Command = GenerateStandardCommand(x.StandardMasterKey) }
            );
            button.Content = label;
            Grid.SetRow(button, x.RowIndex);
            Grid.SetColumn(button, x.ColIndex);
            Grid.SetRowSpan(button, x.RowSpan);
            Grid.SetColumnSpan(button, x.ColSpan);
            grid.Children.Add(button);
        });
        return grid;
    }

    private ICommand GenerateStandardCommand(StandardMasterKeyDto standardMasterKey) =>
        GenerateKeyCommand(
            standardMasterKey.Action,
            () =>
                new RelayCommand<StandardMasterKeyDto>(_ =>
                    HandleStandardKeyPress(standardMasterKey)
                )
        );

    private void HandleStandardKeyPress(StandardMasterKeyDto key)
    {
        HandleText(key.Value);
        if(key.Action is KeyAction.InputAndEnter) {
            viewModel.Comfirm?.Execute(ConvertToFormat(viewModel.Text, viewModel.InputFormat));
            Close();
        }
    }

    #region HandleText

    private void HandleText(string key)
    {
        string plainText = string.IsNullOrEmpty(viewModel.Text)
            ? string.Empty
            : viewModel.Text.TrimStart(AppConstants.CharSubtract, AppConstants.CharAdd);
        HandleInput(key, plainText);
    }

    /// <summary>
    /// Handles the logic for inputting keys into the number pad, enforcing input format constraints such as
    /// maximum integer and decimal lengths, zero at head rules, and special key handling (add, subtract, decimal).
    /// Updates the <see cref="NumberPadViewModel.Text"/> property based on the key pressed and current input state.
    /// </summary>
    /// <param name="key">The key value being input (e.g., digit, decimal, add, subtract).</param>
    /// <param name="plainText">The current text value without leading add/subtract characters.</param>
    private void HandleInput(string key, string plainText)
    {
        // Set default max lengths to int.MaxValue (no limit)
        var maxDecimalLength = int.MaxValue;
        var maxIntergerLength = int.MaxValue;

        // If an input format is specified, parse it to determine max integer and decimal lengths
        if(!string.IsNullOrEmpty(viewModel.InputFormat)) {
            var formatParts = viewModel.InputFormat.Split(AppConstants.CharDecimal);
            maxIntergerLength =
                formatParts[0].Length == default ? int.MaxValue : formatParts[0].Length;
            maxDecimalLength = formatParts.Length > 1 ? formatParts[1].Length : 0;
        }

        // Split the current input into integer and decimal parts
        var currentParts = plainText.Split(AppConstants.CharDecimal);
        bool hasDot = plainText.Contains(AppConstants.CharDecimal);
        bool integerFull = currentParts[0].Length >= maxIntergerLength;
        bool decimalFull = currentParts.Length > 1 && currentParts[1].Length >= maxDecimalLength;

        // Prevent further input if integer or decimal part is full
        if(integerFull) {
            if(hasDot && decimalFull)
                return;
            if(maxDecimalLength == 0)
                return;
            if(!hasDot && key != AppConstants.Decimal)
                return;
        }

        // Prevent input if decimal part is already full
        if(currentParts.Length > 1 && currentParts[1].Length >= maxDecimalLength)
            return;

        // Handle add/subtract keys: only allow at the start of input
        if(key == AppConstants.Subtract || key == AppConstants.Add) {
            viewModel.Text += string.IsNullOrEmpty(viewModel.Text) ? key : string.Empty;
            return;
        }

        // Prevent multiple decimals in the input
        if(key.Contains(AppConstants.CharDecimal)) {
            if(plainText.Contains(AppConstants.CharDecimal))
                return;
        }

        // Handle zero input: prevent leading zeros if not allowed
        if(ContainsOnlyZeros(key)) {
            if(!AllowZeroAtHead && string.IsNullOrEmpty(plainText))
                return;
            viewModel.Text += key;
            return;
        }

        // Handle decimal input (e.g., "0.5"): enforce max decimal length and leading zero rules
        if(IsDecimalInput(key)) {
            if(!AllowZeroAtHead && string.IsNullOrEmpty(plainText))
                return;

            var keyParts = key.Split(AppConstants.CharDecimal);
            if(keyParts[0].Length >= maxIntergerLength)
                return;
            if(keyParts.Length > 1 && keyParts[1].Length >= maxDecimalLength)
                return;

            var dotIndex = key.IndexOf(AppConstants.CharDecimal);
            if(dotIndex >= 0 && key[dotIndex..].Length > maxDecimalLength)
                return;

            var formattedKey = key.StartsWith(AppConstants.Decimal.ToString()) ? "0" + key : key;

            viewModel.Text += string.IsNullOrEmpty(plainText)
                ? formattedKey
                : key[dotIndex..];

            return;
        }

        // Handle decimal point key: add "0." if input is empty, otherwise add decimal
        if(key == AppConstants.Decimal && !plainText.Contains(AppConstants.CharDecimal)) {
            if(!AllowZeroAtHead && string.IsNullOrEmpty(plainText))
                return;
            viewModel.Text += string.IsNullOrEmpty(plainText) ? "0." : AppConstants.Decimal;
            return;
        }

        // Default: append the key to the input
        viewModel.Text += key;
    }

    private static bool ContainsOnlyZeros(string input)
    {
        return Regex.IsMatch(input, @"^0+$");
    }

    private static bool IsDecimalInput(string key)
    {
        return double.TryParse(key, out _) && key.Contains(AppConstants.CharDecimal);
    }

    #endregion HandleText

    private Grid GenerateCustom(SettingNumberPadDto settingNumberPad)
    {
        AllowZeroAtHead = settingNumberPad.AllowZeroAtHead;
        var grid = new Grid
        {
            ColumnSpacing = 5,
            RowSpacing = 5,
            Margin = new Thickness(0),
        };
        for(var i = 0; i < settingNumberPad.Row; i++) {
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Star });
        }

        for(var i = 0; i < settingNumberPad.Column; i++) {
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
        }
        settingNumberPad.CustomMasterKeySettings.ForEach(x =>
        {
            var button = new RadBorder
            {
                BackgroundColor = Color.FromArgb(x.BackgroundColor),
                BorderColor = AppColors.Gray82,
                BorderThickness = 1,
                CornerRadius = Dimens.Thickness4,
                HorizontalOptions = LayoutOptions.Fill,
                IsEnabled = x.IsActive,
                VerticalOptions = LayoutOptions.Fill,
            };
            var label = new Label
            {
                FontSize = Dimens.FontSizeT3,
                HorizontalOptions = LayoutOptions.Center,
                Text = x.CustomMasterKey.Text,
                TextColor = Color.FromArgb(x.TextColor),
                VerticalOptions = LayoutOptions.Center,
            };
            button.GestureRecognizers.Add(
                new TapGestureRecognizer { Command = GenerateCustomCommand(x.CustomMasterKey) }
            );
            button.Content = label;
            Grid.SetRow(button, x.RowIndex);
            Grid.SetColumn(button, x.ColIndex);
            Grid.SetRowSpan(button, x.RowSpan);
            Grid.SetColumnSpan(button, x.ColSpan);
            grid.Children.Add(button);
        });
        return grid;
    }

    private ICommand GenerateCustomCommand(CustomMasterKeyDto customMasterKey) =>
        GenerateKeyCommand(
            customMasterKey.Action,
            () => new RelayCommand<CustomMasterKeyDto>(_ => HandleCustomKeyPress(customMasterKey))
        );

    private ICommand GenerateKeyCommand(KeyAction action, Func<ICommand> defaultHandler)
    {
        return action switch
        {
            KeyAction.BackSpace => BackSpaceCommand,
            KeyAction.Cancel => CancelCommand,
            KeyAction.Delete => DeleteCommand,
            KeyAction.DeleteAndEnter => DeleteAndEnterCommand,
            KeyAction.Enter => EnterCommand,
            _ => defaultHandler(),
        };
    }

    private void HandleCustomKeyPress(CustomMasterKeyDto key)
    {
        viewModel.Text = key.Value;
        viewModel.Comfirm?.Execute(viewModel.Text);
        if(key.Action is KeyAction.InputAndEnter) {
            Close();
        }
    }

    private static string ConvertToFormat(string input, string format)
    {
        string output = input;

        if(!string.IsNullOrEmpty(input) && !string.IsNullOrEmpty(format)) {
            _ = double.TryParse(input, out double convData);
            output = convData.ToString(format);
        }

        return output;
    }
}
