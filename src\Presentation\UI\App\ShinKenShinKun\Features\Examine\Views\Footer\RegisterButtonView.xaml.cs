namespace ShinKenShinKun;

public partial class RegisterButtonView : RadBorder
{
    public RegisterButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty RegisterCommandProperty = BindableProperty.Create(
        nameof(RegisterCommand),
        typeof(IRelayCommand),
        typeof(RegisterButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand RegisterCommand
    {
        get => (IRelayCommand)GetValue(RegisterCommandProperty);
        set => SetValue(RegisterCommandProperty, value);
    }

    public static readonly BindableProperty ClientSelectedProperty = BindableProperty.Create(
        nameof(ClientSelected),
        typeof(ClientInformationModel),
        typeof(RegisterButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public ClientInformationModel ClientSelected
    {
        get => (ClientInformationModel)GetValue(ClientSelectedProperty);
        set => SetValue(ClientSelectedProperty, value);
    }
}