﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace System.Arctec.Ar1000k.DataBase.ArctecServer
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://localhost/AR1000Server/", ConfigurationName="System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap")]
    public interface PublicServiceSoap
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNextGuideDataHoldTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetNextGuideDataHoldTime(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNextGuideDataHoldTime", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetNextGuideDataHoldTimeAsync(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateOnly", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetStateOnly(string sMedicalCheckDate, string sClientID, string sDivision, string medicalCheckNo, string state);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateOnly", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetStateOnlyAsync(string sMedicalCheckDate, string sClientID, string sDivision, string medicalCheckNo, string state);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateOnlyPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetStateOnlyPlural(string sMedicalCheckDate, string sClientID, string sDivision, string[] medicalCheckNo, string[] checkState);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateOnlyPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetStateOnlyPluralAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] medicalCheckNo, string[] checkState);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateTimeToNULL", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetStateTimeToNULL(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateTimeToNULL", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetStateTimeToNULLAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallClientDataAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallClientDataAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallClientDataAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallClientDataAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetCallClientDataAsync(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId, int iCallOrder, int iStatus);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetCallClientDataAsync(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId, int iCallOrder, int iStatus);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteCallClientData(string sMedicalCheckData, string sClientId, string sDivision, int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteCallClientDataAsync(string sMedicalCheckData, string sClientId, string sDivision, int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallExamMasterAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallExamMasterAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallExamMasterAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallExamMasterAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallExamMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetCallExamMaster(short iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallExamMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetCallExamMasterAsync(short iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallExamMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetCallExamMaster(short iExamId, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string Param1, string Param2, string Param3);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallExamMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetCallExamMasterAsync(short iExamId, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string Param1, string Param2, string Param3);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallExamMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteCallExamMaster(short iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallExamMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteCallExamMasterAsync(short iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallStatusAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallStatusAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallStatusAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallStatusAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallStatus", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetCallStatus(int iExamId, int iMaxWaitTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallStatus", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetCallStatusAsync(int iExamId, int iMaxWaitTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallStatus", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteCallStatus(int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallStatus", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteCallStatusAsync(int iExamId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallTerminalMasterAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallTerminalMasterAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallTerminalMasterAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallTerminalMasterAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallTerminalMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetCallTerminalMaster(string sTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCallTerminalMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetCallTerminalMasterAsync(string sTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallTerminalMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetCallTerminalMaster(string sTermID, string sIPAddress, int iIntervalTime, int[] iExamIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetCallTerminalMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetCallTerminalMasterAsync(string sTermID, string sIPAddress, int iIntervalTime, int[] iExamIds);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallTerminalMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteCallTerminalMaster(string sTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteCallTerminalMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteCallTerminalMasterAsync(string sTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/GetSoundSettingPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] GetSoundSettingPlural(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/GetSoundSettingPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> GetSoundSettingPluralAsync(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/GetSoundSettingOne", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] GetSoundSettingOne(string sIp, string patternId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/GetSoundSettingOne", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> GetSoundSettingOneAsync(string sIp, string patternId);
        
        // CODEGEN: Parameter 'repetitionCount' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'Microsoft.Xml.Serialization.XmlElementAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SetSoundSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse SetSoundSetting(System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SetSoundSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse> SetSoundSettingAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteSoundSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteSoundSetting(string sIp, string patternId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteSoundSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteSoundSettingAsync(string sIp, string patternId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool SFCreateServerFile(string sServerPath, string fileName, string msg, bool bOverWrite);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFile", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> SFCreateServerFileAsync(string sServerPath, string fileName, string msg, bool bOverWrite);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFileForEncode", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool SFCreateServerFileForEncode(string sServerPath, string fileName, string msg, bool bOverWrite, string encode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFileForEncode", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> SFCreateServerFileForEncodeAsync(string sServerPath, string fileName, string msg, bool bOverWrite, string encode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileExist", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool SFGetFileExist(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileExist", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> SFGetFileExistAsync(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetDirectoryExist", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool SFGetDirectoryExist(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetDirectoryExist", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> SFGetDirectoryExistAsync(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileContent", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string SFGetFileContent(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileContent", ReplyAction="*")]
        System.Threading.Tasks.Task<string> SFGetFileContentAsync(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileCreateDate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.DateTime SFGetFileCreateDate(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileCreateDate", ReplyAction="*")]
        System.Threading.Tasks.Task<System.DateTime> SFGetFileCreateDateAsync(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileUpdateDate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.DateTime SFGetFileUpdateDate(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileUpdateDate", ReplyAction="*")]
        System.Threading.Tasks.Task<System.DateTime> SFGetFileUpdateDateAsync(string sServerPath, string sFileName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] SFGetFileList(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetFileList", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> SFGetFileListAsync(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetDirectoryList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] SFGetDirectoryList(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetDirectoryList", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> SFGetDirectoryListAsync(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFDeleteFile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool SFDeleteFile(string sServerPath, string sFileName, bool bRestrictFlag);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFDeleteFile", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> SFDeleteFileAsync(string sServerPath, string sFileName, bool bRestrictFlag);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetConditionCsvFile", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] SFGetConditionCsvFile(string sServerPath, string sPatternName, int colIndex, string conditionStr);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFGetConditionCsvFile", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> SFGetConditionCsvFileAsync(string sServerPath, string sPatternName, int colIndex, string conditionStr);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFolder", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void SFCreateServerFolder(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/SFCreateServerFolder", ReplyAction="*")]
        System.Threading.Tasks.Task SFCreateServerFolderAsync(string sServerPath);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrLogin", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.LoginRecord StrLogin(string loginId, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrLogin", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.LoginRecord> StrLoginAsync(string loginId, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrRegister", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        byte StrRegister(string loginId, string userName, string email, byte roleId, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrRegister", ReplyAction="*")]
        System.Threading.Tasks.Task<byte> StrRegisterAsync(string loginId, string userName, string email, byte roleId, string password);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMessage", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetMessageResponseStrGetMessageResult StrGetMessage();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMessage", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetMessageResponseStrGetMessageResult> StrGetMessageAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateStateTimeMore", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateStateTimeMore", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoCustom", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralCheckNoCustom(string sMedicalCheckDate, string[] sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoCustom", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralCheckNoCustomAsync(string sMedicalCheckDate, string[] sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoTable", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetClientDataPluralCheckNoTableResponseStrGetClientDataPluralCheckNoTableResult StrGetClientDataPluralCheckNoTable(string sql);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoTable", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetClientDataPluralCheckNoTableResponseStrGetClientDataPluralCheckNoTableResult> StrGetClientDataPluralCheckNoTableAsync(string sql);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetConciergeMemberList(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberList", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetConciergeMemberListAsync(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetConciergeMemberListNo(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetConciergeMemberListNoAsync(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetConciergeMemberListAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListAll", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetConciergeMemberListAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeMemberListPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeMemberListPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeMemberListPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetConciergeTimeGroupList(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupList", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetConciergeTimeGroupListAsync(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetConciergeTimeGroupListNo(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetConciergeTimeGroupListNoAsync(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetConciergeTimeGroupListAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListAll", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetConciergeTimeGroupListAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeTimeGroupListPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeTimeGroupListPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeTimeGroupListPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClickSwitchSettingTableAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClickSwitchSettingTableAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClickSwitchSettingTableAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClickSwitchSettingTableAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClickSwitchSettingTable", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClickSwitchSettingTable(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClickSwitchSettingTable", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClickSwitchSettingTableAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataTransformedSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataTransformedSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataTransformedSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataTransformedSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataTransformedSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataTransformedSetting(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataTransformedSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataTransformedSettingAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDispChangeSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDispChangeSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDispChangeSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDispChangeSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDispChangeSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDispChangeSetting(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDispChangeSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDispChangeSettingAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemListAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemListAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemListAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemList(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemList", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemListNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemListNo(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemListNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListNoAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingNo(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingNoAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSetting(string medicalCheckNo, string itemNo, string dataType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingAsync(string medicalCheckNo, string itemNo, string dataType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingDataType", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingDataType(string medicalCheckNo, string dataType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingDataType", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingDataTypeAsync(string medicalCheckNo, string dataType);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingMenu", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingMenu(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalItemSettingMenu", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingMenuAsync(string medicalCheckNo);
        
        // CODEGEN: Parameter 'updateValue' requires additional schema information that cannot be captured using the parameter mode. The specific attribute is 'Microsoft.Xml.Serialization.XmlArrayItemAttribute'.
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetMedicalItemSettingMenu", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse StrSetMedicalItemSettingMenu(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetMedicalItemSettingMenu", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse> StrSetMedicalItemSettingMenuAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest request);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFormModeDispAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFormModeDispAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFormModeDispAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFormModeDispAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFormModeDisp", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFormModeDisp(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFormModeDisp", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFormModeDispAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSettingNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSettingNo(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSettingNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingNoAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSetting(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralDispSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralSetting(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPluralSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralSettingAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTransformedSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTransformedSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTransformedSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTransformedSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTransformedSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTransformedSetting(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTransformedSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTransformedSettingAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRemarksData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientRemarksData(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRemarksData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientRemarksDataAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientRemarksInformation", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetClientRemarksInformation(string sMedicalCheckDate, string sClientID, string sDivision, string[] commentData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientRemarksInformation", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetClientRemarksInformationAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] commentData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetRemarksItemListAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetRemarksItemListAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetRemarksItemListAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetRemarksItemListAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectId", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectId(string groupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectId", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectIdAsync(string groupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectMedicalCheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectMedicalCheckNo(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeGroupSelectMedicalCheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectMedicalCheckNoAsync(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlId", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlId(string controlId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlId", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlIdAsync(string controlId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlMedicalcheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlMedicalcheckNo(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeControlMedicalcheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlMedicalcheckNoAsync(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectId", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectId(string initialId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectId", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectIdAsync(string initialId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectMedicalCheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectMedicalCheckNo(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetEyeInitialSelectMedicalCheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectMedicalCheckNoAsync(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalNo(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalNoAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalItemNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalItemNo(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalItemNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalItemNoAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalMeCode", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalMeCode(string meCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeDataRelationalMeCode", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalMeCodeAsync(string meCode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingFormId", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingFormId(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingFormId", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingFormIdAsync(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingFormLabel", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingFormLabel(string formId, string label);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetFontSettingFormLabel", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingFormLabelAsync(string formId, string label);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonSettingAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonSettingAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonSettingAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonSettingAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonDetailAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonDetailAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonDetailAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonDetailAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonDetailbyFormId", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonDetailbyFormId(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCustomizeButtonDetailbyFormId", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonDetailbyFormIdAsync(string formId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetLiteTerminalData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetLiteTerminalData(string liteTerminalID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetLiteTerminalData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetLiteTerminalDataAsync(string liteTerminalID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetLiteTerminalDataAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetLiteTerminalDataAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetLiteTerminalDataAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetLiteTerminalDataAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetLiteTerminalData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetLiteTerminalData(string befClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetLiteTerminalData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetLiteTerminalDataAsync(string befClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientAddInfor", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientAddInfor", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientAddInforAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientAddInfor2", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientAddInfor2", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientAddInfor2Async(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientAddInfor", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientAddInfor", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetClientAddInforAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientAddInfor2", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientAddInfor2", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetClientAddInfor2Async(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckReasonData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckReasonData(string sMedialCheckDate, string sClientId, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckReasonData", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckReasonDataAsync(string sMedialCheckDate, string sClientId, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckItemState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckItemState(string sMedicalCheckDate, string sClientId, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckItemState", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckItemStateAsync(string sMedicalCheckDate, string sClientId, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateEndTimeAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateEndTimeAll(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalCheckData, string[] reasonData, string[] itemState, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateEndTimeAll", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateEndTimeAllAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalCheckData, string[] reasonData, string[] itemState, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItemAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItemAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItemAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItem", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItem(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItem", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItemItemNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItemItemNo(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupItemItemNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemItemNoAsync(string medicalCheckNo, string itemNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckupItem", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetNeedCheckupItem(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetNeedCheckupItemTbl tbl);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckupItem", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetNeedCheckupItemAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetNeedCheckupItemTbl tbl);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIniGuideMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetIniGuideMaster(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIniGuideMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetIniGuideMasterAsync(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIniGuideMasterAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetIniGuideMasterAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIniGuideMasterAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetIniGuideMasterAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTermGuideData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetTermGuideData(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTermGuideData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetTermGuideDataAsync(string ipAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTermGuideDataAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTermGuideDataAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTermGuideDataAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTermGuideDataAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetTermGuideData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetTermGuideData(string ipAddress, string GuideFlg, string TermFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetTermGuideData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetTermGuideDataAsync(string ipAddress, string GuideFlg, string TermFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientGuideDataWithChildGroup", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientGuideDataWithChildGroup(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientGuideDataWithChildGroup", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientGuideDataWithChildGroupAsync(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetChildGroupDetailMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetChildGroupDetailMaster(string childGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetChildGroupDetailMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetChildGroupDetailMasterAsync(string childGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetChildGroupTermMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetChildGroupTermMaster(string childGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetChildGroupTermMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetChildGroupTermMasterAsync(string childGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetExamSettingMaster", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetExamSettingMaster(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetExamSettingMaster", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetExamSettingMasterAsync(string medicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTargetStateNumber", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetTargetStateNumberResponseStrGetTargetStateNumberResult StrGetTargetStateNumber(string sql);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTargetStateNumber", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetTargetStateNumberResponseStrGetTargetStateNumberResult> StrGetTargetStateNumberAsync(string sql);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetAllStateFromExecDate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetAllStateFromExecDate(string execDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetAllStateFromExecDate", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetAllStateFromExecDateAsync(string execDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNextGuideData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetNextGuideData(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNextGuideData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetNextGuideDataAsync(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNextGuideData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteNextGuideData(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNextGuideData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteNextGuideDataAsync(System.DateTime dateTime, string clientId, string division);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNextGuideData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetNextGuideData(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNextGuideData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetNextGuideDataAsync(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/HelloWorld", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string HelloWorld();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/HelloWorld", ReplyAction="*")]
        System.Threading.Tasks.Task<string> HelloWorldAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetHostData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetHostData();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetHostData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetHostDataAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachines", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMachines(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachines", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMachinesAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachinesAllkeys", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMachinesAllkeys();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachinesAllkeys", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMachinesAllkeysAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachinesAllPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMachinesAllPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMachinesAllPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMachinesAllPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientData(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRegistrationNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetClientRegistrationNo(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRegistrationNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetClientRegistrationNoAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientIDDate", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeClientIDDate(string sMedicalCheckDate, string RegistrationNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientIDDate", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeClientIDDateAsync(string sMedicalCheckDate, string RegistrationNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeClientData(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRegistrationNoNoDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetClientRegistrationNoNoDiv(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientRegistrationNoNoDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetClientRegistrationNoNoDivAsync(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientDataNoDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeClientDataNoDiv(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeClientDataNoDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeClientDataNoDivAsync(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPlural(string sMedicalCheckDate, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralAsync(string sMedicalCheckDate, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralCheckNo(string sMedicalCheckDate, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralCheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralCheckNoAsync(string sMedicalCheckDate, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralAllState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralAllState(string sMedicalCheckDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataPluralAllState", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralAllStateAsync(string sMedicalCheckDate);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataNoDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientDataNoDiv(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataNoDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataNoDivAsync(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataRegist", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientDataRegist(string sMedicalCheckDate, string sRegistNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataRegist", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataRegistAsync(string sMedicalCheckDate, string sRegistNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataRegistDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientDataRegistDiv(string sMedicalCheckDate, string sRegistNo, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataRegistDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataRegistDivAsync(string sMedicalCheckDate, string sRegistNo, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataSeachDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientDataSeachDiv(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataSeachDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataSeachDivAsync(string sMedicalCheckDate, string sClientID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataSeachDivRegist", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetClientDataSeachDivRegist(string sMedicalCheckDate, string sRegistNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetClientDataSeachDivRegist", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetClientDataSeachDivRegistAsync(string sMedicalCheckDate, string sRegistNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataCheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetDataCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataCheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetDataCheckNoAsync(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void StrSetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetData", ReplyAction="*")]
        System.Threading.Tasks.Task StrSetDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetDataTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void StrSetDataTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetDataTime", ReplyAction="*")]
        System.Threading.Tasks.Task StrSetDataTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataPlural(string sMedicalCheckDate, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataPluralAsync(string sMedicalCheckDate, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataAll(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataAll", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataAllAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetState(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetState", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetStateAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetStateNext", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetStateNext(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetStateNext", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetStateNextAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void StrSetState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetState", ReplyAction="*")]
        System.Threading.Tasks.Task StrSetStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        void StrSetStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetStateTime", ReplyAction="*")]
        System.Threading.Tasks.Task StrSetStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckList", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetMedicalCheckList(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckList", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetMedicalCheckListAsync(short iMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string StrGetMedicalCheckListNo(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string> StrGetMedicalCheckListNoAsync(string sMedicalCheckName);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListAll", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMedicalCheckListAll();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListAll", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMedicalCheckListAllAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckListPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMedicalCheckListPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckListPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataState", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateDataStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataState", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataState", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTime", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTime", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTime", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeMore", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeMore", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeMore", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeMore", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime nowTime);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTrans", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateDataStateTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTrans", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTrans", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataStateTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTrans", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTransMore", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrStartUpdateDataStateTimeTransMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTransMore", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeTransMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTransMore", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataStateTimeTransMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTransMore", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeTransMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateNoTimeTrans", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrEndUpdateDataStateNoTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrEndUpdateDataStateNoTimeTrans", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrEndUpdateDataStateNoTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, bool hostTransFlg);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIP", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetPermissionIP(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIP", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetPermissionIPAsync(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIP_Plural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPermissionIP_Plural(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIP_Plural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPermissionIP_PluralAsync(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIPallkeys", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetPermissionIPallkeys();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIPallkeys", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetPermissionIPallkeysAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIPallPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPermissionIPallPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetPermissionIPallPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPermissionIPallPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetPermissionIP", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetPermissionIP(string sIp, string termID, string beforeTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetPermissionIP", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetPermissionIPAsync(string sIp, string termID, string beforeTermID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeletePermissionIP", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeletePermissionIP(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeletePermissionIP", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeletePermissionIPAsync(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTerminalSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetTerminalSetting(string sIp, short iSettingId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTerminalSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetTerminalSettingAsync(string sIp, short iSettingId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTerminalSettingPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTerminalSettingPlural(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetTerminalSettingPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTerminalSettingPluralAsync(string sIp);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetTerminalSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetTerminalSetting(string sIp, short iSettingId, string iContent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetTerminalSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetTerminalSettingAsync(string sIp, short iSettingId, string iContent);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteTerminalSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteTerminalSetting(string sIp, short iSettingId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteTerminalSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteTerminalSettingAsync(string sIp, short iSettingId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StartUpdateTermStatus", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StartUpdateTermStatus(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StartUpdateTermStatus", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StartUpdateTermStatusAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/EndUpdateTermStatus", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool EndUpdateTermStatus(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/EndUpdateTermStatus", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> EndUpdateTermStatusAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeOldData(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldData", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeOldDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldDataNoDiv", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeOldDataNoDiv(string sMedicalCheckDate, string sClientID, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldDataNoDiv", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeOldDataNoDivAsync(string sMedicalCheckDate, string sClientID, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldCheckNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetBeforeOldCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetBeforeOldCheckNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetBeforeOldCheckNoAsync(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DatabaseLimitControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DatabaseLimitControl();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DatabaseLimitControl", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DatabaseLimitControlAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataName", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetDataName(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetDataName", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetDataNameAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpName", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMeCheckUpName(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpName", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpNameNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMeCheckUpNameNo(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpNameNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameNoAsync(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpNameKindNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMeCheckUpNameKindNo(string sKindNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeCheckUpNameKindNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameKindNoAsync(string sKindNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeConnectSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetMeConnectSetting(string sDll, short iMeId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetMeConnectSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetMeConnectSettingAsync(string sDll, short iMeId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetMeConnectSetting", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetMeConnectSetting(string sDll, short iMeId, string[] setting, string[] medicalData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetMeConnectSetting", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetMeConnectSettingAsync(string sDll, short iMeId, string[] setting, string[] medicalData);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckup", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetNeedCheckup(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckup", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetNeedCheckupAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckup", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetNeedCheckup(string termID, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckup", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetNeedCheckupAsync(string termID, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNeedCheckup", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteNeedCheckup(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNeedCheckup", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteNeedCheckupAsync(string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupMedicalNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetNeedCheckupMedicalNo(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetNeedCheckupMedicalNo", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetNeedCheckupMedicalNoAsync(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckupMedicalNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetNeedCheckupMedicalNo(string sMedicalCheckNo, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetNeedCheckupMedicalNo", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetNeedCheckupMedicalNoAsync(string sMedicalCheckNo, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNeedCheckupMedicalNo", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteNeedCheckupMedicalNo(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteNeedCheckupMedicalNo", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteNeedCheckupMedicalNoAsync(string sMedicalCheckNo);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIndividualControlPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetIndividualControlPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIndividualControlPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetIndividualControlPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIndividualControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetIndividualControl(string iPAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetIndividualControl", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetIndividualControlAsync(string iPAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetIndividualControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetIndividualControl(string iPAddress, string[] dispCheckNos, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetIndividualControl", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetIndividualControlAsync(string iPAddress, string[] dispCheckNos, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteIndividualControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteIndividualControl(string iPAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteIndividualControl", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteIndividualControlAsync(string iPAddress);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCheckItem", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetCheckItem(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCheckItem", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetCheckItemAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCheckItemPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCheckItemPlural(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetCheckItemPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCheckItemPluralAsync(string sMedicalCheckDate, string sClientID, string sDivision);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        string[] StrGetConciergeControl(string sGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeControl", ReplyAction="*")]
        System.Threading.Tasks.Task<string[]> StrGetConciergeControlAsync(string sGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeControlPlural", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeControlPlural();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrGetConciergeControlPlural", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeControlPluralAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetConciergeControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetConciergeControl(string sGroupId, string sGroupName, string sShortGroupName, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetConciergeControl", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetConciergeControlAsync(string sGroupId, string sGroupName, string sShortGroupName, string[] checkObjectNos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteConciergeControl", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool DeleteConciergeControl(string sGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/DeleteConciergeControl", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> DeleteConciergeControlAsync(string sGroupId);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientData", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        bool StrSetClientData(string sMedicalCheckDate, string sClientID, string sDivision, string[] regData, int regPos);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://localhost/AR1000Server/StrSetClientData", ReplyAction="*")]
        System.Threading.Tasks.Task<bool> StrSetClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] regData, int regPos);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://localhost/AR1000Server/")]
    public partial class PluralRecord
    {
        
        private string[] dataField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlArrayAttribute(Order=0)]
        public string[] Data
        {
            get
            {
                return this.dataField;
            }
            set
            {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://localhost/AR1000Server/")]
    public partial class LoginRecord
    {
        
        private byte statusField;
        
        private string userNameField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=0)]
        public byte Status
        {
            get
            {
                return this.statusField;
            }
            set
            {
                this.statusField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Order=1)]
        public string UserName
        {
            get
            {
                return this.userNameField;
            }
            set
            {
                this.userNameField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetSoundSetting", WrapperNamespace="http://localhost/AR1000Server/", IsWrapped=true)]
    public partial class SetSoundSettingRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=0)]
        public string sIp;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=1)]
        public string patternId;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=2)]
        public string soundFileName;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=3)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<short> repetitionCount;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=4)]
        public string snoozeTime;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=5)]
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<short> volume;
        
        public SetSoundSettingRequest()
        {
        }
        
        public SetSoundSettingRequest(string sIp, string patternId, string soundFileName, System.Nullable<short> repetitionCount, string snoozeTime, System.Nullable<short> volume)
        {
            this.sIp = sIp;
            this.patternId = patternId;
            this.soundFileName = soundFileName;
            this.repetitionCount = repetitionCount;
            this.snoozeTime = snoozeTime;
            this.volume = volume;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="SetSoundSettingResponse", WrapperNamespace="http://localhost/AR1000Server/", IsWrapped=true)]
    public partial class SetSoundSettingResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=0)]
        public bool SetSoundSettingResult;
        
        public SetSoundSettingResponse()
        {
        }
        
        public SetSoundSettingResponse(bool SetSoundSettingResult)
        {
            this.SetSoundSettingResult = SetSoundSettingResult;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://localhost/AR1000Server/")]
    public partial class StrGetMessageResponseStrGetMessageResult
    {
        
        private System.Xml.XmlElement[] anyField;
        
        private System.Xml.XmlElement any1Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        public System.Xml.XmlElement[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=1)]
        public System.Xml.XmlElement Any1
        {
            get
            {
                return this.any1Field;
            }
            set
            {
                this.any1Field = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://localhost/AR1000Server/")]
    public partial class StrGetClientDataPluralCheckNoTableResponseStrGetClientDataPluralCheckNoTableResult
    {
        
        private System.Xml.XmlElement[] anyField;
        
        private System.Xml.XmlElement any1Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        public System.Xml.XmlElement[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=1)]
        public System.Xml.XmlElement Any1
        {
            get
            {
                return this.any1Field;
            }
            set
            {
                this.any1Field = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="StrSetMedicalItemSettingMenu", WrapperNamespace="http://localhost/AR1000Server/", IsWrapped=true)]
    public partial class StrSetMedicalItemSettingMenuRequest
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=0)]
        public string medicalCheckNo;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=1)]
        [System.Xml.Serialization.XmlArrayItemAttribute("ArrayOfString")]
        [System.Xml.Serialization.XmlArrayItemAttribute(NestingLevel=1)]
        public string[][] updateValue;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=2)]
        public int updateType;
        
        public StrSetMedicalItemSettingMenuRequest()
        {
        }
        
        public StrSetMedicalItemSettingMenuRequest(string medicalCheckNo, string[][] updateValue, int updateType)
        {
            this.medicalCheckNo = medicalCheckNo;
            this.updateValue = updateValue;
            this.updateType = updateType;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="StrSetMedicalItemSettingMenuResponse", WrapperNamespace="http://localhost/AR1000Server/", IsWrapped=true)]
    public partial class StrSetMedicalItemSettingMenuResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://localhost/AR1000Server/", Order=0)]
        public bool StrSetMedicalItemSettingMenuResult;
        
        public StrSetMedicalItemSettingMenuResponse()
        {
        }
        
        public StrSetMedicalItemSettingMenuResponse(bool StrSetMedicalItemSettingMenuResult)
        {
            this.StrSetMedicalItemSettingMenuResult = StrSetMedicalItemSettingMenuResult;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://localhost/AR1000Server/")]
    public partial class StrSetNeedCheckupItemTbl
    {
        
        private System.Xml.XmlElement[] anyField;
        
        private System.Xml.XmlElement any1Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        public System.Xml.XmlElement[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=1)]
        public System.Xml.XmlElement Any1
        {
            get
            {
                return this.any1Field;
            }
            set
            {
                this.any1Field = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType=true, Namespace="http://localhost/AR1000Server/")]
    public partial class StrGetTargetStateNumberResponseStrGetTargetStateNumberResult
    {
        
        private System.Xml.XmlElement[] anyField;
        
        private System.Xml.XmlElement any1Field;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="http://www.w3.org/2001/XMLSchema", Order=0)]
        public System.Xml.XmlElement[] Any
        {
            get
            {
                return this.anyField;
            }
            set
            {
                this.anyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlAnyElementAttribute(Namespace="urn:schemas-microsoft-com:xml-diffgram-v1", Order=1)]
        public System.Xml.XmlElement Any1
        {
            get
            {
                return this.any1Field;
            }
            set
            {
                this.any1Field = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public interface PublicServiceSoapChannel : System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public partial class PublicServiceSoapClient : System.ServiceModel.ClientBase<System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap>, System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap
    {
        
        /// <summary>
        /// Implement this partial method to configure the service endpoint.
        /// </summary>
        /// <param name="serviceEndpoint">The endpoint to configure</param>
        /// <param name="clientCredentials">The client credentials</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public PublicServiceSoapClient(EndpointConfiguration endpointConfiguration) : 
                base(PublicServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), PublicServiceSoapClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PublicServiceSoapClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(PublicServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PublicServiceSoapClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(PublicServiceSoapClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public PublicServiceSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public bool StrSetNextGuideDataHoldTime(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            return base.Channel.StrSetNextGuideDataHoldTime(dateTime, clientId, division, childGroupFlg, value);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetNextGuideDataHoldTimeAsync(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            return base.Channel.StrSetNextGuideDataHoldTimeAsync(dateTime, clientId, division, childGroupFlg, value);
        }
        
        public bool StrSetStateOnly(string sMedicalCheckDate, string sClientID, string sDivision, string medicalCheckNo, string state)
        {
            return base.Channel.StrSetStateOnly(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, state);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetStateOnlyAsync(string sMedicalCheckDate, string sClientID, string sDivision, string medicalCheckNo, string state)
        {
            return base.Channel.StrSetStateOnlyAsync(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, state);
        }
        
        public bool StrSetStateOnlyPlural(string sMedicalCheckDate, string sClientID, string sDivision, string[] medicalCheckNo, string[] checkState)
        {
            return base.Channel.StrSetStateOnlyPlural(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, checkState);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetStateOnlyPluralAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] medicalCheckNo, string[] checkState)
        {
            return base.Channel.StrSetStateOnlyPluralAsync(sMedicalCheckDate, sClientID, sDivision, medicalCheckNo, checkState);
        }
        
        public bool StrSetStateTimeToNULL(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrSetStateTimeToNULL(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetStateTimeToNULLAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrSetStateTimeToNULLAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallClientDataAll()
        {
            return base.Channel.StrGetCallClientDataAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallClientDataAllAsync()
        {
            return base.Channel.StrGetCallClientDataAllAsync();
        }
        
        public string[] StrGetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId)
        {
            return base.Channel.StrGetCallClientData(sMedicalCheckDate, sClientId, sDivision, iExamId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetCallClientDataAsync(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId)
        {
            return base.Channel.StrGetCallClientDataAsync(sMedicalCheckDate, sClientId, sDivision, iExamId);
        }
        
        public bool StrSetCallClientData(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId, int iCallOrder, int iStatus)
        {
            return base.Channel.StrSetCallClientData(sMedicalCheckDate, sClientId, sDivision, iExamId, iCallOrder, iStatus);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetCallClientDataAsync(string sMedicalCheckDate, string sClientId, string sDivision, int iExamId, int iCallOrder, int iStatus)
        {
            return base.Channel.StrSetCallClientDataAsync(sMedicalCheckDate, sClientId, sDivision, iExamId, iCallOrder, iStatus);
        }
        
        public bool DeleteCallClientData(string sMedicalCheckData, string sClientId, string sDivision, int iExamId)
        {
            return base.Channel.DeleteCallClientData(sMedicalCheckData, sClientId, sDivision, iExamId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteCallClientDataAsync(string sMedicalCheckData, string sClientId, string sDivision, int iExamId)
        {
            return base.Channel.DeleteCallClientDataAsync(sMedicalCheckData, sClientId, sDivision, iExamId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallExamMasterAll()
        {
            return base.Channel.StrGetCallExamMasterAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallExamMasterAllAsync()
        {
            return base.Channel.StrGetCallExamMasterAllAsync();
        }
        
        public string[] StrGetCallExamMaster(short iExamId)
        {
            return base.Channel.StrGetCallExamMaster(iExamId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetCallExamMasterAsync(short iExamId)
        {
            return base.Channel.StrGetCallExamMasterAsync(iExamId);
        }
        
        public bool StrSetCallExamMaster(short iExamId, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string Param1, string Param2, string Param3)
        {
            return base.Channel.StrSetCallExamMaster(iExamId, sTitle, bStatus, sStartTime, sEndTime, iCapacity, iBackColor, sMessage, sHeader1, sHeader2, iMedicalCheckNos, iPanelType, Param1, Param2, Param3);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetCallExamMasterAsync(short iExamId, string sTitle, bool bStatus, string sStartTime, string sEndTime, int iCapacity, int iBackColor, string sMessage, string sHeader1, string sHeader2, int[] iMedicalCheckNos, int iPanelType, string Param1, string Param2, string Param3)
        {
            return base.Channel.StrSetCallExamMasterAsync(iExamId, sTitle, bStatus, sStartTime, sEndTime, iCapacity, iBackColor, sMessage, sHeader1, sHeader2, iMedicalCheckNos, iPanelType, Param1, Param2, Param3);
        }
        
        public bool DeleteCallExamMaster(short iExamId)
        {
            return base.Channel.DeleteCallExamMaster(iExamId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteCallExamMasterAsync(short iExamId)
        {
            return base.Channel.DeleteCallExamMasterAsync(iExamId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallStatusAll()
        {
            return base.Channel.StrGetCallStatusAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallStatusAllAsync()
        {
            return base.Channel.StrGetCallStatusAllAsync();
        }
        
        public bool StrSetCallStatus(int iExamId, int iMaxWaitTime)
        {
            return base.Channel.StrSetCallStatus(iExamId, iMaxWaitTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetCallStatusAsync(int iExamId, int iMaxWaitTime)
        {
            return base.Channel.StrSetCallStatusAsync(iExamId, iMaxWaitTime);
        }
        
        public bool DeleteCallStatus(int iExamId)
        {
            return base.Channel.DeleteCallStatus(iExamId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteCallStatusAsync(int iExamId)
        {
            return base.Channel.DeleteCallStatusAsync(iExamId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCallTerminalMasterAll()
        {
            return base.Channel.StrGetCallTerminalMasterAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCallTerminalMasterAllAsync()
        {
            return base.Channel.StrGetCallTerminalMasterAllAsync();
        }
        
        public string[] StrGetCallTerminalMaster(string sTermID)
        {
            return base.Channel.StrGetCallTerminalMaster(sTermID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetCallTerminalMasterAsync(string sTermID)
        {
            return base.Channel.StrGetCallTerminalMasterAsync(sTermID);
        }
        
        public bool StrSetCallTerminalMaster(string sTermID, string sIPAddress, int iIntervalTime, int[] iExamIds)
        {
            return base.Channel.StrSetCallTerminalMaster(sTermID, sIPAddress, iIntervalTime, iExamIds);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetCallTerminalMasterAsync(string sTermID, string sIPAddress, int iIntervalTime, int[] iExamIds)
        {
            return base.Channel.StrSetCallTerminalMasterAsync(sTermID, sIPAddress, iIntervalTime, iExamIds);
        }
        
        public bool DeleteCallTerminalMaster(string sTermID)
        {
            return base.Channel.DeleteCallTerminalMaster(sTermID);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteCallTerminalMasterAsync(string sTermID)
        {
            return base.Channel.DeleteCallTerminalMasterAsync(sTermID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] GetSoundSettingPlural(string sIp)
        {
            return base.Channel.GetSoundSettingPlural(sIp);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> GetSoundSettingPluralAsync(string sIp)
        {
            return base.Channel.GetSoundSettingPluralAsync(sIp);
        }
        
        public string[] GetSoundSettingOne(string sIp, string patternId)
        {
            return base.Channel.GetSoundSettingOne(sIp, patternId);
        }
        
        public System.Threading.Tasks.Task<string[]> GetSoundSettingOneAsync(string sIp, string patternId)
        {
            return base.Channel.GetSoundSettingOneAsync(sIp, patternId);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap.SetSoundSetting(System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest request)
        {
            return base.Channel.SetSoundSetting(request);
        }
        
        public bool SetSoundSetting(string sIp, string patternId, string soundFileName, System.Nullable<short> repetitionCount, string snoozeTime, System.Nullable<short> volume)
        {
            System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest inValue = new System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest();
            inValue.sIp = sIp;
            inValue.patternId = patternId;
            inValue.soundFileName = soundFileName;
            inValue.repetitionCount = repetitionCount;
            inValue.snoozeTime = snoozeTime;
            inValue.volume = volume;
            System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse retVal = ((System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap)(this)).SetSoundSetting(inValue);
            return retVal.SetSoundSettingResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse> System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap.SetSoundSettingAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest request)
        {
            return base.Channel.SetSoundSettingAsync(request);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingResponse> SetSoundSettingAsync(string sIp, string patternId, string soundFileName, System.Nullable<short> repetitionCount, string snoozeTime, System.Nullable<short> volume)
        {
            System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest inValue = new System.Arctec.Ar1000k.DataBase.ArctecServer.SetSoundSettingRequest();
            inValue.sIp = sIp;
            inValue.patternId = patternId;
            inValue.soundFileName = soundFileName;
            inValue.repetitionCount = repetitionCount;
            inValue.snoozeTime = snoozeTime;
            inValue.volume = volume;
            return ((System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap)(this)).SetSoundSettingAsync(inValue);
        }
        
        public bool DeleteSoundSetting(string sIp, string patternId)
        {
            return base.Channel.DeleteSoundSetting(sIp, patternId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteSoundSettingAsync(string sIp, string patternId)
        {
            return base.Channel.DeleteSoundSettingAsync(sIp, patternId);
        }
        
        public bool SFCreateServerFile(string sServerPath, string fileName, string msg, bool bOverWrite)
        {
            return base.Channel.SFCreateServerFile(sServerPath, fileName, msg, bOverWrite);
        }
        
        public System.Threading.Tasks.Task<bool> SFCreateServerFileAsync(string sServerPath, string fileName, string msg, bool bOverWrite)
        {
            return base.Channel.SFCreateServerFileAsync(sServerPath, fileName, msg, bOverWrite);
        }
        
        public bool SFCreateServerFileForEncode(string sServerPath, string fileName, string msg, bool bOverWrite, string encode)
        {
            return base.Channel.SFCreateServerFileForEncode(sServerPath, fileName, msg, bOverWrite, encode);
        }
        
        public System.Threading.Tasks.Task<bool> SFCreateServerFileForEncodeAsync(string sServerPath, string fileName, string msg, bool bOverWrite, string encode)
        {
            return base.Channel.SFCreateServerFileForEncodeAsync(sServerPath, fileName, msg, bOverWrite, encode);
        }
        
        public bool SFGetFileExist(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileExist(sServerPath, sFileName);
        }
        
        public System.Threading.Tasks.Task<bool> SFGetFileExistAsync(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileExistAsync(sServerPath, sFileName);
        }
        
        public bool SFGetDirectoryExist(string sServerPath)
        {
            return base.Channel.SFGetDirectoryExist(sServerPath);
        }
        
        public System.Threading.Tasks.Task<bool> SFGetDirectoryExistAsync(string sServerPath)
        {
            return base.Channel.SFGetDirectoryExistAsync(sServerPath);
        }
        
        public string SFGetFileContent(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileContent(sServerPath, sFileName);
        }
        
        public System.Threading.Tasks.Task<string> SFGetFileContentAsync(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileContentAsync(sServerPath, sFileName);
        }
        
        public System.DateTime SFGetFileCreateDate(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileCreateDate(sServerPath, sFileName);
        }
        
        public System.Threading.Tasks.Task<System.DateTime> SFGetFileCreateDateAsync(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileCreateDateAsync(sServerPath, sFileName);
        }
        
        public System.DateTime SFGetFileUpdateDate(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileUpdateDate(sServerPath, sFileName);
        }
        
        public System.Threading.Tasks.Task<System.DateTime> SFGetFileUpdateDateAsync(string sServerPath, string sFileName)
        {
            return base.Channel.SFGetFileUpdateDateAsync(sServerPath, sFileName);
        }
        
        public string[] SFGetFileList(string sServerPath)
        {
            return base.Channel.SFGetFileList(sServerPath);
        }
        
        public System.Threading.Tasks.Task<string[]> SFGetFileListAsync(string sServerPath)
        {
            return base.Channel.SFGetFileListAsync(sServerPath);
        }
        
        public string[] SFGetDirectoryList(string sServerPath)
        {
            return base.Channel.SFGetDirectoryList(sServerPath);
        }
        
        public System.Threading.Tasks.Task<string[]> SFGetDirectoryListAsync(string sServerPath)
        {
            return base.Channel.SFGetDirectoryListAsync(sServerPath);
        }
        
        public bool SFDeleteFile(string sServerPath, string sFileName, bool bRestrictFlag)
        {
            return base.Channel.SFDeleteFile(sServerPath, sFileName, bRestrictFlag);
        }
        
        public System.Threading.Tasks.Task<bool> SFDeleteFileAsync(string sServerPath, string sFileName, bool bRestrictFlag)
        {
            return base.Channel.SFDeleteFileAsync(sServerPath, sFileName, bRestrictFlag);
        }
        
        public string[] SFGetConditionCsvFile(string sServerPath, string sPatternName, int colIndex, string conditionStr)
        {
            return base.Channel.SFGetConditionCsvFile(sServerPath, sPatternName, colIndex, conditionStr);
        }
        
        public System.Threading.Tasks.Task<string[]> SFGetConditionCsvFileAsync(string sServerPath, string sPatternName, int colIndex, string conditionStr)
        {
            return base.Channel.SFGetConditionCsvFileAsync(sServerPath, sPatternName, colIndex, conditionStr);
        }
        
        public void SFCreateServerFolder(string sServerPath)
        {
            base.Channel.SFCreateServerFolder(sServerPath);
        }
        
        public System.Threading.Tasks.Task SFCreateServerFolderAsync(string sServerPath)
        {
            return base.Channel.SFCreateServerFolderAsync(sServerPath);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.LoginRecord StrLogin(string loginId, string password)
        {
            return base.Channel.StrLogin(loginId, password);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.LoginRecord> StrLoginAsync(string loginId, string password)
        {
            return base.Channel.StrLoginAsync(loginId, password);
        }
        
        public byte StrRegister(string loginId, string userName, string email, byte roleId, string password)
        {
            return base.Channel.StrRegister(loginId, userName, email, roleId, password);
        }
        
        public System.Threading.Tasks.Task<byte> StrRegisterAsync(string loginId, string userName, string email, byte roleId, string password)
        {
            return base.Channel.StrRegisterAsync(loginId, userName, email, roleId, password);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetMessageResponseStrGetMessageResult StrGetMessage()
        {
            return base.Channel.StrGetMessage();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetMessageResponseStrGetMessageResult> StrGetMessageAsync()
        {
            return base.Channel.StrGetMessageAsync();
        }
        
        public bool StrStartUpdateStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateStateTimeMore(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateStateTimeMoreAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralCheckNoCustom(string sMedicalCheckDate, string[] sMedicalCheckNo)
        {
            return base.Channel.StrGetClientDataPluralCheckNoCustom(sMedicalCheckDate, sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralCheckNoCustomAsync(string sMedicalCheckDate, string[] sMedicalCheckNo)
        {
            return base.Channel.StrGetClientDataPluralCheckNoCustomAsync(sMedicalCheckDate, sMedicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetClientDataPluralCheckNoTableResponseStrGetClientDataPluralCheckNoTableResult StrGetClientDataPluralCheckNoTable(string sql)
        {
            return base.Channel.StrGetClientDataPluralCheckNoTable(sql);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetClientDataPluralCheckNoTableResponseStrGetClientDataPluralCheckNoTableResult> StrGetClientDataPluralCheckNoTableAsync(string sql)
        {
            return base.Channel.StrGetClientDataPluralCheckNoTableAsync(sql);
        }
        
        public string StrGetConciergeMemberList(short iMedicalCheckNo)
        {
            return base.Channel.StrGetConciergeMemberList(iMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string> StrGetConciergeMemberListAsync(short iMedicalCheckNo)
        {
            return base.Channel.StrGetConciergeMemberListAsync(iMedicalCheckNo);
        }
        
        public string StrGetConciergeMemberListNo(string sMedicalCheckName)
        {
            return base.Channel.StrGetConciergeMemberListNo(sMedicalCheckName);
        }
        
        public System.Threading.Tasks.Task<string> StrGetConciergeMemberListNoAsync(string sMedicalCheckName)
        {
            return base.Channel.StrGetConciergeMemberListNoAsync(sMedicalCheckName);
        }
        
        public string[] StrGetConciergeMemberListAll()
        {
            return base.Channel.StrGetConciergeMemberListAll();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetConciergeMemberListAllAsync()
        {
            return base.Channel.StrGetConciergeMemberListAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeMemberListPlural()
        {
            return base.Channel.StrGetConciergeMemberListPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeMemberListPluralAsync()
        {
            return base.Channel.StrGetConciergeMemberListPluralAsync();
        }
        
        public string StrGetConciergeTimeGroupList(short iMedicalCheckNo)
        {
            return base.Channel.StrGetConciergeTimeGroupList(iMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string> StrGetConciergeTimeGroupListAsync(short iMedicalCheckNo)
        {
            return base.Channel.StrGetConciergeTimeGroupListAsync(iMedicalCheckNo);
        }
        
        public string StrGetConciergeTimeGroupListNo(string sMedicalCheckName)
        {
            return base.Channel.StrGetConciergeTimeGroupListNo(sMedicalCheckName);
        }
        
        public System.Threading.Tasks.Task<string> StrGetConciergeTimeGroupListNoAsync(string sMedicalCheckName)
        {
            return base.Channel.StrGetConciergeTimeGroupListNoAsync(sMedicalCheckName);
        }
        
        public string[] StrGetConciergeTimeGroupListAll()
        {
            return base.Channel.StrGetConciergeTimeGroupListAll();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetConciergeTimeGroupListAllAsync()
        {
            return base.Channel.StrGetConciergeTimeGroupListAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeTimeGroupListPlural()
        {
            return base.Channel.StrGetConciergeTimeGroupListPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeTimeGroupListPluralAsync()
        {
            return base.Channel.StrGetConciergeTimeGroupListPluralAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClickSwitchSettingTableAll()
        {
            return base.Channel.StrGetClickSwitchSettingTableAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClickSwitchSettingTableAllAsync()
        {
            return base.Channel.StrGetClickSwitchSettingTableAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClickSwitchSettingTable(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetClickSwitchSettingTable(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClickSwitchSettingTableAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetClickSwitchSettingTableAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataTransformedSettingAll()
        {
            return base.Channel.StrGetDataTransformedSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataTransformedSettingAllAsync()
        {
            return base.Channel.StrGetDataTransformedSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataTransformedSetting(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetDataTransformedSetting(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataTransformedSettingAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetDataTransformedSettingAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDispChangeSettingAll()
        {
            return base.Channel.StrGetDispChangeSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDispChangeSettingAllAsync()
        {
            return base.Channel.StrGetDispChangeSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDispChangeSetting(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetDispChangeSetting(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDispChangeSettingAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetDispChangeSettingAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemListAll()
        {
            return base.Channel.StrGetMedicalItemListAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListAllAsync()
        {
            return base.Channel.StrGetMedicalItemListAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemList(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetMedicalItemList(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetMedicalItemListAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemListNo(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemListNo(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemListNoAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemListNoAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingAll()
        {
            return base.Channel.StrGetMedicalItemSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingAllAsync()
        {
            return base.Channel.StrGetMedicalItemSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingNo(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemSettingNo(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingNoAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemSettingNoAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSetting(string medicalCheckNo, string itemNo, string dataType)
        {
            return base.Channel.StrGetMedicalItemSetting(medicalCheckNo, itemNo, dataType);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingAsync(string medicalCheckNo, string itemNo, string dataType)
        {
            return base.Channel.StrGetMedicalItemSettingAsync(medicalCheckNo, itemNo, dataType);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingDataType(string medicalCheckNo, string dataType)
        {
            return base.Channel.StrGetMedicalItemSettingDataType(medicalCheckNo, dataType);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingDataTypeAsync(string medicalCheckNo, string dataType)
        {
            return base.Channel.StrGetMedicalItemSettingDataTypeAsync(medicalCheckNo, dataType);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalItemSettingMenu(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemSettingMenu(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalItemSettingMenuAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetMedicalItemSettingMenuAsync(medicalCheckNo);
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap.StrSetMedicalItemSettingMenu(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest request)
        {
            return base.Channel.StrSetMedicalItemSettingMenu(request);
        }
        
        public bool StrSetMedicalItemSettingMenu(string medicalCheckNo, string[][] updateValue, int updateType)
        {
            System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest inValue = new System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest();
            inValue.medicalCheckNo = medicalCheckNo;
            inValue.updateValue = updateValue;
            inValue.updateType = updateType;
            System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse retVal = ((System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap)(this)).StrSetMedicalItemSettingMenu(inValue);
            return retVal.StrSetMedicalItemSettingMenuResult;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse> System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap.StrSetMedicalItemSettingMenuAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest request)
        {
            return base.Channel.StrSetMedicalItemSettingMenuAsync(request);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuResponse> StrSetMedicalItemSettingMenuAsync(string medicalCheckNo, string[][] updateValue, int updateType)
        {
            System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest inValue = new System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetMedicalItemSettingMenuRequest();
            inValue.medicalCheckNo = medicalCheckNo;
            inValue.updateValue = updateValue;
            inValue.updateType = updateType;
            return ((System.Arctec.Ar1000k.DataBase.ArctecServer.PublicServiceSoap)(this)).StrSetMedicalItemSettingMenuAsync(inValue);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFormModeDispAll()
        {
            return base.Channel.StrGetFormModeDispAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFormModeDispAllAsync()
        {
            return base.Channel.StrGetFormModeDispAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFormModeDisp(string medicalCheckNo)
        {
            return base.Channel.StrGetFormModeDisp(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFormModeDispAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetFormModeDispAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSettingAll()
        {
            return base.Channel.StrGetPluralDispSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingAllAsync()
        {
            return base.Channel.StrGetPluralDispSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSettingNo(string medicalCheckNo)
        {
            return base.Channel.StrGetPluralDispSettingNo(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingNoAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetPluralDispSettingNoAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralDispSetting(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetPluralDispSetting(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralDispSettingAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetPluralDispSettingAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralSettingAll()
        {
            return base.Channel.StrGetPluralSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralSettingAllAsync()
        {
            return base.Channel.StrGetPluralSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPluralSetting(string medicalCheckNo)
        {
            return base.Channel.StrGetPluralSetting(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPluralSettingAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetPluralSettingAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTransformedSettingAll()
        {
            return base.Channel.StrGetTransformedSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTransformedSettingAllAsync()
        {
            return base.Channel.StrGetTransformedSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTransformedSetting(string medicalCheckNo)
        {
            return base.Channel.StrGetTransformedSetting(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTransformedSettingAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetTransformedSettingAsync(medicalCheckNo);
        }
        
        public string[] StrGetClientRemarksData(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientRemarksData(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientRemarksDataAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientRemarksDataAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public bool StrSetClientRemarksInformation(string sMedicalCheckDate, string sClientID, string sDivision, string[] commentData)
        {
            return base.Channel.StrSetClientRemarksInformation(sMedicalCheckDate, sClientID, sDivision, commentData);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetClientRemarksInformationAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] commentData)
        {
            return base.Channel.StrSetClientRemarksInformationAsync(sMedicalCheckDate, sClientID, sDivision, commentData);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetRemarksItemListAll()
        {
            return base.Channel.StrGetRemarksItemListAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetRemarksItemListAllAsync()
        {
            return base.Channel.StrGetRemarksItemListAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectAll()
        {
            return base.Channel.StrGetEyeGroupSelectAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectAllAsync()
        {
            return base.Channel.StrGetEyeGroupSelectAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectId(string groupId)
        {
            return base.Channel.StrGetEyeGroupSelectId(groupId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectIdAsync(string groupId)
        {
            return base.Channel.StrGetEyeGroupSelectIdAsync(groupId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeGroupSelectMedicalCheckNo(string formId)
        {
            return base.Channel.StrGetEyeGroupSelectMedicalCheckNo(formId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeGroupSelectMedicalCheckNoAsync(string formId)
        {
            return base.Channel.StrGetEyeGroupSelectMedicalCheckNoAsync(formId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlAll()
        {
            return base.Channel.StrGetEyeControlAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlAllAsync()
        {
            return base.Channel.StrGetEyeControlAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlId(string controlId)
        {
            return base.Channel.StrGetEyeControlId(controlId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlIdAsync(string controlId)
        {
            return base.Channel.StrGetEyeControlIdAsync(controlId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeControlMedicalcheckNo(string formId)
        {
            return base.Channel.StrGetEyeControlMedicalcheckNo(formId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeControlMedicalcheckNoAsync(string formId)
        {
            return base.Channel.StrGetEyeControlMedicalcheckNoAsync(formId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectAll()
        {
            return base.Channel.StrGetEyeInitialSelectAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectAllAsync()
        {
            return base.Channel.StrGetEyeInitialSelectAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectId(string initialId)
        {
            return base.Channel.StrGetEyeInitialSelectId(initialId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectIdAsync(string initialId)
        {
            return base.Channel.StrGetEyeInitialSelectIdAsync(initialId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetEyeInitialSelectMedicalCheckNo(string formId)
        {
            return base.Channel.StrGetEyeInitialSelectMedicalCheckNo(formId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetEyeInitialSelectMedicalCheckNoAsync(string formId)
        {
            return base.Channel.StrGetEyeInitialSelectMedicalCheckNoAsync(formId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalAll()
        {
            return base.Channel.StrGetMeDataRelationalAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalAllAsync()
        {
            return base.Channel.StrGetMeDataRelationalAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalNo(string medicalCheckNo)
        {
            return base.Channel.StrGetMeDataRelationalNo(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalNoAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetMeDataRelationalNoAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalItemNo(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetMeDataRelationalItemNo(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalItemNoAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetMeDataRelationalItemNoAsync(medicalCheckNo, itemNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMeDataRelationalMeCode(string meCode)
        {
            return base.Channel.StrGetMeDataRelationalMeCode(meCode);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMeDataRelationalMeCodeAsync(string meCode)
        {
            return base.Channel.StrGetMeDataRelationalMeCodeAsync(meCode);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingAll()
        {
            return base.Channel.StrGetFontSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingAllAsync()
        {
            return base.Channel.StrGetFontSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingFormId(string formId)
        {
            return base.Channel.StrGetFontSettingFormId(formId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingFormIdAsync(string formId)
        {
            return base.Channel.StrGetFontSettingFormIdAsync(formId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetFontSettingFormLabel(string formId, string label)
        {
            return base.Channel.StrGetFontSettingFormLabel(formId, label);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetFontSettingFormLabelAsync(string formId, string label)
        {
            return base.Channel.StrGetFontSettingFormLabelAsync(formId, label);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonSettingAll()
        {
            return base.Channel.StrGetCustomizeButtonSettingAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonSettingAllAsync()
        {
            return base.Channel.StrGetCustomizeButtonSettingAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonDetailAll()
        {
            return base.Channel.StrGetCustomizeButtonDetailAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonDetailAllAsync()
        {
            return base.Channel.StrGetCustomizeButtonDetailAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCustomizeButtonDetailbyFormId(string formId)
        {
            return base.Channel.StrGetCustomizeButtonDetailbyFormId(formId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCustomizeButtonDetailbyFormIdAsync(string formId)
        {
            return base.Channel.StrGetCustomizeButtonDetailbyFormIdAsync(formId);
        }
        
        public string[] StrGetLiteTerminalData(string liteTerminalID)
        {
            return base.Channel.StrGetLiteTerminalData(liteTerminalID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetLiteTerminalDataAsync(string liteTerminalID)
        {
            return base.Channel.StrGetLiteTerminalDataAsync(liteTerminalID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetLiteTerminalDataAll()
        {
            return base.Channel.StrGetLiteTerminalDataAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetLiteTerminalDataAllAsync()
        {
            return base.Channel.StrGetLiteTerminalDataAllAsync();
        }
        
        public bool StrSetLiteTerminalData(string befClientID)
        {
            return base.Channel.StrSetLiteTerminalData(befClientID);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetLiteTerminalDataAsync(string befClientID)
        {
            return base.Channel.StrSetLiteTerminalDataAsync(befClientID);
        }
        
        public string[] StrGetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientAddInfor(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientAddInforAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientAddInforAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string[] StrGetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientAddInfor2(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientAddInfor2Async(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientAddInfor2Async(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public bool StrSetClientAddInfor(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            return base.Channel.StrSetClientAddInfor(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetClientAddInforAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            return base.Channel.StrSetClientAddInforAsync(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }
        
        public bool StrSetClientAddInfor2(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            return base.Channel.StrSetClientAddInfor2(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetClientAddInfor2Async(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            return base.Channel.StrSetClientAddInfor2Async(sMedicalCheckDate, sClientID, sDivision, addInfo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckReasonData(string sMedialCheckDate, string sClientId, string sDivision)
        {
            return base.Channel.StrGetMedicalCheckReasonData(sMedialCheckDate, sClientId, sDivision);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckReasonDataAsync(string sMedialCheckDate, string sClientId, string sDivision)
        {
            return base.Channel.StrGetMedicalCheckReasonDataAsync(sMedialCheckDate, sClientId, sDivision);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckItemState(string sMedicalCheckDate, string sClientId, string sDivision)
        {
            return base.Channel.StrGetMedicalCheckItemState(sMedicalCheckDate, sClientId, sDivision);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckItemStateAsync(string sMedicalCheckDate, string sClientId, string sDivision)
        {
            return base.Channel.StrGetMedicalCheckItemStateAsync(sMedicalCheckDate, sClientId, sDivision);
        }
        
        public bool StrEndUpdateEndTimeAll(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalCheckData, string[] reasonData, string[] itemState, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateEndTimeAll(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalCheckData, reasonData, itemState, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateEndTimeAllAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalCheckData, string[] reasonData, string[] itemState, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateEndTimeAllAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalCheckData, reasonData, itemState, nowTime);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItemAll()
        {
            return base.Channel.StrGetNeedCheckupItemAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemAllAsync()
        {
            return base.Channel.StrGetNeedCheckupItemAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItem(string medicalCheckNo)
        {
            return base.Channel.StrGetNeedCheckupItem(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetNeedCheckupItemAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupItemItemNo(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetNeedCheckupItemItemNo(medicalCheckNo, itemNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupItemItemNoAsync(string medicalCheckNo, string itemNo)
        {
            return base.Channel.StrGetNeedCheckupItemItemNoAsync(medicalCheckNo, itemNo);
        }
        
        public bool StrSetNeedCheckupItem(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetNeedCheckupItemTbl tbl)
        {
            return base.Channel.StrSetNeedCheckupItem(tbl);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetNeedCheckupItemAsync(System.Arctec.Ar1000k.DataBase.ArctecServer.StrSetNeedCheckupItemTbl tbl)
        {
            return base.Channel.StrSetNeedCheckupItemAsync(tbl);
        }
        
        public string[] StrGetIniGuideMaster(string ipAddress)
        {
            return base.Channel.StrGetIniGuideMaster(ipAddress);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetIniGuideMasterAsync(string ipAddress)
        {
            return base.Channel.StrGetIniGuideMasterAsync(ipAddress);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetIniGuideMasterAll()
        {
            return base.Channel.StrGetIniGuideMasterAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetIniGuideMasterAllAsync()
        {
            return base.Channel.StrGetIniGuideMasterAllAsync();
        }
        
        public string[] StrGetTermGuideData(string ipAddress)
        {
            return base.Channel.StrGetTermGuideData(ipAddress);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetTermGuideDataAsync(string ipAddress)
        {
            return base.Channel.StrGetTermGuideDataAsync(ipAddress);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTermGuideDataAll()
        {
            return base.Channel.StrGetTermGuideDataAll();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTermGuideDataAllAsync()
        {
            return base.Channel.StrGetTermGuideDataAllAsync();
        }
        
        public bool StrSetTermGuideData(string ipAddress, string GuideFlg, string TermFlg)
        {
            return base.Channel.StrSetTermGuideData(ipAddress, GuideFlg, TermFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetTermGuideDataAsync(string ipAddress, string GuideFlg, string TermFlg)
        {
            return base.Channel.StrSetTermGuideDataAsync(ipAddress, GuideFlg, TermFlg);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientGuideDataWithChildGroup(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.StrGetClientGuideDataWithChildGroup(dateTime, clientId, division);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientGuideDataWithChildGroupAsync(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.StrGetClientGuideDataWithChildGroupAsync(dateTime, clientId, division);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetChildGroupDetailMaster(string childGroupId)
        {
            return base.Channel.StrGetChildGroupDetailMaster(childGroupId);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetChildGroupDetailMasterAsync(string childGroupId)
        {
            return base.Channel.StrGetChildGroupDetailMasterAsync(childGroupId);
        }
        
        public string[] StrGetChildGroupTermMaster(string childGroupId)
        {
            return base.Channel.StrGetChildGroupTermMaster(childGroupId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetChildGroupTermMasterAsync(string childGroupId)
        {
            return base.Channel.StrGetChildGroupTermMasterAsync(childGroupId);
        }
        
        public string[] StrGetExamSettingMaster(string medicalCheckNo)
        {
            return base.Channel.StrGetExamSettingMaster(medicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetExamSettingMasterAsync(string medicalCheckNo)
        {
            return base.Channel.StrGetExamSettingMasterAsync(medicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetTargetStateNumberResponseStrGetTargetStateNumberResult StrGetTargetStateNumber(string sql)
        {
            return base.Channel.StrGetTargetStateNumber(sql);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.StrGetTargetStateNumberResponseStrGetTargetStateNumberResult> StrGetTargetStateNumberAsync(string sql)
        {
            return base.Channel.StrGetTargetStateNumberAsync(sql);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetAllStateFromExecDate(string execDate)
        {
            return base.Channel.StrGetAllStateFromExecDate(execDate);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetAllStateFromExecDateAsync(string execDate)
        {
            return base.Channel.StrGetAllStateFromExecDateAsync(execDate);
        }
        
        public string[] StrGetNextGuideData(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.StrGetNextGuideData(dateTime, clientId, division);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetNextGuideDataAsync(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.StrGetNextGuideDataAsync(dateTime, clientId, division);
        }
        
        public bool DeleteNextGuideData(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.DeleteNextGuideData(dateTime, clientId, division);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteNextGuideDataAsync(System.DateTime dateTime, string clientId, string division)
        {
            return base.Channel.DeleteNextGuideDataAsync(dateTime, clientId, division);
        }
        
        public bool StrSetNextGuideData(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            return base.Channel.StrSetNextGuideData(dateTime, clientId, division, childGroupFlg, value);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetNextGuideDataAsync(System.DateTime dateTime, string clientId, string division, bool childGroupFlg, string value)
        {
            return base.Channel.StrSetNextGuideDataAsync(dateTime, clientId, division, childGroupFlg, value);
        }
        
        public string HelloWorld()
        {
            return base.Channel.HelloWorld();
        }
        
        public System.Threading.Tasks.Task<string> HelloWorldAsync()
        {
            return base.Channel.HelloWorldAsync();
        }
        
        public string[] StrGetHostData()
        {
            return base.Channel.StrGetHostData();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetHostDataAsync()
        {
            return base.Channel.StrGetHostDataAsync();
        }
        
        public string[] StrGetMachines(string termID)
        {
            return base.Channel.StrGetMachines(termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMachinesAsync(string termID)
        {
            return base.Channel.StrGetMachinesAsync(termID);
        }
        
        public string[] StrGetMachinesAllkeys()
        {
            return base.Channel.StrGetMachinesAllkeys();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMachinesAllkeysAsync()
        {
            return base.Channel.StrGetMachinesAllkeysAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMachinesAllPlural()
        {
            return base.Channel.StrGetMachinesAllPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMachinesAllPluralAsync()
        {
            return base.Channel.StrGetMachinesAllPluralAsync();
        }
        
        public string[] StrGetClientData(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientData(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientDataAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string StrGetClientRegistrationNo(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientRegistrationNo(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string> StrGetClientRegistrationNoAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetClientRegistrationNoAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string[] StrGetBeforeClientIDDate(string sMedicalCheckDate, string RegistrationNo)
        {
            return base.Channel.StrGetBeforeClientIDDate(sMedicalCheckDate, RegistrationNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeClientIDDateAsync(string sMedicalCheckDate, string RegistrationNo)
        {
            return base.Channel.StrGetBeforeClientIDDateAsync(sMedicalCheckDate, RegistrationNo);
        }
        
        public string[] StrGetBeforeClientData(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetBeforeClientData(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetBeforeClientDataAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string StrGetClientRegistrationNoNoDiv(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientRegistrationNoNoDiv(sMedicalCheckDate, sClientID);
        }
        
        public System.Threading.Tasks.Task<string> StrGetClientRegistrationNoNoDivAsync(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientRegistrationNoNoDivAsync(sMedicalCheckDate, sClientID);
        }
        
        public string[] StrGetBeforeClientDataNoDiv(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetBeforeClientDataNoDiv(sMedicalCheckDate, sClientID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeClientDataNoDivAsync(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetBeforeClientDataNoDivAsync(sMedicalCheckDate, sClientID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPlural(string sMedicalCheckDate, string termID)
        {
            return base.Channel.StrGetClientDataPlural(sMedicalCheckDate, termID);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralAsync(string sMedicalCheckDate, string termID)
        {
            return base.Channel.StrGetClientDataPluralAsync(sMedicalCheckDate, termID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralCheckNo(string sMedicalCheckDate, string sMedicalCheckNo)
        {
            return base.Channel.StrGetClientDataPluralCheckNo(sMedicalCheckDate, sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralCheckNoAsync(string sMedicalCheckDate, string sMedicalCheckNo)
        {
            return base.Channel.StrGetClientDataPluralCheckNoAsync(sMedicalCheckDate, sMedicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetClientDataPluralAllState(string sMedicalCheckDate)
        {
            return base.Channel.StrGetClientDataPluralAllState(sMedicalCheckDate);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetClientDataPluralAllStateAsync(string sMedicalCheckDate)
        {
            return base.Channel.StrGetClientDataPluralAllStateAsync(sMedicalCheckDate);
        }
        
        public string[] StrGetClientDataNoDiv(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientDataNoDiv(sMedicalCheckDate, sClientID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataNoDivAsync(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientDataNoDivAsync(sMedicalCheckDate, sClientID);
        }
        
        public string[] StrGetClientDataRegist(string sMedicalCheckDate, string sRegistNo)
        {
            return base.Channel.StrGetClientDataRegist(sMedicalCheckDate, sRegistNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataRegistAsync(string sMedicalCheckDate, string sRegistNo)
        {
            return base.Channel.StrGetClientDataRegistAsync(sMedicalCheckDate, sRegistNo);
        }
        
        public string[] StrGetClientDataRegistDiv(string sMedicalCheckDate, string sRegistNo, string sDivision)
        {
            return base.Channel.StrGetClientDataRegistDiv(sMedicalCheckDate, sRegistNo, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataRegistDivAsync(string sMedicalCheckDate, string sRegistNo, string sDivision)
        {
            return base.Channel.StrGetClientDataRegistDivAsync(sMedicalCheckDate, sRegistNo, sDivision);
        }
        
        public string[] StrGetClientDataSeachDiv(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientDataSeachDiv(sMedicalCheckDate, sClientID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataSeachDivAsync(string sMedicalCheckDate, string sClientID)
        {
            return base.Channel.StrGetClientDataSeachDivAsync(sMedicalCheckDate, sClientID);
        }
        
        public string[] StrGetClientDataSeachDivRegist(string sMedicalCheckDate, string sRegistNo)
        {
            return base.Channel.StrGetClientDataSeachDivRegist(sMedicalCheckDate, sRegistNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetClientDataSeachDivRegistAsync(string sMedicalCheckDate, string sRegistNo)
        {
            return base.Channel.StrGetClientDataSeachDivRegistAsync(sMedicalCheckDate, sRegistNo);
        }
        
        public string[] StrGetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetData(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetDataAsync(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public string[] StrGetDataCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            return base.Channel.StrGetDataCheckNo(sMedicalCheckDate, sClientID, sDivision, sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetDataCheckNoAsync(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            return base.Channel.StrGetDataCheckNoAsync(sMedicalCheckDate, sClientID, sDivision, sMedicalCheckNo);
        }
        
        public void StrSetData(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg)
        {
            base.Channel.StrSetData(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg);
        }
        
        public System.Threading.Tasks.Task StrSetDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg)
        {
            return base.Channel.StrSetDataAsync(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg);
        }
        
        public void StrSetDataTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg, System.DateTime nowTime)
        {
            base.Channel.StrSetDataTime(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg, nowTime);
        }
        
        public System.Threading.Tasks.Task StrSetDataTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string[] medicalData, int timeFlg, System.DateTime nowTime)
        {
            return base.Channel.StrSetDataTimeAsync(sMedicalCheckDate, sClientID, sDivision, termID, medicalData, timeFlg, nowTime);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataPlural(string sMedicalCheckDate, string termID)
        {
            return base.Channel.StrGetDataPlural(sMedicalCheckDate, termID);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataPluralAsync(string sMedicalCheckDate, string termID)
        {
            return base.Channel.StrGetDataPluralAsync(sMedicalCheckDate, termID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetDataAll(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetDataAll(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetDataAllAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetDataAllAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string[] StrGetState(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetState(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetStateAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetStateAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string StrGetStateNext(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetStateNext(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<string> StrGetStateNextAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetStateNextAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public void StrSetState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg)
        {
            base.Channel.StrSetState(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg);
        }
        
        public System.Threading.Tasks.Task StrSetStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg)
        {
            return base.Channel.StrSetStateAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg);
        }
        
        public void StrSetStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg, System.DateTime nowTime)
        {
            base.Channel.StrSetStateTime(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg, nowTime);
        }
        
        public System.Threading.Tasks.Task StrSetStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, int iTimeFlg, System.DateTime nowTime)
        {
            return base.Channel.StrSetStateTimeAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, iTimeFlg, nowTime);
        }
        
        public string StrGetMedicalCheckList(short iMedicalCheckNo)
        {
            return base.Channel.StrGetMedicalCheckList(iMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string> StrGetMedicalCheckListAsync(short iMedicalCheckNo)
        {
            return base.Channel.StrGetMedicalCheckListAsync(iMedicalCheckNo);
        }
        
        public string StrGetMedicalCheckListNo(string sMedicalCheckName)
        {
            return base.Channel.StrGetMedicalCheckListNo(sMedicalCheckName);
        }
        
        public System.Threading.Tasks.Task<string> StrGetMedicalCheckListNoAsync(string sMedicalCheckName)
        {
            return base.Channel.StrGetMedicalCheckListNoAsync(sMedicalCheckName);
        }
        
        public string[] StrGetMedicalCheckListAll()
        {
            return base.Channel.StrGetMedicalCheckListAll();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMedicalCheckListAllAsync()
        {
            return base.Channel.StrGetMedicalCheckListAllAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetMedicalCheckListPlural()
        {
            return base.Channel.StrGetMedicalCheckListPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetMedicalCheckListPluralAsync()
        {
            return base.Channel.StrGetMedicalCheckListPluralAsync();
        }
        
        public bool StrStartUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state)
        {
            return base.Channel.StrStartUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateDataStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state)
        {
            return base.Channel.StrStartUpdateDataStateAsync(sMedicalCheckDate, sClientID, sDivision, termID, state);
        }
        
        public bool StrEndUpdateDataState(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData)
        {
            return base.Channel.StrEndUpdateDataState(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData)
        {
            return base.Channel.StrEndUpdateDataStateAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData);
        }
        
        public bool StrStartUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateDataStateTime(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateDataStateTimeAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public bool StrEndUpdateDataStateTime(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateDataStateTime(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateDataStateTimeAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, nowTime);
        }
        
        public bool StrStartUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateDataStateTimeMore(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime nowTime)
        {
            return base.Channel.StrStartUpdateDataStateTimeMoreAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, nowTime);
        }
        
        public bool StrEndUpdateDataStateTimeMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateDataStateTimeMore(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, nowTime);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime nowTime)
        {
            return base.Channel.StrEndUpdateDataStateTimeMoreAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, nowTime);
        }
        
        public bool StrStartUpdateDataStateTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrStartUpdateDataStateTimeTrans(sMedicalCheckDate, sClientID, sDivision, termID, state, startTime, endTime, hostTransFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrStartUpdateDataStateTimeTransAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, startTime, endTime, hostTransFlg);
        }
        
        public bool StrEndUpdateDataStateTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateTimeTrans(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, startTime, endTime, hostTransFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateTimeTransAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, startTime, endTime, hostTransFlg);
        }
        
        public bool StrStartUpdateDataStateTimeTransMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrStartUpdateDataStateTimeTransMore(sMedicalCheckDate, sClientID, sDivision, termID, state, startTime, endTime, hostTransFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrStartUpdateDataStateTimeTransMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrStartUpdateDataStateTimeTransMoreAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, startTime, endTime, hostTransFlg);
        }
        
        public bool StrEndUpdateDataStateTimeTransMore(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateTimeTransMore(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, startTime, endTime, hostTransFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateTimeTransMoreAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] termID, string[] state, string[] medicalData, System.DateTime startTime, System.DateTime endTime, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateTimeTransMoreAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, startTime, endTime, hostTransFlg);
        }
        
        public bool StrEndUpdateDataStateNoTimeTrans(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateNoTimeTrans(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, hostTransFlg);
        }
        
        public System.Threading.Tasks.Task<bool> StrEndUpdateDataStateNoTimeTransAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID, string state, string[] medicalData, bool hostTransFlg)
        {
            return base.Channel.StrEndUpdateDataStateNoTimeTransAsync(sMedicalCheckDate, sClientID, sDivision, termID, state, medicalData, hostTransFlg);
        }
        
        public string[] StrGetPermissionIP(string sIp)
        {
            return base.Channel.StrGetPermissionIP(sIp);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetPermissionIPAsync(string sIp)
        {
            return base.Channel.StrGetPermissionIPAsync(sIp);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPermissionIP_Plural(string sIp)
        {
            return base.Channel.StrGetPermissionIP_Plural(sIp);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPermissionIP_PluralAsync(string sIp)
        {
            return base.Channel.StrGetPermissionIP_PluralAsync(sIp);
        }
        
        public string[] StrGetPermissionIPallkeys()
        {
            return base.Channel.StrGetPermissionIPallkeys();
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetPermissionIPallkeysAsync()
        {
            return base.Channel.StrGetPermissionIPallkeysAsync();
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetPermissionIPallPlural()
        {
            return base.Channel.StrGetPermissionIPallPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetPermissionIPallPluralAsync()
        {
            return base.Channel.StrGetPermissionIPallPluralAsync();
        }
        
        public bool StrSetPermissionIP(string sIp, string termID, string beforeTermID)
        {
            return base.Channel.StrSetPermissionIP(sIp, termID, beforeTermID);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetPermissionIPAsync(string sIp, string termID, string beforeTermID)
        {
            return base.Channel.StrSetPermissionIPAsync(sIp, termID, beforeTermID);
        }
        
        public bool DeletePermissionIP(string sIp)
        {
            return base.Channel.DeletePermissionIP(sIp);
        }
        
        public System.Threading.Tasks.Task<bool> DeletePermissionIPAsync(string sIp)
        {
            return base.Channel.DeletePermissionIPAsync(sIp);
        }
        
        public string[] StrGetTerminalSetting(string sIp, short iSettingId)
        {
            return base.Channel.StrGetTerminalSetting(sIp, iSettingId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetTerminalSettingAsync(string sIp, short iSettingId)
        {
            return base.Channel.StrGetTerminalSettingAsync(sIp, iSettingId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetTerminalSettingPlural(string sIp)
        {
            return base.Channel.StrGetTerminalSettingPlural(sIp);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetTerminalSettingPluralAsync(string sIp)
        {
            return base.Channel.StrGetTerminalSettingPluralAsync(sIp);
        }
        
        public bool StrSetTerminalSetting(string sIp, short iSettingId, string iContent)
        {
            return base.Channel.StrSetTerminalSetting(sIp, iSettingId, iContent);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetTerminalSettingAsync(string sIp, short iSettingId, string iContent)
        {
            return base.Channel.StrSetTerminalSettingAsync(sIp, iSettingId, iContent);
        }
        
        public bool DeleteTerminalSetting(string sIp, short iSettingId)
        {
            return base.Channel.DeleteTerminalSetting(sIp, iSettingId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteTerminalSettingAsync(string sIp, short iSettingId)
        {
            return base.Channel.DeleteTerminalSettingAsync(sIp, iSettingId);
        }
        
        public bool StartUpdateTermStatus(string termID)
        {
            return base.Channel.StartUpdateTermStatus(termID);
        }
        
        public System.Threading.Tasks.Task<bool> StartUpdateTermStatusAsync(string termID)
        {
            return base.Channel.StartUpdateTermStatusAsync(termID);
        }
        
        public bool EndUpdateTermStatus(string termID)
        {
            return base.Channel.EndUpdateTermStatus(termID);
        }
        
        public System.Threading.Tasks.Task<bool> EndUpdateTermStatusAsync(string termID)
        {
            return base.Channel.EndUpdateTermStatusAsync(termID);
        }
        
        public string[] StrGetBeforeOldData(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetBeforeOldData(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeOldDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetBeforeOldDataAsync(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public string[] StrGetBeforeOldDataNoDiv(string sMedicalCheckDate, string sClientID, string termID)
        {
            return base.Channel.StrGetBeforeOldDataNoDiv(sMedicalCheckDate, sClientID, termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeOldDataNoDivAsync(string sMedicalCheckDate, string sClientID, string termID)
        {
            return base.Channel.StrGetBeforeOldDataNoDivAsync(sMedicalCheckDate, sClientID, termID);
        }
        
        public string[] StrGetBeforeOldCheckNo(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            return base.Channel.StrGetBeforeOldCheckNo(sMedicalCheckDate, sClientID, sDivision, sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetBeforeOldCheckNoAsync(string sMedicalCheckDate, string sClientID, string sDivision, string sMedicalCheckNo)
        {
            return base.Channel.StrGetBeforeOldCheckNoAsync(sMedicalCheckDate, sClientID, sDivision, sMedicalCheckNo);
        }
        
        public bool DatabaseLimitControl()
        {
            return base.Channel.DatabaseLimitControl();
        }
        
        public System.Threading.Tasks.Task<bool> DatabaseLimitControlAsync()
        {
            return base.Channel.DatabaseLimitControlAsync();
        }
        
        public string[] StrGetDataName(string termID)
        {
            return base.Channel.StrGetDataName(termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetDataNameAsync(string termID)
        {
            return base.Channel.StrGetDataNameAsync(termID);
        }
        
        public string[] StrGetMeCheckUpName(string termID)
        {
            return base.Channel.StrGetMeCheckUpName(termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameAsync(string termID)
        {
            return base.Channel.StrGetMeCheckUpNameAsync(termID);
        }
        
        public string[] StrGetMeCheckUpNameNo(string sMedicalCheckNo)
        {
            return base.Channel.StrGetMeCheckUpNameNo(sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameNoAsync(string sMedicalCheckNo)
        {
            return base.Channel.StrGetMeCheckUpNameNoAsync(sMedicalCheckNo);
        }
        
        public string[] StrGetMeCheckUpNameKindNo(string sKindNo)
        {
            return base.Channel.StrGetMeCheckUpNameKindNo(sKindNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMeCheckUpNameKindNoAsync(string sKindNo)
        {
            return base.Channel.StrGetMeCheckUpNameKindNoAsync(sKindNo);
        }
        
        public string[] StrGetMeConnectSetting(string sDll, short iMeId)
        {
            return base.Channel.StrGetMeConnectSetting(sDll, iMeId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetMeConnectSettingAsync(string sDll, short iMeId)
        {
            return base.Channel.StrGetMeConnectSettingAsync(sDll, iMeId);
        }
        
        public bool StrSetMeConnectSetting(string sDll, short iMeId, string[] setting, string[] medicalData)
        {
            return base.Channel.StrSetMeConnectSetting(sDll, iMeId, setting, medicalData);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetMeConnectSettingAsync(string sDll, short iMeId, string[] setting, string[] medicalData)
        {
            return base.Channel.StrSetMeConnectSettingAsync(sDll, iMeId, setting, medicalData);
        }
        
        public string[] StrGetNeedCheckup(string termID)
        {
            return base.Channel.StrGetNeedCheckup(termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetNeedCheckupAsync(string termID)
        {
            return base.Channel.StrGetNeedCheckupAsync(termID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetNeedCheckupPlural()
        {
            return base.Channel.StrGetNeedCheckupPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetNeedCheckupPluralAsync()
        {
            return base.Channel.StrGetNeedCheckupPluralAsync();
        }
        
        public bool StrSetNeedCheckup(string termID, string[] checkObjectNos)
        {
            return base.Channel.StrSetNeedCheckup(termID, checkObjectNos);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetNeedCheckupAsync(string termID, string[] checkObjectNos)
        {
            return base.Channel.StrSetNeedCheckupAsync(termID, checkObjectNos);
        }
        
        public bool DeleteNeedCheckup(string termID)
        {
            return base.Channel.DeleteNeedCheckup(termID);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteNeedCheckupAsync(string termID)
        {
            return base.Channel.DeleteNeedCheckupAsync(termID);
        }
        
        public string[] StrGetNeedCheckupMedicalNo(string sMedicalCheckNo)
        {
            return base.Channel.StrGetNeedCheckupMedicalNo(sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetNeedCheckupMedicalNoAsync(string sMedicalCheckNo)
        {
            return base.Channel.StrGetNeedCheckupMedicalNoAsync(sMedicalCheckNo);
        }
        
        public bool StrSetNeedCheckupMedicalNo(string sMedicalCheckNo, string[] checkObjectNos)
        {
            return base.Channel.StrSetNeedCheckupMedicalNo(sMedicalCheckNo, checkObjectNos);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetNeedCheckupMedicalNoAsync(string sMedicalCheckNo, string[] checkObjectNos)
        {
            return base.Channel.StrSetNeedCheckupMedicalNoAsync(sMedicalCheckNo, checkObjectNos);
        }
        
        public bool DeleteNeedCheckupMedicalNo(string sMedicalCheckNo)
        {
            return base.Channel.DeleteNeedCheckupMedicalNo(sMedicalCheckNo);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteNeedCheckupMedicalNoAsync(string sMedicalCheckNo)
        {
            return base.Channel.DeleteNeedCheckupMedicalNoAsync(sMedicalCheckNo);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetIndividualControlPlural()
        {
            return base.Channel.StrGetIndividualControlPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetIndividualControlPluralAsync()
        {
            return base.Channel.StrGetIndividualControlPluralAsync();
        }
        
        public string[] StrGetIndividualControl(string iPAddress)
        {
            return base.Channel.StrGetIndividualControl(iPAddress);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetIndividualControlAsync(string iPAddress)
        {
            return base.Channel.StrGetIndividualControlAsync(iPAddress);
        }
        
        public bool StrSetIndividualControl(string iPAddress, string[] dispCheckNos, string[] checkObjectNos)
        {
            return base.Channel.StrSetIndividualControl(iPAddress, dispCheckNos, checkObjectNos);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetIndividualControlAsync(string iPAddress, string[] dispCheckNos, string[] checkObjectNos)
        {
            return base.Channel.StrSetIndividualControlAsync(iPAddress, dispCheckNos, checkObjectNos);
        }
        
        public bool DeleteIndividualControl(string iPAddress)
        {
            return base.Channel.DeleteIndividualControl(iPAddress);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteIndividualControlAsync(string iPAddress)
        {
            return base.Channel.DeleteIndividualControlAsync(iPAddress);
        }
        
        public string[] StrGetCheckItem(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetCheckItem(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetCheckItemAsync(string sMedicalCheckDate, string sClientID, string sDivision, string termID)
        {
            return base.Channel.StrGetCheckItemAsync(sMedicalCheckDate, sClientID, sDivision, termID);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetCheckItemPlural(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetCheckItemPlural(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetCheckItemPluralAsync(string sMedicalCheckDate, string sClientID, string sDivision)
        {
            return base.Channel.StrGetCheckItemPluralAsync(sMedicalCheckDate, sClientID, sDivision);
        }
        
        public string[] StrGetConciergeControl(string sGroupId)
        {
            return base.Channel.StrGetConciergeControl(sGroupId);
        }
        
        public System.Threading.Tasks.Task<string[]> StrGetConciergeControlAsync(string sGroupId)
        {
            return base.Channel.StrGetConciergeControlAsync(sGroupId);
        }
        
        public System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[] StrGetConciergeControlPlural()
        {
            return base.Channel.StrGetConciergeControlPlural();
        }
        
        public System.Threading.Tasks.Task<System.Arctec.Ar1000k.DataBase.ArctecServer.PluralRecord[]> StrGetConciergeControlPluralAsync()
        {
            return base.Channel.StrGetConciergeControlPluralAsync();
        }
        
        public bool StrSetConciergeControl(string sGroupId, string sGroupName, string sShortGroupName, string[] checkObjectNos)
        {
            return base.Channel.StrSetConciergeControl(sGroupId, sGroupName, sShortGroupName, checkObjectNos);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetConciergeControlAsync(string sGroupId, string sGroupName, string sShortGroupName, string[] checkObjectNos)
        {
            return base.Channel.StrSetConciergeControlAsync(sGroupId, sGroupName, sShortGroupName, checkObjectNos);
        }
        
        public bool DeleteConciergeControl(string sGroupId)
        {
            return base.Channel.DeleteConciergeControl(sGroupId);
        }
        
        public System.Threading.Tasks.Task<bool> DeleteConciergeControlAsync(string sGroupId)
        {
            return base.Channel.DeleteConciergeControlAsync(sGroupId);
        }
        
        public bool StrSetClientData(string sMedicalCheckDate, string sClientID, string sDivision, string[] regData, int regPos)
        {
            return base.Channel.StrSetClientData(sMedicalCheckDate, sClientID, sDivision, regData, regPos);
        }
        
        public System.Threading.Tasks.Task<bool> StrSetClientDataAsync(string sMedicalCheckDate, string sClientID, string sDivision, string[] regData, int regPos)
        {
            return base.Channel.StrSetClientDataAsync(sMedicalCheckDate, sClientID, sDivision, regData, regPos);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        #if !NET6_0_OR_GREATER
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        #endif
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.PublicServiceSoap))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            if ((endpointConfiguration == EndpointConfiguration.PublicServiceSoap12))
            {
                System.ServiceModel.Channels.CustomBinding result = new System.ServiceModel.Channels.CustomBinding();
                System.ServiceModel.Channels.TextMessageEncodingBindingElement textBindingElement = new System.ServiceModel.Channels.TextMessageEncodingBindingElement();
                textBindingElement.MessageVersion = System.ServiceModel.Channels.MessageVersion.CreateVersion(System.ServiceModel.EnvelopeVersion.Soap12, System.ServiceModel.Channels.AddressingVersion.None);
                result.Elements.Add(textBindingElement);
                System.ServiceModel.Channels.HttpTransportBindingElement httpBindingElement = new System.ServiceModel.Channels.HttpTransportBindingElement();
                httpBindingElement.AllowCookies = true;
                httpBindingElement.MaxBufferSize = int.MaxValue;
                httpBindingElement.MaxReceivedMessageSize = int.MaxValue;
                result.Elements.Add(httpBindingElement);
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.PublicServiceSoap))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:8999/PublicService.asmx");
            }
            if ((endpointConfiguration == EndpointConfiguration.PublicServiceSoap12))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:8999/PublicService.asmx");
            }
            throw new System.InvalidOperationException(string.Format("Could not find endpoint with name \'{0}\'.", endpointConfiguration));
        }
        
        public enum EndpointConfiguration
        {
            
            PublicServiceSoap,
            
            PublicServiceSoap12,
        }
    }
}
