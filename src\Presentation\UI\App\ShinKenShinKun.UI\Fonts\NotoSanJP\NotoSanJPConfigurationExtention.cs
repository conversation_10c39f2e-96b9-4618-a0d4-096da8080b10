﻿namespace ShinKenShinKun.UI;

public static class NotoSanJPConfigurationExtention
{
    public static IFontCollection AddNotoSanJPFonts(this IFontCollection fonts)
    {
        fonts.AddFont("NotoSansJP-Regular.ttf", FontNames.NotoSansJPRegular);
        fonts.AddFont("NotoSansJP-SemiBold.ttf", FontNames.NotoSansJPSemiBold);
        fonts.AddFont("NotoSansJP-Bold.ttf", FontNames.NotoSansJPBold);
        fonts.AddFont("NotoSansJP-Black.ttf", FontNames.NotoSansJPBlack);
        fonts.AddFont("NotoSansJP-ExtraBold.ttf", FontNames.NotoSansJPExtraBold);
        fonts.AddFont("NotoSansJP-ExtraLight.ttf", FontNames.NotoSansJPExtraLight);
        fonts.AddFont("NotoSansJP-Heavy.ttf", FontNames.NotoSansJPHeavy);
        fonts.AddFont("NotoSansJP-Light.ttf", FontNames.NotoSansJPLight);
        fonts.AddFont("NotoSansJP-Medium.ttf", FontNames.NotoSansJPMedium);
        fonts.AddFont("NotoSansJP-Thin.ttf", FontNames.NotoSansJPThin);
        return fonts;
    }

}
