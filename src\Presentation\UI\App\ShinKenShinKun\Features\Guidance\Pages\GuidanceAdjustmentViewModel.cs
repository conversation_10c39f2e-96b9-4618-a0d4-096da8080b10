﻿namespace ShinKenShinKun;

public partial class GuidanceAdjustmentPageViewModel(
    IAppNavigator appNavigator,
    IAppSettingServices appSettingServices,
    IGuidanceAdjustmentService patientGuidanceAdjustmentService,
    ISessionServices sessionServices,
    IDispatcher dispatcher
    ) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperty

    [ObservableProperty] private ObservableCollection<ClientInformationModel> clientInformations;
    [ObservableProperty] private ClientInformationModel? clientSelected;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string userName;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private ObservableCollection<MedicalCheckModel> medicalCheckDivisions;
    [ObservableProperty] private MedicalCheckModel medicalCheckSelected;
    [ObservableProperty] private string clientIdSearched;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;
    #endregion
    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }


    [RelayCommand]
    private void SearchByClientId()
    {
        ClientInformations = patientGuidanceAdjustmentService
            .ClientInformationByClientId(ClientIdSearched, MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
    }

    [RelayCommand]
    private async Task GoToGuidanceSetting()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await appNavigator.NavigateAsync(nameof(RouterName.GuidanceSettingPage), false, ClientSelected);
    }


    [RelayCommand]
    private async Task GoToPendingTestSelection()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.PendingTestSelectionPage, false, ClientSelected);
    }

    [RelayCommand]
    private async Task GoToStopExamine()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.StopExaminePage, false, ClientSelected);
    }

    [RelayCommand]
    private async Task GoToPatientPending()
    {
        await AppNavigator.NavigateAsync(RouterName.PatientPendingPage, false);
    }

    [RelayCommand]
    private void ResetScroll()
    {
        sessionServices.NoteViewScroll.ScrollY = 0;
        sessionServices.PersonalNoteViewScroll.ScrollY = 0;
    }
    #endregion RelayCommand

    #region Override Method

    partial void OnMedicalCheckSelectedChanged(MedicalCheckModel? oldValue, MedicalCheckModel newValue)
    {
        if(oldValue == null || MedicalCheckSelected == null)
        {
            return;
        }
        ClientInformations = patientGuidanceAdjustmentService
            .ClientInformationByMedicalCheck(ClientIdSearched, MedicalCheckSelected);
        ClientSelected = ClientInformations?.FirstOrDefault();
    }



    protected override void OnBack(IDictionary<string, object> query)
    {
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        ClientSelected = query.GetData<ClientInformationModel>();
    }

    protected override void OnInit(IDictionary<string, object> query)
    {
        base.OnInit(query);

        sessionServices.NoteViewScroll.ScrollY = 0;
        sessionServices.PersonalNoteViewScroll.ScrollY = 0;
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
    }

    public override Task OnAppearingAsync()
    {
        MedicalCheckDivisions = patientGuidanceAdjustmentService.GetMedicalCheckDivisions();
        ClientIdSearched = string.Empty;
        MedicalCheckSelected = MedicalCheckDivisions.First();
        UserName = sessionServices.UserName;
        IsLogin = appSettingServices.AppSettings.IsLogin;
        IsMaskMode = sessionServices.IsMaskMode;
        base.OnAppearingAsync();
        return Task.Run(async () =>
        {
            await Task.Delay(500);
            dispatcher.Dispatch(() =>
            {
                ClientInformations = patientGuidanceAdjustmentService
                    .LoadDataClientInformations();
                ClientSelected = ClientInformations?.FirstOrDefault();
            });
        });
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion Override Method

    #region PublicMethod
    #endregion PublicMethod

    #region Private Method
    #endregion Private Method
}
