﻿namespace ShinKenShinKun.UI;


public class SexConverter : IValueConverter
{
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is Sex gender)
        {
            return gender switch
            {
                Sex.Male => AppResources.Male,
                Sex.Female => AppResources.Female,
                Sex.NoDefine => AppResources.NoDefine,
                _ => string.Empty,
            };
        }
        return string.Empty;
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
