﻿namespace ShinKenShinKun.Utils;

public class DirUtility
{
    public static string GetCurrentDirectory()
    {
        // -------------------------------------------------- //
        // エミュレート用
        OperatingSystem osInfo = Environment.OSVersion;
        bool? IsXpModeFlg = null;

        // 使用マシンがＸＰの場合
        if (osInfo.Version.Major == 5 && osInfo.Version.Minor == 1)
        {
            IsXpModeFlg = true;
        }
        // 使用マシンがWin7の場合
        else if (osInfo.Version.Major == 6 && osInfo.Version.Minor >= 1)
        {
            IsXpModeFlg = true;
        }
        else
        {
            IsXpModeFlg = false;
        }

        // カレントディレクトリパスの取得
        string currentDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase);
        if (IsXpModeFlg.HasValue && IsXpModeFlg.Value)
        {
            currentDirectory = Directory.GetCurrentDirectory();
        }

        return currentDirectory;
    }
}