<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExaminationAfterAuthEntryView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <VerticalStackLayout
        VerticalOptions="Center">
        <Grid
            ColumnDefinitions="auto,*"
            ColumnSpacing="10">
            <Label
                Grid.Column="0"
                Text="{Binding Label, Source={x:Reference this}}"
                TextColor="{Binding Color, Source={x:Reference this}}"
                Style="{x:Static ui:Styles.PatientStatTitleLabel}" />
            <Label
                Grid.Column="1"
                Text="{Binding Value,StringFormat='{}{0:F1}', Source={x:Reference this}}"
                VerticalOptions="Center"
                Style="{x:Static ui:Styles.PatientStatDetailLabel}" />
        </Grid>
        <BoxView
            Margin="{ui:EdgeInsets 
                Top={x:Static ui:Dimens.SpacingXs}}"
            BackgroundColor="{Binding Color, Source={x:Reference this}}"
            Style="{x:Static ui:Styles.HorizontalRule}" />
    </VerticalStackLayout>
</ContentView>
