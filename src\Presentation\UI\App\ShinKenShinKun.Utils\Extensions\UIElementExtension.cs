﻿namespace ShinKenShinKun.Utils.Extensions
{
    public static class UIElementExtension
    {
        //public static void Visible(this UIElement control, bool isVisible)
        //{
        //    control.Visibility = isVisible ? System.Windows.Visibility.Visible : System.Windows.Visibility.Hidden;
        //}

        /// <summary>
        /// .NET 9 MAUI Migrated
        /// </summary>
        public static void Visible(this VisualElement control, bool isVisible)
        {
            control.IsVisible = isVisible;
        }
    }
}