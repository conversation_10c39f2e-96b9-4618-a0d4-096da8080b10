﻿namespace ShinKenShinKun.Utils;

public class SoundUtility
{
    public enum MessageBeepType : int
    {
        SimpleBeep = -1,
        MB_OK = 0x00,
        MB_ICONHAND = 0x10,
        MB_ICONQUESTION = 0x20,
        MB_ICONEXCLAMATION = 0x30,
        MB_ICONASTERISK = 0x40,
    }

    // システムサウンド再生関数
    [System.Runtime.InteropServices.DllImport("coredll.dll")]
    public static extern bool MessageBeep(MessageBeepType uType);

    // Waveファイル再生関数
    [System.Runtime.InteropServices.DllImport("coredll.dll")] // Windows CEの場合
    public static extern bool PlaySound(string pszSound, nint hmod, PlaySoundFlags fdwSound);

    public enum PlaySoundFlags : int
    {
        SND_SYNC = 0x0000,
        SND_ASYNC = 0x0001,
        SND_NODEFAULT = 0x0002,
        SND_MEMORY = 0x0004,
        SND_LOOP = 0x0008,
        SND_NOSTOP = 0x0010,
        SND_NOWAIT = 0x00002000,
        SND_ALIAS = 0x00010000,
        SND_ALIAS_ID = 0x00110000,
        SND_FILENAME = 0x00020000,
        SND_RESOURCE = 0x00040004,
        SND_PURGE = 0x0040,
        SND_APPLICATION = 0x0080
    }

    //// Waveファイル再生関数[Cute以外]
    [System.Runtime.InteropServices.DllImport("winmm.dll")] // Windows XP系の場合
    public static extern bool PlaySound(string pszSound, nint hmod, PlaySoundFlags fdwSound, string dummy);

    public static void MessageBeepInfo()
    {
        // メッセージ（情報）を鳴らす
        MessageBeep(MessageBeepType.MB_ICONASTERISK);
    }

    public static void MessageBeepCheck()
    {
        // 一般の警告音を鳴らす
        MessageBeep(MessageBeepType.MB_OK);
    }

    public static void MessageBeepAlart()
    {
        // メッセージ（警告）を鳴らす
        MessageBeep(MessageBeepType.MB_ICONEXCLAMATION);
    }

    public static void MessageBeepError()
    {
        //システムエラーを鳴らす
        MessageBeep(MessageBeepType.MB_ICONHAND);
    }

    public static void MessageBeepQuestion()
    {
        //メッセージ（問い合わせ）を鳴らす
        MessageBeep(MessageBeepType.MB_ICONQUESTION);
    }

    // 任意のWaveファイルの再生
    public static void PlayWavSound(string wavPath)
    {
        PlayWavSoundCommon(wavPath, PlaySoundFlags.SND_ASYNC);
    }

    public static void PlayWavSoundFileName(string fileName)
    {
        PlayWavSound(
            Path.Combine(
            DirUtility.GetCurrentDirectory(),
            fileName));
    }

    public static void PlayWavSound(string wavPath, PlaySoundFlags psFlg)
    {
        PlayWavSoundCommon(wavPath, psFlg);
    }

    public static void PlayWavSoundCommon(string wavPath, PlaySoundFlags psFlg)
    {
        OperatingSystem osInfo = Environment.OSVersion;

        if (osInfo.Version.Major == 5 && osInfo.Version.Minor == 1)
        {
            return;
        }
        // 使用マシンがWin7の場合
        else if (osInfo.Version.Major == 6 && osInfo.Version.Minor >= 1)
        {
            PlaySound(wavPath, nint.Zero, PlaySoundFlags.SND_FILENAME | psFlg, "");
        }
        else
        {
            PlaySound(wavPath, nint.Zero, PlaySoundFlags.SND_FILENAME | psFlg);
        }
    }
}