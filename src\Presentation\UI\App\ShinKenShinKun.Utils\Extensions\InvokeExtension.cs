﻿namespace ShinKenShinKun.Utils;

public static class InvokeExtension
{
    public static object Invoke(this object obj, Delegate method, params object[] args)
    {
        return Application.Current.Dispatcher.Invoke(method, args);
    }

    public static void BeginInvoke(this object obj, Delegate method, params object[] args)
    {
        Application.Current.Dispatcher.BeginInvoke(method, args);
    }

    public static void BeginInvoke(this object obj, Delegate method)
    {
        Application.Current.Dispatcher.BeginInvoke(method, null);
    }

    //public static void Invoke(this object obj, DispatcherPriority priority, Delegate method)
    //{
    //    Application.Current.Dispatcher.Invoke(priority, method);
    //}

    /// <summary>
    /// .NET 9 MAUI Migrated
    /// </summary>
    public static void Invoke(this object obj, Action method)
    {
        MainThread.BeginInvokeOnMainThread(method);
    }

    //public static void InvokeOnBackground(this object obj)
    //{
    //    Application.Current.Dispatcher.Invoke(DispatcherPriority.Background, new Action(delegate { }));
    //}

    /// <summary>
    /// .NET 9 MAUI Migrated
    /// </summary>
    public static async void InvokeOnBackground(this object obj, Action method)
    {
        // Simulate lower priority with slight delay
        await Task.Delay(100); // optional
        MainThread.BeginInvokeOnMainThread(method);
    }
}