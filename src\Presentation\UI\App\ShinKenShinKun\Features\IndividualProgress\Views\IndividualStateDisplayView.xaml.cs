namespace ShinKenShinKun;
public partial class IndividualStateDisplayView : ContentView
{
    public IndividualStateDisplayView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty StatusCountProperty = BindableProperty.Create(
    nameof(StatusCount),
    typeof(ObservableCollection<string>),
    typeof(IndividualStateDisplayView),
    new ObservableCollection<string> { "0", "0", "0", "0" },
    BindingMode.TwoWay,
    propertyChanged: OnStatusCountChanged);

    public ObservableCollection<string> StatusCount
    {
        get => (ObservableCollection<string>)GetValue(StatusCountProperty);
        set => SetValue(StatusCountProperty, value);
    }

    private static void OnStatusCountChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if(bindable is IndividualStateDisplayView view && newValue is ObservableCollection<string> stateList)
        {
            view.OnStatusCountUpdated(stateList);
        }
    }

    private void OnStatusCountUpdated(ObservableCollection<string> stateList)
    {
        if(stateList.Count >= 4)
        {
            DoneCount = stateList[0];
            RemainCount = stateList[1];
            StopCount = stateList[2];
            PendingCount = stateList[3];
        }
        else
        {
            DoneCount = PendingCount = StopCount = RemainCount = "0";
        }
    }

    public static readonly BindableProperty DoneCountProperty = BindableProperty.Create(
    nameof(DoneCount),
    typeof(string),
    typeof(IndividualStateDisplayView),
    string.Empty,
    BindingMode.TwoWay);

    public string DoneCount
    {
        get => (string)GetValue(DoneCountProperty);
        set => SetValue(DoneCountProperty, value);
    }

    public static readonly BindableProperty RemainCountProperty = BindableProperty.Create(
    nameof(RemainCount),
    typeof(string),
    typeof(IndividualStateDisplayView),
    string.Empty,
    BindingMode.TwoWay);

    public string RemainCount
    {
        get => (string)GetValue(RemainCountProperty);
        set => SetValue(RemainCountProperty, value);
    }

    public static readonly BindableProperty StopCountProperty = BindableProperty.Create(
    nameof(StopCount),
    typeof(string),
    typeof(IndividualStateDisplayView),
    string.Empty,
    BindingMode.TwoWay);

    public string StopCount
    {
        get => (string)GetValue(StopCountProperty);
        set => SetValue(StopCountProperty, value);
    }

    public static readonly BindableProperty PendingCountProperty = BindableProperty.Create(
    nameof(PendingCount),
    typeof(string),
    typeof(IndividualStateDisplayView),
    string.Empty,
    BindingMode.TwoWay);

    public string PendingCount
    {
        get => (string)GetValue(PendingCountProperty);
        set => SetValue(PendingCountProperty, value);
    }
}