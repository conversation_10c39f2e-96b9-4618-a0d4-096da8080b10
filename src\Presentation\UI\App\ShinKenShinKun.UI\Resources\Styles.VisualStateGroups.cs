﻿namespace ShinKenShinKun.UI;

public static partial class Styles
{
    private static VisualStateGroupList ItemNoteMasterCollectionVisualStateGroups()
    {
        var visualStateGroups = new VisualStateGroupList();

        var commonStates = new VisualStateGroup { Name = "CommonStates" };

        var normalState = new VisualState { Name = "Normal" };

        var selectedState = new VisualState { Name = "Selected" };
        selectedState.Setters.Add(new Setter
        {
            Property = VisualElement.BackgroundColorProperty,
            Value = AppColors.SteelBlue
        });

        commonStates.States.Add(normalState);
        commonStates.States.Add(selectedState);

        visualStateGroups.Add(commonStates);

        return visualStateGroups;
    }

    private static VisualStateGroupList DropDownComboboxLabelVisualStateGroups()
    {
        var visualStateGroups = new VisualStateGroupList();

        var commonStates = new VisualStateGroup { Name = "CommonStates" };

        var normalState = new VisualState { Name = "Normal" };

        var mouseOverState = new VisualState { Name = "PointerOver" };
        mouseOverState.Setters.Add(new Setter
        {
            Property = VisualElement.BackgroundColorProperty,
            Value = AppColors.Black.WithAlpha(0.2f)
        });

        commonStates.States.Add(normalState);
        commonStates.States.Add(mouseOverState);

        visualStateGroups.Add(commonStates);

        return visualStateGroups;
    }
}
