<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.UI.DropDownControl"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <ContentView.Resources>
        <ControlTemplate
            x:Key="DropDownButtonControlTemplate">
            <Grid
                BackgroundColor="{TemplateBinding BackgroundColor}">
                <telerik:RadBorder
                    BackgroundColor="{x:Static ui:AppColors.Transparent}"
                    Padding="{ui:EdgeInsets
                        Horizontal={x:Static ui:Dimens.SpacingSm}}"
                    HorizontalOptions="End"
                    VerticalOptions="Center">
                    <Image
                        Aspect="AspectFit"
                        HeightRequest="{x:Static ui:Dimens.Spacing18}"
                        WidthRequest="{x:Static ui:Dimens.Spacing18}"
                        Source="{x:Static ui:Icons.ArrowdownIcon}" />
                </telerik:RadBorder>
            </Grid>
        </ControlTemplate>
    </ContentView.Resources>
    <telerik:RadBorder
        BorderThickness="{Binding BorderThickness, Source={x:Reference this}}"
        BorderColor="{Binding BorderColor, Source={x:Reference this}}"
        CornerRadius="{Binding CornerRadius, Source={x:Reference this}}">
        <telerik:RadComboBox
            Style="{x:Static ui:Styles.ComboboxStyle}"
            DisplayMemberPath="DisplayName"
            BackgroundColor="{Binding BackgroundColorDropDownControl, Source={x:Reference this}}"
            ItemsSource="{Binding ItemsSource, Source={x:Reference this}}"
            SelectedItem="{Binding SelectedItem, Source={x:Reference this}, Mode=TwoWay}"
            DropDownBackgroundColor="{Binding BackgroundColorDropDownControl, Source={x:Reference this}}"
            SelectionChanged="ComboBoxSelectionChanged">
            <telerik:RadComboBox.ItemTemplate>
                <DataTemplate
                    x:DataType="x:Object">
                    <Label
                        Style="{x:Static ui:Styles.DropDownComboboxLabelStyle}"
                        Padding="{ui:EdgeInsets
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}"
                        Text="{Binding DisplayName}" />
                </DataTemplate>
            </telerik:RadComboBox.ItemTemplate>
            <telerik:RadComboBox.SelectedItemTemplate>
                <DataTemplate
                    x:DataType="x:Object">
                    <Label
                        Style="{x:Static ui:Styles.DropDownComboboxLabelStyle}"
                        BackgroundColor="{ui:Color
                            Value={x:Static ui:AppColors.Black},
                            Alpha=0.2}"
                        Padding="{ui:EdgeInsets
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}"
                        Text="{Binding DisplayName}" />
                </DataTemplate>
            </telerik:RadComboBox.SelectedItemTemplate>
            <telerik:RadComboBox.DropDownButtonStyle>
                <Style
                    TargetType="telerik:RadTemplatedButton">
                    <Setter
                        Property="ControlTemplate"
                        Value="{StaticResource DropDownButtonControlTemplate}" />
                    <Setter
                        Property="VerticalOptions"
                        Value="Center" />
                </Style>
            </telerik:RadComboBox.DropDownButtonStyle>
        </telerik:RadComboBox>
    </telerik:RadBorder>
</ContentView>