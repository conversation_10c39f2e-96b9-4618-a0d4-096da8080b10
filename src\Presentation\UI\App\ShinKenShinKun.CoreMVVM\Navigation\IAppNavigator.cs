﻿using CommunityToolkit.Maui.Views;

namespace ShinKenShinKun.CoreMVVM;

public interface IAppNavigator
{
    Task GoBackAsync(bool animated = false, object data = default);

    Task NavigateAsync(string target, bool animated = false, object args = default);

    Task<bool> OpenUrlAsync(string url);

    Task ShareAsync(string text, string title = default);

    Task ShowNotificationDialog(string message, string title = default);
    Task<bool> ShowConfirmationDialog(string message, string title = default);
    Task ShowPopupAsync(Popup view);
}
