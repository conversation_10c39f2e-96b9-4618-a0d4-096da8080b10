﻿using ShinKenShinKunServer.Dtos;
using System.Collections.Generic;

namespace ShinKenShinKunServer.Utils
{

    /// <summary>
    /// Provides methods for securely hashing and verifying passwords using the BCrypt algorithm.
    /// </summary>
    /// <remarks>This class includes functionality for generating password hashes with a salt and verifying passwords
    /// against stored hashes. It is designed to ensure secure password management by leveraging the BCrypt algorithm, which
    /// incorporates salting and adaptive hashing to mitigate brute-force attacks.</remarks>
    internal static class Security
    {
        /// <summary>
        /// Hashes the specified password using BCrypt and returns the hash and salt.
        /// </summary>
        /// <param name="password">The plain text password to hash.</param>
        /// <returns>A <see cref="HashResult"/> containing the hash and salt.</returns>
        internal static (string hash, string salt) HashPassword(string password)
        {
            string salt = BCrypt.Net.BCrypt.GenerateSalt(12);

            string hash = BCrypt.Net.BCrypt.HashPassword(password, salt);

            return (hash, salt);
        }

        /// <summary>
        /// Verifies a password against the stored hash and salt using BCrypt.
        /// </summary>
        /// <param name="password">The plain text password to verify.</param>
        /// <param name="storedHash">The stored hash to compare against.</param>
        /// <param name="storedSalt">The salt used to generate the stored hash.</param>
        /// <returns><c>true</c> if the password matches the stored hash; otherwise, <c>false</c>.</returns>
        internal static bool VerifyPassword(string password, string storedHash, string storedSalt)
        {
            string computedHash = BCrypt.Net.BCrypt.HashPassword(password, storedSalt);

            return computedHash == storedHash;
        }
    }
}