<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.StopTestSelectionPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:StopTestSelectionViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>

    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.SelectionStopTitle}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <telerik:RadBorder
                Style="{x:Static ui:Styles.FrameBorderStyle}"
                Margin="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing10}}"
                Grid.Column="1">
                <!--Guide Items-->
                <app:SelectionTestStateListView
                    MedicalCheckProgresses="{Binding MedicalCheckProgresses}"
                    SelectionChangedCommand="{Binding SelectButtonCommand}" />
            </telerik:RadBorder>
        </Grid>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            BackCommand="{Binding BackCommand}"
            ClientSelected="{Binding ClientSelected}"
            BulkPauseTestsCommand="{Binding StopAllCommand}"
            BulkCancelPauseTestsCommand="{Binding CancelAllCommand}" />
    </Grid>
</mvvm:BasePage>