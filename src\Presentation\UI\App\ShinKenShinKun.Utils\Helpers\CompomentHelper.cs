﻿namespace ShinKenShinKun.Utils;

public class CompomentHelper
{
    public static CompomentStatusModel GetCompomentStatus(string screenId)
    {
        var compomentStatus = new CompomentStatusModel();

        if (!AppConstants.ScreenButtons.TryGetValue(screenId, out var buttonTypes))
            return compomentStatus;

        foreach (var button in buttonTypes)
        {
            switch (button)
            {
                case ButtonType.Title: compomentStatus.IsTitleDisplay = true; break;
                case ButtonType.Home: compomentStatus.IsHomeDisplay = true; break;
                case ButtonType.SwitchMask: compomentStatus.IsSwitchMaskDisplay = true; break;
                case ButtonType.UserLogin: compomentStatus.IsUserLoginDisplay = true; break;
                case ButtonType.SwitchMode: compomentStatus.IsSwitchModeDisplay = true; break;
                case ButtonType.Finish: compomentStatus.IsFinishDisplay = true; break;
                case ButtonType.Logout: compomentStatus.IsLogoutDisplay = true; break;
                case ButtonType.Back: compomentStatus.IsBackDisplay = true; break;
                case ButtonType.EnterId: compomentStatus.IsEnterIdDisplay = true; break;
                case ButtonType.Hold: compomentStatus.IsHoldDisplay = true; break;
                case ButtonType.Pause: compomentStatus.IsPauseDisplay = true; break;
                case ButtonType.Register: compomentStatus.IsRegisterDisplay = true; break;
                case ButtonType.BulkHoldPatients: compomentStatus.IsBulkHoldPatientsDisplay = true; break;
                case ButtonType.ChangeGuidance: compomentStatus.IsChangeGuidanceDisplay = true; break;
                case ButtonType.BulkRegisterHoldPatients: compomentStatus.IsBulkRegisterHoldPatientsDisplay = true; break;
                case ButtonType.BulkReleaseHoldPatients: compomentStatus.IsBulkReleaseHoldPatientsDisplay = true; break;
                case ButtonType.BulkHoldTests: compomentStatus.IsBulkHoldTestsDisplay = true; break;
                case ButtonType.BulkReleaseHoldTests: compomentStatus.IsBulkReleaseHoldTestsDisplay = true; break;
                case ButtonType.BulkPauseTests: compomentStatus.IsBulkPauseTestsDisplay = true; break;
                case ButtonType.BulkCancelPauseTests: compomentStatus.IsBulkCancelPauseTestsDisplay = true; break;
            }
        }

        return compomentStatus;
    }
}
