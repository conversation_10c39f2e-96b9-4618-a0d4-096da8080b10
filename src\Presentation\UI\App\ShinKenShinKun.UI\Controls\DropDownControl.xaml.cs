namespace ShinKenShinKun.UI;

public partial class DropDownControl : ContentView
{
    public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(
        nameof(BorderColor),
        typeof(Color),
        typeof(DropDownControl),
        default);

    public static readonly BindableProperty DisplayMemberPathProperty = BindableProperty.Create(
        nameof(DisplayMemberPath),
        typeof(string),
        typeof(DropDownControl),
        default(string));

    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(
        nameof(ItemsSource),
        typeof(IEnumerable),
        typeof(DropDownControl),
        default);

    public static readonly BindableProperty SelectedItemProperty = BindableProperty.Create(
        nameof(SelectedItem),
        typeof(object),
        typeof(DropDownControl),
        default,
        BindingMode.TwoWay);

    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(Thickness),
        typeof(DropDownControl));

    public static readonly BindableProperty BackgroundColorDropDownControlProperty = BindableProperty.Create(
        nameof(BackgroundColorDropDownControl),
        typeof(Color),
        typeof(DropDownControl));

    public static readonly BindableProperty BorderThicknessProperty = BindableProperty.Create(
        nameof(BorderThickness),
        typeof(Thickness),
        typeof(DropDownControl));

    public DropDownControl()
    {
        InitializeComponent();
    }

    public event EventHandler<Telerik.Maui.Controls.ComboBoxSelectionChangedEventArgs>? SelectionChanged;

    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    public string DisplayMemberPath
    {
        get => (string)GetValue(DisplayMemberPathProperty);
        set => SetValue(DisplayMemberPathProperty, value);
    }

    public IEnumerable ItemsSource
    {
        get => (IEnumerable)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    public object SelectedItem
    {
        get => (object)GetValue(SelectedItemProperty);
        set => SetValue(SelectedItemProperty, value);
    }

    public Thickness CornerRadius
    {
        get => (Thickness)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public Color BackgroundColorDropDownControl
    {
        get => (Color)GetValue(BackgroundColorDropDownControlProperty);
        set => SetValue(BackgroundColorDropDownControlProperty, value);
    }

    public Thickness BorderThickness
    {
        get => (Thickness)GetValue(BorderThicknessProperty);
        set => SetValue(BorderThicknessProperty, value);
    }

    private void ComboBoxSelectionChanged(object sender, Telerik.Maui.Controls.ComboBoxSelectionChangedEventArgs e)
    {
        SelectionChanged?.Invoke(sender, e);
    }
}