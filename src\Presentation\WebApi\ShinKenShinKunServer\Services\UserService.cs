﻿using ShinKenShinKunServer.Common;
using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.UserDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Enums;
using ShinKenShinKunServer.Utils;
using System;
using System.Text.RegularExpressions;

namespace ShinKenShinKunServer.Services
{
    public class UserService
    {
        private readonly Logger logger = null;

        public UserService()
        {
            logger = Logger.GetInstance();
        }

        /// <summary>
        /// Attempts to authenticate a user based on the provided login ID and password.
        /// </summary>
        /// <remarks>The method validates the provided login credentials and checks them against stored
        /// user data. If the credentials are valid, the login is successful, and the user's name is included in the
        /// returned record. If the credentials are invalid or the user does not exist, the login attempt
        /// fails.</remarks>
        /// <param name="loginId">The unique identifier for the user attempting to log in. Cannot be null or empty.</param>
        /// <param name="password">The password associated with the login ID. Cannot be null or empty.</param>
        /// <returns>A <see cref="LoginRecord"/> representing the result of the login attempt.  The record contains the login
        /// status and, if successful, the user's name.</returns>
        internal LoginRecord StrLogin(string loginId, string password)
        {
            if (!ValidateLogin(loginId, password))
            {
                return ReturnLogin(LoginStatus.Invalid);
            }

            UserTableAdapter userAdapter = new UserTableAdapter();
            UserDataSet.UserDataTable userTable;

            try
            {              
                userTable = userAdapter.GetDataByLoginId(loginId);
            }
            catch (Exception ex)
            {
                logger.logWrite(ex.ToString());
                throw;
            }

            if (userTable.Rows.Count > 0)
            {
                UserDataSet.UserRow user = userTable[0];
                //bool isPass = Security.VerifyPassword(password, user.PasswordHash, user.Salt);

                //if (isPass)
                //{
                //    return ReturnLogin(LoginStatus.Successed, user.UserName);
                //}
                return ReturnLogin(LoginStatus.Successed, user.UserName);
            }
            
            return ReturnLogin(LoginStatus.Failed);
        }

        /// <summary>
        /// Registers a new user in the system.
        /// </summary>
        /// <remarks>This method validates the input parameters and checks for existing users with the
        /// same login ID or email. If validation passes and no conflicts are found, the user is added to the database
        /// with a hashed password.</remarks>
        /// <param name="loginId">The unique login identifier for the user. Cannot be null or empty.</param>
        /// <param name="userName">The name of the user. Cannot be null or empty.</param>
        /// <param name="email">The email address of the user. Must be a valid email format and cannot be null or empty.</param>
        /// <param name="roleId">The role identifier for the user. Must correspond to a valid role in the system.</param>
        /// <param name="password">The password for the user account. Cannot be null or empty.</param>
        /// <returns>A byte value representing the registration status. Possible values include: <list type="bullet">
        /// <item><description><see cref="RegisterStatus.Successed"/> if the registration is
        /// successful.</description></item> <item><description><see cref="RegisterStatus.Existed"/> if a user with the
        /// same login ID or email already exists.</description></item> <item><description><see
        /// cref="RegisterStatus.Invalid"/> if the input parameters are invalid.</description></item>
        /// <item><description><see cref="RegisterStatus.Failed"/> if the registration process fails due to an
        /// unexpected error.</description></item> </list></returns>
        internal byte StrRegister(string loginId, string userName, string email, byte roleId, string password)
        {
            if (!ValidateRegister(loginId, userName, email, roleId, password))
            {
                return ReturnRegister(RegisterStatus.Invalid);
            }

            int insertStatus;
            UserDataSet.UserDataTable userTable;
            UserTableAdapter userAdapter = new UserTableAdapter();

            try
            {
                userTable = userAdapter.GetDataByLoginIdWithEmail(loginId, email);
            }
            catch (Exception ex)
            {
                logger.logWrite(ex.ToString());
                throw;
            }

            if (userTable.Rows.Count > 0)
            {
                return ReturnRegister(RegisterStatus.Existed);
            }

            (string hash, string salt) = Security.HashPassword(password);

            try
            {
                insertStatus =  userAdapter.Insert(
                    UserId: Guid.NewGuid().ToString(),
                    UserName: userName,
                    LoginId: loginId,
                    PasswordHash: hash,
                    Salt: salt,
                    RoleId: roleId,
                    IsLocked: false,
                    IsValid: false,
                    Email: email,
                    LoginState: (byte)LoginState.Offline,
                    LoginFailureCount: 0,
                    LastLoginAt: DateTime.Now);
            }
            catch (Exception ex)
            {
                logger.logWrite(ex.ToString());
                throw;
            }

            if (insertStatus == (int)CommandStatus.Successed)
            {
                return ReturnRegister(RegisterStatus.Successed);
            }
            return ReturnRegister(RegisterStatus.Failed);
        }

        /// <summary>
        /// Creates a new <see cref="LoginRecord"/> instance based on the specified login status and user name.
        /// </summary>
        /// <param name="loginStatus">The status of the login operation, represented as a <see cref="LoginStatus"/> enumeration.</param>
        /// <param name="userName">The name of the user associated with the login. If not specified, defaults to an empty string.</param>
        /// <returns>A <see cref="LoginRecord"/> object containing the provided login status and user name.</returns>
        private static LoginRecord ReturnLogin(LoginStatus loginStatus, string userName = "")
        {
            return new LoginRecord
            {
                Status = (byte)loginStatus,
                UserName = userName
            };
        }

        /// <summary>
        /// Converts the specified <see cref="RegisterStatus"/> value to its corresponding byte representation.
        /// </summary>
        /// <param name="registerStatus">The <see cref="RegisterStatus"/> value to convert.</param>
        /// <returns>The byte representation of the specified <see cref="RegisterStatus"/> value.</returns>
        private static byte ReturnRegister(RegisterStatus registerStatus)
        {
            return (byte)registerStatus;
        }

        /// <summary>
        /// Validates the login credentials against the defined criteria.
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        private static bool ValidateLogin(string loginId, string password)
        {
            return
                !string.IsNullOrEmpty(loginId) &&
                !string.IsNullOrEmpty(password) &&

                loginId.Length >= Constants.LoginIdMinLength &&
                loginId.Length <= Constants.LoginIdMaxLength &&

                password.Length >= Constants.PasswordMinLength &&
                password.Length <= Constants.PasswordMaxLength &&

                Regex.IsMatch(loginId, Constants.LoginidPattern) &&
                Regex.IsMatch(password, Constants.PasswordPattern);
        }

        /// <summary>
        /// Validates the registration details against the defined criteria.
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="userName"></param>
        /// <param name="email"></param>
        /// <param name="roleId"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        private static bool ValidateRegister(string loginId, string userName, string email, byte roleId, string password)
        {
            return
                !string.IsNullOrEmpty(loginId) &&
                !string.IsNullOrEmpty(userName) &&
                !string.IsNullOrEmpty(email) &&
                !string.IsNullOrEmpty(password) &&

                loginId.Length >= Constants.LoginIdMinLength &&
                loginId.Length <= Constants.LoginIdMaxLength &&

                userName.Length >= Constants.UserNameMinLength &&
                userName.Length <= Constants.UserNameMaxLength &&

                email.Length >= Constants.EmailMinLength &&
                email.Length <= Constants.EmailMaxLength &&

                password.Length >= Constants.PasswordMinLength &&
                password.Length <= Constants.PasswordMaxLength &&

                Enum.IsDefined(typeof(Role), (int)roleId) &&

                Regex.IsMatch(loginId, Constants.LoginidPattern) &&
                Regex.IsMatch(password, Constants.PasswordPattern);
        }
    }
}