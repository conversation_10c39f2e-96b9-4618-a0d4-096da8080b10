<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualPatientsInfo"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:DataType="app:IndividualProgressViewModel"
    x:Name="this">
    <Grid
        Padding="{x:Static ui:Dimens.SpacingSm2}">
        <telerik:RadBorder
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingS}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <Grid
                RowSpacing="{x:Static ui:Dimens.SpacingMd}"
                RowDefinitions="auto,*">
                <telerik:RadButton
                    Grid.Row="0"
                    Text="{x:Static resources:AppResources.TestStatus}"
                    TextColor="{x:Static ui:AppColors.White}"
                    Grid.Column="0"
                    HorizontalOptions="Start"
                    Style="{Static ui:Styles.NormalButton}">
                </telerik:RadButton>
                <Grid
                    RowDefinitions="auto,auto,*"
                    Grid.Row="1"
                    Padding="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingMd}}"
                    RowSpacing="10">

                    <app:ExaminationBarView
                        Grid.Row="0"
                        ItemSelected="{Binding MedicalCheckSelected}"
                        ItemsSource="{Binding MedicalCheckClients}" />
                    <app:IndividualStateDisplayView
                        Grid.Row="1"
                        StatusCount="{Binding StateCounter}" />
                    <app:IndividualTableView
                        Grid.Row="2"
                        ItemsSource="{Binding ClientInformations}"
                        ItemSelected="{Binding ClientSelected}"
                        IsMaskMode="{Binding IsMaskMode}" />
                </Grid>
            </Grid>
        </telerik:RadBorder>
    </Grid>
</ContentView>
