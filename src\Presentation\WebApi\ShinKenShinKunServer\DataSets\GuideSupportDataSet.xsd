<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="GuideSupportDataSet" targetNamespace="http://tempuri.org/GuideSupportDataSet.xsd" xmlns:mstns="http://tempuri.org/GuideSupportDataSet.xsd" xmlns="http://tempuri.org/GuideSupportDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="2" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupConnectionString" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
          <Connection ConnectionStringObject="Data Source=(local)\ARCTECDATABASE2;Initial Catalog=GenkiPlazaDatabase;Persist Security Info=True;User ID=sa;Password=*************" IsAppSettingsProperty="False" Modifier="Assembly" Name="alnilam\arctecdatabase2.TjkNakanoDatabase.dbo" ParameterPrefix="@" Provider="System.Data.SqlClient">
          </Connection>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupV3ConnectionString" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupV3ConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupV3ConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GuideDetailMasterTableAdapter" GeneratorDataComponentClassName="GuideDetailMasterTableAdapter" Name="GuideDetailMaster" UserDataComponentName="GuideDetailMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GuideDetailMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GuideDetailMaster] WHERE (([GuideMasterDetailId] = @Original_GuideMasterDetailId) AND ([GuideMasterId] = @Original_GuideMasterId) AND ([GuidePriority] = @Original_GuidePriority) AND ([GroupId] = @Original_GroupId) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuidePriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuidePriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GuideDetailMaster] ([GuideMasterDetailId], [GuideMasterId], [GuidePriority], [GroupId], [LastUpdate]) VALUES (@GuideMasterDetailId, @GuideMasterId, @GuidePriority, @GroupId, @LastUpdate);
SELECT GuideMasterDetailId, GuideMasterId, GuidePriority, GroupId, LastUpdate FROM GuideDetailMaster WHERE (GuideMasterDetailId = @GuideMasterDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuidePriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuidePriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GuideMasterDetailId, GuideMasterId, GuidePriority, GroupId, LastUpdate FROM dbo.GuideDetailMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GuideDetailMaster] SET [GuideMasterDetailId] = @GuideMasterDetailId, [GuideMasterId] = @GuideMasterId, [GuidePriority] = @GuidePriority, [GroupId] = @GroupId, [LastUpdate] = @LastUpdate WHERE (([GuideMasterDetailId] = @Original_GuideMasterDetailId) AND ([GuideMasterId] = @Original_GuideMasterId) AND ([GuidePriority] = @Original_GuidePriority) AND ([GroupId] = @Original_GroupId) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT GuideMasterDetailId, GuideMasterId, GuidePriority, GroupId, LastUpdate FROM GuideDetailMaster WHERE (GuideMasterDetailId = @GuideMasterDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuidePriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuidePriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuidePriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuidePriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GuideMasterDetailId" DataSetColumn="GuideMasterDetailId" />
              <Mapping SourceColumn="GuideMasterId" DataSetColumn="GuideMasterId" />
              <Mapping SourceColumn="GuidePriority" DataSetColumn="GuidePriority" />
              <Mapping SourceColumn="GroupId" DataSetColumn="GroupId" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GroupMasterTableAdapter" GeneratorDataComponentClassName="GroupMasterTableAdapter" Name="GroupMaster" UserDataComponentName="GroupMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GroupMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GroupMaster] WHERE (([GroupId] = @Original_GroupId) AND ([GroupName] = @Original_GroupName) AND ([GuideType] = @Original_GuideType) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GroupMaster] ([GroupId], [GroupName], [GuideType], [LastUpdate]) VALUES (@GroupId, @GroupName, @GuideType, @LastUpdate);
SELECT GroupId, GroupName, GuideType, LastUpdate FROM GroupMaster WHERE (GroupId = @GroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GroupId, GroupName, GuideType, LastUpdate FROM dbo.GroupMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GroupMaster] SET [GroupId] = @GroupId, [GroupName] = @GroupName, [GuideType] = @GuideType, [LastUpdate] = @LastUpdate WHERE (([GroupId] = @Original_GroupId) AND ([GroupName] = @Original_GroupName) AND ([GuideType] = @Original_GuideType) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT GroupId, GroupName, GuideType, LastUpdate FROM GroupMaster WHERE (GroupId = @GroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GroupId" DataSetColumn="GroupId" />
              <Mapping SourceColumn="GroupName" DataSetColumn="GroupName" />
              <Mapping SourceColumn="GuideType" DataSetColumn="GuideType" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ChildGroupMasterTableAdapter" GeneratorDataComponentClassName="ChildGroupMasterTableAdapter" Name="ChildGroupMaster" UserDataComponentName="ChildGroupMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ChildGroupMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[ChildGroupMaster] WHERE (([ChildGroupId] = @Original_ChildGroupId) AND ((@IsNull_ChildGroupName = 1 AND [ChildGroupName] IS NULL) OR ([ChildGroupName] = @Original_ChildGroupName)) AND ([ChildGuideType] = @Original_ChildGuideType) AND ([ClientTermCount] = @Original_ClientTermCount) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ChildGroupName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ChildGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGuideType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ClientTermCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClientTermCount" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[ChildGroupMaster] ([ChildGroupId], [ChildGroupName], [ChildGuideType], [ClientTermCount], [LastUpdate]) VALUES (@ChildGroupId, @ChildGroupName, @ChildGuideType, @ClientTermCount, @LastUpdate);
SELECT ChildGroupId, ChildGroupName, ChildGuideType, ClientTermCount, LastUpdate FROM ChildGroupMaster WHERE (ChildGroupId = @ChildGroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ChildGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGuideType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ClientTermCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClientTermCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT ChildGroupId, ChildGroupName, ChildGuideType, ClientTermCount, LastUpdate FROM dbo.ChildGroupMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[ChildGroupMaster] SET [ChildGroupId] = @ChildGroupId, [ChildGroupName] = @ChildGroupName, [ChildGuideType] = @ChildGuideType, [ClientTermCount] = @ClientTermCount, [LastUpdate] = @LastUpdate WHERE (([ChildGroupId] = @Original_ChildGroupId) AND ((@IsNull_ChildGroupName = 1 AND [ChildGroupName] IS NULL) OR ([ChildGroupName] = @Original_ChildGroupName)) AND ([ChildGuideType] = @Original_ChildGuideType) AND ([ClientTermCount] = @Original_ClientTermCount) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT ChildGroupId, ChildGroupName, ChildGuideType, ClientTermCount, LastUpdate FROM ChildGroupMaster WHERE (ChildGroupId = @ChildGroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ChildGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGuideType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ClientTermCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClientTermCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ChildGroupName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ChildGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ChildGroupName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGuideType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGuideType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ClientTermCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClientTermCount" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ChildGroupId" DataSetColumn="ChildGroupId" />
              <Mapping SourceColumn="ChildGroupName" DataSetColumn="ChildGroupName" />
              <Mapping SourceColumn="ChildGuideType" DataSetColumn="ChildGuideType" />
              <Mapping SourceColumn="ClientTermCount" DataSetColumn="ClientTermCount" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ChildGroupTermMasterTableAdapter" GeneratorDataComponentClassName="ChildGroupTermMasterTableAdapter" Name="ChildGroupTermMaster" UserDataComponentName="ChildGroupTermMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ChildGroupTermMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ChildGroupTermId, ChildGroupId, IPAddress, LastUpdate
FROM            ChildGroupTermMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ChildGroupTermId" DataSetColumn="ChildGroupTermId" />
              <Mapping SourceColumn="ChildGroupId" DataSetColumn="ChildGroupId" />
              <Mapping SourceColumn="IPAddress" DataSetColumn="IPAddress" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByGuideFlg" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataByGuideFlg" GeneratorSourceName="FillByGuideFlg" GetMethodModifier="Public" GetMethodName="GetDataByGuideFlg" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataByGuideFlg" UserSourceName="FillByGuideFlg">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ChildGroupTermMaster.ChildGroupTermId, ChildGroupTermMaster.ChildGroupId, 
                      ChildGroupTermMaster.IPAddress, ChildGroupTermMaster.LastUpdate, 
                      TermGuideData.GuideFlg, TermGuideData.TermFlg
FROM            ChildGroupTermMaster INNER JOIN
                      TermGuideData ON 
                      ChildGroupTermMaster.IPAddress = TermGuideData.IPAddress
WHERE           (ChildGroupTermMaster.ChildGroupId = @ChildGroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ChildGroupId" ColumnName="ChildGroupId" DataSourceName="ShibaParkDatabase.dbo.ChildGroupTermMaster" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ExamSettingMasterTableAdapter" GeneratorDataComponentClassName="ExamSettingMasterTableAdapter" Name="ExamSettingMaster" UserDataComponentName="ExamSettingMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ExamSettingMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[ExamSettingMaster] WHERE (([ExamSettingId] = @Original_ExamSettingId) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ExamTime] = @Original_ExamTime) AND ([WaitTime] = @Original_WaitTime) AND ([WaitPeople] = @Original_WaitPeople) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamSettingId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamSettingId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_WaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_WaitPeople" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitPeople" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[ExamSettingMaster] ([ExamSettingId], [MedicalCheckNo], [ExamTime], [WaitTime], [WaitPeople], [LastUpdate]) VALUES (@ExamSettingId, @MedicalCheckNo, @ExamTime, @WaitTime, @WaitPeople, @LastUpdate);
SELECT ExamSettingId, MedicalCheckNo, ExamTime, WaitTime, WaitPeople, LastUpdate FROM ExamSettingMaster WHERE (ExamSettingId = @ExamSettingId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamSettingId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamSettingId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@WaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@WaitPeople" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitPeople" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT ExamSettingId, MedicalCheckNo, ExamTime, WaitTime, WaitPeople, LastUpdate FROM dbo.ExamSettingMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[ExamSettingMaster] SET [ExamSettingId] = @ExamSettingId, [MedicalCheckNo] = @MedicalCheckNo, [ExamTime] = @ExamTime, [WaitTime] = @WaitTime, [WaitPeople] = @WaitPeople, [LastUpdate] = @LastUpdate WHERE (([ExamSettingId] = @Original_ExamSettingId) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ExamTime] = @Original_ExamTime) AND ([WaitTime] = @Original_WaitTime) AND ([WaitPeople] = @Original_WaitPeople) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT ExamSettingId, MedicalCheckNo, ExamTime, WaitTime, WaitPeople, LastUpdate FROM ExamSettingMaster WHERE (ExamSettingId = @ExamSettingId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamSettingId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamSettingId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@WaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@WaitPeople" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitPeople" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamSettingId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamSettingId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_WaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_WaitPeople" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="WaitPeople" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ExamSettingId" DataSetColumn="ExamSettingId" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ExamTime" DataSetColumn="ExamTime" />
              <Mapping SourceColumn="WaitTime" DataSetColumn="WaitTime" />
              <Mapping SourceColumn="WaitPeople" DataSetColumn="WaitPeople" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ExamSettingMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ExamSettingId, MedicalCheckNo, ExamTime, WaitTime, WaitPeople, 
                      LastUpdate
FROM            ExamSettingMaster
WHERE           (MedicalCheckNo = @MedicalCheckNo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="ShibaParkDatabase.dbo.ExamSettingMaster" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TermGuideDataTableAdapter" GeneratorDataComponentClassName="TermGuideDataTableAdapter" Name="TermGuideData" UserDataComponentName="TermGuideDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.TermGuideData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[TermGuideData] WHERE (([IPAddress] = @Original_IPAddress) AND ([GuideFlg] = @Original_GuideFlg) AND ([TermFlg] = @Original_TermFlg) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_TermFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TermFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[TermGuideData] ([IPAddress], [GuideFlg], [TermFlg], [LastUpdate]) VALUES (@IPAddress, @GuideFlg, @TermFlg, @LastUpdate);
SELECT IPAddress, GuideFlg, TermFlg, LastUpdate FROM TermGuideData WHERE (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TermFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TermFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT IPAddress, GuideFlg, TermFlg, LastUpdate FROM dbo.TermGuideData</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[TermGuideData] SET [IPAddress] = @IPAddress, [GuideFlg] = @GuideFlg, [TermFlg] = @TermFlg, [LastUpdate] = @LastUpdate WHERE (([IPAddress] = @Original_IPAddress) AND ([GuideFlg] = @Original_GuideFlg) AND ([TermFlg] = @Original_TermFlg) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT IPAddress, GuideFlg, TermFlg, LastUpdate FROM TermGuideData WHERE (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TermFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TermFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_TermFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TermFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IPAddress" DataSetColumn="IPAddress" />
              <Mapping SourceColumn="GuideFlg" DataSetColumn="GuideFlg" />
              <Mapping SourceColumn="TermFlg" DataSetColumn="TermFlg" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.TermGuideData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          IPAddress, GuideFlg, TermFlg, LastUpdate
FROM            TermGuideData
WHERE           (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="IPAddress" ColumnName="IPAddress" DataSourceName="ShibaParkDatabase.dbo.TermGuideData" DataTypeServer="varchar(15)" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="15" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GroupDetailMasterTableAdapter" GeneratorDataComponentClassName="GroupDetailMasterTableAdapter" Name="GroupDetailMaster" UserDataComponentName="GroupDetailMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GroupDetailMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GroupDetailMaster] WHERE (([GroupDetailId] = @Original_GroupDetailId) AND ([GroupId] = @Original_GroupId) AND ([GroupPriority] = @Original_GroupPriority) AND ([ChildGroupId] = @Original_ChildGroupId) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupPriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GroupDetailMaster] ([GroupDetailId], [GroupId], [GroupPriority], [ChildGroupId], [LastUpdate]) VALUES (@GroupDetailId, @GroupId, @GroupPriority, @ChildGroupId, @LastUpdate);
SELECT GroupDetailId, GroupId, GroupPriority, ChildGroupId, LastUpdate FROM GroupDetailMaster WHERE (GroupDetailId = @GroupDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupPriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GroupDetailId, GroupId, GroupPriority, ChildGroupId, LastUpdate FROM dbo.GroupDetailMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GroupDetailMaster] SET [GroupDetailId] = @GroupDetailId, [GroupId] = @GroupId, [GroupPriority] = @GroupPriority, [ChildGroupId] = @ChildGroupId, [LastUpdate] = @LastUpdate WHERE (([GroupDetailId] = @Original_GroupDetailId) AND ([GroupId] = @Original_GroupId) AND ([GroupPriority] = @Original_GroupPriority) AND ([ChildGroupId] = @Original_ChildGroupId) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT GroupDetailId, GroupId, GroupPriority, ChildGroupId, LastUpdate FROM GroupDetailMaster WHERE (GroupDetailId = @GroupDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupPriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GroupPriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GroupDetailId" DataSetColumn="GroupDetailId" />
              <Mapping SourceColumn="GroupId" DataSetColumn="GroupId" />
              <Mapping SourceColumn="GroupPriority" DataSetColumn="GroupPriority" />
              <Mapping SourceColumn="ChildGroupId" DataSetColumn="ChildGroupId" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GuideRootMasterTableAdapter" GeneratorDataComponentClassName="GuideRootMasterTableAdapter" Name="GuideRootMaster" UserDataComponentName="GuideRootMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GuideRootMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GuideRootMaster] WHERE (([GuideMasterId] = @Original_GuideMasterId) AND ((@IsNull_GuideName = 1 AND [GuideName] IS NULL) OR ([GuideName] = @Original_GuideName)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_GuideName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GuideName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GuideRootMaster] ([GuideMasterId], [GuideName]) VALUES (@GuideMasterId, @GuideName);
SELECT GuideMasterId, GuideName FROM GuideRootMaster WHERE (GuideMasterId = @GuideMasterId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GuideName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GuideMasterId, GuideName FROM dbo.GuideRootMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GuideRootMaster] SET [GuideMasterId] = @GuideMasterId, [GuideName] = @GuideName WHERE (([GuideMasterId] = @Original_GuideMasterId) AND ((@IsNull_GuideName = 1 AND [GuideName] IS NULL) OR ([GuideName] = @Original_GuideName)));
SELECT GuideMasterId, GuideName FROM GuideRootMaster WHERE (GuideMasterId = @GuideMasterId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GuideName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_GuideName" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GuideName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GuideMasterId" DataSetColumn="GuideMasterId" />
              <Mapping SourceColumn="GuideName" DataSetColumn="GuideName" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GuideDivisionMasterTableAdapter" GeneratorDataComponentClassName="GuideDivisionMasterTableAdapter" Name="GuideDivisionMaster" UserDataComponentName="GuideDivisionMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GuideDivisionMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GuideDivisionMaster] WHERE (([GuideDivisionId] = @Original_GuideDivisionId) AND ([GuideDivision] = @Original_GuideDivision) AND ([GuideDivisionValue] = @Original_GuideDivisionValue) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivision" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivision" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GuideDivisionValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideDivisionValue" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GuideDivisionMaster] ([GuideDivisionId], [GuideDivision], [GuideDivisionValue], [LastUpdate]) VALUES (@GuideDivisionId, @GuideDivision, @GuideDivisionValue, @LastUpdate);
SELECT GuideDivisionId, GuideDivision, GuideDivisionValue, LastUpdate FROM GuideDivisionMaster WHERE (GuideDivisionId = @GuideDivisionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivision" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivision" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GuideDivisionValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideDivisionValue" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GuideDivisionId, GuideDivision, GuideDivisionValue, LastUpdate FROM dbo.GuideDivisionMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GuideDivisionMaster] SET [GuideDivisionId] = @GuideDivisionId, [GuideDivision] = @GuideDivision, [GuideDivisionValue] = @GuideDivisionValue, [LastUpdate] = @LastUpdate WHERE (([GuideDivisionId] = @Original_GuideDivisionId) AND ([GuideDivision] = @Original_GuideDivision) AND ([GuideDivisionValue] = @Original_GuideDivisionValue) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT GuideDivisionId, GuideDivision, GuideDivisionValue, LastUpdate FROM GuideDivisionMaster WHERE (GuideDivisionId = @GuideDivisionId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivision" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivision" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GuideDivisionValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideDivisionValue" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivision" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivision" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GuideDivisionValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GuideDivisionValue" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GuideDivisionId" DataSetColumn="GuideDivisionId" />
              <Mapping SourceColumn="GuideDivision" DataSetColumn="GuideDivision" />
              <Mapping SourceColumn="GuideDivisionValue" DataSetColumn="GuideDivisionValue" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GuideMasterTableAdapter" GeneratorDataComponentClassName="GuideMasterTableAdapter" Name="GuideMaster" UserDataComponentName="GuideMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GuideMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GuideMaster] WHERE (([GuideId] = @Original_GuideId) AND ([GuideDivisionId] = @Original_GuideDivisionId) AND ([GuideMasterId] = @Original_GuideMasterId) AND ([Priority] = @Original_Priority) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GuideMaster] ([GuideId], [GuideDivisionId], [GuideMasterId], [Priority], [LastUpdate]) VALUES (@GuideId, @GuideDivisionId, @GuideMasterId, @Priority, @LastUpdate);
SELECT GuideId, GuideDivisionId, GuideMasterId, Priority, LastUpdate FROM GuideMaster WHERE (GuideId = @GuideId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT GuideId, GuideDivisionId, GuideMasterId, Priority, LastUpdate FROM dbo.GuideMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GuideMaster] SET [GuideId] = @GuideId, [GuideDivisionId] = @GuideDivisionId, [GuideMasterId] = @GuideMasterId, [Priority] = @Priority, [LastUpdate] = @LastUpdate WHERE (([GuideId] = @Original_GuideId) AND ([GuideDivisionId] = @Original_GuideDivisionId) AND ([GuideMasterId] = @Original_GuideMasterId) AND ([Priority] = @Original_Priority) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT GuideId, GuideDivisionId, GuideMasterId, Priority, LastUpdate FROM GuideMaster WHERE (GuideId = @GuideId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideDivisionId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideDivisionId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GuideId" DataSetColumn="GuideId" />
              <Mapping SourceColumn="GuideDivisionId" DataSetColumn="GuideDivisionId" />
              <Mapping SourceColumn="GuideMasterId" DataSetColumn="GuideMasterId" />
              <Mapping SourceColumn="Priority" DataSetColumn="Priority" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientGuideDataTableAdapter" GeneratorDataComponentClassName="ClientGuideDataTableAdapter" Name="ClientGuideData" UserDataComponentName="ClientGuideDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ClientGuideData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[ClientGuideData] WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([RegistrationNo] = @Original_RegistrationNo) AND ([GuideMasterID] = @Original_GuideMasterID) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_RegistrationNo" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RegistrationNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[ClientGuideData] ([MedicalCheckDate], [ClientID], [Division], [RegistrationNo], [GuideMasterID], [LastUpdate]) VALUES (@MedicalCheckDate, @ClientID, @Division, @RegistrationNo, @GuideMasterID, @LastUpdate);
SELECT MedicalCheckDate, ClientID, Division, RegistrationNo, GuideMasterID, LastUpdate FROM ClientGuideData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RegistrationNo" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RegistrationNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT MedicalCheckDate, ClientID, Division, RegistrationNo, GuideMasterID, LastUpdate FROM dbo.ClientGuideData</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[ClientGuideData] SET [MedicalCheckDate] = @MedicalCheckDate, [ClientID] = @ClientID, [Division] = @Division, [RegistrationNo] = @RegistrationNo, [GuideMasterID] = @GuideMasterID, [LastUpdate] = @LastUpdate WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([RegistrationNo] = @Original_RegistrationNo) AND ([GuideMasterID] = @Original_GuideMasterID) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT MedicalCheckDate, ClientID, Division, RegistrationNo, GuideMasterID, LastUpdate FROM ClientGuideData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RegistrationNo" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RegistrationNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@GuideMasterID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_RegistrationNo" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RegistrationNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_GuideMasterID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="GuideMasterID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckDate" DataSetColumn="MedicalCheckDate" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Division" DataSetColumn="Division" />
              <Mapping SourceColumn="RegistrationNo" DataSetColumn="RegistrationNo" />
              <Mapping SourceColumn="GuideMasterID" DataSetColumn="GuideMasterID" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByChildID" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataByChildID" GeneratorSourceName="FillByChildID" GetMethodModifier="Public" GetMethodName="GetDataByChildID" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataByChildID" UserSourceName="FillByChildID">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ClientGuideData.MedicalCheckDate, ClientGuideData.ClientID, 
                      ClientGuideData.Division, ClientGuideData.RegistrationNo, 
                      ClientGuideData.GuideMasterID, ClientGuideData.LastUpdate, 
                      GroupDetailMaster.GroupId, ChildGroupMaster.ChildGroupId, 
                      ChildGroupMaster.ChildGroupName, ChildGroupMaster.ChildGuideType, 
                      GuideDetailMaster.GuidePriority, GroupDetailMaster.GroupPriority
FROM            ChildGroupMaster INNER JOIN
                      GroupDetailMaster ON 
                      ChildGroupMaster.ChildGroupId = GroupDetailMaster.ChildGroupId INNER JOIN
                      ClientGuideData INNER JOIN
                      GuideDetailMaster ON 
                      ClientGuideData.GuideMasterID = GuideDetailMaster.GuideMasterId ON 
                      GroupDetailMaster.GroupId = GuideDetailMaster.GroupId
WHERE           (ClientGuideData.MedicalCheckDate = @MedicalCheckDate) AND 
                      (ClientGuideData.ClientID = @ClientID) AND 
                      (ClientGuideData.Division = @Division)
ORDER BY     GuideDetailMaster.GuidePriority, GroupDetailMaster.GroupPriority</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckDate" ColumnName="MedicalCheckDate" DataSourceName="ShibaParkDatabase.dbo.ClientGuideData" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="ShibaParkDatabase.dbo.ClientGuideData" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Division" ColumnName="Division" DataSourceName="ShibaParkDatabase.dbo.ClientGuideData" DataTypeServer="nvarchar(3)" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="3" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ChildGroupDetailMasterTableAdapter" GeneratorDataComponentClassName="ChildGroupDetailMasterTableAdapter" Name="ChildGroupDetailMaster" UserDataComponentName="ChildGroupDetailMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.ChildGroupDetailMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[ChildGroupDetailMaster] WHERE (([ChildGroupDetailId] = @Original_ChildGroupDetailId) AND ([ChildGroupId] = @Original_ChildGroupId) AND ([ChildGroupPriority] = @Original_ChildGroupPriority) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupPriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[ChildGroupDetailMaster] ([ChildGroupDetailId], [ChildGroupId], [ChildGroupPriority], [MedicalCheckNo], [LastUpdate]) VALUES (@ChildGroupDetailId, @ChildGroupId, @ChildGroupPriority, @MedicalCheckNo, @LastUpdate);
SELECT ChildGroupDetailId, ChildGroupId, ChildGroupPriority, MedicalCheckNo, LastUpdate FROM ChildGroupDetailMaster WHERE (ChildGroupDetailId = @ChildGroupDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupPriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT ChildGroupDetailId, ChildGroupId, ChildGroupPriority, MedicalCheckNo, LastUpdate FROM dbo.ChildGroupDetailMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[ChildGroupDetailMaster] SET [ChildGroupDetailId] = @ChildGroupDetailId, [ChildGroupId] = @ChildGroupId, [ChildGroupPriority] = @ChildGroupPriority, [MedicalCheckNo] = @MedicalCheckNo, [LastUpdate] = @LastUpdate WHERE (([ChildGroupDetailId] = @Original_ChildGroupDetailId) AND ([ChildGroupId] = @Original_ChildGroupId) AND ([ChildGroupPriority] = @Original_ChildGroupPriority) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT ChildGroupDetailId, ChildGroupId, ChildGroupPriority, MedicalCheckNo, LastUpdate FROM ChildGroupDetailMaster WHERE (ChildGroupDetailId = @ChildGroupDetailId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupDetailId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildGroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupPriority" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupDetailId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupDetailId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ChildGroupPriority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildGroupPriority" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ChildGroupDetailId" DataSetColumn="ChildGroupDetailId" />
              <Mapping SourceColumn="ChildGroupId" DataSetColumn="ChildGroupId" />
              <Mapping SourceColumn="ChildGroupPriority" DataSetColumn="ChildGroupPriority" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            ChildGroupDetailMaster.ChildGroupDetailId, ChildGroupDetailMaster.ChildGroupId, ChildGroupDetailMaster.ChildGroupPriority, ChildGroupDetailMaster.MedicalCheckNo, 
                        ChildGroupDetailMaster.LastUpdate, MedicalCheckList.MedicalCheckName, NeedCheckup.CheckObjectNo1, NeedCheckup.CheckObjectNo2, NeedCheckup.CheckObjectNo3, 
                        NeedCheckup.CheckObjectNo4, NeedCheckup.CheckObjectNo5, NeedCheckup.CheckObjectNo6, NeedCheckup.CheckObjectNo7, NeedCheckup.CheckObjectNo8, 
                        NeedCheckup.CheckObjectNo9, NeedCheckup.CheckObjectNo10, NeedCheckup.CheckObjectNo11, NeedCheckup.CheckObjectNo12, NeedCheckup.CheckObjectNo13, 
                        NeedCheckup.CheckObjectNo14, NeedCheckup.CheckObjectNo15, NeedCheckup.CheckObjectNo16, NeedCheckup.CheckObjectNo17, NeedCheckup.CheckObjectNo18, 
                        NeedCheckup.CheckObjectNo19, NeedCheckup.CheckObjectNo20, NeedCheckup.CheckObjectNo21, NeedCheckup.CheckObjectNo22, NeedCheckup.CheckObjectNo23, 
                        NeedCheckup.CheckObjectNo24, NeedCheckup.CheckObjectNo25, NeedCheckup.CheckObjectNo26, NeedCheckup.CheckObjectNo27, NeedCheckup.CheckObjectNo28, 
                        NeedCheckup.CheckObjectNo29, NeedCheckup.CheckObjectNo30
FROM              ChildGroupDetailMaster INNER JOIN
                        MedicalCheckList ON ChildGroupDetailMaster.MedicalCheckNo = MedicalCheckList.MedicalCheckNo LEFT OUTER JOIN
                        NeedCheckup ON ChildGroupDetailMaster.MedicalCheckNo = NeedCheckup.MedicalCheckNo
WHERE             (ChildGroupDetailMaster.ChildGroupId = @ChildGroupId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ChildGroupId" ColumnName="ChildGroupId" DataSourceName="TjkNakanoDatabase.dbo.ChildGroupDetailMaster" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ChildGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ChildGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GuideTimeDataTableAdapter" GeneratorDataComponentClassName="GuideTimeDataTableAdapter" Name="GuideTimeData" UserDataComponentName="GuideTimeDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.GuideTimeData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[GuideTimeData] WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([GuideTime] = @Original_GuideTime) AND ([LastUpdate] = @Original_LastUpdate))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_GuideTime" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="GuideTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[GuideTimeData] ([MedicalCheckDate], [ClientID], [Division], [MedicalCheckNo], [GuideTime], [LastUpdate]) VALUES (@MedicalCheckDate, @ClientID, @Division, @MedicalCheckNo, @GuideTime, @LastUpdate);
SELECT MedicalCheckDate, ClientID, Division, MedicalCheckNo, GuideTime, LastUpdate FROM GuideTimeData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate) AND (MedicalCheckNo = @MedicalCheckNo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@GuideTime" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="GuideTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT MedicalCheckDate, ClientID, Division, MedicalCheckNo, GuideTime, LastUpdate FROM dbo.GuideTimeData</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[GuideTimeData] SET [MedicalCheckDate] = @MedicalCheckDate, [ClientID] = @ClientID, [Division] = @Division, [MedicalCheckNo] = @MedicalCheckNo, [GuideTime] = @GuideTime, [LastUpdate] = @LastUpdate WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([GuideTime] = @Original_GuideTime) AND ([LastUpdate] = @Original_LastUpdate));
SELECT MedicalCheckDate, ClientID, Division, MedicalCheckNo, GuideTime, LastUpdate FROM GuideTimeData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate) AND (MedicalCheckNo = @MedicalCheckNo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@GuideTime" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="GuideTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_GuideTime" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="GuideTime" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckDate" DataSetColumn="MedicalCheckDate" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Division" DataSetColumn="Division" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="GuideTime" DataSetColumn="GuideTime" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="IniGuideMasterTableAdapter" GeneratorDataComponentClassName="IniGuideMasterTableAdapter" Name="IniGuideMaster" UserDataComponentName="IniGuideMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.IniGuideMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[IniGuideMaster] WHERE (([IPAddress] = @Original_IPAddress) AND ([IniGuideFlg] = @Original_IniGuideFlg) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_IniGuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="IniGuideFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[IniGuideMaster] ([IPAddress], [IniGuideFlg], [LastUpdate]) VALUES (@IPAddress, @IniGuideFlg, @LastUpdate);
SELECT IPAddress, IniGuideFlg, LastUpdate FROM IniGuideMaster WHERE (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@IniGuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="IniGuideFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT IPAddress, IniGuideFlg, LastUpdate FROM dbo.IniGuideMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[IniGuideMaster] SET [IPAddress] = @IPAddress, [IniGuideFlg] = @IniGuideFlg, [LastUpdate] = @LastUpdate WHERE (([IPAddress] = @Original_IPAddress) AND ([IniGuideFlg] = @Original_IniGuideFlg) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT IPAddress, IniGuideFlg, LastUpdate FROM IniGuideMaster WHERE (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@IniGuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="IniGuideFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_IniGuideFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="IniGuideFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IPAddress" DataSetColumn="IPAddress" />
              <Mapping SourceColumn="IniGuideFlg" DataSetColumn="IniGuideFlg" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.IniGuideMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          IPAddress, LastUpdate, IniGuideFlg
FROM            IniGuideMaster
WHERE           (IPAddress = @IPAddress)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="IPAddress" ColumnName="IPAddress" DataSourceName="ShibaParkDatabase.dbo.IniGuideMaster" DataTypeServer="varchar(15)" DbType="AnsiString" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="VarChar" Scale="0" Size="15" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="NextGuideDataTableAdapter" GeneratorDataComponentClassName="NextGuideDataTableAdapter" Name="NextGuideData" UserDataComponentName="NextGuideDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.NextGuideData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[NextGuideData] WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([ChildGroupFlg] = @Original_ChildGroupFlg) AND ((@IsNull_Value = 1 AND [Value] IS NULL) OR ([Value] = @Original_Value)) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_ChildGroupFlg" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="ChildGroupFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Value" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Value" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[NextGuideData] ([MedicalCheckDate], [ClientID], [Division], [ChildGroupFlg], [Value], [LastUpdate]) VALUES (@MedicalCheckDate, @ClientID, @Division, @ChildGroupFlg, @Value, @LastUpdate);
SELECT MedicalCheckDate, ClientID, Division, ChildGroupFlg, Value, LastUpdate FROM NextGuideData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@ChildGroupFlg" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="ChildGroupFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Value" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT MedicalCheckDate, ClientID, Division, ChildGroupFlg, Value, LastUpdate FROM dbo.NextGuideData</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[NextGuideData] SET [MedicalCheckDate] = @MedicalCheckDate, [ClientID] = @ClientID, [Division] = @Division, [ChildGroupFlg] = @ChildGroupFlg, [Value] = @Value, [LastUpdate] = @LastUpdate WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([ChildGroupFlg] = @Original_ChildGroupFlg) AND ((@IsNull_Value = 1 AND [Value] IS NULL) OR ([Value] = @Original_Value)) AND ((@IsNull_LastUpdate = 1 AND [LastUpdate] IS NULL) OR ([LastUpdate] = @Original_LastUpdate)));
SELECT MedicalCheckDate, ClientID, Division, ChildGroupFlg, Value, LastUpdate FROM NextGuideData WHERE (ClientID = @ClientID) AND (Division = @Division) AND (MedicalCheckDate = @MedicalCheckDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@ChildGroupFlg" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="ChildGroupFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Value" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_ChildGroupFlg" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="ChildGroupFlg" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Value" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Value" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Value" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_LastUpdate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="True" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckDate" DataSetColumn="MedicalCheckDate" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Division" DataSetColumn="Division" />
              <Mapping SourceColumn="ChildGroupFlg" DataSetColumn="ChildGroupFlg" />
              <Mapping SourceColumn="Value" DataSetColumn="Value" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="" DbObjectType="Unknown" GenerateShortCommands="True" GeneratorSourceName="DeleteQuery" Modifier="Public" Name="DeleteQuery" QueryType="NoData" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="DeleteQuery">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>DELETE FROM NextGuideData
WHERE           (MedicalCheckDate = @Original_MedicalCheckDate) AND 
                      (ClientID = @Original_ClientID) AND (Division = @Original_Division)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="Original_MedicalCheckDate" ColumnName="MedicalCheckDate" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Original_ClientID" ColumnName="ClientID" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Original_Division" ColumnName="Division" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="nvarchar(3)" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="3" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="ShibaParkDatabase.dbo.NextGuideData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckDate, ClientID, Division, ChildGroupFlg, Value, LastUpdate
FROM            NextGuideData
WHERE           (MedicalCheckDate = @MedicalCheckDate) AND (ClientID = @ClientID) AND 
                      (Division = @Division)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckDate" ColumnName="MedicalCheckDate" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Division" ColumnName="Division" DataSourceName="ShibaParkDatabase.dbo.NextGuideData" DataTypeServer="nvarchar(3)" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="3" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CallClientDataTableAdapter" GeneratorDataComponentClassName="CallClientDataTableAdapter" Name="CallClientData" UserDataComponentName="CallClientDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallClientData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [CallClientData] WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [CallClientData] ([MedicalCheckDate], [ClientID], [Division], [ExamID], [CallOrder], [Status], [LastUpdate]) VALUES (@MedicalCheckDate, @ClientID, @Division, @ExamID, @CallOrder, @Status, @LastUpdate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CallOrder" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CallOrder" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            MedicalCheckDate, ClientID, Division, ExamID, CallOrder, Status, LastUpdate
FROM              CallClientData</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [CallClientData] SET [MedicalCheckDate] = @MedicalCheckDate, [ClientID] = @ClientID, [Division] = @Division, [ExamID] = @ExamID, [CallOrder] = @CallOrder, [Status] = @Status, [LastUpdate] = @LastUpdate WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division) AND ([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CallOrder" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CallOrder" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastUpdate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastUpdate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckDate" DataSetColumn="MedicalCheckDate" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Division" DataSetColumn="Division" />
              <Mapping SourceColumn="ExamID" DataSetColumn="ExamID" />
              <Mapping SourceColumn="CallOrder" DataSetColumn="CallOrder" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
              <Mapping SourceColumn="LastUpdate" DataSetColumn="LastUpdate" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallClientData" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT CallOrder, ClientID, Division, ExamID, LastUpdate, MedicalCheckDate, Status FROM CallClientData WHERE (MedicalCheckDate = @MedicalCheckDate) AND (ClientID = @ClientID) AND (Division = @Division) AND (ExamID = @ExamID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckDate" ColumnName="MedicalCheckDate" DataSourceName="TjkNakanoDatabase..CallClientData" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" Scale="0" Size="8" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="TjkNakanoDatabase..CallClientData" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" Scale="0" Size="10" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Division" ColumnName="Division" DataSourceName="TjkNakanoDatabase..CallClientData" DataTypeServer="nvarchar(3)" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" Scale="0" Size="3" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ExamID" ColumnName="ExamID" DataSourceName="TjkNakanoDatabase..CallClientData" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@ExamID" Precision="0" Scale="0" Size="2" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CallStatusTableAdapter" GeneratorDataComponentClassName="CallStatusTableAdapter" Name="CallStatus" UserDataComponentName="CallStatusTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallStatus" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [CallStatus] WHERE (([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [CallStatus] ([ExamID], [MaxWaitTime]) VALUES (@ExamID, @MaxWaitTime)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MaxWaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MaxWaitTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            ExamID, MaxWaitTime
FROM              CallStatus</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [CallStatus] SET [ExamID] = @ExamID, [MaxWaitTime] = @MaxWaitTime WHERE (([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MaxWaitTime" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MaxWaitTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ExamID" DataSetColumn="ExamID" />
              <Mapping SourceColumn="MaxWaitTime" DataSetColumn="MaxWaitTime" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallStatus" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT ExamID, MaxWaitTime FROM CallStatus WHERE (ExamID = @ExamID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ExamID" ColumnName="ExamID" DataSourceName="TjkNakanoDatabase..CallStatus" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@ExamID" Precision="0" Scale="0" Size="2" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CallTerminalMasterTableAdapter" GeneratorDataComponentClassName="CallTerminalMasterTableAdapter" Name="CallTerminalMaster" UserDataComponentName="CallTerminalMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallTerminalMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [CallTerminalMaster] WHERE (([TermID] = @Original_TermID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_TermID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TermID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [CallTerminalMaster] ([TermID], [IPAddress], [ChangeSecond], [ExamID1], [ExamID2], [ExamID3], [ExamID4], [ExamID5], [ExamID6], [ExamID7], [ExamID8], [ExamID9], [ExamID10]) VALUES (@TermID, @IPAddress, @ChangeSecond, @ExamID1, @ExamID2, @ExamID3, @ExamID4, @ExamID5, @ExamID6, @ExamID7, @ExamID8, @ExamID9, @ExamID10)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@TermID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TermID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChangeSecond" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChangeSecond" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            TermID, IPAddress, ChangeSecond, ExamID1, ExamID2, ExamID3, ExamID4, ExamID5, ExamID6, ExamID7, ExamID8, ExamID9, ExamID10
FROM              CallTerminalMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [CallTerminalMaster] SET [TermID] = @TermID, [IPAddress] = @IPAddress, [ChangeSecond] = @ChangeSecond, [ExamID1] = @ExamID1, [ExamID2] = @ExamID2, [ExamID3] = @ExamID3, [ExamID4] = @ExamID4, [ExamID5] = @ExamID5, [ExamID6] = @ExamID6, [ExamID7] = @ExamID7, [ExamID8] = @ExamID8, [ExamID9] = @ExamID9, [ExamID10] = @ExamID10 WHERE (([TermID] = @Original_TermID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@TermID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TermID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@IPAddress" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="IPAddress" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChangeSecond" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChangeSecond" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_TermID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="TermID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="TermID" DataSetColumn="TermID" />
              <Mapping SourceColumn="IPAddress" DataSetColumn="IPAddress" />
              <Mapping SourceColumn="ChangeSecond" DataSetColumn="ChangeSecond" />
              <Mapping SourceColumn="ExamID1" DataSetColumn="ExamID1" />
              <Mapping SourceColumn="ExamID2" DataSetColumn="ExamID2" />
              <Mapping SourceColumn="ExamID3" DataSetColumn="ExamID3" />
              <Mapping SourceColumn="ExamID4" DataSetColumn="ExamID4" />
              <Mapping SourceColumn="ExamID5" DataSetColumn="ExamID5" />
              <Mapping SourceColumn="ExamID6" DataSetColumn="ExamID6" />
              <Mapping SourceColumn="ExamID7" DataSetColumn="ExamID7" />
              <Mapping SourceColumn="ExamID8" DataSetColumn="ExamID8" />
              <Mapping SourceColumn="ExamID9" DataSetColumn="ExamID9" />
              <Mapping SourceColumn="ExamID10" DataSetColumn="ExamID10" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TjkNakanoDatabase.dbo.CallTerminalMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT ChangeSecond, ExamID1, ExamID10, ExamID2, ExamID3, ExamID4, ExamID5, ExamID6, ExamID7, ExamID8, ExamID9, IPAddress, TermID FROM CallTerminalMaster WHERE (TermID = @TermID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="TermID" ColumnName="TermID" DataSourceName="TjkNakanoDatabase.dbo.CallTerminalMaster" DataTypeServer="nvarchar(5)" DbType="String" Direction="Input" ParameterName="@TermID" Precision="0" ProviderType="NVarChar" Scale="0" Size="5" SourceColumn="TermID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CallExamMasterTableAdapter" GeneratorDataComponentClassName="CallExamMasterTableAdapter" Name="CallExamMaster" UserDataComponentName="CallExamMasterTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase.dbo.CallExamMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [CallExamMaster] WHERE (([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [CallExamMaster] ([ExamID], [Title], [Status], [DispStartTime], [DispEndTime], [Capacity], [BackgroundColor], [Message], [Header1], [Header2], [MedicalCheckNo1], [MedicalCheckNo2], [MedicalCheckNo3], [MedicalCheckNo4], [MedicalCheckNo5], [MedicalCheckNo6], [MedicalCheckNo7], [MedicalCheckNo8], [MedicalCheckNo9], [MedicalCheckNo10], [PanelType], [Param1], [Param2], [Param3]) VALUES (@ExamID, @Title, @Status, @DispStartTime, @DispEndTime, @Capacity, @BackgroundColor, @Message, @Header1, @Header2, @MedicalCheckNo1, @MedicalCheckNo2, @MedicalCheckNo3, @MedicalCheckNo4, @MedicalCheckNo5, @MedicalCheckNo6, @MedicalCheckNo7, @MedicalCheckNo8, @MedicalCheckNo9, @MedicalCheckNo10, @PanelType, @Param1, @Param2, @Param3)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Title" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DispStartTime" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DispStartTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DispEndTime" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DispEndTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BackgroundColor" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BackgroundColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Message" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Message" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Header1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Header1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Header2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Header2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@PanelType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="PanelType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param3" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            ExamID, Title, Status, DispStartTime, DispEndTime, Capacity, BackgroundColor, Message, Header1, Header2, MedicalCheckNo1, MedicalCheckNo2, MedicalCheckNo3, 
                        MedicalCheckNo4, MedicalCheckNo5, MedicalCheckNo6, MedicalCheckNo7, MedicalCheckNo8, MedicalCheckNo9, MedicalCheckNo10, PanelType, Param1, Param2, Param3
FROM              CallExamMaster</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [CallExamMaster] SET [ExamID] = @ExamID, [Title] = @Title, [Status] = @Status, [DispStartTime] = @DispStartTime, [DispEndTime] = @DispEndTime, [Capacity] = @Capacity, [BackgroundColor] = @BackgroundColor, [Message] = @Message, [Header1] = @Header1, [Header2] = @Header2, [MedicalCheckNo1] = @MedicalCheckNo1, [MedicalCheckNo2] = @MedicalCheckNo2, [MedicalCheckNo3] = @MedicalCheckNo3, [MedicalCheckNo4] = @MedicalCheckNo4, [MedicalCheckNo5] = @MedicalCheckNo5, [MedicalCheckNo6] = @MedicalCheckNo6, [MedicalCheckNo7] = @MedicalCheckNo7, [MedicalCheckNo8] = @MedicalCheckNo8, [MedicalCheckNo9] = @MedicalCheckNo9, [MedicalCheckNo10] = @MedicalCheckNo10, [PanelType] = @PanelType, [Param1] = @Param1, [Param2] = @Param2, [Param3] = @Param3 WHERE (([ExamID] = @Original_ExamID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Title" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Title" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DispStartTime" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DispStartTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@DispEndTime" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="DispEndTime" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Capacity" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Capacity" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BackgroundColor" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BackgroundColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Message" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Message" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Header1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Header1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Header2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Header2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@PanelType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="PanelType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param1" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param2" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Param3" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Param3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ExamID" DataSetColumn="ExamID" />
              <Mapping SourceColumn="Title" DataSetColumn="Title" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
              <Mapping SourceColumn="DispStartTime" DataSetColumn="DispStartTime" />
              <Mapping SourceColumn="DispEndTime" DataSetColumn="DispEndTime" />
              <Mapping SourceColumn="Capacity" DataSetColumn="Capacity" />
              <Mapping SourceColumn="BackgroundColor" DataSetColumn="BackgroundColor" />
              <Mapping SourceColumn="Message" DataSetColumn="Message" />
              <Mapping SourceColumn="MedicalCheckNo1" DataSetColumn="MedicalCheckNo1" />
              <Mapping SourceColumn="MedicalCheckNo2" DataSetColumn="MedicalCheckNo2" />
              <Mapping SourceColumn="MedicalCheckNo3" DataSetColumn="MedicalCheckNo3" />
              <Mapping SourceColumn="MedicalCheckNo4" DataSetColumn="MedicalCheckNo4" />
              <Mapping SourceColumn="MedicalCheckNo5" DataSetColumn="MedicalCheckNo5" />
              <Mapping SourceColumn="MedicalCheckNo6" DataSetColumn="MedicalCheckNo6" />
              <Mapping SourceColumn="MedicalCheckNo7" DataSetColumn="MedicalCheckNo7" />
              <Mapping SourceColumn="MedicalCheckNo8" DataSetColumn="MedicalCheckNo8" />
              <Mapping SourceColumn="MedicalCheckNo9" DataSetColumn="MedicalCheckNo9" />
              <Mapping SourceColumn="MedicalCheckNo10" DataSetColumn="MedicalCheckNo10" />
              <Mapping SourceColumn="Header1" DataSetColumn="Header1" />
              <Mapping SourceColumn="Header2" DataSetColumn="Header2" />
              <Mapping SourceColumn="PanelType" DataSetColumn="PanelType" />
              <Mapping SourceColumn="Param1" DataSetColumn="Param1" />
              <Mapping SourceColumn="Param2" DataSetColumn="Param2" />
              <Mapping SourceColumn="Param3" DataSetColumn="Param3" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase.dbo.CallExamMaster" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT BackgroundColor, Capacity, DispEndTime, DispStartTime, ExamID, Header1, Header2, MedicalCheckNo1, MedicalCheckNo10, MedicalCheckNo2, MedicalCheckNo3, MedicalCheckNo4, MedicalCheckNo5, MedicalCheckNo6, MedicalCheckNo7, MedicalCheckNo8, MedicalCheckNo9, Message, PanelType, Param1, Param2, Param3, Status, Title FROM CallExamMaster WHERE (ExamID = @ExamID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ExamID" ColumnName="ExamID" DataSourceName="GenkiPlazaDatabase.dbo.CallExamMaster" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ExamID" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ExamID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="GuideSupportDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msdata:EnforceConstraints="False" msprop:Generator_UserDSName="GuideSupportDataSet" msprop:Generator_DataSetName="GuideSupportDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="GuideDetailMaster" msprop:Generator_UserTableName="GuideDetailMaster" msprop:Generator_RowDeletedName="GuideDetailMasterRowDeleted" msprop:Generator_RowChangedName="GuideDetailMasterRowChanged" msprop:Generator_RowClassName="GuideDetailMasterRow" msprop:Generator_RowChangingName="GuideDetailMasterRowChanging" msprop:Generator_RowEvArgName="GuideDetailMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GuideDetailMasterRowChangeEventHandler" msprop:Generator_TableClassName="GuideDetailMasterDataTable" msprop:Generator_TableVarName="tableGuideDetailMaster" msprop:Generator_RowDeletingName="GuideDetailMasterRowDeleting" msprop:Generator_TablePropName="GuideDetailMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GuideMasterDetailId" msprop:Generator_UserColumnName="GuideMasterDetailId" msprop:Generator_ColumnVarNameInTable="columnGuideMasterDetailId" msprop:Generator_ColumnPropNameInRow="GuideMasterDetailId" msprop:Generator_ColumnPropNameInTable="GuideMasterDetailIdColumn" type="xs:short" />
              <xs:element name="GuideMasterId" msprop:Generator_UserColumnName="GuideMasterId" msprop:Generator_ColumnVarNameInTable="columnGuideMasterId" msprop:Generator_ColumnPropNameInRow="GuideMasterId" msprop:Generator_ColumnPropNameInTable="GuideMasterIdColumn" type="xs:short" />
              <xs:element name="GuidePriority" msprop:Generator_UserColumnName="GuidePriority" msprop:Generator_ColumnVarNameInTable="columnGuidePriority" msprop:Generator_ColumnPropNameInRow="GuidePriority" msprop:Generator_ColumnPropNameInTable="GuidePriorityColumn" type="xs:short" />
              <xs:element name="GroupId" msprop:Generator_UserColumnName="GroupId" msprop:Generator_ColumnVarNameInTable="columnGroupId" msprop:Generator_ColumnPropNameInRow="GroupId" msprop:Generator_ColumnPropNameInTable="GroupIdColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GroupMaster" msprop:Generator_UserTableName="GroupMaster" msprop:Generator_RowDeletedName="GroupMasterRowDeleted" msprop:Generator_RowChangedName="GroupMasterRowChanged" msprop:Generator_RowClassName="GroupMasterRow" msprop:Generator_RowChangingName="GroupMasterRowChanging" msprop:Generator_RowEvArgName="GroupMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GroupMasterRowChangeEventHandler" msprop:Generator_TableClassName="GroupMasterDataTable" msprop:Generator_TableVarName="tableGroupMaster" msprop:Generator_RowDeletingName="GroupMasterRowDeleting" msprop:Generator_TablePropName="GroupMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GroupId" msprop:Generator_UserColumnName="GroupId" msprop:Generator_ColumnVarNameInTable="columnGroupId" msprop:Generator_ColumnPropNameInRow="GroupId" msprop:Generator_ColumnPropNameInTable="GroupIdColumn" type="xs:short" />
              <xs:element name="GroupName" msprop:Generator_UserColumnName="GroupName" msprop:Generator_ColumnVarNameInTable="columnGroupName" msprop:Generator_ColumnPropNameInRow="GroupName" msprop:Generator_ColumnPropNameInTable="GroupNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GuideType" msprop:Generator_UserColumnName="GuideType" msprop:Generator_ColumnVarNameInTable="columnGuideType" msprop:Generator_ColumnPropNameInRow="GuideType" msprop:Generator_ColumnPropNameInTable="GuideTypeColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ChildGroupMaster" msprop:Generator_UserTableName="ChildGroupMaster" msprop:Generator_RowDeletedName="ChildGroupMasterRowDeleted" msprop:Generator_RowChangedName="ChildGroupMasterRowChanged" msprop:Generator_RowClassName="ChildGroupMasterRow" msprop:Generator_RowChangingName="ChildGroupMasterRowChanging" msprop:Generator_RowEvArgName="ChildGroupMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="ChildGroupMasterRowChangeEventHandler" msprop:Generator_TableClassName="ChildGroupMasterDataTable" msprop:Generator_TableVarName="tableChildGroupMaster" msprop:Generator_RowDeletingName="ChildGroupMasterRowDeleting" msprop:Generator_TablePropName="ChildGroupMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ChildGroupId" msprop:Generator_UserColumnName="ChildGroupId" msprop:Generator_ColumnVarNameInTable="columnChildGroupId" msprop:Generator_ColumnPropNameInRow="ChildGroupId" msprop:Generator_ColumnPropNameInTable="ChildGroupIdColumn" type="xs:short" />
              <xs:element name="ChildGroupName" msprop:Generator_UserColumnName="ChildGroupName" msprop:Generator_ColumnVarNameInTable="columnChildGroupName" msprop:Generator_ColumnPropNameInRow="ChildGroupName" msprop:Generator_ColumnPropNameInTable="ChildGroupNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChildGuideType" msprop:Generator_UserColumnName="ChildGuideType" msprop:Generator_ColumnVarNameInTable="columnChildGuideType" msprop:Generator_ColumnPropNameInRow="ChildGuideType" msprop:Generator_ColumnPropNameInTable="ChildGuideTypeColumn" type="xs:short" />
              <xs:element name="ClientTermCount" msprop:Generator_UserColumnName="ClientTermCount" msprop:Generator_ColumnVarNameInTable="columnClientTermCount" msprop:Generator_ColumnPropNameInRow="ClientTermCount" msprop:Generator_ColumnPropNameInTable="ClientTermCountColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ChildGroupTermMaster" msprop:Generator_UserTableName="ChildGroupTermMaster" msprop:Generator_RowDeletedName="ChildGroupTermMasterRowDeleted" msprop:Generator_RowChangedName="ChildGroupTermMasterRowChanged" msprop:Generator_RowClassName="ChildGroupTermMasterRow" msprop:Generator_RowChangingName="ChildGroupTermMasterRowChanging" msprop:Generator_RowEvArgName="ChildGroupTermMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="ChildGroupTermMasterRowChangeEventHandler" msprop:Generator_TableClassName="ChildGroupTermMasterDataTable" msprop:Generator_TableVarName="tableChildGroupTermMaster" msprop:Generator_RowDeletingName="ChildGroupTermMasterRowDeleting" msprop:Generator_TablePropName="ChildGroupTermMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ChildGroupTermId" msprop:Generator_UserColumnName="ChildGroupTermId" msprop:Generator_ColumnVarNameInTable="columnChildGroupTermId" msprop:Generator_ColumnPropNameInRow="ChildGroupTermId" msprop:Generator_ColumnPropNameInTable="ChildGroupTermIdColumn" type="xs:short" />
              <xs:element name="ChildGroupId" msprop:Generator_UserColumnName="ChildGroupId" msprop:Generator_ColumnVarNameInTable="columnChildGroupId" msprop:Generator_ColumnPropNameInRow="ChildGroupId" msprop:Generator_ColumnPropNameInTable="ChildGroupIdColumn" type="xs:short" />
              <xs:element name="IPAddress" msprop:Generator_UserColumnName="IPAddress" msprop:Generator_ColumnVarNameInTable="columnIPAddress" msprop:Generator_ColumnPropNameInRow="IPAddress" msprop:Generator_ColumnPropNameInTable="IPAddressColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ExamSettingMaster" msprop:Generator_UserTableName="ExamSettingMaster" msprop:Generator_RowDeletedName="ExamSettingMasterRowDeleted" msprop:Generator_RowChangedName="ExamSettingMasterRowChanged" msprop:Generator_RowClassName="ExamSettingMasterRow" msprop:Generator_RowChangingName="ExamSettingMasterRowChanging" msprop:Generator_RowEvArgName="ExamSettingMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="ExamSettingMasterRowChangeEventHandler" msprop:Generator_TableClassName="ExamSettingMasterDataTable" msprop:Generator_TableVarName="tableExamSettingMaster" msprop:Generator_RowDeletingName="ExamSettingMasterRowDeleting" msprop:Generator_TablePropName="ExamSettingMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ExamSettingId" msprop:Generator_UserColumnName="ExamSettingId" msprop:Generator_ColumnVarNameInTable="columnExamSettingId" msprop:Generator_ColumnPropNameInRow="ExamSettingId" msprop:Generator_ColumnPropNameInTable="ExamSettingIdColumn" type="xs:short" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ExamTime" msprop:Generator_UserColumnName="ExamTime" msprop:Generator_ColumnVarNameInTable="columnExamTime" msprop:Generator_ColumnPropNameInRow="ExamTime" msprop:Generator_ColumnPropNameInTable="ExamTimeColumn" type="xs:short" />
              <xs:element name="WaitTime" msprop:Generator_UserColumnName="WaitTime" msprop:Generator_ColumnVarNameInTable="columnWaitTime" msprop:Generator_ColumnPropNameInRow="WaitTime" msprop:Generator_ColumnPropNameInTable="WaitTimeColumn" type="xs:short" />
              <xs:element name="WaitPeople" msprop:Generator_UserColumnName="WaitPeople" msprop:Generator_ColumnVarNameInTable="columnWaitPeople" msprop:Generator_ColumnPropNameInRow="WaitPeople" msprop:Generator_ColumnPropNameInTable="WaitPeopleColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TermGuideData" msprop:Generator_UserTableName="TermGuideData" msprop:Generator_RowDeletedName="TermGuideDataRowDeleted" msprop:Generator_RowChangedName="TermGuideDataRowChanged" msprop:Generator_RowClassName="TermGuideDataRow" msprop:Generator_RowChangingName="TermGuideDataRowChanging" msprop:Generator_RowEvArgName="TermGuideDataRowChangeEvent" msprop:Generator_RowEvHandlerName="TermGuideDataRowChangeEventHandler" msprop:Generator_TableClassName="TermGuideDataDataTable" msprop:Generator_TableVarName="tableTermGuideData" msprop:Generator_RowDeletingName="TermGuideDataRowDeleting" msprop:Generator_TablePropName="TermGuideData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IPAddress" msprop:Generator_UserColumnName="IPAddress" msprop:Generator_ColumnVarNameInTable="columnIPAddress" msprop:Generator_ColumnPropNameInRow="IPAddress" msprop:Generator_ColumnPropNameInTable="IPAddressColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GuideFlg" msprop:Generator_UserColumnName="GuideFlg" msprop:Generator_ColumnVarNameInTable="columnGuideFlg" msprop:Generator_ColumnPropNameInRow="GuideFlg" msprop:Generator_ColumnPropNameInTable="GuideFlgColumn" type="xs:short" />
              <xs:element name="TermFlg" msprop:Generator_UserColumnName="TermFlg" msprop:Generator_ColumnVarNameInTable="columnTermFlg" msprop:Generator_ColumnPropNameInRow="TermFlg" msprop:Generator_ColumnPropNameInTable="TermFlgColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GroupDetailMaster" msprop:Generator_UserTableName="GroupDetailMaster" msprop:Generator_RowDeletedName="GroupDetailMasterRowDeleted" msprop:Generator_RowChangedName="GroupDetailMasterRowChanged" msprop:Generator_RowClassName="GroupDetailMasterRow" msprop:Generator_RowChangingName="GroupDetailMasterRowChanging" msprop:Generator_RowEvArgName="GroupDetailMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GroupDetailMasterRowChangeEventHandler" msprop:Generator_TableClassName="GroupDetailMasterDataTable" msprop:Generator_TableVarName="tableGroupDetailMaster" msprop:Generator_RowDeletingName="GroupDetailMasterRowDeleting" msprop:Generator_TablePropName="GroupDetailMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GroupDetailId" msprop:Generator_UserColumnName="GroupDetailId" msprop:Generator_ColumnVarNameInTable="columnGroupDetailId" msprop:Generator_ColumnPropNameInRow="GroupDetailId" msprop:Generator_ColumnPropNameInTable="GroupDetailIdColumn" type="xs:short" />
              <xs:element name="GroupId" msprop:Generator_UserColumnName="GroupId" msprop:Generator_ColumnVarNameInTable="columnGroupId" msprop:Generator_ColumnPropNameInRow="GroupId" msprop:Generator_ColumnPropNameInTable="GroupIdColumn" type="xs:short" />
              <xs:element name="GroupPriority" msprop:Generator_UserColumnName="GroupPriority" msprop:Generator_ColumnVarNameInTable="columnGroupPriority" msprop:Generator_ColumnPropNameInRow="GroupPriority" msprop:Generator_ColumnPropNameInTable="GroupPriorityColumn" type="xs:short" />
              <xs:element name="ChildGroupId" msprop:Generator_UserColumnName="ChildGroupId" msprop:Generator_ColumnVarNameInTable="columnChildGroupId" msprop:Generator_ColumnPropNameInRow="ChildGroupId" msprop:Generator_ColumnPropNameInTable="ChildGroupIdColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GuideRootMaster" msprop:Generator_UserTableName="GuideRootMaster" msprop:Generator_RowDeletedName="GuideRootMasterRowDeleted" msprop:Generator_RowChangedName="GuideRootMasterRowChanged" msprop:Generator_RowClassName="GuideRootMasterRow" msprop:Generator_RowChangingName="GuideRootMasterRowChanging" msprop:Generator_RowEvArgName="GuideRootMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GuideRootMasterRowChangeEventHandler" msprop:Generator_TableClassName="GuideRootMasterDataTable" msprop:Generator_TableVarName="tableGuideRootMaster" msprop:Generator_RowDeletingName="GuideRootMasterRowDeleting" msprop:Generator_TablePropName="GuideRootMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GuideMasterId" msprop:Generator_UserColumnName="GuideMasterId" msprop:Generator_ColumnVarNameInTable="columnGuideMasterId" msprop:Generator_ColumnPropNameInRow="GuideMasterId" msprop:Generator_ColumnPropNameInTable="GuideMasterIdColumn" type="xs:short" />
              <xs:element name="GuideName" msprop:Generator_UserColumnName="GuideName" msprop:Generator_ColumnVarNameInTable="columnGuideName" msprop:Generator_ColumnPropNameInRow="GuideName" msprop:Generator_ColumnPropNameInTable="GuideNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GuideDivisionMaster" msprop:Generator_UserTableName="GuideDivisionMaster" msprop:Generator_RowDeletedName="GuideDivisionMasterRowDeleted" msprop:Generator_RowChangedName="GuideDivisionMasterRowChanged" msprop:Generator_RowClassName="GuideDivisionMasterRow" msprop:Generator_RowChangingName="GuideDivisionMasterRowChanging" msprop:Generator_RowEvArgName="GuideDivisionMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GuideDivisionMasterRowChangeEventHandler" msprop:Generator_TableClassName="GuideDivisionMasterDataTable" msprop:Generator_TableVarName="tableGuideDivisionMaster" msprop:Generator_RowDeletingName="GuideDivisionMasterRowDeleting" msprop:Generator_TablePropName="GuideDivisionMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GuideDivisionId" msprop:Generator_UserColumnName="GuideDivisionId" msprop:Generator_ColumnVarNameInTable="columnGuideDivisionId" msprop:Generator_ColumnPropNameInRow="GuideDivisionId" msprop:Generator_ColumnPropNameInTable="GuideDivisionIdColumn" type="xs:short" />
              <xs:element name="GuideDivision" msprop:Generator_UserColumnName="GuideDivision" msprop:Generator_ColumnVarNameInTable="columnGuideDivision" msprop:Generator_ColumnPropNameInRow="GuideDivision" msprop:Generator_ColumnPropNameInTable="GuideDivisionColumn" type="xs:short" />
              <xs:element name="GuideDivisionValue" msprop:Generator_UserColumnName="GuideDivisionValue" msprop:Generator_ColumnVarNameInTable="columnGuideDivisionValue" msprop:Generator_ColumnPropNameInRow="GuideDivisionValue" msprop:Generator_ColumnPropNameInTable="GuideDivisionValueColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GuideMaster" msprop:Generator_UserTableName="GuideMaster" msprop:Generator_RowDeletedName="GuideMasterRowDeleted" msprop:Generator_RowChangedName="GuideMasterRowChanged" msprop:Generator_RowClassName="GuideMasterRow" msprop:Generator_RowChangingName="GuideMasterRowChanging" msprop:Generator_RowEvArgName="GuideMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="GuideMasterRowChangeEventHandler" msprop:Generator_TableClassName="GuideMasterDataTable" msprop:Generator_TableVarName="tableGuideMaster" msprop:Generator_RowDeletingName="GuideMasterRowDeleting" msprop:Generator_TablePropName="GuideMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GuideId" msprop:Generator_UserColumnName="GuideId" msprop:Generator_ColumnVarNameInTable="columnGuideId" msprop:Generator_ColumnPropNameInRow="GuideId" msprop:Generator_ColumnPropNameInTable="GuideIdColumn" type="xs:short" />
              <xs:element name="GuideDivisionId" msprop:Generator_UserColumnName="GuideDivisionId" msprop:Generator_ColumnVarNameInTable="columnGuideDivisionId" msprop:Generator_ColumnPropNameInRow="GuideDivisionId" msprop:Generator_ColumnPropNameInTable="GuideDivisionIdColumn" type="xs:short" />
              <xs:element name="GuideMasterId" msprop:Generator_UserColumnName="GuideMasterId" msprop:Generator_ColumnVarNameInTable="columnGuideMasterId" msprop:Generator_ColumnPropNameInRow="GuideMasterId" msprop:Generator_ColumnPropNameInTable="GuideMasterIdColumn" type="xs:short" />
              <xs:element name="Priority" msprop:Generator_UserColumnName="Priority" msprop:Generator_ColumnVarNameInTable="columnPriority" msprop:Generator_ColumnPropNameInRow="Priority" msprop:Generator_ColumnPropNameInTable="PriorityColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientGuideData" msprop:Generator_UserTableName="ClientGuideData" msprop:Generator_RowDeletedName="ClientGuideDataRowDeleted" msprop:Generator_RowChangedName="ClientGuideDataRowChanged" msprop:Generator_RowClassName="ClientGuideDataRow" msprop:Generator_RowChangingName="ClientGuideDataRowChanging" msprop:Generator_RowEvArgName="ClientGuideDataRowChangeEvent" msprop:Generator_RowEvHandlerName="ClientGuideDataRowChangeEventHandler" msprop:Generator_TableClassName="ClientGuideDataDataTable" msprop:Generator_TableVarName="tableClientGuideData" msprop:Generator_RowDeletingName="ClientGuideDataRowDeleting" msprop:Generator_TablePropName="ClientGuideData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckDate" msprop:Generator_UserColumnName="MedicalCheckDate" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckDate" msprop:Generator_ColumnPropNameInRow="MedicalCheckDate" msprop:Generator_ColumnPropNameInTable="MedicalCheckDateColumn" type="xs:dateTime" />
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Division" msprop:Generator_UserColumnName="Division" msprop:Generator_ColumnVarNameInTable="columnDivision" msprop:Generator_ColumnPropNameInRow="Division" msprop:Generator_ColumnPropNameInTable="DivisionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="RegistrationNo" msprop:Generator_UserColumnName="RegistrationNo" msprop:Generator_ColumnVarNameInTable="columnRegistrationNo" msprop:Generator_ColumnPropNameInRow="RegistrationNo" msprop:Generator_ColumnPropNameInTable="RegistrationNoColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GuideMasterID" msprop:Generator_UserColumnName="GuideMasterID" msprop:Generator_ColumnVarNameInTable="columnGuideMasterID" msprop:Generator_ColumnPropNameInRow="GuideMasterID" msprop:Generator_ColumnPropNameInTable="GuideMasterIDColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ChildGroupDetailMaster" msprop:Generator_UserTableName="ChildGroupDetailMaster" msprop:Generator_RowDeletedName="ChildGroupDetailMasterRowDeleted" msprop:Generator_RowChangedName="ChildGroupDetailMasterRowChanged" msprop:Generator_RowClassName="ChildGroupDetailMasterRow" msprop:Generator_RowChangingName="ChildGroupDetailMasterRowChanging" msprop:Generator_RowEvArgName="ChildGroupDetailMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="ChildGroupDetailMasterRowChangeEventHandler" msprop:Generator_TableClassName="ChildGroupDetailMasterDataTable" msprop:Generator_TableVarName="tableChildGroupDetailMaster" msprop:Generator_RowDeletingName="ChildGroupDetailMasterRowDeleting" msprop:Generator_TablePropName="ChildGroupDetailMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ChildGroupDetailId" msprop:Generator_UserColumnName="ChildGroupDetailId" msprop:Generator_ColumnVarNameInTable="columnChildGroupDetailId" msprop:Generator_ColumnPropNameInRow="ChildGroupDetailId" msprop:Generator_ColumnPropNameInTable="ChildGroupDetailIdColumn" type="xs:short" />
              <xs:element name="ChildGroupId" msprop:Generator_UserColumnName="ChildGroupId" msprop:Generator_ColumnVarNameInTable="columnChildGroupId" msprop:Generator_ColumnPropNameInRow="ChildGroupId" msprop:Generator_ColumnPropNameInTable="ChildGroupIdColumn" type="xs:short" />
              <xs:element name="ChildGroupPriority" msprop:Generator_UserColumnName="ChildGroupPriority" msprop:Generator_ColumnVarNameInTable="columnChildGroupPriority" msprop:Generator_ColumnPropNameInRow="ChildGroupPriority" msprop:Generator_ColumnPropNameInTable="ChildGroupPriorityColumn" type="xs:short" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="GuideTimeData" msprop:Generator_UserTableName="GuideTimeData" msprop:Generator_RowDeletedName="GuideTimeDataRowDeleted" msprop:Generator_RowChangedName="GuideTimeDataRowChanged" msprop:Generator_RowClassName="GuideTimeDataRow" msprop:Generator_RowChangingName="GuideTimeDataRowChanging" msprop:Generator_RowEvArgName="GuideTimeDataRowChangeEvent" msprop:Generator_RowEvHandlerName="GuideTimeDataRowChangeEventHandler" msprop:Generator_TableClassName="GuideTimeDataDataTable" msprop:Generator_TableVarName="tableGuideTimeData" msprop:Generator_RowDeletingName="GuideTimeDataRowDeleting" msprop:Generator_TablePropName="GuideTimeData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckDate" msprop:Generator_UserColumnName="MedicalCheckDate" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckDate" msprop:Generator_ColumnPropNameInRow="MedicalCheckDate" msprop:Generator_ColumnPropNameInTable="MedicalCheckDateColumn" type="xs:dateTime" />
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Division" msprop:Generator_UserColumnName="Division" msprop:Generator_ColumnVarNameInTable="columnDivision" msprop:Generator_ColumnPropNameInRow="Division" msprop:Generator_ColumnPropNameInTable="DivisionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="GuideTime" msprop:Generator_UserColumnName="GuideTime" msprop:Generator_ColumnVarNameInTable="columnGuideTime" msprop:Generator_ColumnPropNameInRow="GuideTime" msprop:Generator_ColumnPropNameInTable="GuideTimeColumn" type="xs:dateTime" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="IniGuideMaster" msprop:Generator_UserTableName="IniGuideMaster" msprop:Generator_RowDeletedName="IniGuideMasterRowDeleted" msprop:Generator_RowChangedName="IniGuideMasterRowChanged" msprop:Generator_RowClassName="IniGuideMasterRow" msprop:Generator_RowChangingName="IniGuideMasterRowChanging" msprop:Generator_RowEvArgName="IniGuideMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="IniGuideMasterRowChangeEventHandler" msprop:Generator_TableClassName="IniGuideMasterDataTable" msprop:Generator_TableVarName="tableIniGuideMaster" msprop:Generator_RowDeletingName="IniGuideMasterRowDeleting" msprop:Generator_TablePropName="IniGuideMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IPAddress" msprop:Generator_UserColumnName="IPAddress" msprop:Generator_ColumnVarNameInTable="columnIPAddress" msprop:Generator_ColumnPropNameInRow="IPAddress" msprop:Generator_ColumnPropNameInTable="IPAddressColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IniGuideFlg" msprop:Generator_UserColumnName="IniGuideFlg" msprop:Generator_ColumnVarNameInTable="columnIniGuideFlg" msprop:Generator_ColumnPropNameInRow="IniGuideFlg" msprop:Generator_ColumnPropNameInTable="IniGuideFlgColumn" type="xs:short" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="NextGuideData" msprop:Generator_UserTableName="NextGuideData" msprop:Generator_RowDeletedName="NextGuideDataRowDeleted" msprop:Generator_RowChangedName="NextGuideDataRowChanged" msprop:Generator_RowClassName="NextGuideDataRow" msprop:Generator_RowChangingName="NextGuideDataRowChanging" msprop:Generator_RowEvArgName="NextGuideDataRowChangeEvent" msprop:Generator_RowEvHandlerName="NextGuideDataRowChangeEventHandler" msprop:Generator_TableClassName="NextGuideDataDataTable" msprop:Generator_TableVarName="tableNextGuideData" msprop:Generator_RowDeletingName="NextGuideDataRowDeleting" msprop:Generator_TablePropName="NextGuideData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckDate" msprop:Generator_UserColumnName="MedicalCheckDate" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckDate" msprop:Generator_ColumnPropNameInRow="MedicalCheckDate" msprop:Generator_ColumnPropNameInTable="MedicalCheckDateColumn" type="xs:dateTime" />
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Division" msprop:Generator_UserColumnName="Division" msprop:Generator_ColumnVarNameInTable="columnDivision" msprop:Generator_ColumnPropNameInRow="Division" msprop:Generator_ColumnPropNameInTable="DivisionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChildGroupFlg" msprop:Generator_UserColumnName="ChildGroupFlg" msprop:Generator_ColumnVarNameInTable="columnChildGroupFlg" msprop:Generator_ColumnPropNameInRow="ChildGroupFlg" msprop:Generator_ColumnPropNameInTable="ChildGroupFlgColumn" type="xs:boolean" />
              <xs:element name="Value" msprop:Generator_UserColumnName="Value" msprop:Generator_ColumnVarNameInTable="columnValue" msprop:Generator_ColumnPropNameInRow="Value" msprop:Generator_ColumnPropNameInTable="ValueColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CallClientData" msprop:Generator_UserTableName="CallClientData" msprop:Generator_RowDeletedName="CallClientDataRowDeleted" msprop:Generator_RowChangedName="CallClientDataRowChanged" msprop:Generator_RowClassName="CallClientDataRow" msprop:Generator_RowChangingName="CallClientDataRowChanging" msprop:Generator_RowEvArgName="CallClientDataRowChangeEvent" msprop:Generator_RowEvHandlerName="CallClientDataRowChangeEventHandler" msprop:Generator_TableClassName="CallClientDataDataTable" msprop:Generator_TableVarName="tableCallClientData" msprop:Generator_RowDeletingName="CallClientDataRowDeleting" msprop:Generator_TablePropName="CallClientData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckDate" msprop:Generator_UserColumnName="MedicalCheckDate" msprop:Generator_ColumnPropNameInRow="MedicalCheckDate" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckDate" msprop:Generator_ColumnPropNameInTable="MedicalCheckDateColumn" type="xs:dateTime" />
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Division" msprop:Generator_UserColumnName="Division" msprop:Generator_ColumnPropNameInRow="Division" msprop:Generator_ColumnVarNameInTable="columnDivision" msprop:Generator_ColumnPropNameInTable="DivisionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ExamID" msprop:Generator_UserColumnName="ExamID" msprop:Generator_ColumnPropNameInRow="ExamID" msprop:Generator_ColumnVarNameInTable="columnExamID" msprop:Generator_ColumnPropNameInTable="ExamIDColumn" type="xs:short" />
              <xs:element name="CallOrder" msprop:Generator_UserColumnName="CallOrder" msprop:Generator_ColumnPropNameInRow="CallOrder" msprop:Generator_ColumnVarNameInTable="columnCallOrder" msprop:Generator_ColumnPropNameInTable="CallOrderColumn" type="xs:short" minOccurs="0" />
              <xs:element name="Status" msprop:Generator_UserColumnName="Status" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_ColumnPropNameInTable="StatusColumn" type="xs:short" minOccurs="0" />
              <xs:element name="LastUpdate" msprop:Generator_UserColumnName="LastUpdate" msprop:Generator_ColumnPropNameInRow="LastUpdate" msprop:Generator_ColumnVarNameInTable="columnLastUpdate" msprop:Generator_ColumnPropNameInTable="LastUpdateColumn" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CallStatus" msprop:Generator_UserTableName="CallStatus" msprop:Generator_RowDeletedName="CallStatusRowDeleted" msprop:Generator_RowChangedName="CallStatusRowChanged" msprop:Generator_RowClassName="CallStatusRow" msprop:Generator_RowChangingName="CallStatusRowChanging" msprop:Generator_RowEvArgName="CallStatusRowChangeEvent" msprop:Generator_RowEvHandlerName="CallStatusRowChangeEventHandler" msprop:Generator_TableClassName="CallStatusDataTable" msprop:Generator_TableVarName="tableCallStatus" msprop:Generator_RowDeletingName="CallStatusRowDeleting" msprop:Generator_TablePropName="CallStatus">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ExamID" msprop:Generator_UserColumnName="ExamID" msprop:Generator_ColumnPropNameInRow="ExamID" msprop:Generator_ColumnVarNameInTable="columnExamID" msprop:Generator_ColumnPropNameInTable="ExamIDColumn" type="xs:short" />
              <xs:element name="MaxWaitTime" msprop:Generator_UserColumnName="MaxWaitTime" msprop:Generator_ColumnPropNameInRow="MaxWaitTime" msprop:Generator_ColumnVarNameInTable="columnMaxWaitTime" msprop:Generator_ColumnPropNameInTable="MaxWaitTimeColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CallTerminalMaster" msprop:Generator_UserTableName="CallTerminalMaster" msprop:Generator_RowDeletedName="CallTerminalMasterRowDeleted" msprop:Generator_RowChangedName="CallTerminalMasterRowChanged" msprop:Generator_RowClassName="CallTerminalMasterRow" msprop:Generator_RowChangingName="CallTerminalMasterRowChanging" msprop:Generator_RowEvArgName="CallTerminalMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="CallTerminalMasterRowChangeEventHandler" msprop:Generator_TableClassName="CallTerminalMasterDataTable" msprop:Generator_TableVarName="tableCallTerminalMaster" msprop:Generator_RowDeletingName="CallTerminalMasterRowDeleting" msprop:Generator_TablePropName="CallTerminalMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="TermID" msprop:Generator_UserColumnName="TermID" msprop:Generator_ColumnPropNameInRow="TermID" msprop:Generator_ColumnVarNameInTable="columnTermID" msprop:Generator_ColumnPropNameInTable="TermIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IPAddress" msprop:Generator_UserColumnName="IPAddress" msprop:Generator_ColumnPropNameInRow="IPAddress" msprop:Generator_ColumnVarNameInTable="columnIPAddress" msprop:Generator_ColumnPropNameInTable="IPAddressColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChangeSecond" msprop:Generator_UserColumnName="ChangeSecond" msprop:Generator_ColumnPropNameInRow="ChangeSecond" msprop:Generator_ColumnVarNameInTable="columnChangeSecond" msprop:Generator_ColumnPropNameInTable="ChangeSecondColumn" type="xs:short" />
              <xs:element name="ExamID1" msprop:Generator_UserColumnName="ExamID1" msprop:Generator_ColumnPropNameInRow="ExamID1" msprop:Generator_ColumnVarNameInTable="columnExamID1" msprop:Generator_ColumnPropNameInTable="ExamID1Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID2" msprop:Generator_UserColumnName="ExamID2" msprop:Generator_ColumnPropNameInRow="ExamID2" msprop:Generator_ColumnVarNameInTable="columnExamID2" msprop:Generator_ColumnPropNameInTable="ExamID2Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID3" msprop:Generator_UserColumnName="ExamID3" msprop:Generator_ColumnPropNameInRow="ExamID3" msprop:Generator_ColumnVarNameInTable="columnExamID3" msprop:Generator_ColumnPropNameInTable="ExamID3Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID4" msprop:Generator_UserColumnName="ExamID4" msprop:Generator_ColumnPropNameInRow="ExamID4" msprop:Generator_ColumnVarNameInTable="columnExamID4" msprop:Generator_ColumnPropNameInTable="ExamID4Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID5" msprop:Generator_UserColumnName="ExamID5" msprop:Generator_ColumnPropNameInRow="ExamID5" msprop:Generator_ColumnVarNameInTable="columnExamID5" msprop:Generator_ColumnPropNameInTable="ExamID5Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID6" msprop:Generator_UserColumnName="ExamID6" msprop:Generator_ColumnPropNameInRow="ExamID6" msprop:Generator_ColumnVarNameInTable="columnExamID6" msprop:Generator_ColumnPropNameInTable="ExamID6Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID7" msprop:Generator_UserColumnName="ExamID7" msprop:Generator_ColumnPropNameInRow="ExamID7" msprop:Generator_ColumnVarNameInTable="columnExamID7" msprop:Generator_ColumnPropNameInTable="ExamID7Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID8" msprop:Generator_UserColumnName="ExamID8" msprop:Generator_ColumnPropNameInRow="ExamID8" msprop:Generator_ColumnVarNameInTable="columnExamID8" msprop:Generator_ColumnPropNameInTable="ExamID8Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID9" msprop:Generator_UserColumnName="ExamID9" msprop:Generator_ColumnPropNameInRow="ExamID9" msprop:Generator_ColumnVarNameInTable="columnExamID9" msprop:Generator_ColumnPropNameInTable="ExamID9Column" type="xs:short" minOccurs="0" />
              <xs:element name="ExamID10" msprop:Generator_UserColumnName="ExamID10" msprop:Generator_ColumnPropNameInRow="ExamID10" msprop:Generator_ColumnVarNameInTable="columnExamID10" msprop:Generator_ColumnPropNameInTable="ExamID10Column" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CallExamMaster" msprop:Generator_UserTableName="CallExamMaster" msprop:Generator_RowDeletedName="CallExamMasterRowDeleted" msprop:Generator_RowChangedName="CallExamMasterRowChanged" msprop:Generator_RowClassName="CallExamMasterRow" msprop:Generator_RowChangingName="CallExamMasterRowChanging" msprop:Generator_RowEvArgName="CallExamMasterRowChangeEvent" msprop:Generator_RowEvHandlerName="CallExamMasterRowChangeEventHandler" msprop:Generator_TableClassName="CallExamMasterDataTable" msprop:Generator_TableVarName="tableCallExamMaster" msprop:Generator_RowDeletingName="CallExamMasterRowDeleting" msprop:Generator_TablePropName="CallExamMaster">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ExamID" msprop:Generator_UserColumnName="ExamID" msprop:Generator_ColumnPropNameInRow="ExamID" msprop:Generator_ColumnVarNameInTable="columnExamID" msprop:Generator_ColumnPropNameInTable="ExamIDColumn" type="xs:short" />
              <xs:element name="Title" msprop:Generator_UserColumnName="Title" msprop:Generator_ColumnPropNameInRow="Title" msprop:Generator_ColumnVarNameInTable="columnTitle" msprop:Generator_ColumnPropNameInTable="TitleColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Status" msprop:Generator_UserColumnName="Status" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_ColumnPropNameInTable="StatusColumn" type="xs:boolean" />
              <xs:element name="DispStartTime" msprop:Generator_UserColumnName="DispStartTime" msprop:Generator_ColumnPropNameInRow="DispStartTime" msprop:Generator_ColumnVarNameInTable="columnDispStartTime" msprop:Generator_ColumnPropNameInTable="DispStartTimeColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispEndTime" msprop:Generator_UserColumnName="DispEndTime" msprop:Generator_ColumnPropNameInRow="DispEndTime" msprop:Generator_ColumnVarNameInTable="columnDispEndTime" msprop:Generator_ColumnPropNameInTable="DispEndTimeColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="5" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Capacity" msprop:Generator_UserColumnName="Capacity" msprop:Generator_ColumnPropNameInRow="Capacity" msprop:Generator_ColumnVarNameInTable="columnCapacity" msprop:Generator_ColumnPropNameInTable="CapacityColumn" type="xs:short" />
              <xs:element name="BackgroundColor" msprop:Generator_UserColumnName="BackgroundColor" msprop:Generator_ColumnPropNameInRow="BackgroundColor" msprop:Generator_ColumnVarNameInTable="columnBackgroundColor" msprop:Generator_ColumnPropNameInTable="BackgroundColorColumn" type="xs:int" />
              <xs:element name="Message" msprop:Generator_UserColumnName="Message" msprop:Generator_ColumnPropNameInRow="Message" msprop:Generator_ColumnVarNameInTable="columnMessage" msprop:Generator_ColumnPropNameInTable="MessageColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MedicalCheckNo1" msprop:Generator_UserColumnName="MedicalCheckNo1" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo1" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo1" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo1Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo2" msprop:Generator_UserColumnName="MedicalCheckNo2" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo2" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo2" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo2Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo3" msprop:Generator_UserColumnName="MedicalCheckNo3" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo3" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo3" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo3Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo4" msprop:Generator_UserColumnName="MedicalCheckNo4" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo4" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo4" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo4Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo5" msprop:Generator_UserColumnName="MedicalCheckNo5" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo5" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo5" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo5Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo6" msprop:Generator_UserColumnName="MedicalCheckNo6" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo6" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo6" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo6Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo7" msprop:Generator_UserColumnName="MedicalCheckNo7" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo7" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo7" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo7Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo8" msprop:Generator_UserColumnName="MedicalCheckNo8" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo8" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo8" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo8Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo9" msprop:Generator_UserColumnName="MedicalCheckNo9" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo9" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo9" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo9Column" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo10" msprop:Generator_UserColumnName="MedicalCheckNo10" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo10" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo10" msprop:Generator_ColumnPropNameInTable="MedicalCheckNo10Column" type="xs:short" minOccurs="0" />
              <xs:element name="Header1" msprop:Generator_UserColumnName="Header1" msprop:Generator_ColumnVarNameInTable="columnHeader1" msprop:Generator_ColumnPropNameInRow="Header1" msprop:Generator_ColumnPropNameInTable="Header1Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Header2" msprop:Generator_UserColumnName="Header2" msprop:Generator_ColumnVarNameInTable="columnHeader2" msprop:Generator_ColumnPropNameInRow="Header2" msprop:Generator_ColumnPropNameInTable="Header2Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PanelType" msprop:Generator_UserColumnName="PanelType" msprop:Generator_ColumnVarNameInTable="columnPanelType" msprop:Generator_ColumnPropNameInRow="PanelType" msprop:Generator_ColumnPropNameInTable="PanelTypeColumn" type="xs:short" />
              <xs:element name="Param1" msprop:Generator_UserColumnName="Param1" msprop:Generator_ColumnVarNameInTable="columnParam1" msprop:Generator_ColumnPropNameInRow="Param1" msprop:Generator_ColumnPropNameInTable="Param1Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="256" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Param2" msprop:Generator_UserColumnName="Param2" msprop:Generator_ColumnVarNameInTable="columnParam2" msprop:Generator_ColumnPropNameInRow="Param2" msprop:Generator_ColumnPropNameInTable="Param2Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="256" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Param3" msprop:Generator_UserColumnName="Param3" msprop:Generator_ColumnVarNameInTable="columnParam3" msprop:Generator_ColumnPropNameInRow="Param3" msprop:Generator_ColumnPropNameInTable="Param3Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="256" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GuideDetailMaster" />
      <xs:field xpath="mstns:GuideMasterDetailId" />
    </xs:unique>
    <xs:unique name="GroupMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GroupMaster" />
      <xs:field xpath="mstns:GroupId" />
    </xs:unique>
    <xs:unique name="ChildGroupMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ChildGroupMaster" />
      <xs:field xpath="mstns:ChildGroupId" />
    </xs:unique>
    <xs:unique name="ChildGroupTermMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ChildGroupTermMaster" />
      <xs:field xpath="mstns:ChildGroupTermId" />
    </xs:unique>
    <xs:unique name="ExamSettingMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ExamSettingMaster" />
      <xs:field xpath="mstns:ExamSettingId" />
    </xs:unique>
    <xs:unique name="TermGuideData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TermGuideData" />
      <xs:field xpath="mstns:IPAddress" />
    </xs:unique>
    <xs:unique name="GroupDetailMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GroupDetailMaster" />
      <xs:field xpath="mstns:GroupDetailId" />
    </xs:unique>
    <xs:unique name="GuideRootMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GuideRootMaster" />
      <xs:field xpath="mstns:GuideMasterId" />
    </xs:unique>
    <xs:unique name="GuideDivisionMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GuideDivisionMaster" />
      <xs:field xpath="mstns:GuideDivisionId" />
    </xs:unique>
    <xs:unique name="GuideMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GuideMaster" />
      <xs:field xpath="mstns:GuideId" />
    </xs:unique>
    <xs:unique name="ClientGuideData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClientGuideData" />
      <xs:field xpath="mstns:MedicalCheckDate" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:Division" />
    </xs:unique>
    <xs:unique name="ChildGroupDetailMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ChildGroupDetailMaster" />
      <xs:field xpath="mstns:ChildGroupDetailId" />
    </xs:unique>
    <xs:unique name="GuideTimeData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GuideTimeData" />
      <xs:field xpath="mstns:MedicalCheckDate" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:Division" />
      <xs:field xpath="mstns:MedicalCheckNo" />
    </xs:unique>
    <xs:unique name="IniGuideMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:IniGuideMaster" />
      <xs:field xpath="mstns:IPAddress" />
    </xs:unique>
    <xs:unique name="NextGuideData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:NextGuideData" />
      <xs:field xpath="mstns:MedicalCheckDate" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:Division" />
    </xs:unique>
    <xs:unique name="CallClientData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CallClientData" />
      <xs:field xpath="mstns:MedicalCheckDate" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:Division" />
      <xs:field xpath="mstns:ExamID" />
    </xs:unique>
    <xs:unique name="CallStatus_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CallStatus" />
      <xs:field xpath="mstns:ExamID" />
    </xs:unique>
    <xs:unique name="Constraint2" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CallTerminalMaster" />
      <xs:field xpath="mstns:TermID" />
    </xs:unique>
    <xs:unique name="CallExamMaster_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CallExamMaster" />
      <xs:field xpath="mstns:ExamID" />
    </xs:unique>
  </xs:element>
</xs:schema>