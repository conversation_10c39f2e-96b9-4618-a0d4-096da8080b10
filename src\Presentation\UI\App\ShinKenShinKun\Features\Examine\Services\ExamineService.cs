﻿namespace ShinKenShinKun;

public class ExamineService(
    IFileSystemService fileSystemService,
    IAppSettingServices appSettingServices,
    IMapper mapper) : IExamineService
{
    public ObservableCollection<MedicalCheckModel> GetMedicalCheckDisplayFilterList()
    {
        var data = AppConstants.MedicalCheckFilters
            .Select(x => new MedicalCheckModel { DisplayName = x, Value = x })
            .Prepend(new MedicalCheckModel { DisplayName = AppResources.All });
        return data.ToObservableCollection();
    }

    public ObservableCollection<StayFlagModel> GetGuidedDisplayFilterList()
    {
        return
        [
            new StayFlagModel { DisplayName = AppResources.Guidance, StayFlagType = StayFlag.Guidance },
            new StayFlagModel { DisplayName = AppResources.Plan, StayFlagType = StayFlag.Plan },
        ];
    }

    public ObservableCollection<ClientInformationModel> LoadDataClientInformations(StayFlagModel guide,
        MedicalCheckModel medicalCheck)
    {
        var data = fileSystemService.ReadClientExcelFile()
            .Where(c => guide.StayFlagType switch
                {
                    StayFlag.Guidance => FilterOnGuideState(c, medicalCheck),
                    StayFlag.Plan => FilterOnMedicalListAndState(c, medicalCheck),
                    _ => false
                }
            )
            .OrderByDescending(c => c.WaitTime);
        return !data.Any() ? [] : mapper.Map<ObservableCollection<ClientInformationModel>>(data);
    }

    private bool FilterOnMedicalListAndState(ClientInformationDto client, MedicalCheckModel medicalCheck)
    {
        var medicalChecklists = client.MedicalChecklist.SplitToList();
        var medicalCheckStates = client.MedicalCheckState.SplitToList();
        return medicalCheckStates
            .Select((x, i) =>
                new { MedicalCheck = medicalChecklists[i], State = x })
            .Any(x => IsNullOrEmpty(medicalCheck.Value)
                ? AppConstants.MedicalCheckFilters.Contains(x.MedicalCheck) && x.State == AppResources.Unchecked
                : x.MedicalCheck == medicalCheck.Value && x.State == AppResources.Unchecked);
    }

    private bool FilterOnGuideState(ClientInformationDto client, MedicalCheckModel medicalCheck)
    {
        return IsNullOrEmpty(medicalCheck.Value)
            ? AppConstants.MedicalCheckFilters.Contains(client.GuideState)
            : client.GuideState == medicalCheck.Value;
    }

    public ObservableCollection<AreaModel> GetAreaDatas()
    {
        return appSettingServices.GetAreaSettings().ToObservableCollection();
    }
}