﻿namespace ShinKenShinKun.DataAccess;

public class NetworkService : INetworkService
{
    public List<string> GetIPv4Addresses()
    {
        var ipv4Addresses = new List<string>();

        foreach(var networkInterface in NetworkInterface.GetAllNetworkInterfaces())
        {
            // Skip interfaces that are not operational or are loopback interfaces
            if(networkInterface.OperationalStatus != OperationalStatus.Up ||
                networkInterface.NetworkInterfaceType == NetworkInterfaceType.Loopback)
                continue;

            foreach(var unicastAddress in networkInterface.GetIPProperties().UnicastAddresses)
            {
                // Check if the address family is InterNetwork (IPv4)
                if(unicastAddress.Address.AddressFamily == AddressFamily.InterNetwork)
                {
                    ipv4Addresses.Add(unicastAddress.Address.ToString());
                }
            }
        }

        return ipv4Addresses;
    }
}
