﻿namespace ShinKenShinKun.DataAccess;

public class SettingNumberPad
{
    [Key]
    public int SettingNumberPadId { get; set; }

    public string SettingNumberPadName { get; set; }
    public int Row { get; set; }
    public int Column { get; set; }
    public bool AllowZeroAtHead { get; set; }
    public bool IsStandard { get; set; }


    [InverseProperty(nameof(StandardMasterKeySetting.SettingNumberPad))]
    public ICollection<StandardMasterKeySetting> StandarMasterKeySettings { get; set; }

    [InverseProperty(nameof(CustomMasterKeySetting.SettingNumberPad))]
    public ICollection<CustomMasterKeySetting> CustomMasterKeySettings { get; set; }
}