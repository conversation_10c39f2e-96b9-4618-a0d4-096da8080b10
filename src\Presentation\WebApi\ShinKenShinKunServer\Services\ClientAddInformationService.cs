﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.MedicalDataSetTableAdapters;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Transactions;

namespace ShinKenShinKunServer.Services
{
    /// <summary>
    /// ClientAddInformationService の概要の説明です
    /// </summary>
    public class ClientAddInformationService
    {
        private MedicalDataSet MedicalDataSet = new MedicalDataSet();
        private Service srv;
        private Logger logger = null;

        private readonly int ADD_DATA_COUNT = 50;
        private readonly string MEDICAL_CHECK_DATE_COLUMN = "MedicalCheckDate";
        private readonly string CLIENT_ID_COLUMN = "ClientID";
        private readonly string DIVISION_COLUMN = "Division";
        private readonly string ADD_DATA_COLUMN = "AddData";

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public ClientAddInformationService()
        {
            srv = new Service();
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sMedicalCheckDate"></param>
        /// <param name="ClientID"></param>
        /// <param name="sDivision"></param>
        /// <returns></returns>
        public string[] StrGetClientAddInformation(string sMedicalCheckDate, string ClientID, string sDivision)
        {
            try
            {
                // 動的配列を用意する。
                ArrayList addInfoList = new ArrayList();
                DateTime date = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                // データセットの関数をコール
                MedicalDataSet = srv.GetClientAddInformation(date, ClientID, sDivision);

                // 日付・当日ID・区分をキーの検索し、その行を取得する。
                DataRow row = MedicalDataSet.ClientAddInformation.Rows.Find(new object[] { date, ClientID, sDivision });

                if (row != null)
                {
                    // 検索した情報を配列に追加していく
                    // 受診日の追加
                    addInfoList.Add(row[MEDICAL_CHECK_DATE_COLUMN].ToString().PadLeft(1, ' '));
                    // 当日IDの追加
                    addInfoList.Add(row[CLIENT_ID_COLUMN].ToString().PadLeft(1, ' '));
                    // 区分の追加
                    addInfoList.Add(row[DIVISION_COLUMN].ToString().PadLeft(1, ' '));
                    for (int i = 1; i <= ADD_DATA_COUNT; i++)
                    {
                        addInfoList.Add(row[ADD_DATA_COLUMN + i.ToString()].ToString().PadLeft(1, ' '));
                    }
                }
                else
                {
                    return null;

                }
                // ArrayListからString配列に変換
                string[] addStrs = new string[addInfoList.Count];
                Array.Copy(addInfoList.ToArray(), addStrs, addInfoList.Count);
                return addStrs;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }

        public bool StrSetClientAddInfoData(string sMedicalCheckDate, string sClientID, string sDivision, string[] addInfo)
        {
            try
            {
                using (TransactionScope tx = new TransactionScope())
                {
                    int i;
                    // 日付(yyyyMMdd形式）をDateTime型に変換する
                    DateTime conMedicalCheckDate = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);
                    // データセットの関数をコール
                    MedicalDataSet = srv.GetClientAddInformation(conMedicalCheckDate, sClientID, sDivision);
                    DataRow row = MedicalDataSet.ClientAddInformation.Rows
                        .Find(new object[] { conMedicalCheckDate, sClientID, sDivision });
                    if (row == null)
                    {
                        row = MedicalDataSet.ClientAddInformation.NewClientAddInformationRow();
                    }
                    row.BeginEdit();
                    row["MedicalCheckDate"] = conMedicalCheckDate;
                    row["ClientID"] = sClientID;
                    row["Division"] = sDivision;

                    if (addInfo != null)
                    {
                        for (int j = 0; j < addInfo.Length; j++)
                        {
                            row["AddData" + Convert.ToString(j + 1)] = addInfo[j];
                            //if (addInfo[j] != null && addInfo[j] != "")
                            //{
                            //    row["AddData" + System.Convert.ToString(j + 1)] = addInfo[j].PadLeft(8, ' ');
                            //}
                        }
                    }
                    row.EndEdit();
                    if (MedicalDataSet.ClientAddInformation.Rows.Count == 0)
                    {
                        MedicalDataSet.ClientAddInformation.AddClientAddInformationRow(row as MedicalDataSet.ClientAddInformationRow);
                    }
                    ClientAddInformationTableAdapter adInfoAdapter
                        = new ClientAddInformationTableAdapter();
                    adInfoAdapter.Update(MedicalDataSet.ClientAddInformation);
                    tx.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}