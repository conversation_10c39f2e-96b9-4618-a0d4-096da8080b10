namespace ShinKenShinKun;

public partial class ExaminationTableView : ContentView
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(
        nameof(ItemsSource),
        typeof(ObservableCollection<ClientInformationModel>),
        typeof(ExaminationTableView));

    public static readonly BindableProperty ItemSelectedProperty = BindableProperty.Create(
        nameof(ItemSelected),
        typeof(ClientInformationModel),
        typeof(ExaminationBarView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty IsMaskModeProperty = BindableProperty.Create(
        nameof(IsMaskMode),
        typeof(bool),
        typeof(ExaminationTableView));

    public static readonly BindableProperty ResetScrollCommandProperty = BindableProperty.Create(
        nameof(ResetScrollCommand),
        typeof(IRelayCommand),
        typeof(ExaminationTableView));

    public ExaminationTableView()
    {
        InitializeComponent();
        PatientList.Commands.Add(new KeyDownCommand());
    }

    public ObservableCollection<ClientInformationModel> ItemsSource
    {
        get => (ObservableCollection<ClientInformationModel>)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    public ClientInformationModel ItemSelected
    {
        get => (ClientInformationModel)GetValue(ItemSelectedProperty);
        set => SetValue(ItemSelectedProperty, value);
    }

    public bool IsMaskMode
    {
        get => (bool)GetValue(IsMaskModeProperty);
        set => SetValue(IsMaskModeProperty, value);
    }

    public IRelayCommand ResetScrollCommand
    {
        get => (IRelayCommand)GetValue(ResetScrollCommandProperty);
        set => SetValue(ResetScrollCommandProperty, value);
    }

    private void PatientRowTableSelectionChanged(object sender, Telerik.Maui.Controls.DataGrid.DataGridSelectionChangedEventArgs e)
    {
        foreach(var item in e.AddedItems)
        {
            if(item is ClientInformationModel newPatient && !e.RemovedItems.Contains(newPatient))
            {
                newPatient.ContentCellColor = AppColors.White;
            }
        }
        foreach(var item in e.RemovedItems)
        {
            if(item is ClientInformationModel oldPatient && !e.AddedItems.Contains(oldPatient))
            {
                oldPatient.ContentCellColor = AppColors.Black;
            }
        }
        ResetScrollCommand.Execute(null);
    }
}
