using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class ConciergeMemberList
    {
        private const int MEDICAL_CHECK_NO_POS = 0;
        private const int MEDICAL_CHECK_NAME_POS = 1;
        private const int VIEWORDER_POS = 2;
        private string FldConciergeMemberId;
        private string FldConciergeMemberName;
        private string FldViewOrder;

        public ConciergeMemberList(string[] dbData)
        {
            this.ConciergeMemberId = dbData[MEDICAL_CHECK_NO_POS];
            this.ConciergeMemberName = dbData[MEDICAL_CHECK_NAME_POS];
            this.VierOrder = dbData[VIEWORDER_POS];
        }

        public string ConciergeMemberId
        {
            get
            {
                return this.FldConciergeMemberId;
            }
            set
            {
                this.FldConciergeMemberId = value;
            }
        }

        public string ConciergeMemberName
        {
            get
            {
                return this.FldConciergeMemberName;
            }
            set
            {
                this.FldConciergeMemberName = value;
            }
        }

        public string VierOrder
        {
            get
            {
                return this.FldViewOrder;
            }
            set
            {
                this.FldViewOrder = value;
            }
        }
    }
}
