﻿namespace ShinKenShinKun.DataAccess;

public partial class ComponentLayoutModel : BaseModel
{
    public string ComponentId { get; set; } = null!;
    public ComponentType ComponentType { get; set; }
    public string AreaId { get; set; } = null!;
    public int RowIndex { get; set; }
    public int ColumnIndex { get; set; }
    public int RowSpan { get; set; }
    public int ColumnSpan { get; set; }
    public double Height { get; set; }
    public double Width { get; set; }
    public string Margin { get; set; } = null!;
    public LayoutOptions HorizontalOptions { get; set; }
    public LayoutOptions VerticalOptions { get; set; }

    public Thickness MarginDisplay
    {
        get
        {
            var margins = Margin.SplitToList();
            if (margins.Count == 4 &&
                double.TryParse(margins[0], out double left) &&
                double.TryParse(margins[1], out double top) &&
                double.TryParse(margins[2], out double right) &&
                double.TryParse(margins[3], out double bottom))
            {
                return new Thickness(left, top, right, bottom);
            }
            return new Thickness(0);
        }
    }
}