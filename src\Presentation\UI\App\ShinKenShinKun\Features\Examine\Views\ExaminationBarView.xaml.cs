namespace ShinKenShinKun;

public partial class ExaminationBarView : ContentView
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(
        nameof(ItemsSource),
        typeof(IEnumerable),
        typeof(ExaminationBarView));

    public static readonly BindableProperty ItemSelectedProperty = BindableProperty.Create(
        nameof(ItemSelected),
        typeof(object),
        typeof(ExaminationBarView),
        defaultBindingMode: BindingMode.TwoWay);

    public ExaminationBarView()
    {
        InitializeComponent();
    }

    public IEnumerable ItemsSource
    {
        get => (IEnumerable)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }

    public object ItemSelected
    {
        get => (object)GetValue(ItemSelectedProperty);
        set => SetValue(ItemSelectedProperty, value);
    }
}