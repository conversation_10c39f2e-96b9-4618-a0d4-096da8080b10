namespace ShinKenShinKun;
public partial class IndividualStateCounterView : ContentView
{
    public IndividualStateCounterView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty LabelProperty = BindableProperty.Create(
    nameof(Label),
    typeof(string),
    typeof(IndividualStateCounterView),
    string.Empty,
    BindingMode.TwoWay);

    public string Label
    {
        get => (string)GetValue(LabelProperty);
        set => SetValue(LabelProperty, value);
    }

    public static readonly BindableProperty LabelColorProperty = BindableProperty.Create(
    nameof(LabelColor),
    typeof(Color),
    typeof(IndividualStateCounterView),
    default(Color),
    BindingMode.TwoWay);

    public Color LabelColor
    {
        get => (Color)GetValue(LabelColorProperty);
        set => SetValue(LabelColorProperty, value);
    }

    public static readonly BindableProperty ValueColorProperty = BindableProperty.Create(
    nameof(ValueColor),
    typeof(Color),
    typeof(IndividualStateCounterView),
    default(Color),
    BindingMode.TwoWay);

    public Color ValueColor
    {
        get => (Color)GetValue(ValueColorProperty);
        set => SetValue(ValueColorProperty, value);
    }

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
    nameof(Value),
    typeof(double),
    typeof(IndividualStateCounterView),
    default(double),
    BindingMode.TwoWay);

    public double Value
    {
        get => (double)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }
}