<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="RemarksDataSet" targetNamespace="http://tempuri.org/RemarksDataSet.xsd" xmlns:mstns="http://tempuri.org/RemarksDataSet.xsd" xmlns="http://tempuri.org/RemarksDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupConnectionString" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="RemarksItemListTableAdapter" GeneratorDataComponentClassName="RemarksItemListTableAdapter" Name="RemarksItemList" UserDataComponentName="RemarksItemListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.RemarksItemList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [RemarksItemList] WHERE (([RemarksItemNo] = @Original_RemarksItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_RemarksItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="RemarksItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [RemarksItemList] ([RemarksItemNo], [RemarksItemName], [RemarksItemDetail]) VALUES (@RemarksItemNo, @RemarksItemName, @RemarksItemDetail)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@RemarksItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="RemarksItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RemarksItemName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RemarksItemName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RemarksItemDetail" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RemarksItemDetail" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          RemarksItemNo, RemarksItemName, RemarksItemDetail
FROM            RemarksItemList
WHERE           (RemarksItemNo = @RemarksItemNo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="RemarksItemNo" ColumnName="RemarksItemNo" DataSourceName="MedicalCheckup.dbo.RemarksItemList" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@RemarksItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="RemarksItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [RemarksItemList] SET [RemarksItemNo] = @RemarksItemNo, [RemarksItemName] = @RemarksItemName, [RemarksItemDetail] = @RemarksItemDetail WHERE (([RemarksItemNo] = @Original_RemarksItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@RemarksItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="RemarksItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RemarksItemName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RemarksItemName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@RemarksItemDetail" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="RemarksItemDetail" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_RemarksItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="RemarksItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="RemarksItemNo" DataSetColumn="RemarksItemNo" />
              <Mapping SourceColumn="RemarksItemName" DataSetColumn="RemarksItemName" />
              <Mapping SourceColumn="RemarksItemDetail" DataSetColumn="RemarksItemDetail" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.RemarksItemList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          RemarksItemNo, RemarksItemName, RemarksItemDetail
FROM            RemarksItemList</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientRemarksInformationTableAdapter" GeneratorDataComponentClassName="ClientRemarksInformationTableAdapter" Name="ClientRemarksInformation" UserDataComponentName="ClientRemarksInformationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="FukuokaDatabase.dbo.ClientRemarksInformation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [ClientRemarksInformation] WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [ClientRemarksInformation] ([MedicalCheckDate], [ClientID], [Division], [Comment1], [Comment2], [Comment3], [Comment4], [Comment5], [Comment6], [Comment7], [Comment8], [Comment9], [Comment10], [Comment11], [Comment12], [Comment13], [Comment14], [Comment15], [Comment16], [Comment17], [Comment18], [Comment19], [Comment20], [Comment21], [Comment22], [Comment23], [Comment24], [Comment25], [Comment26], [Comment27], [Comment28], [Comment29], [Comment30], [Comment31], [Comment32], [Comment33], [Comment34], [Comment35], [Comment36], [Comment37], [Comment38], [Comment39], [Comment40], [Comment41], [Comment42], [Comment43], [Comment44], [Comment45], [Comment46], [Comment47], [Comment48], [Comment49], [Comment50]) VALUES (@MedicalCheckDate, @ClientID, @Division, @Comment1, @Comment2, @Comment3, @Comment4, @Comment5, @Comment6, @Comment7, @Comment8, @Comment9, @Comment10, @Comment11, @Comment12, @Comment13, @Comment14, @Comment15, @Comment16, @Comment17, @Comment18, @Comment19, @Comment20, @Comment21, @Comment22, @Comment23, @Comment24, @Comment25, @Comment26, @Comment27, @Comment28, @Comment29, @Comment30, @Comment31, @Comment32, @Comment33, @Comment34, @Comment35, @Comment36, @Comment37, @Comment38, @Comment39, @Comment40, @Comment41, @Comment42, @Comment43, @Comment44, @Comment45, @Comment46, @Comment47, @Comment48, @Comment49, @Comment50)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT          MedicalCheckDate, ClientID, Division, Comment1, Comment2, Comment3, 
                      Comment4, Comment5, Comment6, Comment7, Comment8, Comment9, 
                      Comment10, Comment11, Comment12, Comment13, Comment14, Comment15, 
                      Comment16, Comment17, Comment18, Comment19, Comment20, Comment21, 
                      Comment22, Comment23, Comment24, Comment25, Comment26, Comment27, 
                      Comment28, Comment29, Comment30, Comment31, Comment32, Comment33, 
                      Comment34, Comment35, Comment36, Comment37, Comment38, Comment39, 
                      Comment40, Comment41, Comment42, Comment43, Comment44, Comment45, 
                      Comment46, Comment47, Comment48, Comment49, Comment50
FROM            ClientRemarksInformation
WHERE           (MedicalCheckDate = @MedicalCheckDate) AND (ClientID = @ClientID) AND 
                      (Division = @Division)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckDate" ColumnName="MedicalCheckDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Division" ColumnName="Division" DataSourceName="" DataTypeServer="nvarchar(3)" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="3" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [ClientRemarksInformation] SET [MedicalCheckDate] = @MedicalCheckDate, [ClientID] = @ClientID, [Division] = @Division, [Comment1] = @Comment1, [Comment2] = @Comment2, [Comment3] = @Comment3, [Comment4] = @Comment4, [Comment5] = @Comment5, [Comment6] = @Comment6, [Comment7] = @Comment7, [Comment8] = @Comment8, [Comment9] = @Comment9, [Comment10] = @Comment10, [Comment11] = @Comment11, [Comment12] = @Comment12, [Comment13] = @Comment13, [Comment14] = @Comment14, [Comment15] = @Comment15, [Comment16] = @Comment16, [Comment17] = @Comment17, [Comment18] = @Comment18, [Comment19] = @Comment19, [Comment20] = @Comment20, [Comment21] = @Comment21, [Comment22] = @Comment22, [Comment23] = @Comment23, [Comment24] = @Comment24, [Comment25] = @Comment25, [Comment26] = @Comment26, [Comment27] = @Comment27, [Comment28] = @Comment28, [Comment29] = @Comment29, [Comment30] = @Comment30, [Comment31] = @Comment31, [Comment32] = @Comment32, [Comment33] = @Comment33, [Comment34] = @Comment34, [Comment35] = @Comment35, [Comment36] = @Comment36, [Comment37] = @Comment37, [Comment38] = @Comment38, [Comment39] = @Comment39, [Comment40] = @Comment40, [Comment41] = @Comment41, [Comment42] = @Comment42, [Comment43] = @Comment43, [Comment44] = @Comment44, [Comment45] = @Comment45, [Comment46] = @Comment46, [Comment47] = @Comment47, [Comment48] = @Comment48, [Comment49] = @Comment49, [Comment50] = @Comment50 WHERE (([MedicalCheckDate] = @Original_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original_Division))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Comment50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Comment50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_MedicalCheckDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="MedicalCheckDate" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Division" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Division" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckDate" DataSetColumn="MedicalCheckDate" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Division" DataSetColumn="Division" />
              <Mapping SourceColumn="Comment1" DataSetColumn="Comment1" />
              <Mapping SourceColumn="Comment2" DataSetColumn="Comment2" />
              <Mapping SourceColumn="Comment3" DataSetColumn="Comment3" />
              <Mapping SourceColumn="Comment4" DataSetColumn="Comment4" />
              <Mapping SourceColumn="Comment5" DataSetColumn="Comment5" />
              <Mapping SourceColumn="Comment6" DataSetColumn="Comment6" />
              <Mapping SourceColumn="Comment7" DataSetColumn="Comment7" />
              <Mapping SourceColumn="Comment8" DataSetColumn="Comment8" />
              <Mapping SourceColumn="Comment9" DataSetColumn="Comment9" />
              <Mapping SourceColumn="Comment10" DataSetColumn="Comment10" />
              <Mapping SourceColumn="Comment11" DataSetColumn="Comment11" />
              <Mapping SourceColumn="Comment12" DataSetColumn="Comment12" />
              <Mapping SourceColumn="Comment13" DataSetColumn="Comment13" />
              <Mapping SourceColumn="Comment14" DataSetColumn="Comment14" />
              <Mapping SourceColumn="Comment15" DataSetColumn="Comment15" />
              <Mapping SourceColumn="Comment16" DataSetColumn="Comment16" />
              <Mapping SourceColumn="Comment17" DataSetColumn="Comment17" />
              <Mapping SourceColumn="Comment18" DataSetColumn="Comment18" />
              <Mapping SourceColumn="Comment19" DataSetColumn="Comment19" />
              <Mapping SourceColumn="Comment20" DataSetColumn="Comment20" />
              <Mapping SourceColumn="Comment21" DataSetColumn="Comment21" />
              <Mapping SourceColumn="Comment22" DataSetColumn="Comment22" />
              <Mapping SourceColumn="Comment23" DataSetColumn="Comment23" />
              <Mapping SourceColumn="Comment24" DataSetColumn="Comment24" />
              <Mapping SourceColumn="Comment25" DataSetColumn="Comment25" />
              <Mapping SourceColumn="Comment26" DataSetColumn="Comment26" />
              <Mapping SourceColumn="Comment27" DataSetColumn="Comment27" />
              <Mapping SourceColumn="Comment28" DataSetColumn="Comment28" />
              <Mapping SourceColumn="Comment29" DataSetColumn="Comment29" />
              <Mapping SourceColumn="Comment30" DataSetColumn="Comment30" />
              <Mapping SourceColumn="Comment31" DataSetColumn="Comment31" />
              <Mapping SourceColumn="Comment32" DataSetColumn="Comment32" />
              <Mapping SourceColumn="Comment33" DataSetColumn="Comment33" />
              <Mapping SourceColumn="Comment34" DataSetColumn="Comment34" />
              <Mapping SourceColumn="Comment35" DataSetColumn="Comment35" />
              <Mapping SourceColumn="Comment36" DataSetColumn="Comment36" />
              <Mapping SourceColumn="Comment37" DataSetColumn="Comment37" />
              <Mapping SourceColumn="Comment38" DataSetColumn="Comment38" />
              <Mapping SourceColumn="Comment39" DataSetColumn="Comment39" />
              <Mapping SourceColumn="Comment40" DataSetColumn="Comment40" />
              <Mapping SourceColumn="Comment41" DataSetColumn="Comment41" />
              <Mapping SourceColumn="Comment42" DataSetColumn="Comment42" />
              <Mapping SourceColumn="Comment43" DataSetColumn="Comment43" />
              <Mapping SourceColumn="Comment44" DataSetColumn="Comment44" />
              <Mapping SourceColumn="Comment45" DataSetColumn="Comment45" />
              <Mapping SourceColumn="Comment46" DataSetColumn="Comment46" />
              <Mapping SourceColumn="Comment47" DataSetColumn="Comment47" />
              <Mapping SourceColumn="Comment48" DataSetColumn="Comment48" />
              <Mapping SourceColumn="Comment49" DataSetColumn="Comment49" />
              <Mapping SourceColumn="Comment50" DataSetColumn="Comment50" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="RemarksDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="RemarksDataSet" msprop:Generator_DataSetName="RemarksDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="RemarksItemList" msprop:Generator_UserTableName="RemarksItemList" msprop:Generator_RowDeletedName="RemarksItemListRowDeleted" msprop:Generator_RowChangedName="RemarksItemListRowChanged" msprop:Generator_RowClassName="RemarksItemListRow" msprop:Generator_RowChangingName="RemarksItemListRowChanging" msprop:Generator_RowEvArgName="RemarksItemListRowChangeEvent" msprop:Generator_RowEvHandlerName="RemarksItemListRowChangeEventHandler" msprop:Generator_TableClassName="RemarksItemListDataTable" msprop:Generator_TableVarName="tableRemarksItemList" msprop:Generator_RowDeletingName="RemarksItemListRowDeleting" msprop:Generator_TablePropName="RemarksItemList">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="RemarksItemNo" msprop:Generator_UserColumnName="RemarksItemNo" msprop:Generator_ColumnVarNameInTable="columnRemarksItemNo" msprop:Generator_ColumnPropNameInRow="RemarksItemNo" msprop:Generator_ColumnPropNameInTable="RemarksItemNoColumn" type="xs:short" />
              <xs:element name="RemarksItemName" msprop:Generator_UserColumnName="RemarksItemName" msprop:Generator_ColumnVarNameInTable="columnRemarksItemName" msprop:Generator_ColumnPropNameInRow="RemarksItemName" msprop:Generator_ColumnPropNameInTable="RemarksItemNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="40" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="RemarksItemDetail" msprop:Generator_UserColumnName="RemarksItemDetail" msprop:Generator_ColumnVarNameInTable="columnRemarksItemDetail" msprop:Generator_ColumnPropNameInRow="RemarksItemDetail" msprop:Generator_ColumnPropNameInTable="RemarksItemDetailColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientRemarksInformation" msprop:Generator_UserTableName="ClientRemarksInformation" msprop:Generator_RowDeletedName="ClientRemarksInformationRowDeleted" msprop:Generator_TableClassName="ClientRemarksInformationDataTable" msprop:Generator_RowChangedName="ClientRemarksInformationRowChanged" msprop:Generator_RowClassName="ClientRemarksInformationRow" msprop:Generator_RowChangingName="ClientRemarksInformationRowChanging" msprop:Generator_RowEvArgName="ClientRemarksInformationRowChangeEvent" msprop:Generator_RowEvHandlerName="ClientRemarksInformationRowChangeEventHandler" msprop:Generator_TablePropName="ClientRemarksInformation" msprop:Generator_TableVarName="tableClientRemarksInformation" msprop:Generator_RowDeletingName="ClientRemarksInformationRowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckDate" msprop:Generator_UserColumnName="MedicalCheckDate" msprop:Generator_ColumnPropNameInRow="MedicalCheckDate" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckDate" msprop:Generator_ColumnPropNameInTable="MedicalCheckDateColumn" type="xs:dateTime" />
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Division" msprop:Generator_UserColumnName="Division" msprop:Generator_ColumnPropNameInRow="Division" msprop:Generator_ColumnVarNameInTable="columnDivision" msprop:Generator_ColumnPropNameInTable="DivisionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Comment1" msprop:Generator_UserColumnName="Comment1" msprop:Generator_ColumnPropNameInRow="Comment1" msprop:Generator_ColumnVarNameInTable="columnComment1" msprop:Generator_ColumnPropNameInTable="Comment1Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment2" msprop:Generator_UserColumnName="Comment2" msprop:Generator_ColumnPropNameInRow="Comment2" msprop:Generator_ColumnVarNameInTable="columnComment2" msprop:Generator_ColumnPropNameInTable="Comment2Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment3" msprop:Generator_UserColumnName="Comment3" msprop:Generator_ColumnPropNameInRow="Comment3" msprop:Generator_ColumnVarNameInTable="columnComment3" msprop:Generator_ColumnPropNameInTable="Comment3Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment4" msprop:Generator_UserColumnName="Comment4" msprop:Generator_ColumnPropNameInRow="Comment4" msprop:Generator_ColumnVarNameInTable="columnComment4" msprop:Generator_ColumnPropNameInTable="Comment4Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment5" msprop:Generator_UserColumnName="Comment5" msprop:Generator_ColumnPropNameInRow="Comment5" msprop:Generator_ColumnVarNameInTable="columnComment5" msprop:Generator_ColumnPropNameInTable="Comment5Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment6" msprop:Generator_UserColumnName="Comment6" msprop:Generator_ColumnPropNameInRow="Comment6" msprop:Generator_ColumnVarNameInTable="columnComment6" msprop:Generator_ColumnPropNameInTable="Comment6Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment7" msprop:Generator_UserColumnName="Comment7" msprop:Generator_ColumnPropNameInRow="Comment7" msprop:Generator_ColumnVarNameInTable="columnComment7" msprop:Generator_ColumnPropNameInTable="Comment7Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment8" msprop:Generator_UserColumnName="Comment8" msprop:Generator_ColumnPropNameInRow="Comment8" msprop:Generator_ColumnVarNameInTable="columnComment8" msprop:Generator_ColumnPropNameInTable="Comment8Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment9" msprop:Generator_UserColumnName="Comment9" msprop:Generator_ColumnPropNameInRow="Comment9" msprop:Generator_ColumnVarNameInTable="columnComment9" msprop:Generator_ColumnPropNameInTable="Comment9Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment10" msprop:Generator_UserColumnName="Comment10" msprop:Generator_ColumnPropNameInRow="Comment10" msprop:Generator_ColumnVarNameInTable="columnComment10" msprop:Generator_ColumnPropNameInTable="Comment10Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment11" msprop:Generator_UserColumnName="Comment11" msprop:Generator_ColumnPropNameInRow="Comment11" msprop:Generator_ColumnVarNameInTable="columnComment11" msprop:Generator_ColumnPropNameInTable="Comment11Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment12" msprop:Generator_UserColumnName="Comment12" msprop:Generator_ColumnPropNameInRow="Comment12" msprop:Generator_ColumnVarNameInTable="columnComment12" msprop:Generator_ColumnPropNameInTable="Comment12Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment13" msprop:Generator_UserColumnName="Comment13" msprop:Generator_ColumnPropNameInRow="Comment13" msprop:Generator_ColumnVarNameInTable="columnComment13" msprop:Generator_ColumnPropNameInTable="Comment13Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment14" msprop:Generator_UserColumnName="Comment14" msprop:Generator_ColumnPropNameInRow="Comment14" msprop:Generator_ColumnVarNameInTable="columnComment14" msprop:Generator_ColumnPropNameInTable="Comment14Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment15" msprop:Generator_UserColumnName="Comment15" msprop:Generator_ColumnPropNameInRow="Comment15" msprop:Generator_ColumnVarNameInTable="columnComment15" msprop:Generator_ColumnPropNameInTable="Comment15Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment16" msprop:Generator_UserColumnName="Comment16" msprop:Generator_ColumnPropNameInRow="Comment16" msprop:Generator_ColumnVarNameInTable="columnComment16" msprop:Generator_ColumnPropNameInTable="Comment16Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment17" msprop:Generator_UserColumnName="Comment17" msprop:Generator_ColumnPropNameInRow="Comment17" msprop:Generator_ColumnVarNameInTable="columnComment17" msprop:Generator_ColumnPropNameInTable="Comment17Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment18" msprop:Generator_UserColumnName="Comment18" msprop:Generator_ColumnPropNameInRow="Comment18" msprop:Generator_ColumnVarNameInTable="columnComment18" msprop:Generator_ColumnPropNameInTable="Comment18Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment19" msprop:Generator_UserColumnName="Comment19" msprop:Generator_ColumnPropNameInRow="Comment19" msprop:Generator_ColumnVarNameInTable="columnComment19" msprop:Generator_ColumnPropNameInTable="Comment19Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment20" msprop:Generator_UserColumnName="Comment20" msprop:Generator_ColumnPropNameInRow="Comment20" msprop:Generator_ColumnVarNameInTable="columnComment20" msprop:Generator_ColumnPropNameInTable="Comment20Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment21" msprop:Generator_UserColumnName="Comment21" msprop:Generator_ColumnPropNameInRow="Comment21" msprop:Generator_ColumnVarNameInTable="columnComment21" msprop:Generator_ColumnPropNameInTable="Comment21Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment22" msprop:Generator_UserColumnName="Comment22" msprop:Generator_ColumnPropNameInRow="Comment22" msprop:Generator_ColumnVarNameInTable="columnComment22" msprop:Generator_ColumnPropNameInTable="Comment22Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment23" msprop:Generator_UserColumnName="Comment23" msprop:Generator_ColumnPropNameInRow="Comment23" msprop:Generator_ColumnVarNameInTable="columnComment23" msprop:Generator_ColumnPropNameInTable="Comment23Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment24" msprop:Generator_UserColumnName="Comment24" msprop:Generator_ColumnPropNameInRow="Comment24" msprop:Generator_ColumnVarNameInTable="columnComment24" msprop:Generator_ColumnPropNameInTable="Comment24Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment25" msprop:Generator_UserColumnName="Comment25" msprop:Generator_ColumnPropNameInRow="Comment25" msprop:Generator_ColumnVarNameInTable="columnComment25" msprop:Generator_ColumnPropNameInTable="Comment25Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment26" msprop:Generator_UserColumnName="Comment26" msprop:Generator_ColumnPropNameInRow="Comment26" msprop:Generator_ColumnVarNameInTable="columnComment26" msprop:Generator_ColumnPropNameInTable="Comment26Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment27" msprop:Generator_UserColumnName="Comment27" msprop:Generator_ColumnPropNameInRow="Comment27" msprop:Generator_ColumnVarNameInTable="columnComment27" msprop:Generator_ColumnPropNameInTable="Comment27Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment28" msprop:Generator_UserColumnName="Comment28" msprop:Generator_ColumnPropNameInRow="Comment28" msprop:Generator_ColumnVarNameInTable="columnComment28" msprop:Generator_ColumnPropNameInTable="Comment28Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment29" msprop:Generator_UserColumnName="Comment29" msprop:Generator_ColumnPropNameInRow="Comment29" msprop:Generator_ColumnVarNameInTable="columnComment29" msprop:Generator_ColumnPropNameInTable="Comment29Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment30" msprop:Generator_UserColumnName="Comment30" msprop:Generator_ColumnPropNameInRow="Comment30" msprop:Generator_ColumnVarNameInTable="columnComment30" msprop:Generator_ColumnPropNameInTable="Comment30Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment31" msprop:Generator_UserColumnName="Comment31" msprop:Generator_ColumnPropNameInRow="Comment31" msprop:Generator_ColumnVarNameInTable="columnComment31" msprop:Generator_ColumnPropNameInTable="Comment31Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment32" msprop:Generator_UserColumnName="Comment32" msprop:Generator_ColumnPropNameInRow="Comment32" msprop:Generator_ColumnVarNameInTable="columnComment32" msprop:Generator_ColumnPropNameInTable="Comment32Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment33" msprop:Generator_UserColumnName="Comment33" msprop:Generator_ColumnPropNameInRow="Comment33" msprop:Generator_ColumnVarNameInTable="columnComment33" msprop:Generator_ColumnPropNameInTable="Comment33Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment34" msprop:Generator_UserColumnName="Comment34" msprop:Generator_ColumnPropNameInRow="Comment34" msprop:Generator_ColumnVarNameInTable="columnComment34" msprop:Generator_ColumnPropNameInTable="Comment34Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment35" msprop:Generator_UserColumnName="Comment35" msprop:Generator_ColumnPropNameInRow="Comment35" msprop:Generator_ColumnVarNameInTable="columnComment35" msprop:Generator_ColumnPropNameInTable="Comment35Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment36" msprop:Generator_UserColumnName="Comment36" msprop:Generator_ColumnPropNameInRow="Comment36" msprop:Generator_ColumnVarNameInTable="columnComment36" msprop:Generator_ColumnPropNameInTable="Comment36Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment37" msprop:Generator_UserColumnName="Comment37" msprop:Generator_ColumnPropNameInRow="Comment37" msprop:Generator_ColumnVarNameInTable="columnComment37" msprop:Generator_ColumnPropNameInTable="Comment37Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment38" msprop:Generator_UserColumnName="Comment38" msprop:Generator_ColumnPropNameInRow="Comment38" msprop:Generator_ColumnVarNameInTable="columnComment38" msprop:Generator_ColumnPropNameInTable="Comment38Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment39" msprop:Generator_UserColumnName="Comment39" msprop:Generator_ColumnPropNameInRow="Comment39" msprop:Generator_ColumnVarNameInTable="columnComment39" msprop:Generator_ColumnPropNameInTable="Comment39Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment40" msprop:Generator_UserColumnName="Comment40" msprop:Generator_ColumnPropNameInRow="Comment40" msprop:Generator_ColumnVarNameInTable="columnComment40" msprop:Generator_ColumnPropNameInTable="Comment40Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment41" msprop:Generator_UserColumnName="Comment41" msprop:Generator_ColumnPropNameInRow="Comment41" msprop:Generator_ColumnVarNameInTable="columnComment41" msprop:Generator_ColumnPropNameInTable="Comment41Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment42" msprop:Generator_UserColumnName="Comment42" msprop:Generator_ColumnPropNameInRow="Comment42" msprop:Generator_ColumnVarNameInTable="columnComment42" msprop:Generator_ColumnPropNameInTable="Comment42Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment43" msprop:Generator_UserColumnName="Comment43" msprop:Generator_ColumnPropNameInRow="Comment43" msprop:Generator_ColumnVarNameInTable="columnComment43" msprop:Generator_ColumnPropNameInTable="Comment43Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment44" msprop:Generator_UserColumnName="Comment44" msprop:Generator_ColumnPropNameInRow="Comment44" msprop:Generator_ColumnVarNameInTable="columnComment44" msprop:Generator_ColumnPropNameInTable="Comment44Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment45" msprop:Generator_UserColumnName="Comment45" msprop:Generator_ColumnPropNameInRow="Comment45" msprop:Generator_ColumnVarNameInTable="columnComment45" msprop:Generator_ColumnPropNameInTable="Comment45Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment46" msprop:Generator_UserColumnName="Comment46" msprop:Generator_ColumnPropNameInRow="Comment46" msprop:Generator_ColumnVarNameInTable="columnComment46" msprop:Generator_ColumnPropNameInTable="Comment46Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment47" msprop:Generator_UserColumnName="Comment47" msprop:Generator_ColumnPropNameInRow="Comment47" msprop:Generator_ColumnVarNameInTable="columnComment47" msprop:Generator_ColumnPropNameInTable="Comment47Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment48" msprop:Generator_UserColumnName="Comment48" msprop:Generator_ColumnPropNameInRow="Comment48" msprop:Generator_ColumnVarNameInTable="columnComment48" msprop:Generator_ColumnPropNameInTable="Comment48Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment49" msprop:Generator_UserColumnName="Comment49" msprop:Generator_ColumnPropNameInRow="Comment49" msprop:Generator_ColumnVarNameInTable="columnComment49" msprop:Generator_ColumnPropNameInTable="Comment49Column" type="xs:short" minOccurs="0" />
              <xs:element name="Comment50" msprop:Generator_UserColumnName="Comment50" msprop:Generator_ColumnPropNameInRow="Comment50" msprop:Generator_ColumnVarNameInTable="columnComment50" msprop:Generator_ColumnPropNameInTable="Comment50Column" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:RemarksItemList" />
      <xs:field xpath="mstns:RemarksItemNo" />
    </xs:unique>
    <xs:unique name="ClientRemarksInformation_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClientRemarksInformation" />
      <xs:field xpath="mstns:MedicalCheckDate" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:Division" />
    </xs:unique>
  </xs:element>
</xs:schema>