﻿using Ganss.Excel;

namespace ShinKenShinKun.Utils;

public static class ExcelHelper
{
    public static List<T> Create<T>(string fileName, Action<ExcelMapper>? configure = null)
        where T : class
    {
        var excel = new ExcelMapper(fileName);
        configure?.Invoke(excel);
        var data = excel.Fetch<T>().ToList();
        return data;
    }

    public static List<T> Create<T>(string fileName, string sheetName, Action<ExcelMapper>? configure = null)
    where T : class
    {
        var excel = new ExcelMapper(fileName);
        configure?.Invoke(excel);
        var data = excel.Fetch<T>(sheetName).ToList();
        return data;
    }

    public static List<T> CreateEntity<T>(string fileName, string sheetName, Action<ExcelMapper>? configure = null) where T : class
    {
        var excel = new ExcelMapper(fileName);
        configure?.Invoke(excel);
        var data = excel.Fetch<T>(sheetName).ToList();

        foreach (var item in data)
        {
            ClearNavigationProperties(item);
        }

        return data;
    }

    private static void ClearNavigationProperties<T>(T item) where T : class
    {
        var properties = typeof(T)
            .GetProperties()
            .Where(p => !p.PropertyType.IsValueType &&
                        p.PropertyType != typeof(string) &&
                        !typeof(IEnumerable<>).IsAssignableFrom(p.PropertyType));

        foreach (var prop in properties)
        {
            if (prop.CanWrite)
            {
                prop.SetValue(item, null);
            }
        }
    }
}