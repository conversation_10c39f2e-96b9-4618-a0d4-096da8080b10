﻿namespace ShinKenShinKun.Utils;

#region リストビューのヘッダクリック時並び替え
public class ListViewItemComparer : IComparer
{
    #region 変数
    public enum ComparerMode
    {
        String,
        Integer,
        DateTime
    };
    private int _column;
    private ComparerMode _mode;
    private ComparerMode[] _columnModes;
    #endregion

    #region 並び替えるListView列の番号
    public int Column
    {
        set
        {
            if (_column == value)
            {
            }
            _column = value;
        }
        get
        {
            return _column;
        }
    }
    #endregion

    #region 並び替えの方法
    public ComparerMode Mode
    {
        set
        {
            _mode = value;
        }
        get
        {
            return _mode;
        }
    }
    #endregion

    #region 列ごとの並び替えの方法
    public ComparerMode[] ColumnModes
    {
        set
        {
            _columnModes = value;
        }
    }
    #endregion

    #region コンストラクタ
    /// <param name="col">並び替える列番号</param>
    /// <param name="ord">昇順か降順か</param>
    /// <param name="mthd">並び替えの方法</param>
    public ListViewItemComparer(
        int col, ComparerMode cmod)
    {
        _column = col;
        _mode = cmod;
    }
    public ListViewItemComparer()
    {
        _column = 0;
        _mode = ComparerMode.String;
    }
    #endregion

    #region 比較用関数
    //xがyより小さいときはマイナスの数、大きいときはプラスの数、
    //同じときは0を返す
    public int Compare(object x, object y)
    {
        int result = 0;

        ////ListViewItemの取得
        //ListViewItem itemx = (ListViewItem)x;
        //ListViewItem itemy = (ListViewItem)y;
        ////並べ替えの方法を決定
        //if (_columnModes != null && _columnModes.Length > _column)
        //    _mode = _columnModes[_column];
        ////並び替えの方法別に、xとyを比較する
        //switch (_mode)
        //{
        //    case ComparerMode.String:
        //        result = string.Compare(itemx.SubItems[_column].Text,
        //            itemy.SubItems[_column].Text);
        //        break;
        //    case ComparerMode.Integer:
        //        result = int.Parse(itemx.SubItems[_column].Text) -
        //            int.Parse(itemy.SubItems[_column].Text);
        //        break;
        //    case ComparerMode.DateTime:
        //        result = DateTime.Compare(
        //            DateTime.Parse(itemx.SubItems[_column].Text),
        //            DateTime.Parse(itemy.SubItems[_column].Text));
        //        break;
        //}

        return result;
    }
    #endregion
}
#endregion
