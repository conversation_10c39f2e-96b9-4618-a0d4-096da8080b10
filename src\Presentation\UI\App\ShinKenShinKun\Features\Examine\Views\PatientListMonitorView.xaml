<?xml version="1.0" encoding="utf-8"?>

<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.PatientListMonitorView"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:ExamineBeforeAuthViewModel"
    x:Name="this">
    <Grid
        Padding="{x:Static ui:Dimens.SpacingSm2}"
        RowSpacing="{x:Static ui:Dimens.SpacingSm2}"
        RowDefinitions="*, auto">
        <telerik:RadBorder
            Grid.Row="0"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <Grid
                RowSpacing="{x:Static ui:Dimens.SpacingMd}"
                RowDefinitions="auto, *, auto">
                <Grid
                    Grid.Row="0"
                    ColumnSpacing="{x:Static ui:Dimens.SpacingMd}"
                    ColumnDefinitions="auto, *, auto, auto">
                    <app:ExaminationBarView
                        Grid.Column="0"
                        ItemSelected="{Binding GuidedSelected}"
                        ItemsSource="{Binding GuidedFilters}" />
                    <app:ExaminationBarView
                        Grid.Column="1"
                        ItemSelected="{Binding MedicalCheckSelected}"
                        ItemsSource="{Binding MedicalCheckFilters}" />
                    <telerik:RadBorder
                        Grid.Column="2"
                        Style="{x:Static ui:Styles.UpdateBorderStyle}">
                        <VerticalStackLayout
                            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing2}}"
                            VerticalOptions="End">
                            <Image
                                Aspect="Fill"
                                HorizontalOptions="Center"
                                HeightRequest="{x:Static ui:Dimens.Height28}"
                                WidthRequest="{x:Static ui:Dimens.Width28}"
                                Source="{x:Static ui:Icons.Update}" />
                            <Label
                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                                HorizontalTextAlignment="Center"
                                Text="{x:Static resources:AppResources.Update}" />
                        </VerticalStackLayout>
                        <telerik:RadBorder.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding UpdateCommand}" />
                        </telerik:RadBorder.GestureRecognizers>
                    </telerik:RadBorder>
                    <telerik:RadBorder
                        Grid.Column="3"
                        Style="{x:Static ui:Styles.GuideBorderStyle}">
                        <VerticalStackLayout
                            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing2}}"
                            VerticalOptions="End">
                            <Image
                                Aspect="Fill"
                                HorizontalOptions="Center"
                                HeightRequest="{x:Static ui:Dimens.Height28}"
                                WidthRequest="{x:Static ui:Dimens.Width28}">
                                <Image.Triggers>
                                    <DataTrigger
                                        TargetType="Image"
                                        Binding="{Binding IsPlayGuideMode}"
                                        Value="False">
                                        <Setter
                                            Property="Source"
                                            Value="{x:Static ui:Icons.PlayCircle}" />
                                    </DataTrigger>
                                    <DataTrigger
                                        TargetType="Image"
                                        Binding="{Binding IsPlayGuideMode}"
                                        Value="True">
                                        <Setter
                                            Property="Source"
                                            Value="{x:Static ui:Icons.PauseCircle}" />
                                    </DataTrigger>
                                </Image.Triggers>
                            </Image>
                            <Label
                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                                HorizontalTextAlignment="Center">
                                <Label.Triggers>
                                    <DataTrigger
                                        TargetType="Label"
                                        Binding="{Binding IsPlayGuideMode}"
                                        Value="False">
                                        <Setter
                                            Property="Text"
                                            Value="{x:Static resources:AppResources.GuideOpen}" />
                                    </DataTrigger>
                                    <DataTrigger
                                        TargetType="Label"
                                        Binding="{Binding IsPlayGuideMode}"
                                        Value="True">
                                        <Setter
                                            Property="Text"
                                            Value="{x:Static resources:AppResources.GuideStop}" />
                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </VerticalStackLayout>
                        <telerik:RadBorder.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding GuideModeTransferCommand}" />
                        </telerik:RadBorder.GestureRecognizers>
                    </telerik:RadBorder>
                </Grid>
                <app:ExaminationTableView
                    ItemsSource="{Binding ClientInformations}"
                    ItemSelected="{Binding ClientSelected}"
                    IsMaskMode="{Binding IsMaskMode}"
                    ResetScrollCommand="{Binding ResetScrollCommand}"
                    Grid.Row="1" />
                <HorizontalStackLayout
                    Grid.Row="2"
                    Spacing="{x:Static ui:Dimens.SpacingMd}"
                    HorizontalOptions="End">
                    <telerik:RadButton
                        Clicked="RadButton_Clicked"
                        Style="{x:Static ui:Styles.ExamineFnButton}"
                        Text="F1" />
                    <telerik:RadButton
                        Style="{x:Static ui:Styles.ExamineFnButton}"
                        Text="F2" />
                    <telerik:RadButton
                        Style="{x:Static ui:Styles.ExamineFnButton}"
                        Text="F3" />
                    <telerik:RadButton
                        Style="{x:Static ui:Styles.ExamineFnButton}"
                        Text="F4" />
                    <telerik:RadBorder
                        Style="{x:Static ui:Styles.ReserveBorderStyle}">
                        <VerticalStackLayout
                            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing2}}"
                            VerticalOptions="End">
                            <Image
                                Aspect="Fill"
                                HorizontalOptions="Center"
                                HeightRequest="{x:Static ui:Dimens.Height28}"
                                WidthRequest="{x:Static ui:Dimens.Width28}"
                                Source="{x:Static ui:Icons.ShakeHand}">
                                <Image.Behaviors>
                                    <toolkit:IconTintColorBehavior
                                        TintColor="{x:Static ui:AppColors.White}" />
                                </Image.Behaviors>
                            </Image>
                            <Label
                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                Text="{x:Static resources:AppResources.Reserved}"
                                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                                HorizontalTextAlignment="Center" />
                        </VerticalStackLayout>
                        <telerik:RadBorder.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding ReserveCommand}" />
                        </telerik:RadBorder.GestureRecognizers>
                    </telerik:RadBorder>
                </HorizontalStackLayout>
            </Grid>
        </telerik:RadBorder>
        <telerik:RadBorder
            Grid.Row="1"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
            MinimumHeightRequest="{x:Static ui:Dimens.Height140}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <app:PatientListView
                ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
        </telerik:RadBorder>
    </Grid>
</ContentView>