﻿namespace ShinKenShinKun.UI;

public class LabelLengthToWidthConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null) return 40; // Default small width

        string text = value.ToString();
        if (string.IsNullOrWhiteSpace(text)) return 40;

        int length = text.Length;

        // Base width calculation (adjust as needed)
        double baseWidth = 40 + (length * 8);  // Each character adds 8px width

        // Limit max width
        return Math.Min(baseWidth, 300);  // Max width = 300px
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
