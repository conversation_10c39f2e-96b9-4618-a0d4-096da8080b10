﻿namespace ShinKenShinKun.InterfaceLibrary;

public class CommonDataAnalyze : MeConnectDefine
{
    #region CommonDataAnalyze
    public CommonDataAnalyze()
    {
    }
    #endregion

    #region データ解析部分
    /// <remarks>
    /// 設定値を元に受信データの切り分け処理を行います。
    /// </remarks>
    public string[] Com_DataAnalyze(string AllData, int[] receiveSetting, int[] dataConfig)
    {
        string[] dataList = new string[receiveSetting[RECEIVE_DATACOUNT]];
        int i = 0, j = 0;

        if (receiveSetting[RECEIVE_DATADIVIDETYPE] == RECEIVE_FIXEDSIZE)
        {
            // 格納データ数分、ループする
            for (i = 0; i < receiveSetting[RECEIVE_DATACOUNT]; i++)
            {
                // データの開始位置を取得する
                int dataPos = dataConfig[RECEIVE_DATASTARTPOS + j];
                // データの取得サイズを取得する
                int dataSize = dataConfig[RECEIVE_DATASIZE + j];
                // データの設定項目数分、次へ飛ばす
                j += RECEIVE_DATACONFIGSIZE;

                // 開始位置＆取得位置が共に0なら、格納データは無し
                if (dataPos == 0 && dataSize == 0)
                {
                    dataList[i] = "";
                }
                else
                {
                    // 取得位置から取得サイズ分、データを格納する
                    dataList[i] = AllData.Substring(dataPos, dataSize);
                }
            }
        }
        else if (receiveSetting[RECEIVE_DATADIVIDETYPE] == RECEIVE_COMMASIZE)
        {
            string[] tempDataList = AllData.Split(',');
            // 格納データ数分、ループする
            for (i = 0; i < receiveSetting[RECEIVE_DATACOUNT]; i++)
            {
                // データの開始位置を取得する
                int dataPos = dataConfig[RECEIVE_DATASTARTPOS + j];
                // データの取得サイズを取得する
                int dataSize = dataConfig[RECEIVE_DATASIZE + j];
                // データの設定項目数分、次へ飛ばす
                j += RECEIVE_DATACONFIGSIZE;
                // 取得位置から取得サイズ分、データを格納する
                dataList[i] = tempDataList[dataPos];
            }
        }
        else if (receiveSetting[RECEIVE_DATADIVIDETYPE] == RECEIVE_CRLFSIZE)
        {
            //string[] newLine = new string[1];
            //newLine[0] = "\r\n";
            //string[] tempDataList = AllData.Split(newLine, System.StringSplitOptions.None);
            string[] tempDataList = AllData.Split('n');

            // 格納データ数分、ループする
            for (i = 0; i < receiveSetting[RECEIVE_DATACOUNT]; i++)
            {
                // データの開始位置を取得する
                int dataPos = dataConfig[RECEIVE_DATASTARTPOS + j];
                // データの取得サイズを取得する
                int dataSize = dataConfig[RECEIVE_DATASIZE + j];
                // データの設定項目数分、次へ飛ばす
                j += RECEIVE_DATACONFIGSIZE;
                // 取得位置から取得サイズ分、データを格納する
                dataList[i] = tempDataList[dataPos].TrimEnd('\r');
            }
        }
        return dataList;
    }
    #endregion
}
