namespace ShinKenShinKun;

public partial class NoteEditPopup : Popup
{
    public NoteEditPopup()
    {
        InitializeComponent();
        Opened += OnOpened;
        SelectedNoteMasters = new ObservableCollection<object>();
        SetGridSizeToWindow();
    }

    public event Action<string>? NoteSaved;

    public static readonly BindableProperty IsOpenNoteEditPopupProperty = BindableProperty.Create(
       nameof(IsOpenNoteEditPopup),
       typeof(bool),
       typeof(NoteEditPopup),
       defaultBindingMode: BindingMode.TwoWay,
       propertyChanged: OnIsOpenNoteEditPopupChanged);

    public static readonly BindableProperty ClientNotesProperty = BindableProperty.Create(
        nameof(ClientNotes),
        typeof(ObservableCollection<NoteModel>),
        typeof(NoteEditPopup));

    public static readonly BindableProperty SelectedNoteMastersProperty = BindableProperty.Create(
        nameof(SelectedNoteMasters),
        typeof(ObservableCollection<object>),
        typeof(NoteEditPopup),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty NoteMastersProperty = BindableProperty.Create(
        nameof(NoteMasters),
        typeof(ObservableCollection<NoteMasterModel>),
        typeof(NoteEditPopup));

    public static readonly BindableProperty NoteProperty = BindableProperty.Create(
        nameof(Note),
        typeof(string),
        typeof(NoteEditPopup),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty RemoveNoteCommandProperty = BindableProperty.Create(
        nameof(RemoveNoteCommand),
        typeof(IRelayCommand),
        typeof(PatientInfoView));

    public static readonly BindableProperty AddNoteCommandProperty = BindableProperty.Create(
        nameof(AddNoteCommand),
        typeof(ICommand),
        typeof(PatientInfoView));

    public bool IsOpenNoteEditPopup
    {
        get => (bool)GetValue(IsOpenNoteEditPopupProperty);
        set => SetValue(IsOpenNoteEditPopupProperty, value);
    }

    public ObservableCollection<NoteModel> ClientNotes
    {
        get => (ObservableCollection<NoteModel>)GetValue(ClientNotesProperty);
        set => SetValue(ClientNotesProperty, value);
    }

    public ObservableCollection<object> SelectedNoteMasters
    {
        get => (ObservableCollection<object>)GetValue(SelectedNoteMastersProperty);
        set => SetValue(SelectedNoteMastersProperty, value);
    }

    public ObservableCollection<NoteMasterModel> NoteMasters
    {
        get => (ObservableCollection<NoteMasterModel>)GetValue(NoteMastersProperty);
        set => SetValue(NoteMastersProperty, value);
    }

    public string Note
    {
        get => (string)GetValue(NoteProperty);
        set => SetValue(NoteProperty, value);
    }
    public IRelayCommand RemoveNoteCommand
    {
        get => (IRelayCommand)GetValue(RemoveNoteCommandProperty);
        set => SetValue(RemoveNoteCommandProperty, value);
    }
    public ICommand AddNoteCommand
    {
        get => (ICommand)GetValue(AddNoteCommandProperty);
        set => SetValue(AddNoteCommandProperty, value);
    }
    private void NoteMastersSelectionChanged(object sender, EventArgs e)
    {
        if(sender is Label label && label.BindingContext is NoteMasterModel tappedItem)
        {
            if(SelectedNoteMasters == null)
                SelectedNoteMasters = new ObservableCollection<object>();

            if(SelectedNoteMasters.Contains(tappedItem))
            {
                SelectedNoteMasters.Remove(tappedItem);
                tappedItem.TextColor = AppColors.Black;
                tappedItem.BackgroundColor = AppColors.White;
            }
            else
            {
                SelectedNoteMasters.Add(tappedItem);
                tappedItem.TextColor = AppColors.White;
                tappedItem.BackgroundColor = AppColors.SteelBlue;
            }
        }
    }
    private void OnOpened(object? sender, PopupOpenedEventArgs e)
    {
        NoteMasters = AppConstants.NoteMasters
                 .Select(note => new NoteMasterModel { DisplayName = note })
                 .ToObservableCollection();
        ClientNotes = Note?.Split([','], StringSplitOptions.RemoveEmptyEntries)
            .Select((note, index) => new NoteModel { Index = index + 1, DisplayName = note })
            .ToObservableCollection() ?? [];
    }

    private void AddToNote(object sender, TappedEventArgs e)
    {
        if(SelectedNoteMasters == null || !SelectedNoteMasters.Any())
            return;

        var itemsSelected = SelectedNoteMasters.ToList();

        var newNoteList = itemsSelected
            .Select(noteMaster =>
            {
                var item = noteMaster as NoteMasterModel;
                return item?.DisplayName;
            })
            .Where(name =>
            {
                if(string.IsNullOrEmpty(name))
                    return false;
                if(string.IsNullOrEmpty(Note))
                    return true;
                return !Note.Contains(name);
            });

        var newNote = string.Join(",", newNoteList);

        // Reset visual state and selection
        foreach(var noteMaster in itemsSelected)
        {
            if(noteMaster is NoteMasterModel item)
            {
                item.TextColor = AppColors.Black;
                item.BackgroundColor = AppColors.White;
                SelectedNoteMasters.Remove(item);
            }
        }

        // Update the main note
        Note = string.IsNullOrEmpty(Note) ? newNote : $"{Note},{newNote}";

        // Update the ClientNotes collection
        ClientNotes = Note?.Split([','], StringSplitOptions.RemoveEmptyEntries)
            .Select((note, index) => new NoteModel { Index = index + 1, DisplayName = note })
            .ToObservableCollection() ?? [];
    }

    private void CloseNoteEditPopup(object sender, TappedEventArgs e)
    {
        NoteSaved?.Invoke(Note);
        this.Close();
    }

    private static void OnIsOpenNoteEditPopupChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if(bindable is not NoteEditPopup popup)
        {
            return;
        }
        if(newValue is not bool isOpen)
        {
            return;
        }
        var itemsSelected = popup.SelectedNoteMasters.ToList();
        foreach(var item in itemsSelected)
        {
            popup.SelectedNoteMasters.Remove(item);
        }
    }
    private void RemoveNote(object sender, TappedEventArgs e)
    {
        if(e.Parameter is NoteModel note && ClientNotes != null)
        {
            ClientNotes.Remove(note);
            Note = string.Join(",", ClientNotes.Select(n => n.DisplayName));
        }
    }

    private void OnOutsideTap(object sender, TappedEventArgs e)
    {
        Close();
    }
    private void OnInsideTap(object sender, TappedEventArgs e)
    {
        // Do nothing ? this absorbs the tap
    }
    private void SetGridSizeToWindow()
    {
        var displayInfo = DeviceDisplay.MainDisplayInfo;

        double screenWidth = displayInfo.Width / displayInfo.Density;
        double screenHeight = displayInfo.Height / displayInfo.Density;

        MainGrid.WidthRequest = screenWidth;
        MainGrid.HeightRequest = screenHeight;
    }
}