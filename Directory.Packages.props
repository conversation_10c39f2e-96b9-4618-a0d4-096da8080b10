<Project>
	<PropertyGroup>
		<!-- Enable central package management, https://learn.microsoft.com/en-us/nuget/consume-packages/Central-Package-Management -->
		<ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
	</PropertyGroup>
	<ItemGroup>
		<PackageVersion Include="AdamE.MemoryToolkit.Maui" Version="1.0.0" />
		<PackageVersion Include="AutoMapper" Version="14.0.0" />
		<PackageVersion Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageVersion Include="Azure.Identity" Version="1.13.2" />
		<PackageVersion Include="Bogus" Version="35.6.2" />
		<PackageVersion Include="CommunityToolkit.Maui" Version="11.1.0" />
		<PackageVersion Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageVersion Include="coverlet.collector" Version="6.0.4" />
		<PackageVersion Include="CsvHelper" Version="33.0.1" />
		<PackageVersion Include="ExcelMapper" Version="5.2.598" />
		<PackageVersion Include="FFImageLoading.Maui" Version="1.2.7" />
		<PackageVersion Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
		<PackageVersion Include="MediatR" Version="12.4.1" />
		<PackageVersion Include="Microsoft.AspNetCore.ApiAuthorization.IdentityServer" Version="7.0.20" />
		<PackageVersion Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.3" />
		<PackageVersion Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.4" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.4" />
		<PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4" />
		<PackageVersion Include="Microsoft.Extensions.Configuration" Version="9.0.2" />
		<PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.3" />
		<PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="9.0.2" />
		<PackageVersion Include="Microsoft.Extensions.Logging.Debug" Version="9.0.2" />
		<PackageVersion Include="Microsoft.Maui.Controls" Version="9.0.40" />
		<PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
		<PackageVersion Include="Microsoft.UI.Xaml" Version="2.8.7" />
		<PackageVersion Include="Moq" Version="4.20.72" />
		<PackageVersion Include="Moq.AutoMock" Version="3.5.0" />
		<PackageVersion Include="Serilog" Version="4.2.0" />
		<PackageVersion Include="Serilog.Extensions.Logging" Version="9.0.0" />
		<PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageVersion Include="Serilog.Sinks.File" Version="7.0.0" />
		<PackageVersion Include="Syncfusion.Maui.Toolkit" Version="1.0.3" />
		<PackageVersion Include="System.Diagnostics.EventLog" Version="9.0.2" />
		<PackageVersion Include="System.IO.Ports" Version="9.0.5" />
		<PackageVersion Include="Telerik.UI.for.Maui" Version="10.0.0" />
		<PackageVersion Include="xunit" Version="2.9.3" />
		<PackageVersion Include="xunit.runner.visualstudio" Version="3.0.2" />
		<PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageVersion Include="System.ServiceModel.Duplex" Version="6.0.0" />
		<PackageVersion Include="System.ServiceModel.Federation" Version="8.1.2" />
		<PackageVersion Include="System.ServiceModel.Http" Version="8.1.2" />
		<PackageVersion Include="System.ServiceModel.NetTcp" Version="8.1.2" />
		<PackageVersion Include="System.ServiceModel.Security" Version="6.0.0" />
		<PackageVersion Include="System.ServiceModel.Primitives" Version="8.1.2"/>
	</ItemGroup>
</Project>