﻿using Microsoft.Maui.Layouts;

namespace ShinKenShinKun.UI
{
    public partial class Styles
    {
        public static Style FlexLayoutStyle => CreateStyle<FlexLayout>()
        .Set(FlexLayout.BackgroundColorProperty, AppColors.White)
        .Set(FlexLayout.DirectionProperty, FlexDirection.Row)
        .Set(FlexLayout.WrapProperty, FlexWrap.Wrap)
        .Set(FlexLayout.JustifyContentProperty, FlexJustify.Start)
        .Set(FlexLayout.AlignItemsProperty, FlexAlignItems.Start)
        .Set(FlexLayout.AlignContentProperty, FlexAlignContent.Start);

        public static Style MultiSelectTableCheckboxHeaderStyle => CreateStyle<HorizontalStackLayout>()
        .Set(HorizontalStackLayout.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(HorizontalStackLayout.SpacingProperty, Dimens.SpacingMd)
        .Set(HorizontalStackLayout.PaddingProperty, new Thickness(Dimens.SpacingXs, 0));

        public static Style MultiSelectTableCellStyle => CreateStyle<Grid>()
        .Set(Grid.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(Grid.VerticalOptionsProperty, LayoutOptions.Fill)
        .Set(Grid.PaddingProperty, 0)
        .Set(Grid.MarginProperty, 0);
    }

}
