﻿namespace ShinKenShinKun.Utils;

///_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/
/// <summary>
/// BackUpFileControlComparerクラス
/// </summary>
/// <remarks>
/// ArrayList.Sort用のクラスです。
/// その後メインスレッドを起動します。
/// </remarks>
///_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/_/
public class BackUpFileControlComparer : System.Collections.IComparer,
    IComparer<string>
{
    //xがyより小さいときはマイナスの数、大きいときはプラスの数、
    //同じときは0を返す
    public int Compare(string x, string y)
    {
        //nullが最も小さいとする
        if (x == null && y == null)
            return 0;
        if (x == null)
            return -1;
        if (y == null)
            return 1;

        int ix = int.Parse(x);
        int iy = int.Parse(y);
        return ix - iy;
    }

    public int Compare(object x, object y)
    {
        //nullが最も小さいとする
        if (x == null && y == null)
            return 0;
        if (x == null)
            return -1;
        if (y == null)
            return 1;

        //String型以外の比較はエラー
        if (!(x is string) || !(y is string))
            throw new ArgumentException();

        return Compare((string)x, (string)y);
    }
}