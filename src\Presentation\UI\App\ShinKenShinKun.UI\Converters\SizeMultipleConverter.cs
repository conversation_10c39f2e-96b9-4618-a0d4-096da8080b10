﻿namespace ShinKenShinKun.UI;

public class SizeMultipleConverter : IValueConverter
{
    public double DefaultMultiplier { get; set; } = 3;

    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is double fontSize)
        {
            if (parameter != null && double.TryParse(parameter.ToString(), out var multiplier))
            {
                return fontSize * multiplier;
            }
            return fontSize * DefaultMultiplier;
        }
        return value;
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}