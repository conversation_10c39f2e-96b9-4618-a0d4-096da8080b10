﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// FontSettingService の概要の説明です

    /// </summary>
    public class FontSettingService
    {
        private DispSettingService service;
        private Logger logger = null;

        public FontSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetFontSettingAll()
        {
            try
            {
                DispSettingDataSet.FontSettingDataTable tmpTable
                    = service.GetFontSettingAll().FontSetting;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    for (int i = 0; i < tmpTable.Count; i++)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(tmpTable[i].FormId.ToString());
                        rList.Add(tmpTable[i].Label.ToString());
                        rList.Add(tmpTable[i].FontName.ToString());
                        rList.Add(tmpTable[i].FontSize.ToString());
                        rList.Add(tmpTable[i].FontStyle.ToString());
                        rList.Add(tmpTable[i].FontUnit.ToString());
                        rList.Add(tmpTable[i].CharSet.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }

                    return pRecordList.ToArray();
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFontSettingFormId(string FormId)
        {
            try
            {
                short formIdShr = Convert.ToInt16(FormId);

                DispSettingDataSet.FontSettingDataTable tmpTable
                    = service.GetFontSettingFormId(formIdShr).FontSetting;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    for (int i = 0; i < tmpTable.Count; i++)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(tmpTable[i].FormId.ToString());
                        rList.Add(tmpTable[i].Label.ToString());
                        rList.Add(tmpTable[i].FontName.ToString());
                        rList.Add(tmpTable[i].FontSize.ToString());
                        rList.Add(tmpTable[i].FontStyle.ToString());
                        rList.Add(tmpTable[i].FontUnit.ToString());
                        rList.Add(tmpTable[i].CharSet.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }

                    return pRecordList.ToArray();
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFontSettingFormLabel(string FormId, string Label)
        {
            try
            {
                short formIdShr = Convert.ToInt16(FormId);
                short labelShr = Convert.ToInt16(Label);

                DispSettingDataSet.FontSettingDataTable tmpTable
                    = service.GetFontSettingFormLabel(formIdShr, labelShr).FontSetting;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    for (int i = 0; i < tmpTable.Count; i++)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(tmpTable[i].FormId.ToString());
                        rList.Add(tmpTable[i].Label.ToString());
                        rList.Add(tmpTable[i].FontName.ToString());
                        rList.Add(tmpTable[i].FontSize.ToString());
                        rList.Add(tmpTable[i].FontStyle.ToString());
                        rList.Add(tmpTable[i].FontUnit.ToString());
                        rList.Add(tmpTable[i].CharSet.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }

                    return pRecordList.ToArray();
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}