namespace ShinKenShinKun;

public partial class ExamineAfterAuthPage : BasePage
{
    private readonly IDispatcher dispatcher;
    private readonly ExamineAfterAuthViewModel vm;
    private readonly StackLayout stack = [];

    public ExamineAfterAuthPage(ExamineAfterAuthViewModel vm, IDispatcher dispatcher)
    {
        InitializeComponent();
        this.dispatcher = dispatcher;
        BindingContext = vm;
        this.vm = vm;
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        ShowLayout();
    }

    private void ShowLayout()
    {
        if(stack.Count > 0)
        {
            stack.Clear();
        }
        foreach(var area in vm.Areas.OrderBy(x => x.Order))
        {
            var grid = new AreaControl
            {
                Area = area,
            };
            stack.Children.Add(grid);
        }
        dispatcher.Dispatch(() =>
        {
            AreaLayout.Content = stack;
        });
    }
}