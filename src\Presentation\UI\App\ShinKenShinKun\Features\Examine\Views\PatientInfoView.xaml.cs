
namespace ShinKenShinKun;

public partial class PatientInfoView : ContentView
{
    public static readonly BindableProperty ItemSelectedProperty = BindableProperty.Create(
        nameof(ItemSelected),
        typeof(ClientInformationModel),
        typeof(PatientInfoView),
        defaultBindingMode: BindingMode.TwoWay,
        propertyChanging: OnItemSelectedChanged);

    public static readonly BindableProperty IsMaskModeProperty = BindableProperty.Create(
        nameof(IsMaskMode),
        typeof(bool),
        typeof(PatientInfoView));

    public static readonly BindableProperty IsItemSelectedProperty = BindableProperty.Create(
        nameof(IsItemSelected),
        typeof(bool),
        typeof(PatientInfoView));

    public static readonly BindableProperty ShowNoteEditPopupCommandProperty = BindableProperty.Create(
        nameof(ShowNoteEditPopupCommand),
        typeof(IRelayCommand),
        typeof(PatientInfoView));

    public static readonly BindableProperty NoteScrollProperty = BindableProperty.Create(
        nameof(NoteScroll),
        typeof(ScrollPositionModel),
        typeof(PatientInfoView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty PersonalNoteScrollProperty = BindableProperty.Create(
        nameof(PersonalNoteScroll),
        typeof(ScrollPositionModel),
        typeof(PatientInfoView),
        defaultBindingMode: BindingMode.TwoWay);

    public PatientInfoView()
    {
        InitializeComponent();
        Loaded += OnLoaded;
    }
    private void OnLoaded(object? sender, EventArgs e)
    {
        if(sender is not PatientInfoView view)
        {
            return;
        }

        view.PersonalNoteList.ScrollToAsync(PersonalNoteScroll.ScrollX, PersonalNoteScroll.ScrollY, false);
        view.NoteList.ScrollToAsync(NoteScroll.ScrollX, NoteScroll.ScrollY, false);
    }

    public ClientInformationModel ItemSelected
    {
        get => (ClientInformationModel)GetValue(ItemSelectedProperty);
        set => SetValue(ItemSelectedProperty, value);
    }

    public bool IsMaskMode
    {
        get => (bool)GetValue(IsMaskModeProperty);
        set => SetValue(IsMaskModeProperty, value);
    }

    public bool IsItemSelected
    {
        get => (bool)GetValue(IsItemSelectedProperty);
        set => SetValue(IsItemSelectedProperty, value);
    }

    public IRelayCommand ShowNoteEditPopupCommand
    {
        get => (IRelayCommand)GetValue(ShowNoteEditPopupCommandProperty);
        set => SetValue(ShowNoteEditPopupCommandProperty, value);
    }

    public ScrollPositionModel NoteScroll
    {
        get => (ScrollPositionModel)GetValue(NoteScrollProperty);
        set => SetValue(NoteScrollProperty, value);
    }
    public ScrollPositionModel PersonalNoteScroll
    {
        get => (ScrollPositionModel)GetValue(PersonalNoteScrollProperty);
        set => SetValue(PersonalNoteScrollProperty, value);
    }

    private void PersonalNoteScrolled(object sender, ScrolledEventArgs e)
    {
        PersonalNoteScroll.ScrollY = e.ScrollY;
    }

    private void NoteScrolled(object sender, ScrolledEventArgs e)
    {
        NoteScroll.ScrollY = e.ScrollY;
    }

    private static void OnItemSelectedChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if(bindable is not PatientInfoView view || view.PersonalNoteScroll == null || view.NoteScroll == null)
        {
            return;
        }
        view.PersonalNoteList.ScrollToAsync(view.PersonalNoteScroll.ScrollX, view.PersonalNoteScroll.ScrollY, false);
        view.NoteList.ScrollToAsync(view.NoteScroll.ScrollX, view.NoteScroll.ScrollY, false);
    }
}
