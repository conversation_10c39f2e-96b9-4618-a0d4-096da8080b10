﻿namespace ShinKenShinKun.InterfaceLibrary;

public class CommonCreateAttribute : MeConnectDefine
{
    #region 変数
    private byte[] Attribute;
    private int DataPos = 0;
    private int DataSize = 0;
    private int DataConv = 0;
    private int DataCover = 0;
    private int DataPad = 0;
    private int DataAdd = 0;
    // 格納位置
    private int pos;
    #endregion

    #region カタカナ－ローマ字変換用テーブル

    // カタカナ配列
    public string[] kanaArray = {
                    "ｳﾞｧ","ｳﾞｨ","ｳﾞｩ","ｳﾞｪ","ｳﾞｫ",
                    "ｷﾞｬ","ｷﾞｨ","ｷﾞｭ","ｷﾞｪ","ｷﾞｮ",
                    "ｼﾞｬ","ｼﾞｨ","ｼﾞｭ","ｼﾞｪ","ｼﾞｮ",
                    "ﾁﾞｬ","ﾁﾞｨ","ﾁﾞｭ","ﾁﾞｪ","ﾁﾞｮ",
                    "ﾄﾞｧ","ﾄﾞｨ","ﾄﾞｩ","ﾄﾞｪ","ﾄﾞｫ",
                    "ﾋﾞｬ","ﾋﾞｨ","ﾋﾞｭ","ﾋﾞｪ","ﾋﾞｮ",
                    "ﾋﾟｬ","ﾋﾟｨ","ﾋﾟｭ","ﾋﾟｪ","ﾋﾟｮ",
                    "ｸﾞｧ","ｸﾞｨ","ｸﾞｩ","ｸﾞｪ","ｸﾞｫ",
                    "ﾃﾞｬ","ﾃﾞｨ","ﾃﾞｭ","ﾃﾞｪ","ﾃﾞｮ",
                    "ｶﾞ","ｷﾞ","ｸﾞ","ｹﾞ","ｺﾞ",
                    "ｻﾞ","ｼﾞ","ｽﾞ","ｾﾞ","ｿﾞ",
                    "ﾀﾞ","ﾁﾞ","ﾂﾞ","ﾃﾞ","ﾄﾞ",
                    "ﾊﾞ","ﾋﾞ","ﾌﾞ","ﾍﾞ","ﾎﾞ",
                    "ﾊﾟ","ﾋﾟ","ﾌﾟ","ﾍﾟ","ﾎﾟ",
                    "ｳﾞ",
                    "ｷｬ","ｷｨ","ｷｭ","ｷｪ","ｷｮ",
                    "ｼｬ","ｼｨ","ｼｭ","ｼｪ","ｼｮ",
                    "ﾁｬ","ﾁｨ","ﾁｭ","ﾁｪ","ﾁｮ",
                    "ﾃｬ","ﾃｨ","ﾃｭ","ﾃｪ","ﾃｮ",
                    "ﾆｬ","ﾆｨ","ﾆｭ","ﾆｪ","ﾆｮ",
                    "ﾋｬ","ﾋｨ","ﾋｭ","ﾋｪ","ﾋｮ",
                    "ﾐｬ","ﾐｨ","ﾐｭ","ﾐｪ","ﾐｮ",
                    "ﾘｬ","ﾘｨ","ﾘｭ","ﾘｪ","ﾘｮ",
                    "ｲｪ","ｳｨ","ｳｪ",
                    "ｸｧ","ｸｨ","ｸｩ","ｸｪ","ｸｫ",
                    "ﾂｧ","ﾂｨ","ﾂｩ","ﾂｪ","ﾂｫ",
                    "ﾄｧ","ﾄｨ","ﾄｩ","ﾄｪ","ﾄｫ",
                    "ﾌｧ","ﾌｨ","ﾌｩ","ﾌｪ","ﾌｫ",
                    "ﾌｬ","ﾌｭ","ﾌｮ",
                    "ｱ","ｲ","ｳ","ｴ","ｵ",
                    "ｶ","ｷ","ｸ","ｹ","ｺ",
                    "ｻ","ｼ","ｽ","ｾ","ｿ",
                    "ﾀ","ﾁ","ﾂ","ﾃ","ﾄ",
                    "ﾅ","ﾆ","ﾇ","ﾈ","ﾉ",
                    "ﾊ","ﾋ","ﾌ","ﾍ","ﾎ",
                    "ﾏ","ﾐ","ﾑ","ﾒ","ﾓ",
                    "ﾔ","ﾕ","ﾖ",
                    "ﾗ","ﾘ","ﾙ","ﾚ","ﾛ",
                    "ﾜ","ｦ","ﾝ","ｰ",
                    "ｧ","ｨ","ｩ","ｪ","ｫ",
                    "ｬ","ｭ","ｮ",
                    "｡","｢","｣","､","･"
                };

    // ローマ字配列
    public string[] romeArray = {
                    "VA","VI","VU","VE","VO",
                    "GYA","GYI","GYU","GYE","GYO",
                    "JA","JI","JU","JE","JO",
                    "DYA","DYI","DYU","DYE","DYO",
                    "DWA","DWI","DWU","DWE","DWO",
                    "BYA","BYI","BYU","BYE","BYO",
                    "PYA","PYI","PYU","PYE","PYO",
                    "GWA","GWI","GWU","GWE","GWO",
                    "DHA","DHI","DHU","DHE","DHO",
                    "GA","GI","GU","GE","GO",
                    "ZA","JI","ZU","ZE","ZO",
                    "DA","DI","DU","DE","DO",
                    "BA","BI","BU","BE","BO",
                    "PA","PI","PU","PE","PO",
                    "VU",
                    "KYA","KYI","KYU","KYE","KYO",
                    "SYA","SYI","SYU","SYE","SYO",
                    "CHA","CHI","CHU","CHE","CHO",
                    "THA","THI","THU","THE","THO",
                    "NYA","NYI","NYU","NYE","NYO",
                    "HYA","HYI","HYU","HYE","HYO",
                    "MYA","MYI","MYU","MYE","MYO",
                    "RYA","RYI","RYU","RYE","RYO",
                    "YE","WI","WE",
                    "QA","QI","QU","QE","QO",
                    "TSA","TSI","TSU","TSE","TSO",
                    "TWA","TWI","TWU","TWE","TWO",
                    "FA","FI","FU","FE","FO",
                    "FYA","FYU","FYO",
                    "A","I","U","E","O",
                    "KA","KI","KU","KE","KO",
                    "SA","SHI","SU","SE","SO",
                    "TA","CHI","TSU","TE","TO",
                    "NA","NI","NU","NE","NO",
                    "HA","HI","HU","HE","HO",
                    "MA","MI","MU","ME","MO",
                    "YA","YU","YO",
                    "RA","RI","RU","RE","RO",
                    "WA","WO","NN","-",
                    "LA","LI","LU","LE","LO",
                    "LYA","LYU","LYO",
                    ".","[","]",",",":"
                };

    #endregion

    #region CommonCreateAttribute
    public CommonCreateAttribute()
    {
    }
    #endregion

    #region 属性データ作成部分
    public byte[] Com_CreateAttribute(string[] sendData, int[] sendSetting, int[] dataConfig)
    {
        pos = 0;
        // 属性送信用文字列作成開始
        Attribute = new byte[sendSetting[SEND_DATALENGTH]];
        DataAdd = sendSetting[SEND_DATAAFTER_ADD];

        // 「STX」を付加する
        if (sendSetting[SEND_STARTCODE] == CODE_STX)
        {
            Attribute[pos] = STX;
            pos++;
        }
        int sendCount = 0, configCount = 0;
        for (sendCount = 0; sendCount < sendSetting[SEND_DATACOUNT]; sendCount++)
        {
            // データポジションの取得
            DataPos = dataConfig[SEND_DATASTARTPOS + configCount];
            // データサイズの取得
            DataSize = dataConfig[SEND_DATASIZE + configCount];
            // 変換方式の取得
            DataConv = dataConfig[SEND_DATACONVERT + configCount];
            // 埋め文字の取得
            DataCover = dataConfig[SEND_DATACOVER + configCount];
            // 右/左寄せの取得
            DataPad = dataConfig[SEND_DATAPAD + configCount];
            // 
            configCount += SEND_DATACONFIGSIZE;
            // ポジションとサイズが0の場合には、変換後処理のみ行う。
            if (DataPos == 0 && DataSize == 0)
            {
            }
            // データポジションに888が設定されていた場合
            else if (DataPos == ADD_SPACE)
            {
                for (int i = 0; i < DataSize; i++)
                {
                    Attribute[pos] = SPACE;
                    pos++;
                }
            }
            else
            {
                if (sendData[DataPos] != null)
                {
                    Com_CreateAttribute_ConvMain(sendData[DataPos].Trim());
                }
                else
                {
                    for (int i = 0; i < DataSize; i++)
                    {
                        Attribute[pos] = SPACE;
                        pos++;
                    }
                }
            }
            if (sendCount != sendSetting[SEND_DATACOUNT] - 1)
            {
                Com_CreateAttribute_Tune_After();
            }
        }
        // 「ETX」を付加する
        if (sendSetting[SEND_ENDCODE] == CODE_ETX)
        {
            Attribute[pos] = ETX;
            pos++;
        }
        // 「CRLF」を付加する
        else if (sendSetting[SEND_ENDCODE] == CODE_CRLF)
        {
            Attribute[pos] = CR;
            pos++;
            Attribute[pos] = LF;
            pos++;
        }
        // 「CR」を付加する
        else if (sendSetting[SEND_ENDCODE] == CODE_CR)
        {
            Attribute[pos] = CR;
            pos++;
        }
        // 「LF」を付加する
        else if (sendSetting[SEND_ENDCODE] == CODE_LF)
        {
            Attribute[pos] = LF;
            pos++;
        }
        // 「EOT」を付加する
        else if (sendSetting[SEND_ENDCODE] == CODE_EOT)
        {
            Attribute[pos] = EOT;
            pos++;
        }
        else if (sendSetting[SEND_ENDCODE] == CODE_ETX_BCC)
        {
            Attribute[pos] = ETX;
            pos++;
            Com_CreateAttribute_Bcc();
        }
        return Attribute;
    }
    #endregion

    #region データ変換処理
    private void Com_CreateAttribute_ConvMain(string data)
    {
        // バイト長の調整を行う
        data = Com_CreateAttribute_Tune(data);

        switch (DataConv)
        {
            case NOCHANGE:
                byte[] bufArea = encSjis.GetBytes(data);
                Array.Copy(bufArea, 0, Attribute, pos, DataSize);
                pos += bufArea.Length;
                break;
            case CHANGE_NAME:
            case CHANGE_NAME_SOSI:
                Com_CreateAttribute_Name(data);
                break;
            case CHANGE_SEX_12:
            case CHANGE_SEX_MF:
                Com_CreateAttribute_Sex(data);
                break;
            case CHANGE_BIRTHDAY:
            case CHANGE_YYMMDD:
            case CHANGE_YYMMDD_SLASH:
                Com_CreateAttribute_Birthday(data);
                break;
            case CHANGE_KANATOROMA:
                Com_CreateAttribute_KanaToRoma(data);
                break;
            case CHANGE_BEFORE_DOT:
                Com_CreateAttribute_Dot(data);
                break;
            default:
                break;
        }
    }
    #endregion


    #region サイズ調整処理
    private string Com_CreateAttribute_Tune(string data)
    {
        // 実際のデータ長よりも短い値が指定されていた場合
        if (data.Length > DataSize && DataConv == NOCHANGE)
        {
            return data.Substring(data.Length - DataSize, DataSize);
        }

        // 左寄せ＆スペース埋め
        if (DataPad == PADLEFT && DataCover == SPACECOVER)
        {
            return data.PadRight(DataSize, ' ');
        }
        // 右寄せ＆スペース埋め
        else if (DataPad == PADRIGHT && DataCover == SPACECOVER)
        {
            return data.PadLeft(DataSize, ' ');
        }
        // 右寄せ＆０埋め
        else if (DataPad == PADRIGHT && DataCover == ZEROCOVER)
        {
            return data.PadLeft(DataSize, '0');
        }
        // 左寄せ＆０埋め
        else if (DataPad == PADLEFT && DataCover == ZEROCOVER)
        {
            return data.PadRight(DataSize, '0');
        }
        return "";
    }
    #endregion

    #region サイズ調整処理
    /// <remarks>
    /// 各項目調整・変換後の後処理（CRLF/,付加等）を行います。
    /// </remarks>
    ///===================================================================
    private void Com_CreateAttribute_Tune_After()
    {
        // CRLFを付加する場合
        if (DataAdd == ADD_CRLF)
        {
            byte[] bufArea = new byte[2];
            bufArea[0] = CR;
            bufArea[1] = LF;
            Array.Copy(bufArea, 0, Attribute, pos, 2);
            pos += bufArea.Length;
        }
        // カンマを付加する場合
        else if (DataAdd == ADD_COMMA)
        {
            byte[] bufArea = encSjis.GetBytes(",");
            Array.Copy(bufArea, 0, Attribute, pos, 1);
            pos += bufArea.Length;
        }
    }
    #endregion

    #region 氏名変換処理
    private void Com_CreateAttribute_Name(string name)
    {
        byte[] nameArea;                  // 名前格納用
        byte[] buf;                       // 名前部分(SO,SI無し)格納用

        // 標準形式の場合
        if (DataConv == CHANGE_NAME)
        {
            nameArea = new byte[DataSize];
            // カナ氏名を格納する
            buf = encSjis.GetBytes(name);
            Array.Copy(buf, 0, nameArea, 0, DataSize);
            Array.Copy(nameArea, 0, Attribute, pos, nameArea.Length);
            pos += nameArea.Length;
        }
        // SOSIを含める変換を行う場合
        else if (DataConv == CHANGE_NAME_SOSI)
        {
            nameArea = new byte[DataSize + 2];
            buf = encSjis.GetBytes(name);
            nameArea[0] = SO;
            Array.Copy(buf, 0, nameArea, 1, DataSize);
            nameArea[DataSize + 1] = SI;
            Array.Copy(nameArea, 0, Attribute, pos, nameArea.Length);
            pos = pos + nameArea.Length;
        }
    }
    #endregion

    #region 性別変換処理
    private void Com_CreateAttribute_Sex(string sex)
    {
        byte[] sexArea = new byte[DataSize];     // 性別格納用

        // 1/2変換の場合
        if (DataConv == CHANGE_SEX_12)
        {
            // 性別を格納する
            if (sex.Trim() == "男")
            {
                // sexArea = encSjis.GetBytes("1");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("1"));
            }
            else if (sex.Trim() == "女")
            {
                // sexArea = encSjis.GetBytes("2");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("2"));
            }
            else
            {
                // sexArea = encSjis.GetBytes("O");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("O"));
            }
        }
        // M/F変換の場合
        else if (DataConv == CHANGE_SEX_MF)
        {
            // 性別を格納する
            if (sex.Trim() == "男")
            {
                // sexArea = encSjis.GetBytes("M");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("M"));
            }
            else if (sex.Trim() == "女")
            {
                // sexArea = encSjis.GetBytes("F");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("F"));
            }
            else
            {
                // sexArea = encSjis.GetBytes("O");
                sexArea = encSjis.GetBytes(Com_CreateAttribute_Tune("O"));
            }
        }
        Array.Copy(sexArea, 0, Attribute, pos, sexArea.Length);
        pos += sexArea.Length;
    }
    #endregion

    #region 生年月日変換処理
    /// <remarks>
    /// 生年月日をYYYY/MM/DD → yyyymmdd形式に変換します。
    /// </remarks>
    private void Com_CreateAttribute_Birthday(string birthDay)
    {
        byte[] birthdayArea = new byte[DataSize];// 生年月日格納用
        string tempBirthday = "";              // 生年月日作成用(YYYY/MM/DD → yyyymmdd)

        // YYYYMMDD形式の場合
        if (DataConv == CHANGE_BIRTHDAY)
        {
            // 生年月日をYYYYMMDD形式で格納する
            tempBirthday = birthDay.Substring(0, 4) +
                            birthDay.Substring(5, 2) +
                             birthDay.Substring(8, 2);
        }
        else if (DataConv == CHANGE_YYMMDD)
        {
            tempBirthday = birthDay.Substring(2, 2) +
                            birthDay.Substring(5, 2) +
                             birthDay.Substring(8, 2);
        }
        else if (DataConv == CHANGE_YYMMDD_SLASH)
        {
            tempBirthday = birthDay.Substring(0, 4) + "/" +
                            birthDay.Substring(5, 2) + "/" +
                             birthDay.Substring(8, 2);
        }
        birthdayArea = encSjis.GetBytes(tempBirthday.Trim());
        Array.Copy(birthdayArea, 0, Attribute, pos, birthdayArea.Length);
        pos += birthdayArea.Length;
    }
    #endregion

    #region 半角カタカナ → ローマ字変換処理
    /// <remarks>
    /// 半角カナをローマ字に変換します。
    /// </remarks>
    private void Com_CreateAttribute_KanaToRoma(string cKana)
    {
        byte[] romaNameArea = new byte[DataSize];// 生年月日格納用
        string str, converted;
        int rPos, i;

        converted = "";
        rPos = 0;

        // 20200903tama カナ氏名がデータサイズより大きかった場合の回避処理
        if (cKana.Length > DataSize)
        {
            cKana = cKana.Substring(0, DataSize);
        }

        str = cKana;
        while (rPos < str.Length)
        {
            for (i = 0; i < kanaArray.Length; i++)
            {
                if (str.Length - rPos < kanaArray[i].Length)
                {
                    continue;
                }
                if (str.Substring(rPos, kanaArray[i].Length) == kanaArray[i])
                {
                    converted += romeArray[i];
                    rPos += kanaArray[i].Length;
                    break;
                }
            }
            // "ｯ"があった場合は、それ以後の文字を一文字コピーする。
            if (converted.IndexOf("ｯ") != -1)
            {
                int t = converted.IndexOf("ｯ");
                if (converted.Length < t + 1)
                {
                    converted = converted.Replace("ｯ", converted.Substring(t + 1, 1));
                }
                else
                {
                    converted = converted.Replace("ｯ", string.Empty);
                }
            }
            // 配列上に変換文字が存在しなかった場合(含むスペース)、
            // そのまま出力する。
            if (i == kanaArray.Length)
            {
                converted += str.Substring(rPos, 1);
                rPos++;
            }
        }

        //20110801 hig Correction
        //半角カナ氏名の長さより、ローマ字変換後の長さが
        //短くなった場合にエラーになる対策
        if (converted.Length >= str.Length)
        {
            converted = converted.Substring(0, str.Length);
        }
        else
        {
            converted = converted.PadRight(str.Length, ' ');
        }

        romaNameArea = encSjis.GetBytes(converted);
        Array.Copy(romaNameArea, 0, Attribute, pos, romaNameArea.Length);
        pos += romaNameArea.Length;
    }
    #endregion

    #region 半角カタカナ → ローマ字変換処理
    /// <remarks>
    /// 半角カナをローマ字に変換します。
    /// </remarks>
    public byte[] CreateAttribute_KanaToRoma(string cKana)
    {
        byte[] romaNameArea;
        string str, converted;
        int rPos, i;

        // 20200903tama カナ氏名がデータサイズより大きかった場合の回避処理
        if (cKana.Length > DataSize)
        {
            cKana = cKana.Substring(0, DataSize);
        }

        converted = "";
        rPos = 0;
        str = cKana.PadRight(20);
        while (rPos < str.Length)
        {
            for (i = 0; i < kanaArray.Length; i++)
            {
                if (str.Length - rPos < kanaArray[i].Length)
                {
                    continue;
                }
                if (str.Substring(rPos, kanaArray[i].Length) == kanaArray[i])
                {
                    converted += romeArray[i];
                    rPos += kanaArray[i].Length;
                    break;
                }
            }
            // "ｯ"があった場合は、それ以後の文字を一文字コピーする。
            if (converted.IndexOf("ｯ") != -1)
            {
                int t = converted.IndexOf("ｯ");
                if (converted.Length < t + 1)
                {
                    converted = converted.Replace("ｯ", converted.Substring(t + 1, 1));
                }
                else
                {
                    converted = converted.Replace("ｯ", string.Empty);
                }
            }
            // 配列上に変換文字が存在しなかった場合(含むスペース)、
            // そのまま出力する。
            if (i == kanaArray.Length)
            {
                converted += str.Substring(rPos, 1);
                rPos++;
            }
        }
        //converted = converted.Substring(0, str.Length);

        //20110801 hig Correction
        //半角カナ氏名の長さより、ローマ字変換後の長さが
        //短くなった場合にエラーになる対策
        if (converted.Length >= str.Length)
        {
            converted = converted.Substring(0, str.Length);
        }
        else
        {
            converted = converted.PadRight(str.Length, ' ');
        }
        romaNameArea = encSjis.GetBytes(converted);
        return romaNameArea;
    }
    #endregion

    #region 小数点前処理
    private void Com_CreateAttribute_Dot(string data)
    {
        int dotPos = data.IndexOf(".");
        if (dotPos < 0)
        {
            dotPos = 0;
        }
        data = data.Substring(0, dotPos);

        // 左寄せ＆スペース埋め
        if (DataPad == PADLEFT && DataCover == SPACECOVER)
        {
            data = data.PadRight(DataSize, ' ');
        }
        // 右寄せ＆スペース埋め
        else if (DataPad == PADRIGHT && DataCover == SPACECOVER)
        {
            data = data.PadLeft(DataSize, ' ');
        }
        // 右寄せ＆０埋め
        else if (DataPad == PADRIGHT && DataCover == ZEROCOVER)
        {
            data = data.PadLeft(DataSize, '0');
        }
        // 左寄せ＆０埋め
        else if (DataPad == PADLEFT && DataCover == ZEROCOVER)
        {
            data = data.PadRight(DataSize, '0');
        }
        byte[] bufAreaDot = encSjis.GetBytes(data);
        Array.Copy(bufAreaDot, 0, Attribute, pos, DataSize);
        pos += bufAreaDot.Length;
    }
    #endregion

    #region ＢＣＣ計算
    /// <remarks>
    /// STXを除いてETXまでの排他的論理和（ETXを含む）
    /// </remarks>
    private void Com_CreateAttribute_Bcc()
    {
        int iBcc = 0;
        // BCC計算。STXを除いてETXまでの排他的論理和（ETXを含む）
        for (int i = 1; i < Attribute.Length - 1; i++)
        {
            iBcc ^= Attribute[i];
        }
        Attribute[Attribute.Length - 1] = (byte)iBcc;
    }
    #endregion
}
