﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MedicalItemListService の概要の説明です

    /// </summary>
    public class MedicalItemListService
    {
        private DispSettingService service;
        private Logger logger = null;
        public MedicalItemListService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }
        public PluralRecord[] GetMedicalItemListAll()
        {
            try
            {
                DispSettingDataSet.MedicalItemListDataTable tmpTable =
                    service.GetMedicalItemListAll().MedicalItemList;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemListRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.ItemName);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMedicalItemListNo(string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.MedicalItemListDataTable tmpTable =
                    service.GetMedicalItemListNo(medicalCheckNoShr).MedicalItemList;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemListRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.ItemName);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMedicalItemList(
            string medicalCheckNo,
            string itemNo)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                short itemNoShr = Convert.ToInt16(itemNo);
                DispSettingDataSet.MedicalItemListDataTable tmpTable =
                    service.GetMedicalItemList(medicalCheckNoShr, itemNoShr).MedicalItemList;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemListRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.ItemName);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}