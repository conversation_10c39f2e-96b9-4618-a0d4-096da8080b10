﻿namespace ShinKenShinKun.Utils;

public class ColorHelper
{
    private static double CalculateLuminance(Color color)
    {
        if (color.Alpha == 0)
            return 1;
        double r = color.Red;
        double g = color.Green;
        double b = color.Blue;

        r = (r <= 0.03928) ? r / 12.92 : <PERSON><PERSON>Pow((r + 0.055) / 1.055, 2.4);
        g = (g <= 0.03928) ? g / 12.92 : <PERSON><PERSON>Pow((g + 0.055) / 1.055, 2.4);
        b = (b <= 0.03928) ? b / 12.92 : <PERSON>.Pow((b + 0.055) / 1.055, 2.4);

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    public static Color GetTextColor(Color backgroundColor)
    {
        double luminance = CalculateLuminance(backgroundColor);
        return luminance > 0.179 ? Colors.Black : Colors.White;
    }
}