namespace ShinKenShinKun;

public partial class HomeButtonView : RadBorder
{

    public static readonly BindableProperty GoHomeCommandProperty = BindableProperty.Create(
        nameof(GoHomeCommand),
        typeof(IRelayCommand),
        typeof(HomeButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public HomeButtonView()
    {
        InitializeComponent();
    }

    public IRelayCommand GoHomeCommand
    {
        get => (IRelayCommand)GetValue(GoHomeCommandProperty);
        set => SetValue(GoHomeCommandProperty, value);
    }
}