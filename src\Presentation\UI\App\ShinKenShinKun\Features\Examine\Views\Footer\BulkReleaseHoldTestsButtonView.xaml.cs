namespace ShinKenShinKun;

public partial class BulkReleaseHoldTestsButtonView : RadBorder
{
    public BulkReleaseHoldTestsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkReleaseHoldTestsCommandProperty = BindableProperty.Create(
        nameof(BulkReleaseHoldTestsCommand),
        typeof(IRelayCommand),
        typeof(BulkReleaseHoldTestsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkReleaseHoldTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkReleaseHoldTestsCommandProperty);
        set => SetValue(BulkReleaseHoldTestsCommandProperty, value);
    }
}