namespace ShinKenShinKun.DataAccess;

public class FileSystemService : IFileSystemService
{
    public List<ClientInformationDto> ReadClientExcelFile()
    {
        try
        {
            return Create<ClientInformationDto>(AppConstants.ClientInformationExcelFile);
        }
        catch (Exception e)
        {
            Log.Error(e.Message, e);
        }

        return new List<ClientInformationDto>();
    }

    public List<GuidanceItemDto> ReadGuidanceExcelFile()
    {
        var getFullPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, AppConstants.GuidanceExcelFile);
        return Create<GuidanceItemDto>(AppConstants.GuidanceExcelFile);
    }

    private static List<T> Create<T>(string fileName)
        where T : class
    {
        var excel = new ExcelMapper(fileName);
        var data = excel.Fetch<T>().ToList();
        return data;
    }

    public Task SaveClientExcelFile(ClientInformationDto clientInformation)
    {
        return Task.Run(() =>
        {
            var excel = new ExcelMapper(AppConstants.ClientInformationExcelFile);
            var data = excel.Fetch<ClientInformationDto>().ToList();
            var selectedData = data.FirstOrDefault(x => x.ClientId == clientInformation.ClientId);

            if (selectedData == null)
                return;

            selectedData.MedicalChecklist = clientInformation.MedicalChecklist;

            excel.Save(AppConstants.ClientInformationExcelFile, data);
        });
    }
}