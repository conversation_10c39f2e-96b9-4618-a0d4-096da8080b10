<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualSectionView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:DataType="app:IndividualProgressViewModel"
    x:Name="this">
    <VerticalStackLayout
        Spacing="{x:Static ui:Dimens.SpacingMd}">
        <telerik:RadButton
            Text="{Binding SectionName, Source={x:Reference this}}"
            Style="{Static ui:Styles.IndividualSectionButton}">
        </telerik:RadButton>
        <telerik:RadCollectionView
            SelectionMode="None"
            ItemsSource="{Binding ItemSource, Source={x:Reference this}}">
            <telerik:RadCollectionView.ItemsLayout>
                <telerik:CollectionViewGridLayout
                    SpanCount="{Binding SpanCount, Source={x:Reference this}}"
                    HorizontalItemSpacing="{x:Static ui:Dimens.SpacingXs}"
                    VerticalItemSpacing="{x:Static ui:Dimens.SpacingXs}" />
            </telerik:RadCollectionView.ItemsLayout>
            <telerik:RadCollectionView.ItemTemplate>
                <DataTemplate
                    x:DataType="app:IndividualPatientModel">
                    <app:IndividualPatientLabelView
                        Label="{Binding Name}"
                        Value="{Binding Value}"
                        Color="{x:Static ui:AppColors.Charcoal}" />
                </DataTemplate>
            </telerik:RadCollectionView.ItemTemplate>
            <telerik:RadCollectionView.ItemViewStyle>
                <Style
                    TargetType="telerik:RadCollectionViewItemView">
                    <Setter
                        Property="VisualStateManager.VisualStateGroups">
                        <VisualStateGroupList />
                    </Setter>
                </Style>
            </telerik:RadCollectionView.ItemViewStyle>
        </telerik:RadCollectionView>
    </VerticalStackLayout>
</ContentView>