namespace ShinKenShinKun;

public partial class BulkCancelPauseTestsButtonView : RadBorder
{
    public BulkCancelPauseTestsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkCancelPauseTestsCommandProperty = BindableProperty.Create(
        nameof(BulkCancelPauseTestsCommand),
        typeof(IRelayCommand),
        typeof(BulkCancelPauseTestsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkCancelPauseTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkCancelPauseTestsCommandProperty);
        set => SetValue(BulkCancelPauseTestsCommandProperty, value);
    }
}