﻿namespace ShinKenShinKun.DataAccess;

public class MapperProfile : Profile
{
    public MapperProfile()
    {
        CreateMap<StandardMasterKey, StandardMasterKeyDto>();
        CreateMap<CustomMasterKey, CustomMasterKeyDto>();
        CreateMap<SettingNumberPad, SettingNumberPadDto>();
        CreateMap<StandardMasterKeySetting, StandardMasterKeySettingDto>();
        CreateMap<CustomMasterKeySetting, CustomMasterKeySettingDto>();
        CreateMap<Message, DialogMessageDto>()
            .ForMember(
                des => des.MessageType,
                opt => opt.MapFrom(src => DictionaryHelpers.MessageTypeDic[src.MessageType])
            )
            .ForMember(
                des => des.ButtonType,
                opt => opt.MapFrom(src => DictionaryHelpers.ButtonTypeDic[src.ButtonType])
            );
    }
}