namespace ShinKenShinKun;

public partial class PatientListView : ContentView
{
    public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(
        nameof(ItemsSource),
        typeof(ObservableCollection<MedicalCheckStateModel>),
        typeof(PatientListView));

    public PatientListView()
    {
        InitializeComponent();
    }

    public ObservableCollection<MedicalCheckStateModel> ItemsSource
    {
        get => (ObservableCollection<MedicalCheckStateModel>)GetValue(ItemsSourceProperty);
        set => SetValue(ItemsSourceProperty, value);
    }
}