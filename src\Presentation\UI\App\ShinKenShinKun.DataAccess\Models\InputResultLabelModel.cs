﻿namespace ShinKenShinKun.DataAccess;

public partial class InputResultLabelModel : ComponentLayoutModel
{
    [ObservableProperty] private string title;
    [ObservableProperty] private string value;
    [ObservableProperty] private DisplayMode displayMode;
    [ObservableProperty] private LabelResultType labelResultType;
    [ObservableProperty] private IList<string> categories;
    [ObservableProperty] private string examinationTypeId;
    [ObservableProperty] private string examinationItemId;
    [ObservableProperty] private string keyNumericSettingId;
    [ObservableProperty] private string switchPressSettingId;
}