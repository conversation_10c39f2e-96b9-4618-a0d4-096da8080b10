<?xml version="1.0" encoding="utf-8" ?>
<HorizontalStackLayout
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Class="ShinKenShinKun.SwitchModeView"
    Margin="{ui:EdgeInsets Right={Static ui:Dimens.Spacing100}}"
    HorizontalOptions="End"
    VerticalOptions="Center">
    <Grid
        ColumnDefinitions="auto,auto,auto"
        ColumnSpacing="{Static ui:Dimens.Spacing10}">
        <Label
            Style="{Static ui:Styles.OnlineOfflineModeLabelStyle}"
            Text="{Static resources:AppResources.Online}" />
        <telerik:RadBorder
            Grid.Column="1"
            BackgroundColor="{Static ui:AppColors.HanBlue}"
            CornerRadius="{Static ui:Dimens.RadBorderCornerRadius4}">
            <telerik:RadBorder.GestureRecognizers>
                <TapGestureRecognizer
                    Command="{Binding ChangeModeCommand}" />
            </telerik:RadBorder.GestureRecognizers>
            <Grid
                Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.Spacing1},
                                                    Horizontal={Static ui:Dimens.Spacing3}}"
                ColumnDefinitions="auto,auto"
                ColumnSpacing="2">
                <telerik:RadButton
                    x:Name="onlineModeButton"
                    Command="{Binding ChangeModeCommand}"
                    IsEnabled="{Binding IsOnlineMode}"
                    Style="{Static ui:Styles.OnlineOfflineSwitchModeButton}">
                    <telerik:RadButton.Triggers>
                        <DataTrigger
                            Binding="{Binding IsOnlineMode}"
                            TargetType="telerik:RadButton"
                            Value="False">
                            <Setter
                                Property="BackgroundColor"
                                Value="{Static ui:AppColors.Transparent}" />
                        </DataTrigger>
                    </telerik:RadButton.Triggers>
                </telerik:RadButton>

                <telerik:RadButton
                    x:Name="offlineModeButton"
                    Grid.Column="1"
                    Command="{Binding ChangeModeCommand}"
                    IsEnabled="{Binding IsOfflineMode}"
                    Style="{Static ui:Styles.OnlineOfflineSwitchModeButton}">
                    <telerik:RadButton.Triggers>
                        <DataTrigger
                            Binding="{Binding IsOfflineMode}"
                            TargetType="telerik:RadButton"
                            Value="False">
                            <Setter
                                Property="BackgroundColor"
                                Value="{Static ui:AppColors.Transparent}" />
                        </DataTrigger>
                    </telerik:RadButton.Triggers>
                </telerik:RadButton>
            </Grid>
        </telerik:RadBorder>
        <Label
            Grid.Column="2"
            Style="{Static ui:Styles.OnlineOfflineModeLabelStyle}"
            Text="{Static resources:AppResources.Offline}" />
    </Grid>
</HorizontalStackLayout>
