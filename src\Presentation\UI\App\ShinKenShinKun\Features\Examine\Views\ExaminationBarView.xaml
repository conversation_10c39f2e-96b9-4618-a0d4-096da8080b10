<?xml version="1.0" encoding="utf-8" ?>
    
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExaminationBarView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <ContentView.Resources>
        <ControlTemplate
            x:Key="ItemBarItemTemplate">
            <Grid
                BackgroundColor="{TemplateBinding BackgroundColor}">
                <ContentPresenter />
            </Grid>
        </ControlTemplate>
    </ContentView.Resources>
    <telerik:RadBorder
        Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingSm}}"
        Style="{x:Static ui:Styles.ExaminationMenuBorderStyle}">
        <telerik:RadCollectionView
            SelectionMode="Single"
            DisplayMemberPath="DisplayName"
            SelectedItem="{Binding ItemSelected, Source={x:Reference this}, Mode=TwoWay}"
            ItemsSource="{Binding ItemsSource, Source={x:Reference this}}">
            <telerik:RadCollectionView.ItemViewStyle>
                <Style
                    BasedOn="{x:Static ui:Styles.ItemBarCollectionStyle}"
                    TargetType="telerik:RadCollectionViewItemView">
                    <Setter
                        Property="ControlTemplate"
                        Value="{StaticResource ItemBarItemTemplate}" />
                    <Setter
                        Property="VisualStateManager.VisualStateGroups">
                        <VisualStateGroupList>
                            <VisualStateGroup
                                Name="CommonStates">
                                <VisualState
                                    Name="Normal" />
                                <VisualState
                                    Name="MouseOver">
                                    <VisualState.Setters>
                                        <Setter
                                            Property="BackgroundColor"
                                            Value="{x:Static ui:AppColors.DarkSlateBlue}" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState
                                    Name="Selected">
                                    <VisualState.Setters>
                                        <Setter
                                            Property="BackgroundColor"
                                            Value="{x:Static ui:AppColors.DarkSlateBlue}" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateGroupList>
                    </Setter>
                </Style>
            </telerik:RadCollectionView.ItemViewStyle>
            <telerik:RadCollectionView.ItemsLayout>
                <telerik:CollectionViewLinearLayout
                    Orientation="Horizontal"
                    ItemSpacing="{x:Static ui:Dimens.SpacingXxs}" />
            </telerik:RadCollectionView.ItemsLayout>
            <telerik:RadCollectionView.ItemTemplate>
                <DataTemplate
                    x:DataType="x:Object">
                    <Label
                        Style="{x:Static ui:Styles.BarItemLabelStyle}"
                        Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.Spacing24}}"
                        Text="{Binding DisplayName}" />
                </DataTemplate>
            </telerik:RadCollectionView.ItemTemplate>
        </telerik:RadCollectionView>
    </telerik:RadBorder>
</ContentView>
