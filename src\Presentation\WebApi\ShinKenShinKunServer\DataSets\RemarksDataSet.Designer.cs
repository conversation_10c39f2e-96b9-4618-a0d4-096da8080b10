﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace ShinKenShinKunServer.DataSets {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("RemarksDataSet")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class RemarksDataSet : global::System.Data.DataSet {
        
        private RemarksItemListDataTable tableRemarksItemList;
        
        private ClientRemarksInformationDataTable tableClientRemarksInformation;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public RemarksDataSet() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected RemarksDataSet(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["RemarksItemList"] != null)) {
                    base.Tables.Add(new RemarksItemListDataTable(ds.Tables["RemarksItemList"]));
                }
                if ((ds.Tables["ClientRemarksInformation"] != null)) {
                    base.Tables.Add(new ClientRemarksInformationDataTable(ds.Tables["ClientRemarksInformation"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public RemarksItemListDataTable RemarksItemList {
            get {
                return this.tableRemarksItemList;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public ClientRemarksInformationDataTable ClientRemarksInformation {
            get {
                return this.tableClientRemarksInformation;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public override global::System.Data.DataSet Clone() {
            RemarksDataSet cln = ((RemarksDataSet)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["RemarksItemList"] != null)) {
                    base.Tables.Add(new RemarksItemListDataTable(ds.Tables["RemarksItemList"]));
                }
                if ((ds.Tables["ClientRemarksInformation"] != null)) {
                    base.Tables.Add(new ClientRemarksInformationDataTable(ds.Tables["ClientRemarksInformation"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        internal void InitVars(bool initTable) {
            this.tableRemarksItemList = ((RemarksItemListDataTable)(base.Tables["RemarksItemList"]));
            if ((initTable == true)) {
                if ((this.tableRemarksItemList != null)) {
                    this.tableRemarksItemList.InitVars();
                }
            }
            this.tableClientRemarksInformation = ((ClientRemarksInformationDataTable)(base.Tables["ClientRemarksInformation"]));
            if ((initTable == true)) {
                if ((this.tableClientRemarksInformation != null)) {
                    this.tableClientRemarksInformation.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitClass() {
            this.DataSetName = "RemarksDataSet";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/RemarksDataSet.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableRemarksItemList = new RemarksItemListDataTable();
            base.Tables.Add(this.tableRemarksItemList);
            this.tableClientRemarksInformation = new ClientRemarksInformationDataTable();
            base.Tables.Add(this.tableClientRemarksInformation);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private bool ShouldSerializeRemarksItemList() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private bool ShouldSerializeClientRemarksInformation() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            RemarksDataSet ds = new RemarksDataSet();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public delegate void RemarksItemListRowChangeEventHandler(object sender, RemarksItemListRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public delegate void ClientRemarksInformationRowChangeEventHandler(object sender, ClientRemarksInformationRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class RemarksItemListDataTable : global::System.Data.TypedTableBase<RemarksItemListRow> {
            
            private global::System.Data.DataColumn columnRemarksItemNo;
            
            private global::System.Data.DataColumn columnRemarksItemName;
            
            private global::System.Data.DataColumn columnRemarksItemDetail;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListDataTable() {
                this.TableName = "RemarksItemList";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal RemarksItemListDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected RemarksItemListDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn RemarksItemNoColumn {
                get {
                    return this.columnRemarksItemNo;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn RemarksItemNameColumn {
                get {
                    return this.columnRemarksItemName;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn RemarksItemDetailColumn {
                get {
                    return this.columnRemarksItemDetail;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRow this[int index] {
                get {
                    return ((RemarksItemListRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event RemarksItemListRowChangeEventHandler RemarksItemListRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event RemarksItemListRowChangeEventHandler RemarksItemListRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event RemarksItemListRowChangeEventHandler RemarksItemListRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event RemarksItemListRowChangeEventHandler RemarksItemListRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void AddRemarksItemListRow(RemarksItemListRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRow AddRemarksItemListRow(short RemarksItemNo, string RemarksItemName, string RemarksItemDetail) {
                RemarksItemListRow rowRemarksItemListRow = ((RemarksItemListRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        RemarksItemNo,
                        RemarksItemName,
                        RemarksItemDetail};
                rowRemarksItemListRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowRemarksItemListRow);
                return rowRemarksItemListRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRow FindByRemarksItemNo(short RemarksItemNo) {
                return ((RemarksItemListRow)(this.Rows.Find(new object[] {
                            RemarksItemNo})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                RemarksItemListDataTable cln = ((RemarksItemListDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new RemarksItemListDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal void InitVars() {
                this.columnRemarksItemNo = base.Columns["RemarksItemNo"];
                this.columnRemarksItemName = base.Columns["RemarksItemName"];
                this.columnRemarksItemDetail = base.Columns["RemarksItemDetail"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            private void InitClass() {
                this.columnRemarksItemNo = new global::System.Data.DataColumn("RemarksItemNo", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRemarksItemNo);
                this.columnRemarksItemName = new global::System.Data.DataColumn("RemarksItemName", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRemarksItemName);
                this.columnRemarksItemDetail = new global::System.Data.DataColumn("RemarksItemDetail", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRemarksItemDetail);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnRemarksItemNo}, true));
                this.columnRemarksItemNo.AllowDBNull = false;
                this.columnRemarksItemNo.Unique = true;
                this.columnRemarksItemName.MaxLength = 40;
                this.columnRemarksItemDetail.MaxLength = 200;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRow NewRemarksItemListRow() {
                return ((RemarksItemListRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new RemarksItemListRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(RemarksItemListRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.RemarksItemListRowChanged != null)) {
                    this.RemarksItemListRowChanged(this, new RemarksItemListRowChangeEvent(((RemarksItemListRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.RemarksItemListRowChanging != null)) {
                    this.RemarksItemListRowChanging(this, new RemarksItemListRowChangeEvent(((RemarksItemListRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.RemarksItemListRowDeleted != null)) {
                    this.RemarksItemListRowDeleted(this, new RemarksItemListRowChangeEvent(((RemarksItemListRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.RemarksItemListRowDeleting != null)) {
                    this.RemarksItemListRowDeleting(this, new RemarksItemListRowChangeEvent(((RemarksItemListRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void RemoveRemarksItemListRow(RemarksItemListRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                RemarksDataSet ds = new RemarksDataSet();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "RemarksItemListDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class ClientRemarksInformationDataTable : global::System.Data.TypedTableBase<ClientRemarksInformationRow> {
            
            private global::System.Data.DataColumn columnMedicalCheckDate;
            
            private global::System.Data.DataColumn columnClientID;
            
            private global::System.Data.DataColumn columnDivision;
            
            private global::System.Data.DataColumn columnComment1;
            
            private global::System.Data.DataColumn columnComment2;
            
            private global::System.Data.DataColumn columnComment3;
            
            private global::System.Data.DataColumn columnComment4;
            
            private global::System.Data.DataColumn columnComment5;
            
            private global::System.Data.DataColumn columnComment6;
            
            private global::System.Data.DataColumn columnComment7;
            
            private global::System.Data.DataColumn columnComment8;
            
            private global::System.Data.DataColumn columnComment9;
            
            private global::System.Data.DataColumn columnComment10;
            
            private global::System.Data.DataColumn columnComment11;
            
            private global::System.Data.DataColumn columnComment12;
            
            private global::System.Data.DataColumn columnComment13;
            
            private global::System.Data.DataColumn columnComment14;
            
            private global::System.Data.DataColumn columnComment15;
            
            private global::System.Data.DataColumn columnComment16;
            
            private global::System.Data.DataColumn columnComment17;
            
            private global::System.Data.DataColumn columnComment18;
            
            private global::System.Data.DataColumn columnComment19;
            
            private global::System.Data.DataColumn columnComment20;
            
            private global::System.Data.DataColumn columnComment21;
            
            private global::System.Data.DataColumn columnComment22;
            
            private global::System.Data.DataColumn columnComment23;
            
            private global::System.Data.DataColumn columnComment24;
            
            private global::System.Data.DataColumn columnComment25;
            
            private global::System.Data.DataColumn columnComment26;
            
            private global::System.Data.DataColumn columnComment27;
            
            private global::System.Data.DataColumn columnComment28;
            
            private global::System.Data.DataColumn columnComment29;
            
            private global::System.Data.DataColumn columnComment30;
            
            private global::System.Data.DataColumn columnComment31;
            
            private global::System.Data.DataColumn columnComment32;
            
            private global::System.Data.DataColumn columnComment33;
            
            private global::System.Data.DataColumn columnComment34;
            
            private global::System.Data.DataColumn columnComment35;
            
            private global::System.Data.DataColumn columnComment36;
            
            private global::System.Data.DataColumn columnComment37;
            
            private global::System.Data.DataColumn columnComment38;
            
            private global::System.Data.DataColumn columnComment39;
            
            private global::System.Data.DataColumn columnComment40;
            
            private global::System.Data.DataColumn columnComment41;
            
            private global::System.Data.DataColumn columnComment42;
            
            private global::System.Data.DataColumn columnComment43;
            
            private global::System.Data.DataColumn columnComment44;
            
            private global::System.Data.DataColumn columnComment45;
            
            private global::System.Data.DataColumn columnComment46;
            
            private global::System.Data.DataColumn columnComment47;
            
            private global::System.Data.DataColumn columnComment48;
            
            private global::System.Data.DataColumn columnComment49;
            
            private global::System.Data.DataColumn columnComment50;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationDataTable() {
                this.TableName = "ClientRemarksInformation";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal ClientRemarksInformationDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected ClientRemarksInformationDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn MedicalCheckDateColumn {
                get {
                    return this.columnMedicalCheckDate;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn ClientIDColumn {
                get {
                    return this.columnClientID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn DivisionColumn {
                get {
                    return this.columnDivision;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment1Column {
                get {
                    return this.columnComment1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment2Column {
                get {
                    return this.columnComment2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment3Column {
                get {
                    return this.columnComment3;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment4Column {
                get {
                    return this.columnComment4;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment5Column {
                get {
                    return this.columnComment5;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment6Column {
                get {
                    return this.columnComment6;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment7Column {
                get {
                    return this.columnComment7;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment8Column {
                get {
                    return this.columnComment8;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment9Column {
                get {
                    return this.columnComment9;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment10Column {
                get {
                    return this.columnComment10;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment11Column {
                get {
                    return this.columnComment11;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment12Column {
                get {
                    return this.columnComment12;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment13Column {
                get {
                    return this.columnComment13;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment14Column {
                get {
                    return this.columnComment14;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment15Column {
                get {
                    return this.columnComment15;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment16Column {
                get {
                    return this.columnComment16;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment17Column {
                get {
                    return this.columnComment17;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment18Column {
                get {
                    return this.columnComment18;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment19Column {
                get {
                    return this.columnComment19;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment20Column {
                get {
                    return this.columnComment20;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment21Column {
                get {
                    return this.columnComment21;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment22Column {
                get {
                    return this.columnComment22;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment23Column {
                get {
                    return this.columnComment23;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment24Column {
                get {
                    return this.columnComment24;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment25Column {
                get {
                    return this.columnComment25;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment26Column {
                get {
                    return this.columnComment26;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment27Column {
                get {
                    return this.columnComment27;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment28Column {
                get {
                    return this.columnComment28;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment29Column {
                get {
                    return this.columnComment29;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment30Column {
                get {
                    return this.columnComment30;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment31Column {
                get {
                    return this.columnComment31;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment32Column {
                get {
                    return this.columnComment32;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment33Column {
                get {
                    return this.columnComment33;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment34Column {
                get {
                    return this.columnComment34;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment35Column {
                get {
                    return this.columnComment35;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment36Column {
                get {
                    return this.columnComment36;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment37Column {
                get {
                    return this.columnComment37;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment38Column {
                get {
                    return this.columnComment38;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment39Column {
                get {
                    return this.columnComment39;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment40Column {
                get {
                    return this.columnComment40;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment41Column {
                get {
                    return this.columnComment41;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment42Column {
                get {
                    return this.columnComment42;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment43Column {
                get {
                    return this.columnComment43;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment44Column {
                get {
                    return this.columnComment44;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment45Column {
                get {
                    return this.columnComment45;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment46Column {
                get {
                    return this.columnComment46;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment47Column {
                get {
                    return this.columnComment47;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment48Column {
                get {
                    return this.columnComment48;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment49Column {
                get {
                    return this.columnComment49;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataColumn Comment50Column {
                get {
                    return this.columnComment50;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRow this[int index] {
                get {
                    return ((ClientRemarksInformationRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event ClientRemarksInformationRowChangeEventHandler ClientRemarksInformationRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event ClientRemarksInformationRowChangeEventHandler ClientRemarksInformationRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event ClientRemarksInformationRowChangeEventHandler ClientRemarksInformationRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public event ClientRemarksInformationRowChangeEventHandler ClientRemarksInformationRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void AddClientRemarksInformationRow(ClientRemarksInformationRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRow AddClientRemarksInformationRow(
                        System.DateTime MedicalCheckDate, 
                        string ClientID, 
                        string Division, 
                        short Comment1, 
                        short Comment2, 
                        short Comment3, 
                        short Comment4, 
                        short Comment5, 
                        short Comment6, 
                        short Comment7, 
                        short Comment8, 
                        short Comment9, 
                        short Comment10, 
                        short Comment11, 
                        short Comment12, 
                        short Comment13, 
                        short Comment14, 
                        short Comment15, 
                        short Comment16, 
                        short Comment17, 
                        short Comment18, 
                        short Comment19, 
                        short Comment20, 
                        short Comment21, 
                        short Comment22, 
                        short Comment23, 
                        short Comment24, 
                        short Comment25, 
                        short Comment26, 
                        short Comment27, 
                        short Comment28, 
                        short Comment29, 
                        short Comment30, 
                        short Comment31, 
                        short Comment32, 
                        short Comment33, 
                        short Comment34, 
                        short Comment35, 
                        short Comment36, 
                        short Comment37, 
                        short Comment38, 
                        short Comment39, 
                        short Comment40, 
                        short Comment41, 
                        short Comment42, 
                        short Comment43, 
                        short Comment44, 
                        short Comment45, 
                        short Comment46, 
                        short Comment47, 
                        short Comment48, 
                        short Comment49, 
                        short Comment50) {
                ClientRemarksInformationRow rowClientRemarksInformationRow = ((ClientRemarksInformationRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        MedicalCheckDate,
                        ClientID,
                        Division,
                        Comment1,
                        Comment2,
                        Comment3,
                        Comment4,
                        Comment5,
                        Comment6,
                        Comment7,
                        Comment8,
                        Comment9,
                        Comment10,
                        Comment11,
                        Comment12,
                        Comment13,
                        Comment14,
                        Comment15,
                        Comment16,
                        Comment17,
                        Comment18,
                        Comment19,
                        Comment20,
                        Comment21,
                        Comment22,
                        Comment23,
                        Comment24,
                        Comment25,
                        Comment26,
                        Comment27,
                        Comment28,
                        Comment29,
                        Comment30,
                        Comment31,
                        Comment32,
                        Comment33,
                        Comment34,
                        Comment35,
                        Comment36,
                        Comment37,
                        Comment38,
                        Comment39,
                        Comment40,
                        Comment41,
                        Comment42,
                        Comment43,
                        Comment44,
                        Comment45,
                        Comment46,
                        Comment47,
                        Comment48,
                        Comment49,
                        Comment50};
                rowClientRemarksInformationRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowClientRemarksInformationRow);
                return rowClientRemarksInformationRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRow FindByMedicalCheckDateClientIDDivision(System.DateTime MedicalCheckDate, string ClientID, string Division) {
                return ((ClientRemarksInformationRow)(this.Rows.Find(new object[] {
                            MedicalCheckDate,
                            ClientID,
                            Division})));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                ClientRemarksInformationDataTable cln = ((ClientRemarksInformationDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new ClientRemarksInformationDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal void InitVars() {
                this.columnMedicalCheckDate = base.Columns["MedicalCheckDate"];
                this.columnClientID = base.Columns["ClientID"];
                this.columnDivision = base.Columns["Division"];
                this.columnComment1 = base.Columns["Comment1"];
                this.columnComment2 = base.Columns["Comment2"];
                this.columnComment3 = base.Columns["Comment3"];
                this.columnComment4 = base.Columns["Comment4"];
                this.columnComment5 = base.Columns["Comment5"];
                this.columnComment6 = base.Columns["Comment6"];
                this.columnComment7 = base.Columns["Comment7"];
                this.columnComment8 = base.Columns["Comment8"];
                this.columnComment9 = base.Columns["Comment9"];
                this.columnComment10 = base.Columns["Comment10"];
                this.columnComment11 = base.Columns["Comment11"];
                this.columnComment12 = base.Columns["Comment12"];
                this.columnComment13 = base.Columns["Comment13"];
                this.columnComment14 = base.Columns["Comment14"];
                this.columnComment15 = base.Columns["Comment15"];
                this.columnComment16 = base.Columns["Comment16"];
                this.columnComment17 = base.Columns["Comment17"];
                this.columnComment18 = base.Columns["Comment18"];
                this.columnComment19 = base.Columns["Comment19"];
                this.columnComment20 = base.Columns["Comment20"];
                this.columnComment21 = base.Columns["Comment21"];
                this.columnComment22 = base.Columns["Comment22"];
                this.columnComment23 = base.Columns["Comment23"];
                this.columnComment24 = base.Columns["Comment24"];
                this.columnComment25 = base.Columns["Comment25"];
                this.columnComment26 = base.Columns["Comment26"];
                this.columnComment27 = base.Columns["Comment27"];
                this.columnComment28 = base.Columns["Comment28"];
                this.columnComment29 = base.Columns["Comment29"];
                this.columnComment30 = base.Columns["Comment30"];
                this.columnComment31 = base.Columns["Comment31"];
                this.columnComment32 = base.Columns["Comment32"];
                this.columnComment33 = base.Columns["Comment33"];
                this.columnComment34 = base.Columns["Comment34"];
                this.columnComment35 = base.Columns["Comment35"];
                this.columnComment36 = base.Columns["Comment36"];
                this.columnComment37 = base.Columns["Comment37"];
                this.columnComment38 = base.Columns["Comment38"];
                this.columnComment39 = base.Columns["Comment39"];
                this.columnComment40 = base.Columns["Comment40"];
                this.columnComment41 = base.Columns["Comment41"];
                this.columnComment42 = base.Columns["Comment42"];
                this.columnComment43 = base.Columns["Comment43"];
                this.columnComment44 = base.Columns["Comment44"];
                this.columnComment45 = base.Columns["Comment45"];
                this.columnComment46 = base.Columns["Comment46"];
                this.columnComment47 = base.Columns["Comment47"];
                this.columnComment48 = base.Columns["Comment48"];
                this.columnComment49 = base.Columns["Comment49"];
                this.columnComment50 = base.Columns["Comment50"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            private void InitClass() {
                this.columnMedicalCheckDate = new global::System.Data.DataColumn("MedicalCheckDate", typeof(global::System.DateTime), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnMedicalCheckDate);
                this.columnClientID = new global::System.Data.DataColumn("ClientID", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnClientID);
                this.columnDivision = new global::System.Data.DataColumn("Division", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnDivision);
                this.columnComment1 = new global::System.Data.DataColumn("Comment1", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment1);
                this.columnComment2 = new global::System.Data.DataColumn("Comment2", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment2);
                this.columnComment3 = new global::System.Data.DataColumn("Comment3", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment3);
                this.columnComment4 = new global::System.Data.DataColumn("Comment4", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment4);
                this.columnComment5 = new global::System.Data.DataColumn("Comment5", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment5);
                this.columnComment6 = new global::System.Data.DataColumn("Comment6", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment6);
                this.columnComment7 = new global::System.Data.DataColumn("Comment7", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment7);
                this.columnComment8 = new global::System.Data.DataColumn("Comment8", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment8);
                this.columnComment9 = new global::System.Data.DataColumn("Comment9", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment9);
                this.columnComment10 = new global::System.Data.DataColumn("Comment10", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment10);
                this.columnComment11 = new global::System.Data.DataColumn("Comment11", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment11);
                this.columnComment12 = new global::System.Data.DataColumn("Comment12", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment12);
                this.columnComment13 = new global::System.Data.DataColumn("Comment13", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment13);
                this.columnComment14 = new global::System.Data.DataColumn("Comment14", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment14);
                this.columnComment15 = new global::System.Data.DataColumn("Comment15", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment15);
                this.columnComment16 = new global::System.Data.DataColumn("Comment16", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment16);
                this.columnComment17 = new global::System.Data.DataColumn("Comment17", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment17);
                this.columnComment18 = new global::System.Data.DataColumn("Comment18", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment18);
                this.columnComment19 = new global::System.Data.DataColumn("Comment19", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment19);
                this.columnComment20 = new global::System.Data.DataColumn("Comment20", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment20);
                this.columnComment21 = new global::System.Data.DataColumn("Comment21", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment21);
                this.columnComment22 = new global::System.Data.DataColumn("Comment22", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment22);
                this.columnComment23 = new global::System.Data.DataColumn("Comment23", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment23);
                this.columnComment24 = new global::System.Data.DataColumn("Comment24", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment24);
                this.columnComment25 = new global::System.Data.DataColumn("Comment25", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment25);
                this.columnComment26 = new global::System.Data.DataColumn("Comment26", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment26);
                this.columnComment27 = new global::System.Data.DataColumn("Comment27", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment27);
                this.columnComment28 = new global::System.Data.DataColumn("Comment28", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment28);
                this.columnComment29 = new global::System.Data.DataColumn("Comment29", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment29);
                this.columnComment30 = new global::System.Data.DataColumn("Comment30", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment30);
                this.columnComment31 = new global::System.Data.DataColumn("Comment31", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment31);
                this.columnComment32 = new global::System.Data.DataColumn("Comment32", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment32);
                this.columnComment33 = new global::System.Data.DataColumn("Comment33", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment33);
                this.columnComment34 = new global::System.Data.DataColumn("Comment34", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment34);
                this.columnComment35 = new global::System.Data.DataColumn("Comment35", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment35);
                this.columnComment36 = new global::System.Data.DataColumn("Comment36", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment36);
                this.columnComment37 = new global::System.Data.DataColumn("Comment37", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment37);
                this.columnComment38 = new global::System.Data.DataColumn("Comment38", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment38);
                this.columnComment39 = new global::System.Data.DataColumn("Comment39", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment39);
                this.columnComment40 = new global::System.Data.DataColumn("Comment40", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment40);
                this.columnComment41 = new global::System.Data.DataColumn("Comment41", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment41);
                this.columnComment42 = new global::System.Data.DataColumn("Comment42", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment42);
                this.columnComment43 = new global::System.Data.DataColumn("Comment43", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment43);
                this.columnComment44 = new global::System.Data.DataColumn("Comment44", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment44);
                this.columnComment45 = new global::System.Data.DataColumn("Comment45", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment45);
                this.columnComment46 = new global::System.Data.DataColumn("Comment46", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment46);
                this.columnComment47 = new global::System.Data.DataColumn("Comment47", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment47);
                this.columnComment48 = new global::System.Data.DataColumn("Comment48", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment48);
                this.columnComment49 = new global::System.Data.DataColumn("Comment49", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment49);
                this.columnComment50 = new global::System.Data.DataColumn("Comment50", typeof(short), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnComment50);
                this.Constraints.Add(new global::System.Data.UniqueConstraint("Constraint1", new global::System.Data.DataColumn[] {
                                this.columnMedicalCheckDate,
                                this.columnClientID,
                                this.columnDivision}, true));
                this.columnMedicalCheckDate.AllowDBNull = false;
                this.columnClientID.AllowDBNull = false;
                this.columnClientID.MaxLength = 10;
                this.columnDivision.AllowDBNull = false;
                this.columnDivision.MaxLength = 3;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRow NewClientRemarksInformationRow() {
                return ((ClientRemarksInformationRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new ClientRemarksInformationRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(ClientRemarksInformationRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.ClientRemarksInformationRowChanged != null)) {
                    this.ClientRemarksInformationRowChanged(this, new ClientRemarksInformationRowChangeEvent(((ClientRemarksInformationRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.ClientRemarksInformationRowChanging != null)) {
                    this.ClientRemarksInformationRowChanging(this, new ClientRemarksInformationRowChangeEvent(((ClientRemarksInformationRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.ClientRemarksInformationRowDeleted != null)) {
                    this.ClientRemarksInformationRowDeleted(this, new ClientRemarksInformationRowChangeEvent(((ClientRemarksInformationRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.ClientRemarksInformationRowDeleting != null)) {
                    this.ClientRemarksInformationRowDeleting(this, new ClientRemarksInformationRowChangeEvent(((ClientRemarksInformationRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void RemoveClientRemarksInformationRow(ClientRemarksInformationRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                RemarksDataSet ds = new RemarksDataSet();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "ClientRemarksInformationDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class RemarksItemListRow : global::System.Data.DataRow {
            
            private RemarksItemListDataTable tableRemarksItemList;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal RemarksItemListRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableRemarksItemList = ((RemarksItemListDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short RemarksItemNo {
                get {
                    return ((short)(this[this.tableRemarksItemList.RemarksItemNoColumn]));
                }
                set {
                    this[this.tableRemarksItemList.RemarksItemNoColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public string RemarksItemName {
                get {
                    try {
                        return ((string)(this[this.tableRemarksItemList.RemarksItemNameColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'RemarksItemName\' in table \'RemarksItemList\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableRemarksItemList.RemarksItemNameColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public string RemarksItemDetail {
                get {
                    try {
                        return ((string)(this[this.tableRemarksItemList.RemarksItemDetailColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'RemarksItemDetail\' in table \'RemarksItemList\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableRemarksItemList.RemarksItemDetailColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsRemarksItemNameNull() {
                return this.IsNull(this.tableRemarksItemList.RemarksItemNameColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetRemarksItemNameNull() {
                this[this.tableRemarksItemList.RemarksItemNameColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsRemarksItemDetailNull() {
                return this.IsNull(this.tableRemarksItemList.RemarksItemDetailColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetRemarksItemDetailNull() {
                this[this.tableRemarksItemList.RemarksItemDetailColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class ClientRemarksInformationRow : global::System.Data.DataRow {
            
            private ClientRemarksInformationDataTable tableClientRemarksInformation;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            internal ClientRemarksInformationRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableClientRemarksInformation = ((ClientRemarksInformationDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public System.DateTime MedicalCheckDate {
                get {
                    return ((global::System.DateTime)(this[this.tableClientRemarksInformation.MedicalCheckDateColumn]));
                }
                set {
                    this[this.tableClientRemarksInformation.MedicalCheckDateColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public string ClientID {
                get {
                    return ((string)(this[this.tableClientRemarksInformation.ClientIDColumn]));
                }
                set {
                    this[this.tableClientRemarksInformation.ClientIDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public string Division {
                get {
                    return ((string)(this[this.tableClientRemarksInformation.DivisionColumn]));
                }
                set {
                    this[this.tableClientRemarksInformation.DivisionColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment1 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment1\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment2 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment2Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment2\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment3 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment3Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment3\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment3Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment4 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment4Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment4\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment4Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment5 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment5Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment5\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment5Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment6 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment6Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment6\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment6Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment7 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment7Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment7\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment7Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment8 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment8Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment8\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment8Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment9 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment9Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment9\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment9Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment10 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment10Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment10\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment10Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment11 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment11Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment11\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment11Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment12 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment12Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment12\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment12Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment13 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment13Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment13\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment13Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment14 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment14Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment14\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment14Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment15 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment15Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment15\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment15Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment16 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment16Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment16\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment16Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment17 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment17Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment17\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment17Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment18 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment18Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment18\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment18Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment19 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment19Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment19\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment19Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment20 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment20Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment20\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment20Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment21 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment21Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment21\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment21Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment22 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment22Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment22\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment22Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment23 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment23Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment23\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment23Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment24 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment24Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment24\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment24Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment25 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment25Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment25\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment25Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment26 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment26Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment26\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment26Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment27 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment27Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment27\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment27Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment28 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment28Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment28\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment28Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment29 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment29Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment29\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment29Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment30 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment30Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment30\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment30Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment31 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment31Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment31\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment31Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment32 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment32Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment32\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment32Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment33 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment33Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment33\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment33Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment34 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment34Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment34\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment34Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment35 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment35Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment35\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment35Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment36 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment36Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment36\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment36Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment37 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment37Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment37\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment37Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment38 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment38Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment38\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment38Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment39 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment39Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment39\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment39Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment40 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment40Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment40\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment40Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment41 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment41Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment41\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment41Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment42 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment42Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment42\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment42Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment43 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment43Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment43\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment43Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment44 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment44Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment44\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment44Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment45 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment45Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment45\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment45Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment46 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment46Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment46\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment46Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment47 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment47Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment47\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment47Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment48 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment48Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment48\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment48Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment49 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment49Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment49\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment49Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public short Comment50 {
                get {
                    try {
                        return ((short)(this[this.tableClientRemarksInformation.Comment50Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("The value for column \'Comment50\' in table \'ClientRemarksInformation\' is DBNull.", e);
                    }
                }
                set {
                    this[this.tableClientRemarksInformation.Comment50Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment1Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment1Null() {
                this[this.tableClientRemarksInformation.Comment1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment2Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment2Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment2Null() {
                this[this.tableClientRemarksInformation.Comment2Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment3Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment3Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment3Null() {
                this[this.tableClientRemarksInformation.Comment3Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment4Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment4Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment4Null() {
                this[this.tableClientRemarksInformation.Comment4Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment5Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment5Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment5Null() {
                this[this.tableClientRemarksInformation.Comment5Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment6Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment6Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment6Null() {
                this[this.tableClientRemarksInformation.Comment6Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment7Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment7Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment7Null() {
                this[this.tableClientRemarksInformation.Comment7Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment8Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment8Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment8Null() {
                this[this.tableClientRemarksInformation.Comment8Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment9Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment9Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment9Null() {
                this[this.tableClientRemarksInformation.Comment9Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment10Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment10Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment10Null() {
                this[this.tableClientRemarksInformation.Comment10Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment11Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment11Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment11Null() {
                this[this.tableClientRemarksInformation.Comment11Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment12Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment12Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment12Null() {
                this[this.tableClientRemarksInformation.Comment12Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment13Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment13Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment13Null() {
                this[this.tableClientRemarksInformation.Comment13Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment14Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment14Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment14Null() {
                this[this.tableClientRemarksInformation.Comment14Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment15Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment15Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment15Null() {
                this[this.tableClientRemarksInformation.Comment15Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment16Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment16Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment16Null() {
                this[this.tableClientRemarksInformation.Comment16Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment17Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment17Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment17Null() {
                this[this.tableClientRemarksInformation.Comment17Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment18Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment18Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment18Null() {
                this[this.tableClientRemarksInformation.Comment18Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment19Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment19Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment19Null() {
                this[this.tableClientRemarksInformation.Comment19Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment20Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment20Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment20Null() {
                this[this.tableClientRemarksInformation.Comment20Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment21Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment21Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment21Null() {
                this[this.tableClientRemarksInformation.Comment21Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment22Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment22Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment22Null() {
                this[this.tableClientRemarksInformation.Comment22Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment23Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment23Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment23Null() {
                this[this.tableClientRemarksInformation.Comment23Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment24Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment24Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment24Null() {
                this[this.tableClientRemarksInformation.Comment24Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment25Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment25Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment25Null() {
                this[this.tableClientRemarksInformation.Comment25Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment26Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment26Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment26Null() {
                this[this.tableClientRemarksInformation.Comment26Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment27Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment27Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment27Null() {
                this[this.tableClientRemarksInformation.Comment27Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment28Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment28Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment28Null() {
                this[this.tableClientRemarksInformation.Comment28Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment29Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment29Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment29Null() {
                this[this.tableClientRemarksInformation.Comment29Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment30Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment30Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment30Null() {
                this[this.tableClientRemarksInformation.Comment30Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment31Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment31Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment31Null() {
                this[this.tableClientRemarksInformation.Comment31Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment32Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment32Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment32Null() {
                this[this.tableClientRemarksInformation.Comment32Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment33Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment33Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment33Null() {
                this[this.tableClientRemarksInformation.Comment33Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment34Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment34Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment34Null() {
                this[this.tableClientRemarksInformation.Comment34Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment35Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment35Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment35Null() {
                this[this.tableClientRemarksInformation.Comment35Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment36Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment36Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment36Null() {
                this[this.tableClientRemarksInformation.Comment36Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment37Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment37Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment37Null() {
                this[this.tableClientRemarksInformation.Comment37Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment38Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment38Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment38Null() {
                this[this.tableClientRemarksInformation.Comment38Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment39Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment39Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment39Null() {
                this[this.tableClientRemarksInformation.Comment39Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment40Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment40Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment40Null() {
                this[this.tableClientRemarksInformation.Comment40Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment41Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment41Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment41Null() {
                this[this.tableClientRemarksInformation.Comment41Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment42Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment42Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment42Null() {
                this[this.tableClientRemarksInformation.Comment42Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment43Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment43Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment43Null() {
                this[this.tableClientRemarksInformation.Comment43Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment44Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment44Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment44Null() {
                this[this.tableClientRemarksInformation.Comment44Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment45Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment45Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment45Null() {
                this[this.tableClientRemarksInformation.Comment45Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment46Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment46Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment46Null() {
                this[this.tableClientRemarksInformation.Comment46Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment47Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment47Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment47Null() {
                this[this.tableClientRemarksInformation.Comment47Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment48Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment48Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment48Null() {
                this[this.tableClientRemarksInformation.Comment48Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment49Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment49Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment49Null() {
                this[this.tableClientRemarksInformation.Comment49Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public bool IsComment50Null() {
                return this.IsNull(this.tableClientRemarksInformation.Comment50Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public void SetComment50Null() {
                this[this.tableClientRemarksInformation.Comment50Column] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public class RemarksItemListRowChangeEvent : global::System.EventArgs {
            
            private RemarksItemListRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRowChangeEvent(RemarksItemListRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public RemarksItemListRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public class ClientRemarksInformationRowChangeEvent : global::System.EventArgs {
            
            private ClientRemarksInformationRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRowChangeEvent(ClientRemarksInformationRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public ClientRemarksInformationRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}
namespace ShinKenShinKunServer.DataSets.RemarksDataSetTableAdapters {
    
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class RemarksItemListTableAdapter : global::System.ComponentModel.Component {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public RemarksItemListTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "RemarksItemList";
            tableMapping.ColumnMappings.Add("RemarksItemNo", "RemarksItemNo");
            tableMapping.ColumnMappings.Add("RemarksItemName", "RemarksItemName");
            tableMapping.ColumnMappings.Add("RemarksItemDetail", "RemarksItemDetail");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [RemarksItemList] WHERE (([RemarksItemNo] = @Original_RemarksItemNo))" +
                "";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RemarksItemNo", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemNo", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = "INSERT INTO [RemarksItemList] ([RemarksItemNo], [RemarksItemName], [RemarksItemDe" +
                "tail]) VALUES (@RemarksItemNo, @RemarksItemName, @RemarksItemDetail)";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemNo", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemNo", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemName", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemName", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemDetail", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemDetail", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = "UPDATE [RemarksItemList] SET [RemarksItemNo] = @RemarksItemNo, [RemarksItemName] " +
                "= @RemarksItemName, [RemarksItemDetail] = @RemarksItemDetail WHERE (([RemarksIte" +
                "mNo] = @Original_RemarksItemNo))";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemNo", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemNo", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemName", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemName", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemDetail", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemDetail", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_RemarksItemNo", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemNo", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["MedicalCheckupConnectionString"].ConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[2];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = "SELECT          RemarksItemNo, RemarksItemName, RemarksItemDetail\r\nFROM          " +
                "  RemarksItemList\r\nWHERE           (RemarksItemNo = @RemarksItemNo)";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@RemarksItemNo", global::System.Data.SqlDbType.SmallInt, 2, global::System.Data.ParameterDirection.Input, 0, 0, "RemarksItemNo", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[1] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[1].Connection = this.Connection;
            this._commandCollection[1].CommandText = "SELECT          RemarksItemNo, RemarksItemName, RemarksItemDetail\r\nFROM          " +
                "  RemarksItemList";
            this._commandCollection[1].CommandType = global::System.Data.CommandType.Text;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(RemarksDataSet.RemarksItemListDataTable dataTable, short RemarksItemNo) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            this.Adapter.SelectCommand.Parameters[0].Value = ((short)(RemarksItemNo));
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual RemarksDataSet.RemarksItemListDataTable GetData(short RemarksItemNo) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            this.Adapter.SelectCommand.Parameters[0].Value = ((short)(RemarksItemNo));
            RemarksDataSet.RemarksItemListDataTable dataTable = new RemarksDataSet.RemarksItemListDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, false)]
        public virtual int FillBy(RemarksDataSet.RemarksItemListDataTable dataTable) {
            this.Adapter.SelectCommand = this.CommandCollection[1];
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, false)]
        public virtual RemarksDataSet.RemarksItemListDataTable GetDataBy() {
            this.Adapter.SelectCommand = this.CommandCollection[1];
            RemarksDataSet.RemarksItemListDataTable dataTable = new RemarksDataSet.RemarksItemListDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(RemarksDataSet.RemarksItemListDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(RemarksDataSet dataSet) {
            return this.Adapter.Update(dataSet, "RemarksItemList");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(short Original_RemarksItemNo) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((short)(Original_RemarksItemNo));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(short RemarksItemNo, string RemarksItemName, string RemarksItemDetail) {
            this.Adapter.InsertCommand.Parameters[0].Value = ((short)(RemarksItemNo));
            if ((RemarksItemName == null)) {
                this.Adapter.InsertCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[1].Value = ((string)(RemarksItemName));
            }
            if ((RemarksItemDetail == null)) {
                this.Adapter.InsertCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.InsertCommand.Parameters[2].Value = ((string)(RemarksItemDetail));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(short RemarksItemNo, string RemarksItemName, string RemarksItemDetail, short Original_RemarksItemNo) {
            this.Adapter.UpdateCommand.Parameters[0].Value = ((short)(RemarksItemNo));
            if ((RemarksItemName == null)) {
                this.Adapter.UpdateCommand.Parameters[1].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[1].Value = ((string)(RemarksItemName));
            }
            if ((RemarksItemDetail == null)) {
                this.Adapter.UpdateCommand.Parameters[2].Value = global::System.DBNull.Value;
            }
            else {
                this.Adapter.UpdateCommand.Parameters[2].Value = ((string)(RemarksItemDetail));
            }
            this.Adapter.UpdateCommand.Parameters[3].Value = ((short)(Original_RemarksItemNo));
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(string RemarksItemName, string RemarksItemDetail, short Original_RemarksItemNo) {
            return this.Update(Original_RemarksItemNo, RemarksItemName, RemarksItemDetail, Original_RemarksItemNo);
        }
    }
    
    /// <summary>
    ///Represents the connection and commands used to retrieve and save data.
    ///</summary>
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.ComponentModel.DataObjectAttribute(true)]
    [global::System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner" +
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
    public partial class ClientRemarksInformationTableAdapter : global::System.ComponentModel.Component {
        
        private global::System.Data.SqlClient.SqlDataAdapter _adapter;
        
        private global::System.Data.SqlClient.SqlConnection _connection;
        
        private global::System.Data.SqlClient.SqlCommand[] _commandCollection;
        
        private bool _clearBeforeFill;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public ClientRemarksInformationTableAdapter() {
            this.ClearBeforeFill = true;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private global::System.Data.SqlClient.SqlDataAdapter Adapter {
            get {
                if ((this._adapter == null)) {
                    this.InitAdapter();
                }
                return this._adapter;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        internal global::System.Data.SqlClient.SqlConnection Connection {
            get {
                if ((this._connection == null)) {
                    this.InitConnection();
                }
                return this._connection;
            }
            set {
                this._connection = value;
                if ((this.Adapter.InsertCommand != null)) {
                    this.Adapter.InsertCommand.Connection = value;
                }
                if ((this.Adapter.DeleteCommand != null)) {
                    this.Adapter.DeleteCommand.Connection = value;
                }
                if ((this.Adapter.UpdateCommand != null)) {
                    this.Adapter.UpdateCommand.Connection = value;
                }
                for (int i = 0; (i < this.CommandCollection.Length); i = (i + 1)) {
                    if ((this.CommandCollection[i] != null)) {
                        ((global::System.Data.SqlClient.SqlCommand)(this.CommandCollection[i])).Connection = value;
                    }
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        protected global::System.Data.SqlClient.SqlCommand[] CommandCollection {
            get {
                if ((this._commandCollection == null)) {
                    this.InitCommandCollection();
                }
                return this._commandCollection;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        public bool ClearBeforeFill {
            get {
                return this._clearBeforeFill;
            }
            set {
                this._clearBeforeFill = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitAdapter() {
            this._adapter = new global::System.Data.SqlClient.SqlDataAdapter();
            global::System.Data.Common.DataTableMapping tableMapping = new global::System.Data.Common.DataTableMapping();
            tableMapping.SourceTable = "Table";
            tableMapping.DataSetTable = "ClientRemarksInformation";
            tableMapping.ColumnMappings.Add("MedicalCheckDate", "MedicalCheckDate");
            tableMapping.ColumnMappings.Add("ClientID", "ClientID");
            tableMapping.ColumnMappings.Add("Division", "Division");
            tableMapping.ColumnMappings.Add("Comment1", "Comment1");
            tableMapping.ColumnMappings.Add("Comment2", "Comment2");
            tableMapping.ColumnMappings.Add("Comment3", "Comment3");
            tableMapping.ColumnMappings.Add("Comment4", "Comment4");
            tableMapping.ColumnMappings.Add("Comment5", "Comment5");
            tableMapping.ColumnMappings.Add("Comment6", "Comment6");
            tableMapping.ColumnMappings.Add("Comment7", "Comment7");
            tableMapping.ColumnMappings.Add("Comment8", "Comment8");
            tableMapping.ColumnMappings.Add("Comment9", "Comment9");
            tableMapping.ColumnMappings.Add("Comment10", "Comment10");
            tableMapping.ColumnMappings.Add("Comment11", "Comment11");
            tableMapping.ColumnMappings.Add("Comment12", "Comment12");
            tableMapping.ColumnMappings.Add("Comment13", "Comment13");
            tableMapping.ColumnMappings.Add("Comment14", "Comment14");
            tableMapping.ColumnMappings.Add("Comment15", "Comment15");
            tableMapping.ColumnMappings.Add("Comment16", "Comment16");
            tableMapping.ColumnMappings.Add("Comment17", "Comment17");
            tableMapping.ColumnMappings.Add("Comment18", "Comment18");
            tableMapping.ColumnMappings.Add("Comment19", "Comment19");
            tableMapping.ColumnMappings.Add("Comment20", "Comment20");
            tableMapping.ColumnMappings.Add("Comment21", "Comment21");
            tableMapping.ColumnMappings.Add("Comment22", "Comment22");
            tableMapping.ColumnMappings.Add("Comment23", "Comment23");
            tableMapping.ColumnMappings.Add("Comment24", "Comment24");
            tableMapping.ColumnMappings.Add("Comment25", "Comment25");
            tableMapping.ColumnMappings.Add("Comment26", "Comment26");
            tableMapping.ColumnMappings.Add("Comment27", "Comment27");
            tableMapping.ColumnMappings.Add("Comment28", "Comment28");
            tableMapping.ColumnMappings.Add("Comment29", "Comment29");
            tableMapping.ColumnMappings.Add("Comment30", "Comment30");
            tableMapping.ColumnMappings.Add("Comment31", "Comment31");
            tableMapping.ColumnMappings.Add("Comment32", "Comment32");
            tableMapping.ColumnMappings.Add("Comment33", "Comment33");
            tableMapping.ColumnMappings.Add("Comment34", "Comment34");
            tableMapping.ColumnMappings.Add("Comment35", "Comment35");
            tableMapping.ColumnMappings.Add("Comment36", "Comment36");
            tableMapping.ColumnMappings.Add("Comment37", "Comment37");
            tableMapping.ColumnMappings.Add("Comment38", "Comment38");
            tableMapping.ColumnMappings.Add("Comment39", "Comment39");
            tableMapping.ColumnMappings.Add("Comment40", "Comment40");
            tableMapping.ColumnMappings.Add("Comment41", "Comment41");
            tableMapping.ColumnMappings.Add("Comment42", "Comment42");
            tableMapping.ColumnMappings.Add("Comment43", "Comment43");
            tableMapping.ColumnMappings.Add("Comment44", "Comment44");
            tableMapping.ColumnMappings.Add("Comment45", "Comment45");
            tableMapping.ColumnMappings.Add("Comment46", "Comment46");
            tableMapping.ColumnMappings.Add("Comment47", "Comment47");
            tableMapping.ColumnMappings.Add("Comment48", "Comment48");
            tableMapping.ColumnMappings.Add("Comment49", "Comment49");
            tableMapping.ColumnMappings.Add("Comment50", "Comment50");
            this._adapter.TableMappings.Add(tableMapping);
            this._adapter.DeleteCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.DeleteCommand.Connection = this.Connection;
            this._adapter.DeleteCommand.CommandText = "DELETE FROM [ClientRemarksInformation] WHERE (([MedicalCheckDate] = @Original_Med" +
                "icalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @Original" +
                "_Division))";
            this._adapter.DeleteCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MedicalCheckDate", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MedicalCheckDate", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_ClientID", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "ClientID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.DeleteCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_Division", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Division", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.InsertCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.InsertCommand.Connection = this.Connection;
            this._adapter.InsertCommand.CommandText = @"INSERT INTO [ClientRemarksInformation] ([MedicalCheckDate], [ClientID], [Division], [Comment1], [Comment2], [Comment3], [Comment4], [Comment5], [Comment6], [Comment7], [Comment8], [Comment9], [Comment10], [Comment11], [Comment12], [Comment13], [Comment14], [Comment15], [Comment16], [Comment17], [Comment18], [Comment19], [Comment20], [Comment21], [Comment22], [Comment23], [Comment24], [Comment25], [Comment26], [Comment27], [Comment28], [Comment29], [Comment30], [Comment31], [Comment32], [Comment33], [Comment34], [Comment35], [Comment36], [Comment37], [Comment38], [Comment39], [Comment40], [Comment41], [Comment42], [Comment43], [Comment44], [Comment45], [Comment46], [Comment47], [Comment48], [Comment49], [Comment50]) VALUES (@MedicalCheckDate, @ClientID, @Division, @Comment1, @Comment2, @Comment3, @Comment4, @Comment5, @Comment6, @Comment7, @Comment8, @Comment9, @Comment10, @Comment11, @Comment12, @Comment13, @Comment14, @Comment15, @Comment16, @Comment17, @Comment18, @Comment19, @Comment20, @Comment21, @Comment22, @Comment23, @Comment24, @Comment25, @Comment26, @Comment27, @Comment28, @Comment29, @Comment30, @Comment31, @Comment32, @Comment33, @Comment34, @Comment35, @Comment36, @Comment37, @Comment38, @Comment39, @Comment40, @Comment41, @Comment42, @Comment43, @Comment44, @Comment45, @Comment46, @Comment47, @Comment48, @Comment49, @Comment50)";
            this._adapter.InsertCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MedicalCheckDate", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MedicalCheckDate", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@ClientID", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "ClientID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Division", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Division", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment1", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment1", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment2", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment2", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment3", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment3", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment4", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment4", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment5", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment5", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment6", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment6", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment7", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment7", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment8", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment8", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment9", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment9", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment10", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment10", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment11", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment11", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment12", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment12", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment13", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment13", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment14", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment14", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment15", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment15", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment16", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment16", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment17", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment17", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment18", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment18", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment19", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment19", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment20", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment20", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment21", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment21", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment22", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment22", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment23", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment23", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment24", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment24", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment25", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment25", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment26", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment26", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment27", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment27", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment28", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment28", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment29", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment29", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment30", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment30", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment31", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment31", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment32", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment32", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment33", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment33", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment34", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment34", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment35", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment35", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment36", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment36", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment37", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment37", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment38", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment38", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment39", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment39", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment40", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment40", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment41", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment41", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment42", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment42", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment43", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment43", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment44", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment44", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment45", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment45", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment46", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment46", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment47", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment47", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment48", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment48", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment49", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment49", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.InsertCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment50", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment50", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand = new global::System.Data.SqlClient.SqlCommand();
            this._adapter.UpdateCommand.Connection = this.Connection;
            this._adapter.UpdateCommand.CommandText = "UPDATE [ClientRemarksInformation] SET [MedicalCheckDate] = @MedicalCheckDate, [Cl" +
                "ientID] = @ClientID, [Division] = @Division, [Comment1] = @Comment1, [Comment2] " +
                "= @Comment2, [Comment3] = @Comment3, [Comment4] = @Comment4, [Comment5] = @Comme" +
                "nt5, [Comment6] = @Comment6, [Comment7] = @Comment7, [Comment8] = @Comment8, [Co" +
                "mment9] = @Comment9, [Comment10] = @Comment10, [Comment11] = @Comment11, [Commen" +
                "t12] = @Comment12, [Comment13] = @Comment13, [Comment14] = @Comment14, [Comment1" +
                "5] = @Comment15, [Comment16] = @Comment16, [Comment17] = @Comment17, [Comment18]" +
                " = @Comment18, [Comment19] = @Comment19, [Comment20] = @Comment20, [Comment21] =" +
                " @Comment21, [Comment22] = @Comment22, [Comment23] = @Comment23, [Comment24] = @" +
                "Comment24, [Comment25] = @Comment25, [Comment26] = @Comment26, [Comment27] = @Co" +
                "mment27, [Comment28] = @Comment28, [Comment29] = @Comment29, [Comment30] = @Comm" +
                "ent30, [Comment31] = @Comment31, [Comment32] = @Comment32, [Comment33] = @Commen" +
                "t33, [Comment34] = @Comment34, [Comment35] = @Comment35, [Comment36] = @Comment3" +
                "6, [Comment37] = @Comment37, [Comment38] = @Comment38, [Comment39] = @Comment39," +
                " [Comment40] = @Comment40, [Comment41] = @Comment41, [Comment42] = @Comment42, [" +
                "Comment43] = @Comment43, [Comment44] = @Comment44, [Comment45] = @Comment45, [Co" +
                "mment46] = @Comment46, [Comment47] = @Comment47, [Comment48] = @Comment48, [Comm" +
                "ent49] = @Comment49, [Comment50] = @Comment50 WHERE (([MedicalCheckDate] = @Orig" +
                "inal_MedicalCheckDate) AND ([ClientID] = @Original_ClientID) AND ([Division] = @" +
                "Original_Division))";
            this._adapter.UpdateCommand.CommandType = global::System.Data.CommandType.Text;
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MedicalCheckDate", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MedicalCheckDate", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@ClientID", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "ClientID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Division", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Division", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment1", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment1", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment2", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment2", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment3", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment3", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment4", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment4", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment5", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment5", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment6", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment6", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment7", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment7", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment8", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment8", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment9", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment9", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment10", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment10", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment11", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment11", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment12", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment12", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment13", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment13", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment14", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment14", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment15", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment15", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment16", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment16", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment17", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment17", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment18", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment18", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment19", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment19", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment20", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment20", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment21", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment21", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment22", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment22", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment23", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment23", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment24", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment24", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment25", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment25", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment26", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment26", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment27", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment27", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment28", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment28", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment29", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment29", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment30", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment30", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment31", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment31", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment32", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment32", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment33", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment33", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment34", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment34", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment35", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment35", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment36", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment36", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment37", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment37", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment38", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment38", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment39", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment39", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment40", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment40", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment41", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment41", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment42", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment42", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment43", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment43", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment44", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment44", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment45", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment45", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment46", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment46", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment47", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment47", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment48", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment48", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment49", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment49", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Comment50", global::System.Data.SqlDbType.SmallInt, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Comment50", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_MedicalCheckDate", global::System.Data.SqlDbType.DateTime, 0, global::System.Data.ParameterDirection.Input, 0, 0, "MedicalCheckDate", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_ClientID", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "ClientID", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
            this._adapter.UpdateCommand.Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Original_Division", global::System.Data.SqlDbType.NVarChar, 0, global::System.Data.ParameterDirection.Input, 0, 0, "Division", global::System.Data.DataRowVersion.Original, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitConnection() {
            this._connection = new global::System.Data.SqlClient.SqlConnection();
            this._connection.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["MedicalCheckupConnectionString"].ConnectionString;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        private void InitCommandCollection() {
            this._commandCollection = new global::System.Data.SqlClient.SqlCommand[1];
            this._commandCollection[0] = new global::System.Data.SqlClient.SqlCommand();
            this._commandCollection[0].Connection = this.Connection;
            this._commandCollection[0].CommandText = @"SELECT          MedicalCheckDate, ClientID, Division, Comment1, Comment2, Comment3, 
                      Comment4, Comment5, Comment6, Comment7, Comment8, Comment9, 
                      Comment10, Comment11, Comment12, Comment13, Comment14, Comment15, 
                      Comment16, Comment17, Comment18, Comment19, Comment20, Comment21, 
                      Comment22, Comment23, Comment24, Comment25, Comment26, Comment27, 
                      Comment28, Comment29, Comment30, Comment31, Comment32, Comment33, 
                      Comment34, Comment35, Comment36, Comment37, Comment38, Comment39, 
                      Comment40, Comment41, Comment42, Comment43, Comment44, Comment45, 
                      Comment46, Comment47, Comment48, Comment49, Comment50
FROM            ClientRemarksInformation
WHERE           (MedicalCheckDate = @MedicalCheckDate) AND (ClientID = @ClientID) AND 
                      (Division = @Division)";
            this._commandCollection[0].CommandType = global::System.Data.CommandType.Text;
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@MedicalCheckDate", global::System.Data.SqlDbType.DateTime, 8, global::System.Data.ParameterDirection.Input, 0, 0, "MedicalCheckDate", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@ClientID", global::System.Data.SqlDbType.NVarChar, 10, global::System.Data.ParameterDirection.Input, 0, 0, "ClientID", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
            this._commandCollection[0].Parameters.Add(new global::System.Data.SqlClient.SqlParameter("@Division", global::System.Data.SqlDbType.NVarChar, 3, global::System.Data.ParameterDirection.Input, 0, 0, "Division", global::System.Data.DataRowVersion.Current, false, null, "", "", ""));
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Fill, true)]
        public virtual int Fill(RemarksDataSet.ClientRemarksInformationDataTable dataTable, System.DateTime MedicalCheckDate, string ClientID, string Division) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            this.Adapter.SelectCommand.Parameters[0].Value = ((System.DateTime)(MedicalCheckDate));
            if ((ClientID == null)) {
                throw new global::System.ArgumentNullException("ClientID");
            }
            else {
                this.Adapter.SelectCommand.Parameters[1].Value = ((string)(ClientID));
            }
            if ((Division == null)) {
                throw new global::System.ArgumentNullException("Division");
            }
            else {
                this.Adapter.SelectCommand.Parameters[2].Value = ((string)(Division));
            }
            if ((this.ClearBeforeFill == true)) {
                dataTable.Clear();
            }
            int returnValue = this.Adapter.Fill(dataTable);
            return returnValue;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Select, true)]
        public virtual RemarksDataSet.ClientRemarksInformationDataTable GetData(System.DateTime MedicalCheckDate, string ClientID, string Division) {
            this.Adapter.SelectCommand = this.CommandCollection[0];
            this.Adapter.SelectCommand.Parameters[0].Value = ((System.DateTime)(MedicalCheckDate));
            if ((ClientID == null)) {
                throw new global::System.ArgumentNullException("ClientID");
            }
            else {
                this.Adapter.SelectCommand.Parameters[1].Value = ((string)(ClientID));
            }
            if ((Division == null)) {
                throw new global::System.ArgumentNullException("Division");
            }
            else {
                this.Adapter.SelectCommand.Parameters[2].Value = ((string)(Division));
            }
            RemarksDataSet.ClientRemarksInformationDataTable dataTable = new RemarksDataSet.ClientRemarksInformationDataTable();
            this.Adapter.Fill(dataTable);
            return dataTable;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(RemarksDataSet.ClientRemarksInformationDataTable dataTable) {
            return this.Adapter.Update(dataTable);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(RemarksDataSet dataSet) {
            return this.Adapter.Update(dataSet, "ClientRemarksInformation");
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow dataRow) {
            return this.Adapter.Update(new global::System.Data.DataRow[] {
                        dataRow});
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        public virtual int Update(global::System.Data.DataRow[] dataRows) {
            return this.Adapter.Update(dataRows);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Delete, true)]
        public virtual int Delete(System.DateTime Original_MedicalCheckDate, string Original_ClientID, string Original_Division) {
            this.Adapter.DeleteCommand.Parameters[0].Value = ((System.DateTime)(Original_MedicalCheckDate));
            if ((Original_ClientID == null)) {
                throw new global::System.ArgumentNullException("Original_ClientID");
            }
            else {
                this.Adapter.DeleteCommand.Parameters[1].Value = ((string)(Original_ClientID));
            }
            if ((Original_Division == null)) {
                throw new global::System.ArgumentNullException("Original_Division");
            }
            else {
                this.Adapter.DeleteCommand.Parameters[2].Value = ((string)(Original_Division));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.DeleteCommand.Connection.State;
            if (((this.Adapter.DeleteCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.DeleteCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.DeleteCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.DeleteCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Insert, true)]
        public virtual int Insert(
                    System.DateTime MedicalCheckDate, 
                    string ClientID, 
                    string Division, 
                    global::System.Nullable<short> Comment1, 
                    global::System.Nullable<short> Comment2, 
                    global::System.Nullable<short> Comment3, 
                    global::System.Nullable<short> Comment4, 
                    global::System.Nullable<short> Comment5, 
                    global::System.Nullable<short> Comment6, 
                    global::System.Nullable<short> Comment7, 
                    global::System.Nullable<short> Comment8, 
                    global::System.Nullable<short> Comment9, 
                    global::System.Nullable<short> Comment10, 
                    global::System.Nullable<short> Comment11, 
                    global::System.Nullable<short> Comment12, 
                    global::System.Nullable<short> Comment13, 
                    global::System.Nullable<short> Comment14, 
                    global::System.Nullable<short> Comment15, 
                    global::System.Nullable<short> Comment16, 
                    global::System.Nullable<short> Comment17, 
                    global::System.Nullable<short> Comment18, 
                    global::System.Nullable<short> Comment19, 
                    global::System.Nullable<short> Comment20, 
                    global::System.Nullable<short> Comment21, 
                    global::System.Nullable<short> Comment22, 
                    global::System.Nullable<short> Comment23, 
                    global::System.Nullable<short> Comment24, 
                    global::System.Nullable<short> Comment25, 
                    global::System.Nullable<short> Comment26, 
                    global::System.Nullable<short> Comment27, 
                    global::System.Nullable<short> Comment28, 
                    global::System.Nullable<short> Comment29, 
                    global::System.Nullable<short> Comment30, 
                    global::System.Nullable<short> Comment31, 
                    global::System.Nullable<short> Comment32, 
                    global::System.Nullable<short> Comment33, 
                    global::System.Nullable<short> Comment34, 
                    global::System.Nullable<short> Comment35, 
                    global::System.Nullable<short> Comment36, 
                    global::System.Nullable<short> Comment37, 
                    global::System.Nullable<short> Comment38, 
                    global::System.Nullable<short> Comment39, 
                    global::System.Nullable<short> Comment40, 
                    global::System.Nullable<short> Comment41, 
                    global::System.Nullable<short> Comment42, 
                    global::System.Nullable<short> Comment43, 
                    global::System.Nullable<short> Comment44, 
                    global::System.Nullable<short> Comment45, 
                    global::System.Nullable<short> Comment46, 
                    global::System.Nullable<short> Comment47, 
                    global::System.Nullable<short> Comment48, 
                    global::System.Nullable<short> Comment49, 
                    global::System.Nullable<short> Comment50) {
            this.Adapter.InsertCommand.Parameters[0].Value = ((System.DateTime)(MedicalCheckDate));
            if ((ClientID == null)) {
                throw new global::System.ArgumentNullException("ClientID");
            }
            else {
                this.Adapter.InsertCommand.Parameters[1].Value = ((string)(ClientID));
            }
            if ((Division == null)) {
                throw new global::System.ArgumentNullException("Division");
            }
            else {
                this.Adapter.InsertCommand.Parameters[2].Value = ((string)(Division));
            }
            if ((Comment1.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[3].Value = ((short)(Comment1.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Comment2.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[4].Value = ((short)(Comment2.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((Comment3.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[5].Value = ((short)(Comment3.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            if ((Comment4.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[6].Value = ((short)(Comment4.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[6].Value = global::System.DBNull.Value;
            }
            if ((Comment5.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[7].Value = ((short)(Comment5.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            if ((Comment6.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[8].Value = ((short)(Comment6.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            if ((Comment7.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[9].Value = ((short)(Comment7.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            if ((Comment8.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[10].Value = ((short)(Comment8.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[10].Value = global::System.DBNull.Value;
            }
            if ((Comment9.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[11].Value = ((short)(Comment9.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[11].Value = global::System.DBNull.Value;
            }
            if ((Comment10.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[12].Value = ((short)(Comment10.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[12].Value = global::System.DBNull.Value;
            }
            if ((Comment11.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[13].Value = ((short)(Comment11.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[13].Value = global::System.DBNull.Value;
            }
            if ((Comment12.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[14].Value = ((short)(Comment12.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[14].Value = global::System.DBNull.Value;
            }
            if ((Comment13.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[15].Value = ((short)(Comment13.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[15].Value = global::System.DBNull.Value;
            }
            if ((Comment14.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[16].Value = ((short)(Comment14.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[16].Value = global::System.DBNull.Value;
            }
            if ((Comment15.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[17].Value = ((short)(Comment15.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[17].Value = global::System.DBNull.Value;
            }
            if ((Comment16.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[18].Value = ((short)(Comment16.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[18].Value = global::System.DBNull.Value;
            }
            if ((Comment17.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[19].Value = ((short)(Comment17.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[19].Value = global::System.DBNull.Value;
            }
            if ((Comment18.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[20].Value = ((short)(Comment18.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[20].Value = global::System.DBNull.Value;
            }
            if ((Comment19.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[21].Value = ((short)(Comment19.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[21].Value = global::System.DBNull.Value;
            }
            if ((Comment20.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[22].Value = ((short)(Comment20.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[22].Value = global::System.DBNull.Value;
            }
            if ((Comment21.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[23].Value = ((short)(Comment21.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[23].Value = global::System.DBNull.Value;
            }
            if ((Comment22.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[24].Value = ((short)(Comment22.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[24].Value = global::System.DBNull.Value;
            }
            if ((Comment23.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[25].Value = ((short)(Comment23.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[25].Value = global::System.DBNull.Value;
            }
            if ((Comment24.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[26].Value = ((short)(Comment24.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[26].Value = global::System.DBNull.Value;
            }
            if ((Comment25.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[27].Value = ((short)(Comment25.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[27].Value = global::System.DBNull.Value;
            }
            if ((Comment26.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[28].Value = ((short)(Comment26.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[28].Value = global::System.DBNull.Value;
            }
            if ((Comment27.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[29].Value = ((short)(Comment27.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[29].Value = global::System.DBNull.Value;
            }
            if ((Comment28.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[30].Value = ((short)(Comment28.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[30].Value = global::System.DBNull.Value;
            }
            if ((Comment29.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[31].Value = ((short)(Comment29.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[31].Value = global::System.DBNull.Value;
            }
            if ((Comment30.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[32].Value = ((short)(Comment30.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[32].Value = global::System.DBNull.Value;
            }
            if ((Comment31.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[33].Value = ((short)(Comment31.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[33].Value = global::System.DBNull.Value;
            }
            if ((Comment32.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[34].Value = ((short)(Comment32.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[34].Value = global::System.DBNull.Value;
            }
            if ((Comment33.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[35].Value = ((short)(Comment33.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[35].Value = global::System.DBNull.Value;
            }
            if ((Comment34.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[36].Value = ((short)(Comment34.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[36].Value = global::System.DBNull.Value;
            }
            if ((Comment35.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[37].Value = ((short)(Comment35.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[37].Value = global::System.DBNull.Value;
            }
            if ((Comment36.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[38].Value = ((short)(Comment36.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[38].Value = global::System.DBNull.Value;
            }
            if ((Comment37.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[39].Value = ((short)(Comment37.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[39].Value = global::System.DBNull.Value;
            }
            if ((Comment38.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[40].Value = ((short)(Comment38.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[40].Value = global::System.DBNull.Value;
            }
            if ((Comment39.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[41].Value = ((short)(Comment39.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[41].Value = global::System.DBNull.Value;
            }
            if ((Comment40.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[42].Value = ((short)(Comment40.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[42].Value = global::System.DBNull.Value;
            }
            if ((Comment41.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[43].Value = ((short)(Comment41.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[43].Value = global::System.DBNull.Value;
            }
            if ((Comment42.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[44].Value = ((short)(Comment42.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[44].Value = global::System.DBNull.Value;
            }
            if ((Comment43.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[45].Value = ((short)(Comment43.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[45].Value = global::System.DBNull.Value;
            }
            if ((Comment44.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[46].Value = ((short)(Comment44.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[46].Value = global::System.DBNull.Value;
            }
            if ((Comment45.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[47].Value = ((short)(Comment45.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[47].Value = global::System.DBNull.Value;
            }
            if ((Comment46.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[48].Value = ((short)(Comment46.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[48].Value = global::System.DBNull.Value;
            }
            if ((Comment47.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[49].Value = ((short)(Comment47.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[49].Value = global::System.DBNull.Value;
            }
            if ((Comment48.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[50].Value = ((short)(Comment48.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[50].Value = global::System.DBNull.Value;
            }
            if ((Comment49.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[51].Value = ((short)(Comment49.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[51].Value = global::System.DBNull.Value;
            }
            if ((Comment50.HasValue == true)) {
                this.Adapter.InsertCommand.Parameters[52].Value = ((short)(Comment50.Value));
            }
            else {
                this.Adapter.InsertCommand.Parameters[52].Value = global::System.DBNull.Value;
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.InsertCommand.Connection.State;
            if (((this.Adapter.InsertCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.InsertCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.InsertCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.InsertCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(
                    System.DateTime MedicalCheckDate, 
                    string ClientID, 
                    string Division, 
                    global::System.Nullable<short> Comment1, 
                    global::System.Nullable<short> Comment2, 
                    global::System.Nullable<short> Comment3, 
                    global::System.Nullable<short> Comment4, 
                    global::System.Nullable<short> Comment5, 
                    global::System.Nullable<short> Comment6, 
                    global::System.Nullable<short> Comment7, 
                    global::System.Nullable<short> Comment8, 
                    global::System.Nullable<short> Comment9, 
                    global::System.Nullable<short> Comment10, 
                    global::System.Nullable<short> Comment11, 
                    global::System.Nullable<short> Comment12, 
                    global::System.Nullable<short> Comment13, 
                    global::System.Nullable<short> Comment14, 
                    global::System.Nullable<short> Comment15, 
                    global::System.Nullable<short> Comment16, 
                    global::System.Nullable<short> Comment17, 
                    global::System.Nullable<short> Comment18, 
                    global::System.Nullable<short> Comment19, 
                    global::System.Nullable<short> Comment20, 
                    global::System.Nullable<short> Comment21, 
                    global::System.Nullable<short> Comment22, 
                    global::System.Nullable<short> Comment23, 
                    global::System.Nullable<short> Comment24, 
                    global::System.Nullable<short> Comment25, 
                    global::System.Nullable<short> Comment26, 
                    global::System.Nullable<short> Comment27, 
                    global::System.Nullable<short> Comment28, 
                    global::System.Nullable<short> Comment29, 
                    global::System.Nullable<short> Comment30, 
                    global::System.Nullable<short> Comment31, 
                    global::System.Nullable<short> Comment32, 
                    global::System.Nullable<short> Comment33, 
                    global::System.Nullable<short> Comment34, 
                    global::System.Nullable<short> Comment35, 
                    global::System.Nullable<short> Comment36, 
                    global::System.Nullable<short> Comment37, 
                    global::System.Nullable<short> Comment38, 
                    global::System.Nullable<short> Comment39, 
                    global::System.Nullable<short> Comment40, 
                    global::System.Nullable<short> Comment41, 
                    global::System.Nullable<short> Comment42, 
                    global::System.Nullable<short> Comment43, 
                    global::System.Nullable<short> Comment44, 
                    global::System.Nullable<short> Comment45, 
                    global::System.Nullable<short> Comment46, 
                    global::System.Nullable<short> Comment47, 
                    global::System.Nullable<short> Comment48, 
                    global::System.Nullable<short> Comment49, 
                    global::System.Nullable<short> Comment50, 
                    System.DateTime Original_MedicalCheckDate, 
                    string Original_ClientID, 
                    string Original_Division) {
            this.Adapter.UpdateCommand.Parameters[0].Value = ((System.DateTime)(MedicalCheckDate));
            if ((ClientID == null)) {
                throw new global::System.ArgumentNullException("ClientID");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[1].Value = ((string)(ClientID));
            }
            if ((Division == null)) {
                throw new global::System.ArgumentNullException("Division");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[2].Value = ((string)(Division));
            }
            if ((Comment1.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[3].Value = ((short)(Comment1.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[3].Value = global::System.DBNull.Value;
            }
            if ((Comment2.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[4].Value = ((short)(Comment2.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[4].Value = global::System.DBNull.Value;
            }
            if ((Comment3.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[5].Value = ((short)(Comment3.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[5].Value = global::System.DBNull.Value;
            }
            if ((Comment4.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[6].Value = ((short)(Comment4.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[6].Value = global::System.DBNull.Value;
            }
            if ((Comment5.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[7].Value = ((short)(Comment5.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[7].Value = global::System.DBNull.Value;
            }
            if ((Comment6.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[8].Value = ((short)(Comment6.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[8].Value = global::System.DBNull.Value;
            }
            if ((Comment7.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[9].Value = ((short)(Comment7.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[9].Value = global::System.DBNull.Value;
            }
            if ((Comment8.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[10].Value = ((short)(Comment8.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[10].Value = global::System.DBNull.Value;
            }
            if ((Comment9.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[11].Value = ((short)(Comment9.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[11].Value = global::System.DBNull.Value;
            }
            if ((Comment10.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[12].Value = ((short)(Comment10.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[12].Value = global::System.DBNull.Value;
            }
            if ((Comment11.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[13].Value = ((short)(Comment11.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[13].Value = global::System.DBNull.Value;
            }
            if ((Comment12.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[14].Value = ((short)(Comment12.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[14].Value = global::System.DBNull.Value;
            }
            if ((Comment13.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[15].Value = ((short)(Comment13.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[15].Value = global::System.DBNull.Value;
            }
            if ((Comment14.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[16].Value = ((short)(Comment14.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[16].Value = global::System.DBNull.Value;
            }
            if ((Comment15.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[17].Value = ((short)(Comment15.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[17].Value = global::System.DBNull.Value;
            }
            if ((Comment16.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[18].Value = ((short)(Comment16.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[18].Value = global::System.DBNull.Value;
            }
            if ((Comment17.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[19].Value = ((short)(Comment17.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[19].Value = global::System.DBNull.Value;
            }
            if ((Comment18.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[20].Value = ((short)(Comment18.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[20].Value = global::System.DBNull.Value;
            }
            if ((Comment19.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[21].Value = ((short)(Comment19.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[21].Value = global::System.DBNull.Value;
            }
            if ((Comment20.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[22].Value = ((short)(Comment20.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[22].Value = global::System.DBNull.Value;
            }
            if ((Comment21.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[23].Value = ((short)(Comment21.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[23].Value = global::System.DBNull.Value;
            }
            if ((Comment22.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[24].Value = ((short)(Comment22.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[24].Value = global::System.DBNull.Value;
            }
            if ((Comment23.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[25].Value = ((short)(Comment23.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[25].Value = global::System.DBNull.Value;
            }
            if ((Comment24.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[26].Value = ((short)(Comment24.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[26].Value = global::System.DBNull.Value;
            }
            if ((Comment25.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[27].Value = ((short)(Comment25.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[27].Value = global::System.DBNull.Value;
            }
            if ((Comment26.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[28].Value = ((short)(Comment26.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[28].Value = global::System.DBNull.Value;
            }
            if ((Comment27.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[29].Value = ((short)(Comment27.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[29].Value = global::System.DBNull.Value;
            }
            if ((Comment28.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[30].Value = ((short)(Comment28.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[30].Value = global::System.DBNull.Value;
            }
            if ((Comment29.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[31].Value = ((short)(Comment29.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[31].Value = global::System.DBNull.Value;
            }
            if ((Comment30.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[32].Value = ((short)(Comment30.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[32].Value = global::System.DBNull.Value;
            }
            if ((Comment31.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[33].Value = ((short)(Comment31.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[33].Value = global::System.DBNull.Value;
            }
            if ((Comment32.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[34].Value = ((short)(Comment32.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[34].Value = global::System.DBNull.Value;
            }
            if ((Comment33.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[35].Value = ((short)(Comment33.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[35].Value = global::System.DBNull.Value;
            }
            if ((Comment34.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[36].Value = ((short)(Comment34.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[36].Value = global::System.DBNull.Value;
            }
            if ((Comment35.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[37].Value = ((short)(Comment35.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[37].Value = global::System.DBNull.Value;
            }
            if ((Comment36.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[38].Value = ((short)(Comment36.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[38].Value = global::System.DBNull.Value;
            }
            if ((Comment37.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[39].Value = ((short)(Comment37.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[39].Value = global::System.DBNull.Value;
            }
            if ((Comment38.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[40].Value = ((short)(Comment38.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[40].Value = global::System.DBNull.Value;
            }
            if ((Comment39.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[41].Value = ((short)(Comment39.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[41].Value = global::System.DBNull.Value;
            }
            if ((Comment40.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[42].Value = ((short)(Comment40.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[42].Value = global::System.DBNull.Value;
            }
            if ((Comment41.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[43].Value = ((short)(Comment41.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[43].Value = global::System.DBNull.Value;
            }
            if ((Comment42.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[44].Value = ((short)(Comment42.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[44].Value = global::System.DBNull.Value;
            }
            if ((Comment43.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[45].Value = ((short)(Comment43.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[45].Value = global::System.DBNull.Value;
            }
            if ((Comment44.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[46].Value = ((short)(Comment44.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[46].Value = global::System.DBNull.Value;
            }
            if ((Comment45.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[47].Value = ((short)(Comment45.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[47].Value = global::System.DBNull.Value;
            }
            if ((Comment46.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[48].Value = ((short)(Comment46.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[48].Value = global::System.DBNull.Value;
            }
            if ((Comment47.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[49].Value = ((short)(Comment47.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[49].Value = global::System.DBNull.Value;
            }
            if ((Comment48.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[50].Value = ((short)(Comment48.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[50].Value = global::System.DBNull.Value;
            }
            if ((Comment49.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[51].Value = ((short)(Comment49.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[51].Value = global::System.DBNull.Value;
            }
            if ((Comment50.HasValue == true)) {
                this.Adapter.UpdateCommand.Parameters[52].Value = ((short)(Comment50.Value));
            }
            else {
                this.Adapter.UpdateCommand.Parameters[52].Value = global::System.DBNull.Value;
            }
            this.Adapter.UpdateCommand.Parameters[53].Value = ((System.DateTime)(Original_MedicalCheckDate));
            if ((Original_ClientID == null)) {
                throw new global::System.ArgumentNullException("Original_ClientID");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[54].Value = ((string)(Original_ClientID));
            }
            if ((Original_Division == null)) {
                throw new global::System.ArgumentNullException("Original_Division");
            }
            else {
                this.Adapter.UpdateCommand.Parameters[55].Value = ((string)(Original_Division));
            }
            global::System.Data.ConnectionState previousConnectionState = this.Adapter.UpdateCommand.Connection.State;
            if (((this.Adapter.UpdateCommand.Connection.State & global::System.Data.ConnectionState.Open) 
                        != global::System.Data.ConnectionState.Open)) {
                this.Adapter.UpdateCommand.Connection.Open();
            }
            try {
                int returnValue = this.Adapter.UpdateCommand.ExecuteNonQuery();
                return returnValue;
            }
            finally {
                if ((previousConnectionState == global::System.Data.ConnectionState.Closed)) {
                    this.Adapter.UpdateCommand.Connection.Close();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "17.0.0.0")]
        [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")]
        [global::System.ComponentModel.DataObjectMethodAttribute(global::System.ComponentModel.DataObjectMethodType.Update, true)]
        public virtual int Update(
                    global::System.Nullable<short> Comment1, 
                    global::System.Nullable<short> Comment2, 
                    global::System.Nullable<short> Comment3, 
                    global::System.Nullable<short> Comment4, 
                    global::System.Nullable<short> Comment5, 
                    global::System.Nullable<short> Comment6, 
                    global::System.Nullable<short> Comment7, 
                    global::System.Nullable<short> Comment8, 
                    global::System.Nullable<short> Comment9, 
                    global::System.Nullable<short> Comment10, 
                    global::System.Nullable<short> Comment11, 
                    global::System.Nullable<short> Comment12, 
                    global::System.Nullable<short> Comment13, 
                    global::System.Nullable<short> Comment14, 
                    global::System.Nullable<short> Comment15, 
                    global::System.Nullable<short> Comment16, 
                    global::System.Nullable<short> Comment17, 
                    global::System.Nullable<short> Comment18, 
                    global::System.Nullable<short> Comment19, 
                    global::System.Nullable<short> Comment20, 
                    global::System.Nullable<short> Comment21, 
                    global::System.Nullable<short> Comment22, 
                    global::System.Nullable<short> Comment23, 
                    global::System.Nullable<short> Comment24, 
                    global::System.Nullable<short> Comment25, 
                    global::System.Nullable<short> Comment26, 
                    global::System.Nullable<short> Comment27, 
                    global::System.Nullable<short> Comment28, 
                    global::System.Nullable<short> Comment29, 
                    global::System.Nullable<short> Comment30, 
                    global::System.Nullable<short> Comment31, 
                    global::System.Nullable<short> Comment32, 
                    global::System.Nullable<short> Comment33, 
                    global::System.Nullable<short> Comment34, 
                    global::System.Nullable<short> Comment35, 
                    global::System.Nullable<short> Comment36, 
                    global::System.Nullable<short> Comment37, 
                    global::System.Nullable<short> Comment38, 
                    global::System.Nullable<short> Comment39, 
                    global::System.Nullable<short> Comment40, 
                    global::System.Nullable<short> Comment41, 
                    global::System.Nullable<short> Comment42, 
                    global::System.Nullable<short> Comment43, 
                    global::System.Nullable<short> Comment44, 
                    global::System.Nullable<short> Comment45, 
                    global::System.Nullable<short> Comment46, 
                    global::System.Nullable<short> Comment47, 
                    global::System.Nullable<short> Comment48, 
                    global::System.Nullable<short> Comment49, 
                    global::System.Nullable<short> Comment50, 
                    System.DateTime Original_MedicalCheckDate, 
                    string Original_ClientID, 
                    string Original_Division) {
            return this.Update(Original_MedicalCheckDate, Original_ClientID, Original_Division, Comment1, Comment2, Comment3, Comment4, Comment5, Comment6, Comment7, Comment8, Comment9, Comment10, Comment11, Comment12, Comment13, Comment14, Comment15, Comment16, Comment17, Comment18, Comment19, Comment20, Comment21, Comment22, Comment23, Comment24, Comment25, Comment26, Comment27, Comment28, Comment29, Comment30, Comment31, Comment32, Comment33, Comment34, Comment35, Comment36, Comment37, Comment38, Comment39, Comment40, Comment41, Comment42, Comment43, Comment44, Comment45, Comment46, Comment47, Comment48, Comment49, Comment50, Original_MedicalCheckDate, Original_ClientID, Original_Division);
        }
    }
}

#pragma warning restore 1591