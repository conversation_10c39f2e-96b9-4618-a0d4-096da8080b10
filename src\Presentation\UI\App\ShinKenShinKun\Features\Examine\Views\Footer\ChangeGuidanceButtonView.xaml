<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    Style="{x:Static ui:Styles.DarkBlueWidth150ButtonBorderStyle}"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ChangeGuidanceButtonView"
    x:Name="this">
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Aspect="Fill"
            Source="{x:Static ui:Icons.ChangeGuideIcon}"
            Style="{x:Static ui:Styles.ImageFooterXlSizeButtonStyle}" />
        <Label
            Style="{x:Static ui:Styles.ChangeGuidanceLabelStyle}" />
    </VerticalStackLayout>
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding ChangeGuidanceCommand, Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
    <telerik:RadBorder.Triggers>
        <DataTrigger
            TargetType="telerik:RadBorder"
            Binding="{Binding ClientSelected, Converter={x:Static ui:AppConverters.ObjectToBoolConverter}, Source={x:Reference this}}"
            Value="False">
            <Setter
                Property="Opacity"
                Value="0.7" />
        </DataTrigger>
    </telerik:RadBorder.Triggers>
</telerik:RadBorder>
