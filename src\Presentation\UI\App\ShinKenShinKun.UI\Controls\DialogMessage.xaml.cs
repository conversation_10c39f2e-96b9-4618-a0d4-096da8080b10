namespace ShinKenShinKun.UI;

public partial class DialogMessage : BasePopup
{
    private readonly DialogMessageViewModel viewModel;
    private Color baseColor;

    public DialogMessage(DialogMessageViewModel viewModel)
    {
        InitializeComponent();
        if(Application.Current?.RequestedTheme == AppTheme.Light)
        {
            // TODO: Issue 4 corners #42986
            //parent.Shadow = new Shadow
            //{
            //    Brush = Colors.Black,
            //    Offset = new Point(0, 0),
            //    Radius = 20,
            //    Opacity = 0.3f
            //};
            parent.BorderThickness = Dimens.RadBorderThickness1;
            parent.BorderColor = AppColors.WhisperGray;
        }
        BindingContext = viewModel;
        this.viewModel = viewModel;
    }

    protected override void OnAppearing(object? sender, PopupOpenedEventArgs e)
    {
        base.OnAppearing(sender, e);
        baseColor = DictionaryHelpers.MessageBaseColorDic[viewModel.DialogMessage.MessageType];
        MainThread.BeginInvokeOnMainThread(() => GenerateActionButton());
    }

    private void GenerateActionButton()
    {
        var layout = GenerateLayoutButtonType(viewModel.DialogMessage.ButtonType);
        for (int column = 0; column < layout.Count; column++)
        {
            View? item = layout[column];
            Action.Add(item, column);
        }
    }

    private List<RadButton> GenerateLayoutButtonType(MessageButtonType buttonType)
    {
        var buttons = DictionaryHelpers.MessageButtonDic[buttonType];
        var actionButton = new List<RadButton>();
        var isSingleButton = buttons.Count == 1;
        foreach (var button in buttons)
        {
            Action.ColumnDefinitions.Add(new ColumnDefinition(GridLength.Star));
            var radButton = new RadButton
            {
                Text = button.Text,
                HeightRequest = Dimens.Height40,
                BackgroundColor = AddButtonColor(button, isSingleButton),
                TextColor = AddTextColor(button, isSingleButton),
                Command = GetCommand(button),
                WidthRequest = isSingleButton ? Dimens.Width220 : -1,
                BorderColor = baseColor,
                CommandParameter = button.Result,
                HorizontalOptions = isSingleButton ? LayoutOptions.End : LayoutOptions.Fill,
            };
            actionButton.Add(radButton);
        }
        return actionButton;
    }

    private ICommand GetCommand(MessageButton button)
    {
        return button.Text switch
        {
            var text when text == AppResources.Ok => new Command(() =>
            {
                viewModel.OKCommand?.Execute(button.Result);
                Close();
            }),
            var text when text == AppResources.Yes => new Command(() =>
            {
                viewModel.YesCommand?.Execute(button.Result);
                Close();
            }),
            var text when text == AppResources.No => new Command(() =>
            {
                viewModel.NoCommand?.Execute(button.Result);
                Close();
            }),
            var text when text == AppResources.Cancel => new Command(() =>
            {
                viewModel.CancelCommand?.Execute(button.Result);
                Close();
            }),
            _ => new Command(() =>
            {
                Close();
            }),
        };
    }

    private Color AddTextColor(MessageButton button, bool isSingleButton)
    {
        if (isSingleButton)
            return AppColors.White;

        if (button.Text == AppResources.Cancel)
            return baseColor;

        return AppColors.White;
    }

    private Color AddButtonColor(MessageButton button, bool isSingleButton)
    {
        if (isSingleButton)
            return baseColor;

        if (button.Text == AppResources.Cancel)
            return AppColors.White;

        return baseColor;
    }
}