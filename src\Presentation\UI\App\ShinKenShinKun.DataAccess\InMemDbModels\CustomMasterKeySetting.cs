﻿namespace ShinKenShinKun.DataAccess;

public class CustomMasterKeySetting
{
    [ForeignKey(nameof(SettingNumberPad))]
    public int SettingNumberPadId { get; set; }

    [ForeignKey(nameof(CustomMasterKey))]
    public int CustomMasterKeyId { get; set; }

    public int RowIndex { get; set; }
    public int ColIndex { get; set; }
    public int RowSpan { get; set; }
    public int ColSpan { get; set; }
    public string BackgroundColor { get; set; }
    public string TextColor { get; set; }
    public bool IsActive { get; set; }

    public SettingNumberPad SettingNumberPad { get; set; }
    public CustomMasterKey CustomMasterKey { get; set; }
}