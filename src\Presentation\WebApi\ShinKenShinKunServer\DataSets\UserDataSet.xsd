﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="UserDataSet" targetNamespace="http://tempuri.org/UserDataSet.xsd" xmlns:mstns="http://tempuri.org/UserDataSet.xsd" xmlns="http://tempuri.org/UserDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="MedicalCheckupConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupConnectionString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="UserTableAdapter" GeneratorDataComponentClassName="UserTableAdapter" Name="User" UserDataComponentName="UserTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase2.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[User] WHERE (([UserId] = @Original_UserId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[User] ([UserId], [UserName], [LoginId], [PasswordHash], [Salt], [RoleId], [IsLocked], [IsValid], [Email], [LoginState], [LoginFailureCount], [LastLoginAt]) VALUES (@UserId, @UserName, @LoginId, @PasswordHash, @Salt, @RoleId, @IsLocked, @IsValid, @Email, @LoginState, @LoginFailureCount, @LastLoginAt)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@UserName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="UserName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LoginId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LoginId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Salt" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Salt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsLocked" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsLocked" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsValid" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsValid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@LoginState" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="LoginState" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@LoginFailureCount" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="LoginFailureCount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@LastLoginAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="LastLoginAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT UserId, UserName, LoginId, PasswordHash, Salt, RoleId, IsLocked, IsValid, Email, LoginState, LoginFailureCount, LastLoginAt FROM dbo.[User]</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[User] SET [UserId] = @UserId, [UserName] = @UserName, [LoginId] = @LoginId, [PasswordHash] = @PasswordHash, [Salt] = @Salt, [RoleId] = @RoleId, [IsLocked] = @IsLocked, [IsValid] = @IsValid, [Email] = @Email, [LoginState] = @LoginState, [LoginFailureCount] = @LoginFailureCount, [LastLoginAt] = @LastLoginAt WHERE (([UserId] = @Original_UserId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@UserId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@UserName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="UserName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LoginId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LoginId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@PasswordHash" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="PasswordHash" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Salt" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Salt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@RoleId" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="RoleId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsLocked" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsLocked" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsValid" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsValid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@LoginState" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="LoginState" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@LoginFailureCount" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="LoginFailureCount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime2" Direction="Input" ParameterName="@LastLoginAt" Precision="0" ProviderType="DateTime2" Scale="0" Size="0" SourceColumn="LastLoginAt" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_UserId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="UserId" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="UserId" DataSetColumn="UserId" />
              <Mapping SourceColumn="UserName" DataSetColumn="UserName" />
              <Mapping SourceColumn="LoginId" DataSetColumn="LoginId" />
              <Mapping SourceColumn="PasswordHash" DataSetColumn="PasswordHash" />
              <Mapping SourceColumn="Salt" DataSetColumn="Salt" />
              <Mapping SourceColumn="RoleId" DataSetColumn="RoleId" />
              <Mapping SourceColumn="IsLocked" DataSetColumn="IsLocked" />
              <Mapping SourceColumn="IsValid" DataSetColumn="IsValid" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="LoginState" DataSetColumn="LoginState" />
              <Mapping SourceColumn="LoginFailureCount" DataSetColumn="LoginFailureCount" />
              <Mapping SourceColumn="LastLoginAt" DataSetColumn="LastLoginAt" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase2.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByLoginId" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByLoginId" GeneratorSourceName="FillByLoginId" GetMethodModifier="Public" GetMethodName="GetDataByLoginId" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByLoginId" UserSourceName="FillByLoginId">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      TOP (1) *
FROM                         [User]
WHERE                       (LoginId = @LoginId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="LoginId" ColumnName="LoginId" DataSourceName="GenkiPlazaDatabase2.dbo.[User]" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@LoginId" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="LoginId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase2.dbo.[User]" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByLoginIdWithEmail" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByLoginIdWithEmail" GeneratorSourceName="FillByLoginIdWithEmail" GetMethodModifier="Public" GetMethodName="GetDataByLoginIdWithEmail" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByLoginIdWithEmail" UserSourceName="FillByLoginIdWithEmail">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT                      TOP (1) *
FROM                         [User]
WHERE                       (LoginId = @LoginId) OR (Email = @Email) </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="LoginId" ColumnName="LoginId" DataSourceName="GenkiPlazaDatabase2.dbo.[User]" DataTypeServer="varchar(50)" DbType="AnsiString" Direction="Input" ParameterName="@LoginId" Precision="0" ProviderType="VarChar" Scale="0" Size="50" SourceColumn="LoginId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Email" ColumnName="Email" DataSourceName="GenkiPlazaDatabase2.dbo.[User]" DataTypeServer="varchar(256)" DbType="AnsiString" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="VarChar" Scale="0" Size="256" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="UserDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="UserDataSet" msprop:Generator_DataSetName="UserDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="User" msprop:Generator_RowEvHandlerName="UserRowChangeEventHandler" msprop:Generator_RowDeletedName="UserRowDeleted" msprop:Generator_RowDeletingName="UserRowDeleting" msprop:Generator_RowEvArgName="UserRowChangeEvent" msprop:Generator_TablePropName="User" msprop:Generator_RowChangedName="UserRowChanged" msprop:Generator_UserTableName="User" msprop:Generator_RowChangingName="UserRowChanging" msprop:Generator_RowClassName="UserRow" msprop:Generator_TableClassName="UserDataTable" msprop:Generator_TableVarName="tableUser">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UserId" msprop:Generator_ColumnPropNameInTable="UserIdColumn" msprop:Generator_ColumnPropNameInRow="UserId" msprop:Generator_UserColumnName="UserId" msprop:Generator_ColumnVarNameInTable="columnUserId">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="36" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="UserName" msprop:Generator_ColumnPropNameInTable="UserNameColumn" msprop:Generator_ColumnPropNameInRow="UserName" msprop:Generator_UserColumnName="UserName" msprop:Generator_ColumnVarNameInTable="columnUserName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LoginId" msprop:Generator_ColumnPropNameInTable="LoginIdColumn" msprop:Generator_ColumnPropNameInRow="LoginId" msprop:Generator_UserColumnName="LoginId" msprop:Generator_ColumnVarNameInTable="columnLoginId">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PasswordHash" msprop:Generator_ColumnPropNameInTable="PasswordHashColumn" msprop:Generator_ColumnPropNameInRow="PasswordHash" msprop:Generator_UserColumnName="PasswordHash" msprop:Generator_ColumnVarNameInTable="columnPasswordHash">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="256" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Salt" msprop:Generator_ColumnPropNameInTable="SaltColumn" msprop:Generator_ColumnPropNameInRow="Salt" msprop:Generator_UserColumnName="Salt" msprop:Generator_ColumnVarNameInTable="columnSalt">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="128" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="RoleId" msprop:Generator_ColumnPropNameInTable="RoleIdColumn" msprop:Generator_ColumnPropNameInRow="RoleId" msprop:Generator_UserColumnName="RoleId" msprop:Generator_ColumnVarNameInTable="columnRoleId" type="xs:unsignedByte" />
              <xs:element name="IsLocked" msprop:Generator_ColumnPropNameInTable="IsLockedColumn" msprop:Generator_ColumnPropNameInRow="IsLocked" msprop:Generator_UserColumnName="IsLocked" msprop:Generator_ColumnVarNameInTable="columnIsLocked" type="xs:boolean" />
              <xs:element name="IsValid" msprop:Generator_ColumnPropNameInTable="IsValidColumn" msprop:Generator_ColumnPropNameInRow="IsValid" msprop:Generator_UserColumnName="IsValid" msprop:Generator_ColumnVarNameInTable="columnIsValid" type="xs:boolean" />
              <xs:element name="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_UserColumnName="Email" msprop:Generator_ColumnVarNameInTable="columnEmail">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="256" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LoginState" msprop:Generator_ColumnPropNameInTable="LoginStateColumn" msprop:Generator_ColumnPropNameInRow="LoginState" msprop:Generator_UserColumnName="LoginState" msprop:Generator_ColumnVarNameInTable="columnLoginState" type="xs:unsignedByte" minOccurs="0" />
              <xs:element name="LoginFailureCount" msprop:Generator_ColumnPropNameInTable="LoginFailureCountColumn" msprop:Generator_ColumnPropNameInRow="LoginFailureCount" msprop:Generator_UserColumnName="LoginFailureCount" msprop:Generator_ColumnVarNameInTable="columnLoginFailureCount" type="xs:unsignedByte" />
              <xs:element name="LastLoginAt" msprop:Generator_ColumnPropNameInTable="LastLoginAtColumn" msprop:Generator_ColumnPropNameInRow="LastLoginAt" msprop:Generator_UserColumnName="LastLoginAt" msprop:Generator_ColumnVarNameInTable="columnLastLoginAt" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:User" />
      <xs:field xpath="mstns:UserId" />
    </xs:unique>
  </xs:element>
</xs:schema>