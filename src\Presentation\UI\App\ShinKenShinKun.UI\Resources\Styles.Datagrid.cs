﻿using Telerik.Maui.Controls.DataGrid;

namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ExaminationTableStyle => CreateStyle<DataGridBorderAppearance>()
        .Set(DataGridBorderAppearance.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(DataGridBorderAppearance.BackgroundColorProperty, AppColors.SteelBlue)
        .Set(DataGridBorderAppearance.SearchMatchBackgroundColorProperty, AppColors.SteelBlue);

    public static Style ExaminationMouseHoverStyle => CreateStyle<DataGridMouseHoverAppearance>()
        .Set(DataGridBorderAppearance.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(DataGridBorderAppearance.BackgroundColorProperty, AppColors.Transparent)
        .Set(DataGridBorderAppearance.SearchMatchBackgroundColorProperty, AppColors.Transparent);

    public static Style HeaderExaminationFrameStyle => CreateStyle<DataGridColumnHeaderAppearance>()
        .Set(DataGridColumnHeaderAppearance.BorderThicknessProperty, Dimens.RadBorderThickness1)
        .Set(DataGridColumnHeaderAppearance.BorderColorProperty, AppColors.LightSkyBlue)
        .Set(DataGridColumnHeaderAppearance.TextVerticalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.TextHorizontalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.SortIndicatorHorizontalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.HoverBackgroundColorProperty, AppColors.DarkLightBlueGray)
        .Set(DataGridColumnHeaderAppearance.BackgroundColorProperty, AppColors.DarkLightBlueGray);

    public static Style MultiSelectTableStyle => CreateStyle<DataGridColumnHeaderAppearance>()
        .Set(DataGridColumnHeaderAppearance.BorderThicknessProperty, Dimens.RadBorderThickness1)
        .Set(DataGridColumnHeaderAppearance.BorderColorProperty, AppColors.LightSkyBlue)
        .Set(DataGridColumnHeaderAppearance.TextVerticalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.TextHorizontalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.SortIndicatorHorizontalOptionsProperty, LayoutOptions.Center)
        .Set(DataGridColumnHeaderAppearance.HoverBackgroundColorProperty, AppColors.DarkLightBlueGray)
        .Set(DataGridColumnHeaderAppearance.TextFontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(DataGridColumnHeaderAppearance.BackgroundColorProperty, AppColors.DarkLightBlueGray);

    public static Style PatientListDataGridStyle => CreateStyle<RadDataGrid>()
        .Set(RadDataGrid.AutoGenerateColumnsProperty, false)
        .Set(RadDataGrid.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(RadDataGrid.CanUserExpandMultipleRowDetailsProperty, false)
        .Set(RadDataGrid.CanUserReorderColumnsProperty, false)
        .Set(RadDataGrid.CanUserResizeColumnsProperty, false)
        .Set(RadDataGrid.GridLinesVisibilityProperty, GridLinesVisibility.Both)
        .Set(RadDataGrid.GridLinesColorProperty, AppColors.DarkLightBlueGray)
        .Set(RadDataGrid.GridLinesThicknessProperty, Dimens.Thickness2)
        .Set(RadDataGrid.UserEditModeProperty, DataGridUserEditMode.None)
        .Set(RadDataGrid.UserFilterModeProperty, DataGridUserFilterMode.Disabled)
        .Set(RadDataGrid.UserGroupModeProperty, DataGridUserGroupMode.Disabled)
        .Set(RadDataGrid.UserSortModeProperty, DataGridUserSortMode.None)
        .Set(RadDataGrid.SelectionUnitProperty, DataGridSelectionUnit.Row)
        .Set(RadDataGrid.RowHeightProperty, Dimens.Height42)
        .Set(RadDataGrid.CurrentCellStyleProperty, ExaminationTableStyle)
        .Set(RadDataGrid.MouseHoverStyleProperty, ExaminationMouseHoverStyle)
        .Set(RadDataGrid.SelectionStyleProperty, ExaminationTableStyle);

    public static Style PatientPendingListDataGridStyle =>
        CreateStyle<RadDataGrid>()
            .Set(RadDataGrid.AutoGenerateColumnsProperty, false)
            .Set(RadDataGrid.BorderThicknessProperty, Dimens.Thickness0)
            .Set(VisualElement.BackgroundColorProperty, AppColors.White)
            .Set(RadDataGrid.CanUserExpandMultipleRowDetailsProperty, false)
            .Set(RadDataGrid.CanUserReorderColumnsProperty, false)
            .Set(RadDataGrid.CanUserResizeColumnsProperty, false)
            .Set(RadDataGrid.GridLinesVisibilityProperty, GridLinesVisibility.Both)
            .Set(RadDataGrid.GridLinesColorProperty, AppColors.AzureishWhiteBlue)
            .Set(RadDataGrid.GridLinesThicknessProperty, Dimens.Thickness2)
            .Set(RadDataGrid.UserEditModeProperty, DataGridUserEditMode.None)
            .Set(RadDataGrid.UserFilterModeProperty, DataGridUserFilterMode.Disabled)
            .Set(RadDataGrid.UserGroupModeProperty, DataGridUserGroupMode.Disabled)
            .Set(RadDataGrid.UserSortModeProperty, DataGridUserSortMode.None)
            .Set(RadDataGrid.SelectionUnitProperty, DataGridSelectionUnit.Row)
            .Set(RadDataGrid.MouseHoverStyleProperty, ExaminationMouseHoverStyle)
            .Set(RadDataGrid.SelectionStyleProperty, PatientPendingCellStyle)
            .Set(RadDataGrid.CurrentCellStyleProperty, PatientPendingCellStyle)
            .Set(RadDataGrid.RowHeightProperty, 53);

    public static Style PatientPendingCellStyle =>
        CreateStyle<DataGridBorderAppearance>()
            .Set(DataGridBorderAppearance.BorderThicknessProperty, Dimens.RadBorderThickness0)
            .Set(DataGridBorderAppearance.BackgroundColorProperty, AppColors.Transparent)
            .Set(
                DataGridBorderAppearance.SearchMatchBackgroundColorProperty,
                AppColors.Transparent
            );

    public static Style PatientPendingListDataGridHeaderStyle =>
        CreateStyle<DataGridColumnHeaderAppearance>()
            .Set(DataGridColumnHeaderAppearance.BorderThicknessProperty, Dimens.Thickness0)
            .Set(DataGridColumnHeaderAppearance.BorderColorProperty, AppColors.Transparent)
            .Set(DataGridColumnHeaderAppearance.TextVerticalOptionsProperty, LayoutOptions.Center)
            .Set(DataGridColumnHeaderAppearance.TextHorizontalOptionsProperty, LayoutOptions.Center)
            .Set(
                DataGridColumnHeaderAppearance.SortIndicatorHorizontalOptionsProperty,
                LayoutOptions.Center
            )
            .Set(DataGridColumnHeaderAppearance.HoverBackgroundColorProperty, AppColors.AzureishWhiteBlue)
            .Set(DataGridColumnHeaderAppearance.BackgroundColorProperty, AppColors.AzureishWhiteBlue);
}
