namespace ShinKenShinKun;

public partial class GuidanceSelectionListView : ContentView
{
    public static readonly BindableProperty GuideItemsProperty = BindableProperty.Create(
        nameof(GuideItems),
        typeof(ObservableCollection<GuideItem>),
        typeof(GuidanceSelectionListView));

    public static readonly BindableProperty SelectionChangedCommandProperty = BindableProperty.Create(
        nameof(SelectionChangedCommand),
        typeof(IRelayCommand),
        typeof(GuidanceSelectionListView));
    public GuidanceSelectionListView()
    {
        InitializeComponent();
    }
    public ObservableCollection<GuideItem> GuideItems
    {
        get => (ObservableCollection<GuideItem>)GetValue(GuideItemsProperty);
        set => SetValue(GuideItemsProperty, value);
    }
    public IRelayCommand SelectionChangedCommand
    {
        get => (IRelayCommand)GetValue(SelectionChangedCommandProperty);
        set => SetValue(SelectionChangedCommandProperty, value);
    }
}