namespace ShinKenShinKun;
public partial class SelectionTestStateListView : ContentView
{
    public static readonly BindableProperty MedicalCheckProgressesProperty = BindableProperty.Create(
        nameof(MedicalCheckProgresses),
        typeof(ObservableCollection<MedicalCheckStateModel>),
        typeof(SelectionTestStateListView));

    public static readonly BindableProperty SelectionChangedCommandProperty = BindableProperty.Create(
        nameof(SelectionChangedCommand),
        typeof(IRelayCommand),
        typeof(SelectionTestStateListView));

    public SelectionTestStateListView()
    {
        InitializeComponent();
    }

    public ObservableCollection<MedicalCheckStateModel> MedicalCheckProgresses
    {
        get => (ObservableCollection<MedicalCheckStateModel>)GetValue(MedicalCheckProgressesProperty);
        set => SetValue(MedicalCheckProgressesProperty, value);
    }
    public IRelayCommand SelectionChangedCommand
    {
        get => (IRelayCommand)GetValue(SelectionChangedCommandProperty);
        set => SetValue(SelectionChangedCommandProperty, value);
    }
}