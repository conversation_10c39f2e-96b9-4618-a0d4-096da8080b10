<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.MaskButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    CornerRadius="{x:Static ui:Dimens.RadBorderCornerRadius23}"
    x:Name="this">
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightGray}"
        HeightRequest="{x:Static ui:Dimens.Height46}"
        Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing4}}"
        ColumnDefinitions="Auto,Auto">
        <Label
            Style="{x:Static ui:Styles.LabelMaskedStyle}"
            Grid.Column="0" />
        <telerik:RadBorder
            HeightRequest="{x:Static ui:Dimens.Height38}"
            WidthRequest="{x:Static ui:Dimens.Width90}"
            CornerRadius="{x:Static ui:Dimens.RadBorderCornerRadius18}"
            Grid.Column="1">
            <telerik:RadBorder.GestureRecognizers>
                <TapGestureRecognizer
                    Command="{Binding ChangeModeCommand, Source={x:Reference this}}" />
            </telerik:RadBorder.GestureRecognizers>
            <Grid>
                <telerik:RadBorder>
                    <Grid
                        ColumnDefinitions="*,38"
                        BackgroundColor="{x:Static ui:AppColors.BrightBlue}">
                        <Label
                            Grid.Column="0"
                            Style="{x:Static ui:Styles.MaskOnSwitchModeLabel}"
                            Text="{x:Static resources:AppResources.On}" />
                        <Border
                            Grid.Column="1"
                            Style="{x:Static ui:Styles.MaskOnSwitchModeBorder}" />
                    </Grid>
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            TargetType="telerik:RadBorder"
                            Binding="{Binding IsMaskMode, Source={x:Reference this}}"
                            Value="False">
                            <Setter
                                Property="IsVisible"
                                Value="False" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                </telerik:RadBorder>
                <telerik:RadBorder>
                    <Grid
                        ColumnDefinitions="38,*"
                        BackgroundColor="{x:Static ui:AppColors.White}">
                        <Label
                            Grid.Column="1"
                            Style="{x:Static ui:Styles.MaskOffSwitchModeLabel}"
                            Text="{x:Static resources:AppResources.Off}" />
                        <Border
                            Grid.Column="0"
                            Style="{x:Static ui:Styles.MaskOffSwitchModeBorder}"/>
                    </Grid>
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            TargetType="telerik:RadBorder"
                            Binding="{Binding IsMaskMode, Source={x:Reference this}}"
                            Value="True">
                            <Setter
                                Property="IsVisible"
                                Value="False" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                </telerik:RadBorder>
            </Grid>
        </telerik:RadBorder>
    </Grid>
</telerik:RadBorder>
