namespace ShinKenShinKun;
public partial class IndividualPatientLabelView : ContentView
{
    public IndividualPatientLabelView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty LabelProperty = BindableProperty.Create(
    nameof(Label),
    typeof(string),
    typeof(IndividualPatientLabelView),
    string.Empty,
    BindingMode.TwoWay);

    public string Label
    {
        get => (string)GetValue(LabelProperty);
        set => SetValue(LabelProperty, value);
    }

    public static readonly BindableProperty ColorProperty = BindableProperty.Create(
    nameof(Color),
    typeof(Color),
    typeof(IndividualPatientLabelView),
    default(Color),
    BindingMode.TwoWay);

    public Color Color
    {
        get => (Color)GetValue(ColorProperty);
        set => SetValue(ColorProperty, value);
    }

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
    nameof(Value),
    typeof(string),
    typeof(IndividualPatientLabelView),
    "",
    BindingMode.TwoWay);

    public string Value
    {
        get => (string)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }
}