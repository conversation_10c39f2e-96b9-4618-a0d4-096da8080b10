﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// EyeInitialSelectService の概要の説明です
    /// </summary>
    public class EyeInitialSelectService
    {
        private DispSettingService service;
        private Logger logger = null;
        public EyeInitialSelectService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }

        #region EyeInitialSelectテーブル全取得
        /// <summary>
        /// EyeInitialSelectテーブル全取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] GetEyeInitialSelectAll()
        {
            try
            {
                DispSettingDataSet.EyeInitialSelectDataTable tmpTable =
                    service.GetEyeInitialSelectAll().EyeInitialSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeInitialSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeInitialSelectテーブル取得（InitialIDがKey）
        /// <summary>
        /// EyeInitialSelectテーブル取得（InitialIDがKey）
        /// </summary>
        /// <param name="initialId"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeInitialSelectId(string initialId)
        {
            try
            {
                short initialIdSht = Convert.ToInt16(initialId);
                DispSettingDataSet.EyeInitialSelectDataTable tmpTable =
                    service.GetEyeInitialSelectId(initialIdSht).EyeInitialSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeInitialSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeInitialSelectテーブル取得（formIdがKey）
        /// <summary>
        /// EyeInitialSelectテーブル取得（formIdがKey）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeInitialSelectMedicalCheckNo(string formId)
        {
            try
            {
                short formIdSht = Convert.ToInt16(formId);
                DispSettingDataSet.EyeInitialSelectDataTable tmpTable =
                    service.GetEyeInitialSelectMedicalNo(formIdSht).EyeInitialSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeInitialSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region PluralRecord設定
        /// <summary>
        /// PluralRecord設定
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        private PluralRecord SetPluralRecord(DispSettingDataSet.EyeInitialSelectRow row)
        {
            List<string> rList = new List<string>();
            // InitialID設定
            rList.Add(row.InitialId.ToString());
            // MedicalCheckNo設定
            rList.Add(row.MedicalCheckNo.ToString());
            // ItemNo設定
            rList.Add(row.ItemNo.ToString());
            // Priority設定
            rList.Add(row.Priority.ToString());
            // FormID設定
            rList.Add(row.FormId.ToString());
            PluralRecord pRec = new PluralRecord(rList.ToArray());
            return pRec;
        }
        #endregion
    }
}