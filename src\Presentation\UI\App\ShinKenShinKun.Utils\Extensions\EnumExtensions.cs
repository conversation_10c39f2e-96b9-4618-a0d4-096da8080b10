﻿namespace ShinKenShinKun.Utils;

public static class EnumExtensions
{
    public static bool Contains<T>(this T @enum, params T[] list) where T : struct, IConvertible, IFormattable
    {
        CheckIsEnum<T>();

        return @enum.Contains(list ?? Enumerable.Empty<T>());
    }

    public static bool Contains<T>(this T @enum, IEnumerable<T> list) where T : struct, IConvertible, IFormattable
    {
        if(list == null) {
            return false;
        }

        CheckIsEnum<T>();

        var type = @enum.GetType();

        if(!(@enum is Enum enumValue)) {
            return false;
        }

        var isFlags = type.GetTypeInfo().GetCustomAttribute<FlagsAttribute>() != null;

        foreach(var item in list) {
            if(!(item is Enum enumItem)) {
                continue;
            }
            if(@enum.Equals(item) || isFlags && enumValue.HasFlag(enumItem)) {
                return true;
            }
        }
        return false;
    }

    static void CheckIsEnum(Type type)
    {
        if(!type.GetTypeInfo().IsEnum) {
            throw new ArgumentException($"Argument {type.FullName} is not an Enum");
        }
    }

    static void CheckIsEnum<T>()
    {
        CheckIsEnum(typeof(T));
    }
}
