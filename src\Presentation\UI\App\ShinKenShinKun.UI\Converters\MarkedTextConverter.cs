﻿namespace ShinKenShinKun.UI;

public class MarkedTextConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values.Length == 0) return string.Empty;
        if (values[0] is bool isItemSelected && !isItemSelected)
        {
            return string.Empty;
        }
        else if (values[1] is bool isMaskMode && isMaskMode)
        {
            return AppResources.MaskText;
        }

        return values[2];
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
