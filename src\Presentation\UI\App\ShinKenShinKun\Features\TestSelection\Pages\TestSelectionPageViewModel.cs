﻿namespace ShinKenShinKun;

public partial class TestSelectionPageViewModel(
    ISessionServices sessionServices,
    IMedicalCheckItemServices medicalCheckItemServices,
    IAppNavigator appNavigator) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperty

    [ObservableProperty]
    private ObservableCollection<MedicalChecklist> medicalChecklists;

    [ObservableProperty] private string userName;

    [ObservableProperty] private bool isMaskMode;
    internal Action hiddenEntryFocus;

    #endregion ObservableProperty

    #region Override Method
    protected override void OnInit(IDictionary<string, object> query)
    {
        var data = query.GetData<MedicalMachinesDataRecord[]>();
        MedicalChecklists = new ObservableCollection<MedicalChecklist>(
            data.Select(machine => new MedicalChecklist { Name = machine.MedicalMachineName, Machine = machine })
        );
    }
    public override Task OnAppearingAsync()
    {
        sessionServices.SeletedMedicalMachine = null;
        UserName = sessionServices.UserName;
        IsMaskMode = sessionServices.IsMaskMode;
        return base.OnAppearingAsync();
    }
    #endregion

    #region RelayCommand
    [RelayCommand]
    public async Task GotoTest(MedicalChecklist medicalCheckName)
    {
        medicalCheckName.IsSelected = true;
        await Task.Delay(100);
        hiddenEntryFocus.Invoke();
        sessionServices.SeletedMedicalMachine = medicalCheckName.Machine;
        await AppNavigator.NavigateAsync(RouterName.ExamineBeforeAuthPage);
        medicalCheckName.IsSelected = false;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }
    #endregion RelayCommand
}