﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Globalization;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MedicalCheckItemStateService の概要の説明です
    /// </summary>
    public class MedicalCheckItemStateService
    {
        private MedicalDataSet MedicalDataSet = new MedicalDataSet();
        private Service srv;
        private Logger logger = null;

        private readonly int ADD_DATA_COUNT = 50;
        private readonly string MEDICAL_CHECK_DATE_COLUMN = "MedicalCheckDate";
        private readonly string CLIENT_ID_COLUMN = "ClientID";
        private readonly string DIVISION_COLUMN = "Division";
        private readonly string MEDICAL_CHECK_NO_COLUMN = "MedicalCheckNo";
        private readonly string START_TIME_COLUMN = "StartDate";
        private readonly string END_TIME_COLUMN = "EndDate";
        private readonly string STATE_COLUMN = "State";
        private readonly string TRANSFERFLAG_COLUMN = "TransferFlag";
        private readonly string CHANGEFLAG_COLUMN = "ChangeFlag";
        private readonly int STATE_COUNT = 50;

        public MedicalCheckItemStateService()
        {
            srv = new Service();
        }

        public PluralRecord[] GetMedicalCheckItemStateClientId(string sMedicalCheckDate, string clientId, string division)
        {
            try
            {
                // 動的配列を用意する。
                ArrayList itemStateArray = new ArrayList();
                DateTime date = DateTime.ParseExact(sMedicalCheckDate.Substring(0, 8), "yyyyMMdd", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                // データセットの関数をコールする
                MedicalDataSet = srv.GetMedicalCheckItemState(date, clientId, division);
                // 取得したレコード数のチェック
                int recCount = MedicalDataSet.MedicalCheckItemState.Rows.Count;
                PluralRecord[] pRecord = new PluralRecord[recCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recCount; j++)
                {
                    DataRow row = MedicalDataSet.MedicalCheckItemState.Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        itemStateArray.Add(row[MEDICAL_CHECK_DATE_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[CLIENT_ID_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[DIVISION_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[MEDICAL_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[START_TIME_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[END_TIME_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[TRANSFERFLAG_COLUMN].ToString().PadLeft(1, ' '));
                        itemStateArray.Add(row[CHANGEFLAG_COLUMN].ToString().PadLeft(1, ' '));
                        for (int i = 1; i <= STATE_COUNT; i++)
                        {
                            itemStateArray.Add(row[STATE_COLUMN + i.ToString()].ToString().PadLeft(1, ' '));
                        }
                    }
                    else
                    {
                    }
                    // ArrayListからstring配列に変換
                    string[] itemList = new string[itemStateArray.Count];
                    Array.Copy(itemStateArray.ToArray(), itemList, itemStateArray.Count);
                    pRecord[j] = new PluralRecord(itemList);
                    itemStateArray.Clear();
                }
                return pRecord;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                throw ex;
            }
        }
    }
}