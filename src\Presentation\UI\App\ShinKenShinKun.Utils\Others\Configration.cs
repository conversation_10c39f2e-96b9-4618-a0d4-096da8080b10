﻿namespace ShinKenShinKun.Utils;

public class Configration
{
    // データセット
    private DataSet data = new DataSet();

    /// <summary>
    /// コンストラクタ
    /// ファイルパスの読み込み中にエラーが発生した場合は
    /// 例外をそのまま上位に投げます。
    /// </summary>
    /// <param name="FilePath">設定ファイルパス</param>
    public Configration(string FilePath)
    {
        // 入力ストリーム
        StreamReader Reader = null;

        try
        {
            // 入力ストリームのオープン
            Reader = new StreamReader(FilePath);
            // Xmlの読み込み
            data.ReadXml(Reader);
        }
        catch (Exception e)
        {
            // 例外はそのまま上位にスローします。
            throw e;
        }
        finally
        {
            if (Reader != null)
            {
                Reader.Close();
                //Reader.Dispose();
            }
            Reader = null;
        }
    }

    /// <summary>
    /// 読み込んだデータをテーブルごと返します。
    /// </summary>
    /// <param name="TableName">テーブル名</param>
    /// <returns>テーブルのデータ</returns>
    public DataTable GetTable(string TableName)
    {
        return data.Tables[TableName];
    }
}