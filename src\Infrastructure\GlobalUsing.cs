﻿global using Microsoft.Extensions.Logging;
global using ShinKenShinKun.Domain.Entities;
global using ShinKenShinKun.Infrastructure.Identity;
global using Microsoft.AspNetCore.Identity;
global using Microsoft.EntityFrameworkCore;
global using Duende.IdentityServer.Models;
global using ShinKenShinKun.Application.Common.Interfaces;
global using ShinKenShinKun.Infrastructure.Files;
global using ShinKenShinKun.Infrastructure.Persistence.Interceptors;
global using ShinKenShinKun.Infrastructure.Services;
global using Microsoft.AspNetCore.Authentication;
global using Microsoft.Extensions.Configuration;
global using ShinKenShinKun.Infrastructure.Persistence;
global using System.Reflection;
global using Duende.IdentityServer.EntityFramework.Options;
global using MediatR;
global using Microsoft.AspNetCore.ApiAuthorization.IdentityServer;
global using Microsoft.Extensions.Options;
global using ShinKenShinKun.Domain.Common;
global using Microsoft.EntityFrameworkCore.ChangeTracking;
global using Microsoft.EntityFrameworkCore.Diagnostics;
global using Microsoft.EntityFrameworkCore.Metadata.Builders;
global using ShinKenShinKun.Application.Common.Models;
global using System.Globalization;
global using ShinKenShinKun.Application.TodoLists.Queries.ExportTodos;
global using CsvHelper.Configuration;
global using ShinKenShinKun.Infrastructure.Files.Maps;
global using CsvHelper;
global using Microsoft.AspNetCore.Authorization;