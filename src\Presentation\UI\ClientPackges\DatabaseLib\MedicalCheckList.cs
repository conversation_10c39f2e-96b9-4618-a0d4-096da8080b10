using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class MedicalCheckList
    {
        private const int MEDICAL_CHECK_NO_POS = 0;
        private const int MEDICAL_CHECK_NAME_POS = 1;
        private const int VIEWORDER_POS = 2;
        private string FldMedicalCheckNo;
        private string FldMedicalCheckName;
        private string FldViewOrder;

        public MedicalCheckList(string[] dbData)
        {
            this.MedicalCheckNo = dbData[MEDICAL_CHECK_NO_POS];
            this.MedicalCheckName = dbData[MEDICAL_CHECK_NAME_POS];
            this.VierOrder = dbData[VIEWORDER_POS];
        }

        public string MedicalCheckNo
        {
            get
            {
                return this.FldMedicalCheckNo;
            }
            set
            {
                this.FldMedicalCheckNo = value;
            }
        }

        public string MedicalCheckName
        {
            get
            {
                return this.FldMedicalCheckName;
            }
            set
            {
                this.FldMedicalCheckName = value;
            }
        }

        public string VierOrder
        {
            get
            {
                return this.FldViewOrder;
            }
            set
            {
                this.FldViewOrder = value;
            }
        }
    }
}
