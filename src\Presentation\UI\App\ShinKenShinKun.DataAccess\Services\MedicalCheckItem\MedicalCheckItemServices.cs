﻿namespace ShinKenShinKun.DataAccess;

public class MedicalCheckItemServices(DataAccessControl dataAccessControl) : IMedicalCheckItemServices
{
    public async Task<MedicalMachinesDataRecord[]> GetMedicalCheckItems()
    {
        var records = await dataAccessControl.GetMachinesDataRecordAllAsync();
        records = records
        .Where(r => r.UseFlag == true)
        .ToArray();
        return records;
    }
}
