<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.UI.MultiLineLabelControl"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <telerik:RadBorder
        Style="{x:Static ui:Styles.BlackThinBorderStyle}">
        <Label
            Text="{Binding Text, Source={x:Reference this}}"
            Style="{x:Static ui:Styles.WrappedTextLabelStyle}" />
    </telerik:RadBorder>
</ContentView>