﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// PluralDispSettingService の概要の説明です

    /// </summary>
    public class PluralDispSettingService
    {
        private DispSettingService service;
        private Logger logger = null;

        public PluralDispSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetPluralDispSettingAll()
        {
            try
            {
                DispSettingDataSet.PluralDispSettingDataTable tmpTable =
                    service.GetPluralDispSettingAll().PluralDispSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.PluralDispSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispCount.ToString());
                        rList.Add(row.SelectCount.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        public PluralRecord[] GetPluralDispSettingNo(string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.PluralDispSettingDataTable tmpTable =
                    service.GetPluralDispSettingNo(medicalCheckNoSht).PluralDispSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.PluralDispSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispCount.ToString());
                        rList.Add(row.SelectCount.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetPluralDispSetting(
            string medicalCheckNo,
            string itemNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                short itemNoSht = Convert.ToInt16(itemNo);
                DispSettingDataSet.PluralDispSettingDataTable tmpTable =
                    service.GetPluralDispSetting(medicalCheckNoSht, itemNoSht).PluralDispSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.PluralDispSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispCount.ToString());
                        rList.Add(row.SelectCount.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}