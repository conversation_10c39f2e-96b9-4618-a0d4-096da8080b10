﻿<?xml version="1.0"?>
<!-- 
    郢晢ｽ｡郢晢ｽ｢: 邵ｺ阮�?�ｽｮ郢晁ｼ斐＜郢ｧ�ｽ､郢晢ｽｫ郢ｧ蜻育�碑恪霈斐帝こ�ｽｨ鬮ｮ?邵ｺ蜷ｶ�ｽ玖脂�ｽ｣郢ｧ荳奇ｽ顔ｸｺ�ｽｫ邵ｲ莉抛b 驍ゑｽ｡騾�?郢�?郢晢ｽｼ郢晢ｽｫ郢ｧ蜑��ｽｽ�ｽｿ騾包ｽｨ 
    邵ｺ蜉ｱ窶ｻ郢ｧ�ｽ｢郢晏干ﾎ懃ｹｧ�ｽｱ郢晢ｽｼ郢ｧ�ｽｷ郢晢ｽｧ郢晢ｽｳ邵ｺ�ｽｮ髫ｪ�ｽｭ陞ｳ螢ｹ�ｽ定ｮ貞玄?闊娯��郢ｧ荵晢ｼ�邵ｺ�ｽｨ邵ｺ蠕後堤ｸｺ髦ｪ竏ｪ邵ｺ蜷ｶﾂ�ｼ擁sual Studio 
    邵ｺ�ｽｮ [Web 郢ｧ�ｽｵ郢ｧ�ｽ､郢�?] 郢晢ｽ｡郢昜ｹ斟礼ｹ晢ｽｼ邵ｺ�ｽｫ邵ｺ繧��ｽ� [ASP.NET 隶貞玄?逍ｹ 郢ｧ�ｽｪ郢晏干縺咏ｹ晢ｽｧ郢晢ｽｳ邵ｺ荵晢ｽ蛾坎�ｽｭ陞ｳ?
    郢ｧ螳夲ｽ｡蠕娯夢邵ｺ�ｽｦ邵ｺ荳岩味邵ｺ霈費ｼ樒ｸｲ繧奇ｽｨ�ｽｭ陞ｳ螢ｹ笙郢ｧ蛹ｻ?�ｽｳ郢ｧ�ｽｳ郢晄ｧｭﾎｦ郢晏ｳｨ?�ｽｮ闕ｳﾂ髫包ｽｧ邵ｺ�ｽｯ邵ｲ?鬨ｾ螢ｼ�ｽｸ�ｽｸ
    \Windows\Microsoft.Net\Framework\v2.x\Config 邵ｺ�ｽｫ邵ｺ繧��ｽ�
    machine.config.comments 邵ｺ�ｽｧ驕抵ｽｺ髫ｱ髦ｪ縲堤ｸｺ髦ｪ竏ｪ邵ｺ蜷ｶﾂ?
-->
<configuration xmlns="http://schemas.microsoft.com/.NetConfiguration/v2.0">
	<appSettings>
		<add key="CheckTime" value="500"/>
		<add key="CheckCount" value="3"/>
		<add key="LoggerPass" value="C:\Arctec\Log"/>
		<add key="LoggerName" value="ServiceLog"/>
		<add key="LimitDay" value="90"/>
		<add key="AllDivision" value=" 1,000,00,A1,1  ,2  "/>
		<add key="Max_State" value="100"/>
		<add key="Max_Data" value="50"/>
	</appSettings>
	<connectionStrings>
		<add name="MedicalCheckupConnectionString2" connectionString="Data Source=***************;Initial Catalog=GenkiPlazaDatabase2;Persist Security Info=True;User ID=sa;Password=*************;TrustServerCertificate=True" providerName="System.Data.SqlClient"/>
		<add name="MedicalCheckupConnectionString" connectionString="Data Source=localhost,1435;Initial Catalog=GenkiPlazaDatabase2;Persist Security Info=True;User ID=sa;Password=StrongP@ssw0rd123;TrustServerCertificate=True" providerName="System.Data.SqlClient"/>
	</connectionStrings>
	<!--
    web.config 邵ｺ�ｽｮ陞溽判蟲ｩ霓､�ｽｹ邵ｺ�ｽｮ髫ｱ�ｽｬ隴丞ｼｱ竊鍋ｸｺ�ｽ､邵ｺ?邵ｺ�ｽｦ邵ｺ�ｽｯ邵ｲ窶掖tp://go.microsoft.com/fwlink/?LinkId=235367 郢ｧ雋樒崟霎｣�ｽｧ邵ｺ蜉ｱ窶ｻ邵ｺ荳岩味邵ｺ霈費ｼ樒ｸｲ?

    隹ｺ�ｽ｡邵ｺ�ｽｮ陞ｻ讓環�ｽｧ郢ｧ? <httpRuntime> 郢ｧ�ｽｿ郢ｧ�ｽｰ邵ｺ�ｽｫ髫ｪ�ｽｭ陞ｳ螢ｹ縲堤ｸｺ髦ｪ竏ｪ邵ｺ蜷ｶﾂ?
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
	<system.web>
		<!-- 
            郢�?郢晁�後Ε郢ｧ�ｽｰ 郢ｧ�ｽｷ郢晢ｽｳ郢晄㈱ﾎ晉ｹｧ蛛ｵ縺慕ｹ晢ｽｳ郢昜ｻ｣縺�郢晢ｽｫ邵ｺ霈費ｽ檎ｸｺ貅�?螢ｹ?�ｽｼ郢ｧ�ｽｸ邵ｺ�ｽｫ隰厄ｽｿ陷茨ｽ･邵ｺ蜷ｶ�ｽ狗ｸｺ�ｽｫ
            邵ｺ�ｽｯ邵ｲ窶ｦompilation debug="true" 邵ｺ�ｽｫ髫ｪ�ｽｭ陞ｳ螢ｹ�ｼ�邵ｺ�ｽｾ邵ｺ蜷ｶﾂ繧��ｼ�邵ｺ�ｽｮ髫ｪ�ｽｭ
            陞ｳ螢ｹ?�ｽｯ郢昜ｻ｣繝ｵ郢ｧ�ｽｩ郢晢ｽｼ郢晄ｧｭﾎｦ郢ｧ�ｽｹ邵ｺ�ｽｫ陟厄ｽｱ鬮ｻ�ｽｿ邵ｺ蜷ｶ�ｽ狗ｸｺ貅假ｽ∫ｸｲ?鬮｢迢怜験隴弱ｅ?�ｽｮ邵ｺ�ｽｿ邵ｺ阮�?�ｽｮ陋滂ｽ､
            郢ｧ? true 邵ｺ�ｽｫ髫ｪ�ｽｭ陞ｳ螢ｹ�ｼ�邵ｺ�ｽｦ邵ｺ荳岩味邵ｺ霈費ｼ樒ｸｲ?
        -->
		<compilation debug="true" targetFramework="4.8">
			<assemblies>
				<add assembly="System.Transactions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.Net.Http, Version=4.2.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
				<add assembly="System.Numerics, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
				<add assembly="System.Security, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
			</assemblies>
		</compilation>
		<!--
            <authentication> 郢ｧ�ｽｻ郢ｧ�ｽｯ郢ｧ�ｽｷ郢晢ｽｧ郢晢ｽｳ邵ｺ�ｽｯ邵ｲ竏墅倡ｹ晢ｽｼ郢ｧ�ｽｶ郢晢ｽｼ郢ｧ螳夲ｽｭ莨懈肩邵ｺ蜷ｶ�ｽ狗ｸｺ貅假ｽ�
            邵ｺ�ｽｫ邵ｲ縲ヾP.NET 邵ｺ�ｽｧ闖ｴ�ｽｿ騾包ｽｨ邵ｺ霈費ｽ檎ｹｧ荵昴◎郢ｧ�ｽｭ郢晢ｽ･郢晢ｽｪ郢�?郢ｧ�ｽ｣髫ｱ蟠趣ｽｨ�ｽｼ郢晢ｽ｢郢晢ｽｼ郢晏ｳｨ?�ｽｮ隶貞玄??
            郢ｧ蜻域剰怏�ｽｹ邵ｺ�ｽｫ邵ｺ蜉ｱ竏ｪ邵ｺ蜷ｶﾂ?
        -->
		<authentication mode="Windows"/>
		<!--
            <customErrors> 郢ｧ�ｽｻ郢ｧ�ｽｯ郢ｧ�ｽｷ郢晢ｽｧ郢晢ｽｳ邵ｺ�ｽｯ邵ｲ竏ｬ�ｽｦ竏ｵ�ｽｱ繧�?�ｽｮ陞ｳ貅ｯ�ｽ｡蠕｡�ｽｸ�ｽｭ邵ｺ�ｽｫ郢昜ｸ莞ｦ郢晏ｳｨﾎ�
            邵ｺ霈費ｽ檎ｸｺ�ｽｦ邵ｺ?邵ｺ�ｽｪ邵ｺ?郢ｧ�ｽｨ郢晢ｽｩ郢晢ｽｼ邵ｺ讙主験騾墓ｺ假ｼ�邵ｺ貅ｷ?�ｽｴ陷ｷ蛹ｻ?�ｽｮ陷��ｽｦ騾�?隴��ｽｹ雎戊ｼ�?�ｽｮ隶貞玄?闊鯉ｽ�
            隴帷甥譟醍ｸｺ�ｽｫ邵ｺ蜉ｱ竏ｪ邵ｺ蜷ｶﾂ繧�?�ｽｷ闖ｴ骰句飭邵ｺ�ｽｫ邵ｺ�ｽｯ邵ｲ?鬮｢迢怜験髢�?邵ｺ? HTML 郢ｧ�ｽｨ郢晢ｽｩ郢晢ｽｼ 郢�?
            郢晢ｽｼ郢ｧ�ｽｸ郢ｧ蛛ｵ縺帷ｹｧ�ｽｿ郢�?郢ｧ�ｽｯ 郢晏現ﾎ樒ｹ晢ｽｼ郢ｧ�ｽｹ邵ｺ�ｽｮ郢ｧ�ｽｨ郢晢ｽｩ郢晢ｽｼ邵ｺ�ｽｮ闔会ｽ｣郢ｧ荳奇ｽ顔ｸｺ�ｽｫ髯ｦ�ｽｨ驕会ｽｺ邵ｺ蜷ｶ�ｽ狗ｹｧ蛹ｻ竕ｧ邵ｺ�ｽｫ隶�?
            隰瑚�娯��郢ｧ荵晢ｼ�邵ｺ�ｽｨ郢ｧ雋槫ｺ�髢ｭ�ｽｽ邵ｺ�ｽｫ邵ｺ蜉ｱ竏ｪ邵ｺ蜷ｶﾂ?
        <customErrors mode="RemoteOnly" defaultRedirect="GenericErrorPage.htm">
            <error statusCode="403" redirect="NoAccess.htm" />
            <error statusCode="404" redirect="FileNotFound.htm" />
        </customErrors>
        -->
		<pages controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID"/>
	</system.web>
</configuration>