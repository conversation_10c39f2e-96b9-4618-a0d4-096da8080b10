﻿using Microsoft.Maui.Layouts;

namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ButtonStyle => CreateStyle<Button>()
        .Set(Button.TextColorProperty, Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.Blue : AppColors.White)
        .Set(Button.BackgroundColorProperty, Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.Blue : AppColors.White)
        .Set(Button.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Button.BorderColorProperty, AppColors.Transparent)
        .Set(Button.BorderWidthProperty, 0)
        .Set(Button.CornerRadiusProperty, Dimens.SpacingXxs)
        .Set(Button.MinimumHeightRequestProperty, 44)
        .Set(Button.MinimumWidthRequestProperty, 44)
        .Set(Button.FontSizeProperty, Dimens.FontSizeT6);

    public static Style OnlineOfflineSwitchModeButton => CreateStyle<RadButton>()
        .Set(Button.BorderColorProperty, AppColors.Transparent)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightSteelBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height30)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width50);

    public static Style ExamineFnButton => CreateStyle<RadButton>()
        .Set(Button.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(VisualElement.BackgroundColorProperty, AppColors.SlateGray)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height58)
        .Set(Button.TextColorProperty, AppColors.White)
        .Set(Button.PaddingProperty, Dimens.SpacingXxs)
        .Set(RadButton.VerticalContentAlignmentProperty, TextAlignment.End)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width120)
        .Set(Button.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Button.CornerRadiusProperty, Dimens.CornerRadius16);

    public static Style NormalButton => CreateStyle<RadButton>()
        .Set(Button.BorderColorProperty, AppColors.Transparent)
        .Set(RadButton.CornerRadiusProperty, Dimens.RadCornerRadius12)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightBlue3)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height30)
        .Set(RadButton.PaddingProperty, new Thickness(30, 0, 30, 0))
        .Set(RadButton.TextColorProperty, AppColors.White);

    public static Style IndividualSectionButton => CreateStyle<RadButton>()
        .Set(Button.BorderColorProperty, AppColors.Transparent)
        .Set(Button.HorizontalOptionsProperty, LayoutOptions.Start)
        .Set(RadButton.CornerRadiusProperty, Dimens.RadCornerRadius12)
        .Set(VisualElement.BackgroundColorProperty, AppColors.LightGrey)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height30)
        .Set(RadButton.PaddingProperty, new Thickness(30, 0, 30, 0))
        .Set(RadButton.TextColorProperty, AppColors.White);

    public static Style GuideSelectButton => CreateStyle<RadButton>()
        .Set(FlexLayout.BasisProperty, new FlexBasis(0.6f, true))
        .Set(Button.BorderWidthProperty, Dimens.Thickness4)
        .Set(Button.BorderColorProperty, AppColors.Black)
        .Set(Button.CornerRadiusProperty, Dimens.ButtonCornerRadius16)
        .Set(RadButton.VerticalContentAlignmentProperty, TextAlignment.Center)
        .Set(Button.FontAttributesProperty, FontAttributes.Bold);


}
