using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class NextGuideDataRecord
    {
        private const int MEDICALCHECKDATE_POS = 0;
        private const int CLIENT_ID_POS = 1;
        private const int DIVISION_POS = 2;
        private const int CHILDGUIDE_FLG_POS = 3;
        private const int VALUE_POS = 4;
        private const int LASTUPDATE_POS = 5;
        private string FldMedicalCheckDate;
        private string FldClientId;
        private string FldDivision;
        private string FldChildGuideFlg;
        private string FldValue;
        private string FldLastUpdate;

        public NextGuideDataRecord(string[] dbData)
        {
            this.MedicalCheckDate = dbData[MEDICALCHECKDATE_POS];
            this.ClientId = dbData[CLIENT_ID_POS];
            this.Division = dbData[DIVISION_POS];
            this.ChildGuideFlg = dbData[CHILDGUIDE_FLG_POS];
            this.Value = dbData[VALUE_POS];
            this.LastUpDate = dbData[LASTUPDATE_POS];
        }

        public string MedicalCheckDate
        {
            get
            {
                return this.FldMedicalCheckDate;
            }
            set
            {
                this.FldMedicalCheckDate = value;
            }
        }

        public string ClientId
        {
            get
            {
                return this.FldClientId;
            }
            set
            {
                this.FldClientId = value;
            }
        }

        public string Division
        {
            get
            {
                return this.FldDivision;
            }
            set
            {
                this.FldDivision = value;
            }
        }

        public string ChildGuideFlg
        {
            get
            {
                return this.FldChildGuideFlg;
            }
            set
            {
                this.FldChildGuideFlg = value;
            }
        }

        public string Value
        {
            get
            {
                return this.FldValue;
            }
            set
            {
                this.FldValue = value;
            }
        }

        public string LastUpDate
        {
            get
            {
                return this.FldLastUpdate;
            }
            set
            {
                this.FldLastUpdate = value;
            }
        }
    }
}
