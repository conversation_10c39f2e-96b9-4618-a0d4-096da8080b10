<?xml version="1.0" encoding="utf-8" ?>
<Grid
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.HeaderView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:app="clr-namespace:ShinKenShinKun"
    ColumnDefinitions="*, Auto, *"
    BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
    x:Name="this">
    <HorizontalStackLayout
        Grid.Column="0"
        VerticalOptions="Center"
        Spacing="{x:Static ui:Dimens.SpacingXl}"
        Margin="{ui:EdgeInsets Left={x:Static ui:Dimens.SpacingSm2}}">
        <app:HomeButtonView
            IsEnabled="{Binding IsHomeButtonEnabled, Source={x:Reference this}}"
            IsVisible="{Binding CompomentStatus.IsHomeDisplay, Source={x:Reference this}}"
            GoHomeCommand="{Binding GoHomeCommand, Source={x:Reference this}}" />
        <app:MaskButtonView
            IsVisible="{Binding CompomentStatus.IsSwitchMaskDisplay, Source={x:Reference this}}"
            Grid.Column="1"
            VerticalOptions="Center"
            ChangeModeCommand="{Binding ChangeModeCommand, Source={x:Reference this}}"
            IsMaskMode="{Binding IsMaskMode, Source={x:Reference this}}" />
    </HorizontalStackLayout>
    <HorizontalStackLayout
        Grid.Column="1"
        HorizontalOptions="Center"
        Spacing="{x:Static ui:Dimens.SpacingLg}">
        <Label
            IsVisible="{Binding CompomentStatus.IsTitleDisplay, Source={x:Reference this}}"
            Text="{Binding Title, Source={x:Reference this}}"
            Style="{Static ui:Styles.AppHeaderLabelStyle}" />
        <Label
            Text="{Binding MedicalMachineName, Source={x:Reference this}}"
            Style="{Static ui:Styles.AppHeaderLabelStyle}">
            <Label.Triggers>
                <DataTrigger
                    TargetType="Label"
                    Binding="{Binding MedicalMachineName, Source={x:Reference this}}"
                    Value="">
                    <Setter
                        Property="IsVisible"
                        Value="False" />
                </DataTrigger>
                <DataTrigger
                    TargetType="Label"
                    Binding="{Binding CompomentStatus.IsTitleDisplay, Source={x:Reference this}}"
                    Value="False">
                    <Setter
                        Property="IsVisible"
                        Value="False" />
                </DataTrigger>
            </Label.Triggers>
        </Label>
    </HorizontalStackLayout>

    <app:LoginUserView
        IsVisible="{Binding CompomentStatus.IsUserLoginDisplay, Source={x:Reference this}}"
        LoginUser="{Binding LoginUser, Source={x:Reference this}}"
        Grid.Column="2" />
</Grid>