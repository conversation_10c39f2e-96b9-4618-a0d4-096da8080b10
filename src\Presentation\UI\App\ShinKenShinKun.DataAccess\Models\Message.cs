﻿namespace ShinKenShinKun.DataAccess;

public partial class Message
{
    [Key]
    public string MessegeId { get; set; }

    public string MessageCode { get; set; }

    public string LanguageCode { get; set; }
    public string MessageType { get; set; }
    public string? MessageTitle { get; set; }
    public string? MessageBody { get; set; }
    public string ButtonType { get; set; }
    public byte? DefaltButtonIndex { get; set; }
    public bool IsVisible { get; set; }
    public byte AutoSelectedButtonIndex { get; set; }
}