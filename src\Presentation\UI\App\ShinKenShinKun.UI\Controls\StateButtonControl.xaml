<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="ShinKenShinKun.UI.StateButtonControl"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:Name="this">
    <telerik:RadBorder Style="{Binding BorderStyle, Source={Reference this}}">
        <Grid ColumnDefinitions="2*,8*">
            <Label
                BackgroundColor="{Binding CurrentState.StatusColor, Source={x:Reference this}}"
                Style="{Binding StateStyle, Source={x:Reference this}}"
                Text="{Binding CurrentState.StatusName, Source={x:Reference this}}" />
            <Label
                Grid.Column="1"
                Style="{Binding TextStyle, Source={x:Reference this}}"
                Text="{Binding Text, Source={x:Reference this}}" />
        </Grid>
    </telerik:RadBorder>
</ContentView>