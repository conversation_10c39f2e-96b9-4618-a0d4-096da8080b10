﻿namespace ShinKenShinKun.Utils;

public class FileOut
{
    #region メンバ変数

    private static FileOut Instance;
    private const string LOG_TITLE = "【FileOut】";
    private string FileOutLocalPath;
    private int LocalkeepDays = 10;
    private ClientLogger Logger;
    private DataAccessControl DbCtl;

    #endregion メンバ変数

    #region コンストラクタ

    private FileOut()
    {
        try
        {
            DbCtl = DataAccessControl.GetInstance();
            Logger = ClientLogger.GetInstance();
            getLocalPath();
            FileDelete(FileOutLocalPath);
        }
        catch (Exception)
        {
        }
    }

    #endregion コンストラクタ

    #region ファイル作成

    public bool CreateFile(string outPath, string outName, string data)
    {
        Logger.Write(LOG_TITLE, "↓ファイル作成開始↓");
        Logger.Write(LOG_TITLE, "ディレクトリ:" + outPath);
        Logger.Write(LOG_TITLE, "ファイル名:" + outName);
        if (data != "")
        {
            Logger.Write(LOG_TITLE, "内容:" + data);
        }
        bool ret = false;
        if (!Directory.Exists(outPath))
        {
            try
            {
                Logger.Write(LOG_TITLE, "フォルダ作成：" + outPath);
                Directory.CreateDirectory(outPath);
            }
            catch (Exception e1)
            {
                Logger.Write(LOG_TITLE, "フォルダ作成：失敗");
                Logger.Write(LOG_TITLE, "↑ファイル作成終了↑[失敗]e1:" + e1.StackTrace);
                return false;
            }
        }

        try
        {
            string tempName = outName + ".tmp";
            File.Delete(FileOutLocalPath + tempName);
            Logger.Write(LOG_TITLE, "①一時ファイル作成(local)");
            using (StreamWriter writer = new StreamWriter(FileOutLocalPath + tempName, true, Encoding.GetEncoding("shift_jis")))
            {
                try
                {
                    Logger.Write(LOG_TITLE, "②一時ファイル内容書き込み(local):" + data);
                    writer.Write(data);
                    writer.Close();
                    if (File.Exists(outPath + tempName))
                    {//かぶっていた場合は削除
                        Logger.Write(LOG_TITLE, "ファイル削除:" + outPath + tempName);
                        File.Delete(outPath + tempName);
                    }
                    //一時ファイルを出力先に作成
                    Logger.Write(LOG_TITLE, "③一時ファイル作成");
                    File.Copy(FileOutLocalPath + tempName, outPath + tempName);
                    if (File.Exists(outPath + outName))
                    {//かぶっていた場合は削除
                        Logger.Write(LOG_TITLE, "ファイル削除:" + outPath + outName);
                        File.Delete(outPath + outName);
                    }
                    //一時ファイルをリネームして完成
                    Logger.Write(LOG_TITLE, "④ファイル作成（リネーム）");
                    File.Move(outPath + tempName, outPath + outName);
                }
                catch (Exception e)
                {
                    Logger.Write(LOG_TITLE, e.StackTrace);
                }
                ret = true;
            }
        }
        catch (Exception e2)
        {
            Logger.Write(LOG_TITLE, "↑ファイル作成終了↑[失敗]e2:" + e2.StackTrace);
            return false;
        }
        Logger.Write(LOG_TITLE, "↑ファイル作成終了↑[成功]");
        return ret;
    }

    #endregion ファイル作成

    #region インスタンス取得

    public static FileOut GetInstance()
    {
        if (Instance == null)
        {
            Instance = new FileOut();
        }
        return Instance;
    }

    #endregion インスタンス取得

    #region ヘルゼアファイル連携

    ////出力ファイル名：
    //FILE_HEADER_YYYYMMDDHHMMSSmmm_MODE_KNO                 _YMD_KBN_JNO.ken
    //[1]FILE_HEADER
    //"OUT"固定
    //[2]作成日時(半角数字17桁)
    //YYYYMMDDHHMMSSmmm
    //[3]MODE(動作モード)
    //1：読影済み保存（規定所見を設定）
    //2：所見入力
    //1固定
    //[4]KNO（個人番号）半角数字12桁
    //[5]YMD（受診日）8桁
    //'YYYYMMDD'
    //[6]KBN（受診区分）半角英数3桁
    //※ヘルゼアネクスト側のVC_JSNKBNのコード
    //[7]JNO（受診番号）半角数字5桁
    public void HellseherFileIO(ClientInfoData tmpClientData, string tmpOutPath, string data)
    {
        string outFileName = "OUT_";
        try
        {
            //string[] filelist = Directory.GetFiles(tmpOutPath);

            #region iis経由ver

            string[] filelist = DbCtl.SFGetFileList(tmpOutPath);

            #endregion iis経由ver

            foreach (string file in filelist)
            {
                try
                {
                    int i = file.LastIndexOf("\\");
                    string filename = file.Substring(i + 1);
                    Logger.Write(LOG_TITLE, "不要ファイル削除：" + filename);
                    File.Delete(tmpOutPath + filename);

                    #region iis経由ver

                    Logger.Write(LOG_TITLE, "不要ファイル削除：" + filename + " [処理:" + DbCtl.SFDeleteFile(tmpOutPath, filename) + "]");

                    #endregion iis経由ver
                }
                catch (Exception e)
                {
                    Logger.Write(LOG_TITLE, "不要ファイル削除：失敗" + e.Message + e.StackTrace);
                }
            }

            outFileName += DateTime.Now.ToString("yyyyMMddHHmmssfff") + "_";
            outFileName += "1" + "_";//検査モード1固定
            outFileName += tmpClientData.RegistrationNo + "_";//個人番号
            outFileName += tmpClientData.sMedicalCheckDate + "_";//受診日
            outFileName += tmpClientData.Division + "_";//受診区分
            outFileName += tmpClientData.ClientId.Trim();//当日番号5桁
            outFileName += ".ken";
            CreateFile(tmpOutPath, outFileName, data);

            #region iis経由ver

            if (DbCtl.SFCreateServerFile(tmpOutPath, outFileName, data))
            {
                Logger.Write(LOG_TITLE, "ヘルゼア連携ファイル作成[成功]：" + outFileName);
            }
            else
            {
                Logger.Write(LOG_TITLE, "ヘルゼア連携ファイル作成[失敗]：" + outFileName);
            }

            #endregion iis経由ver
        }
        catch (Exception ex)
        {
            Logger.Write(LOG_TITLE, "HellseherFileIO：失敗" + ex.Message + ex.StackTrace);
        }
    }

    #endregion ヘルゼアファイル連携

    #region getLocalPath

    /// <summary>
    /// getLocalPath
    /// </summary>
    /// <param name="kaiso">階層</param>
    /// <returns></returns>
    public void getLocalPath()
    {
        Uri currentPath = new Uri(Path.GetDirectoryName(Assembly.GetExecutingAssembly().GetName().CodeBase));
        FileOutLocalPath = currentPath.LocalPath + "\\";
        FileOutLocalPath = FileOutLocalPath + "tmp\\";
        try
        {
            if (!Directory.Exists(FileOutLocalPath))
            {
                try
                {
                    Directory.CreateDirectory(FileOutLocalPath);
                }
                catch (Exception er)
                {
                }
            }
        }
        catch
        {
        }
    }

    #endregion getLocalPath

    #region ファイル削除

    public void FileDelete(string path)
    {
        try
        {
            // フォルダがなければ処理終了
            if (!Directory.Exists(path))
            {
                return;
            }
            foreach (string file in Directory.GetFiles(path))
            {
                FileInfo info = new FileInfo(file);
                if (info.LastWriteTime < DateTime.Now.AddDays(-LocalkeepDays))
                {
                    Logger.Write(LOG_TITLE, "期限切れ(" + LocalkeepDays + "日)ファイル削除:" + file);
                    File.Delete(file);
                }
            }
        }
        catch (Exception ex)
        {
        }
    }

    #endregion ファイル削除

    #region LocalPathChange

    public void LocalPathChange(int kaiso)
    {
        for (int i = 0; i < kaiso; i++)
        {
            try
            {
                FileOutLocalPath = FileOutLocalPath.Substring(0, FileOutLocalPath.LastIndexOf('\\'));
                FileOutLocalPath = FileOutLocalPath.Substring(0, FileOutLocalPath.LastIndexOf('\\') + 1);
            }
            catch
            {
                FileOutLocalPath = FileOutLocalPath.Substring(0, FileOutLocalPath.LastIndexOf('/'));
                FileOutLocalPath = FileOutLocalPath.Substring(0, FileOutLocalPath.LastIndexOf('/') + 1);
            }
        }
    }

    #endregion LocalPathChange

    #region LocalkeepDaysChange

    public void LocalkeepDaysChange(int fLocalkeepDays)
    {
        LocalkeepDays = fLocalkeepDays;
    }

    #endregion LocalkeepDaysChange
}