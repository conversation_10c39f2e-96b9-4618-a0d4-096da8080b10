﻿namespace ShinKenShinKun.UI;

public partial class DialogMessageViewModel(IAppNavigator appNavigator) : BaseViewModel(appNavigator)
{
    [ObservableProperty]
    private DialogMessageDto dialogMessage;

    [ObservableProperty]
    private string icon = "";

    public object?[] MessageParam = [""];

    public ICommand OKCommand;
    public ICommand YesCommand;
    public ICommand NoCommand;
    public ICommand CancelCommand;

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        LoadDialogMessagesAsync();
    }

    private async Task LoadDialogMessagesAsync()
    {
        if (DialogMessage != default)
        {
            DialogMessage.MessageBody = string.Format(DialogMessage.MessageBody, MessageParam);
            DialogMessage.MessageBody = Regex.Unescape(DialogMessage.MessageBody);
            Icon = DictionaryHelpers.MessageIconDic[DialogMessage.MessageType];
        }
    }
}