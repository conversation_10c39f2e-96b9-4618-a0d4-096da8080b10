<?xml version="1.0" encoding="utf-8" ?>
<Grid
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.FooterView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
    Padding="{x:Static ui:Dimens.SpacingSm2}"
    HeightRequest="{x:Static ui:Dimens.Height100}"
    Grid.Row="2"
    ColumnDefinitions="Auto,*,Auto"
    x:Name="this">
    <!--8: Back-->
    <app:BackButtonView
        IsEnabled="{Binding IsBackButtonEnabled, Source={x:Reference this}}"
        IsVisible="{Binding CompomentStatus.IsBackDisplay, Source={x:Reference this}}"
        BackCommand="{Binding BackCommand, Source={x:Reference this}}"
        Grid.Column="0" />
    <HorizontalStackLayout
        Grid.Column="2"
        Spacing="{x:Static ui:Dimens.SpacingMd}">
        <!--9: EnterId-->
        <app:EnterIdButtonView
            IsVisible="{Binding CompomentStatus.IsEnterIdDisplay, Source={x:Reference this}}"
            EnterIdCommand="{Binding EnterIdCommand, Source={x:Reference this}}" />

        <!--13: BulkHoldPatients-->
        <app:BulkHoldPatientsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkHoldPatientsDisplay, Source={x:Reference this}}"
            BulkHoldPatientsCommand="{Binding BulkHoldPatientsCommand, Source={x:Reference this}}" />

        <!--17: BulkHoldTests-->
        <app:BulkHoldTestsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkHoldTestsDisplay, Source={x:Reference this}}"
            BulkHoldTestsCommand="{Binding BulkHoldTestsCommand, Source={x:Reference this}}" />

        <!--10: Hold-->
        <app:HoldButtonView
            IsVisible="{Binding CompomentStatus.IsHoldDisplay, Source={x:Reference this}}"
            HoldCommand="{Binding HoldCommand, Source={x:Reference this}}" />

        <!--15: BulkRegisterHoldPatient-->
        <app:BulkRegisterHoldPatientsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkRegisterHoldPatientsDisplay, Source={x:Reference this}}"
            BulkRegisterHoldPatientsCommand="{Binding BulkRegisterHoldPatientsCommand, Source={x:Reference this}}" />

        <!--11: Pause-->
        <app:PauseButtonView
            IsVisible="{Binding CompomentStatus.IsPauseDisplay, Source={x:Reference this}}"
            PauseCommand="{Binding PauseCommand, Source={x:Reference this}}" />

        <!--19: BulkPauseTests-->
        <app:BulkPauseTestsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkPauseTestsDisplay, Source={x:Reference this}}"
            BulkPauseTestsCommand="{Binding BulkPauseTestsCommand, Source={x:Reference this}}" />

        <!--18: BulkReleaseHoldTests-->
        <app:BulkReleaseHoldTestsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkReleaseHoldTestsDisplay, Source={x:Reference this}}"
            BulkReleaseHoldTestsCommand="{Binding BulkReleaseHoldTestsCommand, Source={x:Reference this}}" />

        <!--20: BulkCancelPauseTests-->
        <app:BulkCancelPauseTestsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkCancelPauseTestsDisplay, Source={x:Reference this}}"
            BulkCancelPauseTestsCommand="{Binding BulkCancelPauseTestsCommand, Source={x:Reference this}}" />

        <!--16: BulkReleaseHoldPatients-->
        <app:BulkReleaseHoldPatientsButtonView
            IsVisible="{Binding CompomentStatus.IsBulkReleaseHoldPatientsDisplay, Source={x:Reference this}}"
            BulkReleaseHoldPatientsCommand="{Binding BulkReleaseHoldPatientsCommand, Source={x:Reference this}}" />

        <!--14: ChangeGuidance-->
        <app:ChangeGuidanceButtonView
            IsVisible="{Binding CompomentStatus.IsChangeGuidanceDisplay, Source={x:Reference this}}"
            ClientSelected="{Binding ClientSelected, Source={x:Reference this}}"
            ChangeGuidanceCommand="{Binding ChangeGuidanceCommand, Source={x:Reference this}}" />

        <!--12: Register-->
        <app:RegisterButtonView
            IsVisible="{Binding CompomentStatus.IsRegisterDisplay, Source={x:Reference this}}"
            ClientSelected="{Binding ClientSelected, Source={x:Reference this}}"
            RegisterCommand="{Binding RegisterCommand, Source={x:Reference this}}" />

        <!--7: Logout-->
        <app:LogoutButtonView
            IsVisible="{Binding CompomentStatus.IsLogoutDisplay, Source={x:Reference this}}"
            LogoutCommand="{Binding LogoutCommand, Source={x:Reference this}}" />

        <!--6: Finish-->
        <app:FinishButtonView
            IsVisible="{Binding CompomentStatus.IsFinishDisplay, Source={x:Reference this}}"
            FinishCommand="{Binding FinishCommand, Source={x:Reference this}}" />
    </HorizontalStackLayout>
</Grid>
