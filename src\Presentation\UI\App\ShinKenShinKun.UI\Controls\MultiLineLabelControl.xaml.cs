namespace ShinKenShinKun.UI;

public partial class MultiLineLabelControl : ContentView
{
    public MultiLineLabelControl() => InitializeComponent();

    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        nameof(Text),
        typeof(string),
        typeof(MultiLineLabelControl),
        string.Empty,
        BindingMode.TwoWay);

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }
}