﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.GuideSupportDataSetTableAdapters;
using System;

namespace ShinKenShinKunServer.Services
{
    public class GuideSupportService
    {
        #region データセット、テーブルアダプターのインスタンス化

        /// <summary>
        /// データセットクラス
        /// </summary>
        private GuideSupportDataSet GuideDataSet = new GuideSupportDataSet();

        /// <summary>
        /// TableAdapterクラス
        /// </summary>
        //private ChildGroupDetailMasterTableAdapter adapterChildGroupDetailMaster
        //    = new ChildGroupDetailMasterTableAdapter();
        //private ChildGroupMasterTableAdapter adapterChildGroupMaster
        //    = new ChildGroupMasterTableAdapter();
        //private ChildGroupTermMasterTableAdapter adapterChildGroupTermMaster
        //    = new ChildGroupTermMasterTableAdapter();
        //private ClientGuideDataTableAdapter adapterClientGuideData
        //    = new ClientGuideDataTableAdapter();
        //private ExamSettingMasterTableAdapter adapterExamSettingMaster
        //    = new ExamSettingMasterTableAdapter();
        //private GroupDetailMasterTableAdapter adapterGroupDetailMaster
        //    = new GroupDetailMasterTableAdapter();
        //private GroupMasterTableAdapter adapterGroupMaster
        //    = new GroupMasterTableAdapter();
        //private GuideDetailMasterTableAdapter adapterGuideDetailMaster
        //    = new GuideDetailMasterTableAdapter();
        //private GuideDivisionMasterTableAdapter adapterGuideDivisionMaster
        //    = new GuideDivisionMasterTableAdapter();
        //private GuideMasterTableAdapter adapterGuideMaster
        //    = new GuideMasterTableAdapter();
        //private GuideRootMasterTableAdapter adapterGuideRootMaster
        //    = new GuideRootMasterTableAdapter();
        //private GuideTimeDataTableAdapter adapterGuideTimeData
        //    = new GuideTimeDataTableAdapter();
        //private IniGuideMasterTableAdapter adapterIniGuideMaster
        //    = new IniGuideMasterTableAdapter();
        //private TermGuideDataTableAdapter adapterTermGuideData
        //    = new TermGuideDataTableAdapter();
        //private NextGuideDataTableAdapter adapterNextGuideData
        //    = new NextGuideDataTableAdapter();

        #endregion

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public GuideSupportService()
        {
        }

        public GuideSupportDataSet GetTermGuideData(string ipAddress)
        {
            TermGuideDataTableAdapter adapterTermGuideData
                = new TermGuideDataTableAdapter();
            int count = adapterTermGuideData.FillBy(GuideDataSet.TermGuideData, ipAddress);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetTermGuideDataAll()
        {
            TermGuideDataTableAdapter adapterTermGuideData
                = new TermGuideDataTableAdapter();
            int count = adapterTermGuideData.Fill(GuideDataSet.TermGuideData);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetIniGuideMaster(string ipAddress)
        {
            IniGuideMasterTableAdapter adapterIniGuideMaster
                = new IniGuideMasterTableAdapter();
            int count = adapterIniGuideMaster.FillBy(GuideDataSet.IniGuideMaster, ipAddress);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetIniGuideMasterAll()
        {
            IniGuideMasterTableAdapter adapterIniGuideMaster
                = new IniGuideMasterTableAdapter();
            int count = adapterIniGuideMaster.Fill(GuideDataSet.IniGuideMaster);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetClientGuideDataWithChildGroup(DateTime dateTime, string clientId, string division)
        {
            ClientGuideDataTableAdapter adapterClientGuideData
                = new ClientGuideDataTableAdapter();
            int count = adapterClientGuideData.FillByChildID(GuideDataSet.ClientGuideData, dateTime, clientId, division);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetChildGroupDetailMaster(short childGroupId)
        {
            ChildGroupDetailMasterTableAdapter adapterChildGroupDetailMaster
                = new ChildGroupDetailMasterTableAdapter();
            int count = adapterChildGroupDetailMaster.FillBy(GuideDataSet.ChildGroupDetailMaster, childGroupId);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetChildGroupTermMaster(short childGroupId)
        {
            ChildGroupTermMasterTableAdapter adapterChildGroupTermMaster
                = new ChildGroupTermMasterTableAdapter();
            int count = adapterChildGroupTermMaster.FillByGuideFlg(GuideDataSet.ChildGroupTermMaster, childGroupId);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetExamSettingMaster(short medicalCheckNo)
        {
            ExamSettingMasterTableAdapter adapterExamSettingMaster
                = new ExamSettingMasterTableAdapter();
            int count = adapterExamSettingMaster.FillBy(GuideDataSet.ExamSettingMaster, medicalCheckNo);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetNextGuideDataMaster(DateTime dateTime, string clientId, string division)
        {
            NextGuideDataTableAdapter adapterNextGuideData
                = new NextGuideDataTableAdapter();
            int count = adapterNextGuideData.FillBy(GuideDataSet.NextGuideData, dateTime, clientId, division);
            return GuideDataSet;
        }
    }
}