﻿namespace ShinKenShinKun;

public partial class TestStatusModel : BaseModel
{
    [ObservableProperty]
    private string displayName;

    [ObservableProperty]
    private MedicalState medicalState;

    [ObservableProperty]
    private Color statusColor;

    [ObservableProperty]
    private Color progressColor;

    [ObservableProperty]
    private Color textColor;

    public TestStatusModel Clone()
    {
        return new TestStatusModel
        {
            DisplayName = this.DisplayName,
            MedicalState = this.MedicalState,
            StatusColor = this.StatusColor,
            ProgressColor = this.ProgressColor,
            TextColor = this.TextColor,
        };
    }
}
