﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// FormDispSetting の概要の説明です
    /// </summary>
    public class FormDispSettingService
    {
        private DispSettingService service;
        private Logger logger = null;
        public FormDispSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetFormDispSettingAll()
        {
            try
            {
                DispSettingDataSet.FormDispItemDataTable tmpTable =
                    service.GetFormDispItemDataAll().FormDispItem;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispItemRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        rList.Add(row.MaxData);
                        rList.Add(row.MinData);
                        rList.Add(row.GapData);
                        rList.Add(row.CheckEmpty.ToString());
                        rList.Add(row.InputFormat);
                        rList.Add(row.DataIndex.ToString());
                        rList.Add(row.DataType.ToString());
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        rList.Add(row.WarningBackColor);
                        rList.Add(row.WarningForeColor);
                        rList.Add(row.DispFlg.ToString());
                        rList.Add(row.ReadOnly.ToString());
                        rList.Add(row.TitleSelect.ToString());
                        rList.Add(row.FormId.ToString());
                        if (row.IsComponentTypeNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.ComponentType.ToString());
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFormDispSettingNo(string formId)
        {
            try
            {
                short formIDShr = Convert.ToInt16(formId);
                DispSettingDataSet.FormDispItemDataTable tmpTable =
                    service.GetFormDispItemNo(formIDShr).FormDispItem;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispItemRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        rList.Add(row.MaxData);
                        rList.Add(row.MinData);
                        rList.Add(row.GapData);
                        rList.Add(row.CheckEmpty.ToString());
                        rList.Add(row.InputFormat);
                        rList.Add(row.DataIndex.ToString());
                        rList.Add(row.DataType.ToString());
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        rList.Add(row.WarningBackColor);
                        rList.Add(row.WarningForeColor);
                        rList.Add(row.DispFlg.ToString());
                        rList.Add(row.ReadOnly.ToString());
                        rList.Add(row.TitleSelect.ToString());
                        rList.Add(row.FormId.ToString());
                        if (row.IsComponentTypeNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.ComponentType.ToString());
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFormDispSettingNo(string formId, string itemNo, string dataType)
        {
            try
            {
                short formIdShr = Convert.ToInt16(formId);
                short itemNoShr = Convert.ToInt16(itemNo);
                short dataTypeShr = Convert.ToInt16(dataType);
                DispSettingDataSet.FormDispItemDataTable tmpTable =
                    service.GetFormDispItemDataType(formIdShr, itemNoShr, dataTypeShr).FormDispItem;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispItemRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        rList.Add(row.MaxData);
                        rList.Add(row.MinData);
                        rList.Add(row.GapData);
                        rList.Add(row.CheckEmpty.ToString());
                        rList.Add(row.InputFormat);
                        rList.Add(row.DataIndex.ToString());
                        rList.Add(row.DataType.ToString());
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        rList.Add(row.WarningBackColor);
                        rList.Add(row.WarningForeColor);
                        rList.Add(row.DispFlg.ToString());
                        rList.Add(row.ReadOnly.ToString());
                        rList.Add(row.TitleSelect.ToString());
                        rList.Add(row.FormId.ToString());
                        if (row.IsComponentTypeNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.ComponentType.ToString());
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFormDispSettingNo(string formId, string dataType)
        {
            try
            {
                short formIdShr = Convert.ToInt16(formId);
                short dataTypeShr = Convert.ToInt16(dataType);
                DispSettingDataSet.FormDispItemDataTable tmpTable =
                    service.GetFormDispItemDataType(formIdShr, dataTypeShr).FormDispItem;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispItemRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        if (row.IsMaxDataNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.MaxData);
                        }
                        if (row.IsMinDataNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.MinData);
                        }
                        if (row.IsGapDataNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.GapData);
                        }
                        rList.Add(row.CheckEmpty.ToString());
                        rList.Add(row.InputFormat);
                        rList.Add(row.DataIndex.ToString());
                        rList.Add(row.DataType.ToString());
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        if (row.IsWarningBackColorNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.WarningBackColor);
                        }
                        if (row.IsWarningForeColorNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.WarningForeColor);
                        }
                        rList.Add(row.DispFlg.ToString());
                        rList.Add(row.ReadOnly.ToString());
                        rList.Add(row.TitleSelect.ToString());
                        rList.Add(row.FormId.ToString());
                        if (row.IsComponentTypeNull())
                        {
                            rList.Add(string.Empty);
                        }
                        else
                        {
                            rList.Add(row.ComponentType.ToString());
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}