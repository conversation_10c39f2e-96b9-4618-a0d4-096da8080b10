﻿#if WINDOWS
using Microsoft.Maui.LifecycleEvents;
using Microsoft.UI;
using Microsoft.UI.Windowing;
using System.Arctec.Ar1000k.DataBase;
#endif

namespace ShinKenShinKun;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseTelerik()
            .UseMauiCommunityToolkit()
            .UseFFImageLoading()
            .ConfigureSyncfusionToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
                fonts.AddNotoSanJPFonts();
            })
            .BuildConfig()
            .RegisterServices()
            .AddImMemDb()
            .RegisterLog()
            .RegisterPages();
#if WINDOWS
        builder.ConfigureLifecycleEvents(lifecycleBuilder =>
        {
            lifecycleBuilder.AddWindows(windowsLifecycleBuilder =>
            {
                windowsLifecycleBuilder.OnWindowCreated(window =>
                {
                    if (window is not MauiWinUIWindow mauiWinUiWindow)
                        return;
                    mauiWinUiWindow.ExtendsContentIntoTitleBar = false;
                    var handle = WinRT.Interop.WindowNative.GetWindowHandle(mauiWinUiWindow);
                    var id = Win32Interop.GetWindowIdFromWindow(handle);
                    var appWindow = AppWindow.GetFromWindowId(id);

                    switch (appWindow.Presenter)
                    {
                        case OverlappedPresenter overlappedPresenter:
                            overlappedPresenter.IsMaximizable = true;
                            overlappedPresenter.IsResizable = false;
                            overlappedPresenter.IsMinimizable = false;
                            overlappedPresenter.SetBorderAndTitleBar(false, false);
                            overlappedPresenter.Maximize();
                            break;
                    }
                });
            });
        });
#endif

#if DEBUG
        builder.Logging.AddDebug();
        // Ensure UseLeakDetection is called after logging has been configured!
        //builder.UseLeakDetection(collectionTarget =>
        //{
        //    // This callback will run any time a leak is detected.
        //    Application.Current?.MainPage?.DisplayAlert("💦Leak Detected💦",
        //        $"❗🧟❗{collectionTarget.Name} is a zombie!", "OK");
        //});
#endif

        var app = builder.Build();
        ConfigInMemoryDb.SeedData(app.Services);
        return app;
    }

    private static MauiAppBuilder RegisterServices(this MauiAppBuilder builder)
    {
        builder.Services.AddAutoMapperConfig();
        builder.Services.AddTransient<ILogService, LogService>();
        builder.Services.AddSingleton<IAppNavigator, AppNavigator>();
        builder.Services.AddSingleton<IAppSettingServices, AppSettingServices>();
        builder.Services.AddSingleton<IFileSystemService, FileSystemService>();
        builder.Services.AddSingleton<IExamineService, ExamineService>();
        builder.Services.AddSingleton<ISessionServices, SessionServices>();
        builder.Services.AddSingleton<IGuidanceAdjustmentService, GuidanceAdjustmentService>();
        builder.Services.AddSingleton<IIndividualProgressServices, IndividualProgressServices>();
        builder.Services.AddTransient<INumberPadServices, NumberPadServices>();
        builder.Services.AddTransient<IDialogMessageService, DialogMessageService>();
        builder.Services.AddSingleton<IAuthServices, AuthServices>();
        builder.Services.AddSingleton<IMedicalCheckItemServices, MedicalCheckItemServices>();
#if WINDOWS
        builder.Services.AddSingleton<DataAccessControl>(_ =>
        {
            var dataAccessControl = DataAccessControl.GetInstance();
            var serverUrl = builder.Configuration.GetValue<string>("DataAccessControl:ServerUrl");
            var timeOut = builder.Configuration.GetValue<int>("DataAccessControl:TimeOut");
            dataAccessControl.SetAccessUrl(serverUrl);
            dataAccessControl.SetDataAccessTimeOut(timeOut);
            return dataAccessControl;
        });
        builder.Services.AddTransient<IBarcodeScannerServices, WindowBarcodeScannerServices>();
        builder.Services.AddTransient<IKeyboardHandlerServices, WindowKeyBoardHandleServices>();
        builder.Services.AddSingleton<INetworkService, NetworkService>();
#endif
        return builder;
    }

    private static MauiAppBuilder RegisterPages(this MauiAppBuilder builder)
    {
        builder.Services.AddPage<LoginPage, LoginPageViewModel>();
        builder.Services.AddPage<HomePage, HomePageViewModel>();
        builder.Services.AddPage<TestSelectionPage, TestSelectionPageViewModel>();
        builder.Services.AddPage<ExamineBeforeAuthPage, ExamineBeforeAuthViewModel>();
        builder.Services.AddPage<GuidanceSelectionPage, GuidanceSelectionViewModel>();
        builder.Services.AddPage<ExamineAfterAuthPage, ExamineAfterAuthViewModel>();
        builder.Services.AddPage<IndividualProgressPage, IndividualProgressViewModel>();
        builder.Services.AddPage<GuidanceAdjustmentPage, GuidanceAdjustmentPageViewModel>();
        builder.Services.AddPage<GuidanceSettingPage, GuidanceSettingViewModel>();
        builder.Services.AddPage<StopExaminePage, StopExaminePageViewModel>();
        builder.Services.AddPage<PatientPendingPage, PatientPendingPageViewModel>();
        builder.Services.AddPage<PendingTestSelectionPage, PendingTestSelectionViewModel>();
        builder.Services.AddPage<StopTestSelectionPage, StopTestSelectionViewModel>();
        builder.Services.AddPage<BlankPage, BlankPageViewModel>();
        builder.Services.AddTransientPopup<NumberPad, NumberPadViewModel>();
        builder.Services.AddTransientPopup<LoadingIndicator, LoadingIndicatorViewModel>();
        builder.Services.AddTransientPopup<UI.DialogMessage, DialogMessageViewModel>();
        return builder;
    }

    private static IServiceCollection AddPage<TPage, TViewModel>(this IServiceCollection services)
        where TPage : BasePage
        where TViewModel : BaseViewModel
    {
        services.AddTransient<TPage>();
        services.AddTransient<TViewModel>();
        return services;
    }

    private static MauiAppBuilder RegisterLog(this MauiAppBuilder builder)
    {
        // Build a temporary service provider to resolve configuration
        var serviceProvider = builder.Services.BuildServiceProvider();
        var configuration = serviceProvider.GetService<IConfiguration>();
        LogSettingModel? logSetting;

        // Attempt to get the log settings from the configuration
        try
        {
            logSetting = configuration?.GetSection("LogSettings").Get<LogSettingModel>();
        }
        catch (InvalidOperationException)
        {
            return builder;
        }

        if (logSetting == null) { return builder; }

        // Validate the log path
        var logDirectory = logSetting.LogPath?.TrimEnd(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar) ?? "";
        var logFileName = $"{logSetting.LogName}.log";
        var logFilePath = Path.Combine(logDirectory, logFileName);

        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Default", LogEventLevel.Fatal)
            .MinimumLevel.Override("Microsoft", LogEventLevel.Fatal)
            .MinimumLevel.Override("System", LogEventLevel.Fatal)
            .WriteTo.File(
                new EncryptedTextFormatter(),
                logFilePath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 30
            )
            .CreateLogger();

        // Add Serilog to the logging providers
        builder.Logging.ClearProviders();
        builder.Logging.AddSerilog();
        return builder;
    }
}