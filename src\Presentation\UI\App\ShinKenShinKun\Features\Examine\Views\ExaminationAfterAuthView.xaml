<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExaminationAfterAuthView"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <Grid
        Padding="{x:Static ui:Dimens.SpacingSm2}"
        RowSpacing="{x:Static ui:Dimens.SpacingSm2}"
        RowDefinitions="*,auto">
        <telerik:RadBorder
            Grid.Row="0"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingXxl}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <ScrollView
                Orientation="Vertical">
                <VerticalStackLayout
                    Grid.Column="0"
                    BindableLayout.ItemsSource="{Binding PatientBodyInfo,Source={x:Reference this} }">
                    <BindableLayout.ItemTemplate>
                        <DataTemplate
                            x:DataType="app:ExamineAfterAuthModel">
                            <Grid
                                Grid.Row="0"
                                Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing30}}"
                                ColumnDefinitions="*,*"
                                ColumnSpacing="{x:Static ui:Dimens.Spacing60}"
                                RowSpacing="{x:Static ui:Dimens.SpacingSm2}">
                                <Grid.Triggers>
                                    <DataTrigger
                                        TargetType="Grid"
                                        Binding="{Binding IsAfter}"
                                        Value="True">
                                        <Setter
                                            Property="Margin"
                                            Value="{ui:EdgeInsets Top={x:Static ui:Dimens.Spacing40}}" />
                                    </DataTrigger>
                                </Grid.Triggers>
                                <app:ExaminationAfterAuthEntryView
                                    Grid.Column="0"
                                    Value="{Binding NewValue}"
                                    Label="{Binding Name}"
                                    Color="{x:Static ui:AppColors.LightBlue}" />
                                <app:ExaminationAfterAuthEntryView
                                    Grid.Column="1"
                                    Value="{Binding OldValue}"
                                    Label="{Binding Name}"
                                    Color="{x:Static ui:AppColors.Grey40}" />
                            </Grid>
                        </DataTemplate>
                    </BindableLayout.ItemTemplate>
                </VerticalStackLayout>

            </ScrollView>
        </telerik:RadBorder>

        <telerik:RadBorder
            Grid.Row="1"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <app:PatientListView
                ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
        </telerik:RadBorder>
    </Grid>
</ContentView>
