﻿namespace ShinKenShinKun;

public class RouteHelpers
{
    public static string GetRouteAfterLogin(AppSettings appSettings) => BuildRoute(appSettings);

    public static string GetRouteDefault(AppSettings appSettings) =>
        appSettings.IsLogin ? $"//{RouterName.LoginPage}" : BuildRoute(appSettings);

    public static async Task RunMonitoringSystem(AppSettings appSettings)
    {
        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = appSettings.MonitoringSystemFolderPath,
                Arguments = "",
                UseShellExecute = true,
                Verb = "runas",
                CreateNoWindow = false,
                WindowStyle = ProcessWindowStyle.Normal,
                WorkingDirectory = appSettings.MonitoringSystemWorkingFolderPath,
            };

            Log.Information($"{AppConstants.AppSource}: {appSettings.MonitoringSystemFolderPath}");

            var process = new Process { StartInfo = startInfo, EnableRaisingEvents = true };
            process.Exited += (sender, e) =>
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    ToggleLayoutTransparency(false);
                });
            process.Start();
            ToggleLayoutTransparency(true);
        }
        catch (Exception ex)
        {
            ToggleLayoutTransparency(false);
            Log.Error(ex, ex.StackTrace);
            Shell.Current.DisplayAlert(
                 AppResources.Error,
                 AppResources.CanNotRunExternalSystem,
                 AppResources.Yes
             );
        }
    }

    //TODO: Replace string.IsNullOrEmpty(Screen) with AppConstants.DefaultScreen.Contains(Screen) and define ScreenCode
    private static void AppendDefaultScreen(StringBuilder route, AppSettings appSettings)
    {
        if (!AppConstants.DefaultScreen.Contains(appSettings.DefaultScreen))
            return;

        string screenRoute = appSettings.DefaultScreen switch
        {
            AppConstants.TestSelectionScreenCode when string.IsNullOrEmpty(appSettings.DefaultMedicalCheck) => $"//{RouterName.TestSelectionPage}",
            AppConstants.TestSelectionScreenCode when !string.IsNullOrEmpty(appSettings.DefaultMedicalCheck) => $"//{RouterName.ExamineBeforeAuthPage}",
            AppConstants.ExamineBeforeAuthScreenCode when string.IsNullOrEmpty(appSettings.DefaultMedicalCheck) => $"//{RouterName.TestSelectionPage}",
            _ => $"//{AppDictionaryHelpers.DefaultSreenCodeDictionary[appSettings.DefaultScreen]}",
        };
        route.Append(screenRoute);
    }

    private static string BuildRoute(AppSettings appSettings)
    {
        var route = new StringBuilder(RouterName.HomePage);
        if (appSettings.DefaultScreen == AppConstants.OverallProgressScreenCode)
        {
            return route.ToString();
        }
        AppendDefaultScreen(route, appSettings);
        return route.ToString();
    }

    private static void ToggleLayoutTransparency(bool isTransparent)
    {
        if (
            Shell.Current?.CurrentPage is BasePage currentPage
            && currentPage.Content is VisualElement layout
        )
        {
            layout.InputTransparent = isTransparent;
        }
    }
}
