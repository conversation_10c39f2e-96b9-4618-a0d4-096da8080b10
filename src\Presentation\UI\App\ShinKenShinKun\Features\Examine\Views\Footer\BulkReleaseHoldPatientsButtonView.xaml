<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    Style="{x:Static ui:Styles.GrayBackWidth80ButtonBorderStyle}"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.BulkReleaseHoldPatientsButtonView"
    x:Name="this">
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Aspect="Fill"
            Source="{x:Static ui:Icons.CancelIcon}"
            Style="{x:Static ui:Styles.ImageFooterXlSizeButtonStyle}" />
        <Label
            Style="{x:Static ui:Styles.BulkReleaseHoldPatientsLabelStyle}" />
    </VerticalStackLayout>
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding BulkReleaseHoldPatientsCommand, Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
</telerik:RadBorder>
