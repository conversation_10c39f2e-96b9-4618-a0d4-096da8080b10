﻿namespace ShinKenShinKun.InterfaceLibrary;
public class TimeOutException : Exception
{
    #region 変数
    private int FldErrorCode;
    #endregion

    #region TimeOutException
    public TimeOutException()
    {
    }
    #endregion

    #region TimeOutException
    public TimeOutException(int errorCode)
    {
        ErrorCode = errorCode;
    }
    #endregion

    #region ErrorCode
    public int ErrorCode
    {
        set
        {
            FldErrorCode = value;
        }
        get
        {
            return FldErrorCode;
        }
    }
    #endregion

}
