namespace ShinKenShinKun;

public partial class PatientListMonitorView : ContentView
{
    public PatientListMonitorView()
    {
        InitializeComponent();
    }

    private void RadButton_Clicked(object sender, EventArgs e)
    {
        var appSettingServices = Application.Current.Handler.GetService<IAppSettingServices>();
        var popupService = Application.Current.Handler.GetService<IPopupService>();
        popupService.ShowPopup<DialogMessageViewModel>(vm =>
            vm.DialogMessage = appSettingServices.DialogMessages.FirstOrDefault()
        );
    }
}
