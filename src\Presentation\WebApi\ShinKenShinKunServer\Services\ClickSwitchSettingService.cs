﻿using System;
using System.Data.SqlClient;
using System.Collections.Generic;
using ShinKenShinKunServer.Utils;
using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// ClickSwitchSettingService の概要の説明です

    /// </summary>
    public class ClickSwitchSettingService
    {
        //private DispSettingService service;
        private FunctionDataSetService service;
        private Logger logger = null;

        public ClickSwitchSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            //this.service = new DispSettingService();
            service = new FunctionDataSetService();
        }

        public PluralRecord[] GetClickSwitchSettingAll()
        {
            try
            {
                FunctionDataSet.ClickSettingDataTable tmpTable =
                    service.GetClickSettingAll().ClickSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (FunctionDataSet.ClickSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispStr1);
                        rList.Add(row.DispStr2);
                        rList.Add(row.DispStr3);
                        rList.Add(row.DispStr4);
                        rList.Add(row.DispStr5);
                        rList.Add(row.DispStr6);
                        rList.Add(row.DispStr7);
                        rList.Add(row.DispStr8);
                        rList.Add(row.DispStr9);
                        rList.Add(row.DispStr10);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }

                //DispSettingDataSet.ClickSwitchDatasDataTable tmpTable =
                //    this.service.GetClickSwitchSettingAll().ClickSwitchDatas;
                //if (tmpTable != null)
                //{
                //    List<PluralRecord> pRecordList = new List<PluralRecord>();
                //    foreach (DispSettingDataSet.ClickSwitchDatasRow row in tmpTable)
                //    {
                //        List<string> rList = new List<string>();
                //        rList.Add(row.MedicalCheckNo.ToString());
                //        rList.Add(row.ItemNo.ToString());
                //        rList.Add(row.DispStr1);
                //        rList.Add(row.DispStr2);
                //        rList.Add(row.DispStr3);
                //        rList.Add(row.DispStr4);
                //        rList.Add(row.DispStr5);
                //        rList.Add(row.DispStr6);
                //        rList.Add(row.DispStr7);
                //        rList.Add(row.DispStr8);
                //        rList.Add(row.DispStr9);
                //        rList.Add(row.DispStr10);
                //        PluralRecord pRec = new PluralRecord(rList.ToArray());
                //        pRecordList.Add(pRec);
                //    }
                //    if (pRecordList.Count != 0)
                //    {
                //        return pRecordList.ToArray();
                //    }
                //}


                //DispSettingDataSet.ClickSwitchSettingDataTable tmpTable =
                //    this.service.GetClickSwitchSettingAll().ClickSwitchSetting;
                //if (tmpTable != null)
                //{
                //    List<PluralRecord> pRecordList = new List<PluralRecord>();
                //    foreach(DispSettingDataSet.ClickSwitchSettingRow row in tmpTable)
                //    {
                //        List<string> rList = new List<string>();
                //        rList.Add(row.MedicalCheckNo.ToString());
                //        rList.Add(row.ItemNo.ToString());
                //        rList.Add(row.DispStr1);
                //        rList.Add(row.DispStr2);
                //        rList.Add(row.DispStr3);
                //        rList.Add(row.DispStr4);
                //        rList.Add(row.DispStr5);
                //        rList.Add(row.DispStr6);
                //        rList.Add(row.DispStr7);
                //        rList.Add(row.DispStr8);
                //        rList.Add(row.DispStr9);
                //        rList.Add(row.DispStr10);
                //        PluralRecord pRec = new PluralRecord(rList.ToArray());
                //        pRecordList.Add(pRec);
                //    }
                //    if (pRecordList.Count != 0)
                //    {
                //        return pRecordList.ToArray();
                //    }
                //}
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        public PluralRecord[] GetClickSwitchSetting(
            string medicalCheckNo,
            string itemNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                short itemNoSht = Convert.ToInt16(itemNo);
                FunctionDataSet.ClickSettingDataTable tmpTable =
                    service.GetClickSetting(medicalCheckNoSht, itemNoSht).ClickSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (FunctionDataSet.ClickSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispStr1);
                        rList.Add(row.DispStr2);
                        rList.Add(row.DispStr3);
                        rList.Add(row.DispStr4);
                        rList.Add(row.DispStr5);
                        rList.Add(row.DispStr6);
                        rList.Add(row.DispStr7);
                        rList.Add(row.DispStr8);
                        rList.Add(row.DispStr9);
                        rList.Add(row.DispStr10);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                //short medicalCheckNoSht = System.Convert.ToInt16(medicalCheckNo);
                //short itemNoSht = System.Convert.ToInt16(itemNo);
                ////DispSettingDataSet.ClickSwitchSettingDataTable tmpTable =
                ////    this.service.GetClickSwitchSetting(medicalCheckNoSht, itemNoSht).ClickSwitchSetting;
                //DispSettingDataSet.ClickSwitchDatasDataTable tmpTable =
                //    this.service.GetClickSwitchSetting(medicalCheckNoSht, itemNoSht).ClickSwitchDatas;
                //if (tmpTable != null)
                //{
                //    List<PluralRecord> pRecordList = new List<PluralRecord>();

                //    //foreach (DispSettingDataSet.ClickSwitchSettingRow row in tmpTable)
                //    foreach (DispSettingDataSet.ClickSwitchDatasRow row in tmpTable)
                //    {
                //        List<string> rList = new List<string>();
                //        rList.Add(row.MedicalCheckNo.ToString());
                //        rList.Add(row.ItemNo.ToString());
                //        rList.Add(row.DispStr1);
                //        rList.Add(row.DispStr2);
                //        rList.Add(row.DispStr3);
                //        rList.Add(row.DispStr4);
                //        rList.Add(row.DispStr5);
                //        rList.Add(row.DispStr6);
                //        rList.Add(row.DispStr7);
                //        rList.Add(row.DispStr8);
                //        rList.Add(row.DispStr9);
                //        rList.Add(row.DispStr10);
                //        PluralRecord pRec = new PluralRecord(rList.ToArray());
                //        pRecordList.Add(pRec);
                //    }
                //    if (pRecordList.Count != 0)
                //    {
                //        return pRecordList.ToArray();
                //    }
                //}
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}