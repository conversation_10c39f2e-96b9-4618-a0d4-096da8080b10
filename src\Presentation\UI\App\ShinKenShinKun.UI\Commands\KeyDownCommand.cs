﻿namespace ShinKenShinKun.UI;
public class KeyDownCommand : DataGridCommand
{
    public KeyDownCommand()
    {
        Id = DataGridCommandId.KeyDown;
    }
    public override bool CanExecute(object parameter)
    {
        var key = (parameter as KeyboardInfo)?.key;
        return key != RadKeyboardKey.Enter;
    }
    public override void Execute(object parameter)
    {
        this.Owner.CommandService.ExecuteDefaultCommand(DataGridCommandId.KeyDown, parameter);
    }
}