<toolkit:Popup
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.UI.IdInputPopup"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    x:Name="this">
    <telerik:RadBorder
        BackgroundColor="{x:Static ui:AppColors.WhisperGray}"
        CornerRadius="4">
        <VerticalStackLayout
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingXxs2}}">
            <!--Title-->
            <Grid
                Padding="{ui:EdgeInsets Right={x:Static ui:Dimens.SpacingXxs2},
                                    Left={x:Static ui:Dimens.Spacing10},
                                    Vertical={x:Static ui:Dimens.Spacing10}}">
                <Label
                    Text="{Binding Title, Source={x:Reference this}}"
                    Style="{x:Static ui:Styles.IdInputTitleLabelStyle}" />
                <Image
                    Source="{x:Static ui:Icons.XIcon}"
                    Style="{x:Static ui:Styles.IdInputCloseButtonStyle}">
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer
                            Tapped="OnClose" />
                    </Image.GestureRecognizers>
                </Image>
            </Grid>
            <!-- Input Display -->
            <Grid
                ColumnDefinitions="Auto,Auto,Auto,Auto"
                RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto"
                HorizontalOptions="Center"
                ColumnSpacing="5"
                RowSpacing="5">
                <telerik:RadBorder
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="4"
                    Style="{x:Static ui:Styles.IdInputDisplayBorderStyle}">
                    <Label
                        x:Name="InputDisplay"
                        Text="{Binding InputValue,Mode=TwoWay, Source={x:Reference this}}"
                        Style="{x:Static ui:Styles.IdInputDisplayLabelStyle}" />
                </telerik:RadBorder>

                <!-- Numbers 1-9 -->
                <telerik:RadButton
                    Text="7"
                    Grid.Row="1"
                    Grid.Column="0"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="8"
                    Grid.Row="1"
                    Grid.Column="1"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="9"
                    Grid.Row="1"
                    Grid.Column="2"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="DEL"
                    Grid.Row="1"
                    Grid.Column="3"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnDelete" />
                <telerik:RadButton
                    Text="4"
                    Grid.Row="2"
                    Grid.Column="0"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="5"
                    Grid.Row="2"
                    Grid.Column="1"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="6"
                    Grid.Row="2"
                    Grid.Column="2"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="BS"
                    Grid.Row="2"
                    Grid.Column="3"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnBackspace" />
                <telerik:RadButton
                    Text="1"
                    Grid.Row="3"
                    Grid.Column="0"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="2"
                    Grid.Row="3"
                    Grid.Column="1"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="3"
                    Grid.Row="3"
                    Grid.Column="2"
                    Style="{x:Static ui:Styles.IdInputButtonStyle}"
                    Clicked="OnKeyPressed" />
                <telerik:RadButton
                    Text="Enter"
                    Grid.Row="3"
                    Grid.Column="3"
                    Grid.RowSpan="2"
                    Style="{x:Static ui:Styles.IdInputEnterButtonStyle}"
                    Command="{Binding ConfirmCommand, Source={x:Reference this}}" />
                <telerik:RadButton
                    Text="0"
                    Grid.Row="4"
                    Grid.ColumnSpan="3"
                    Style="{x:Static ui:Styles.IdInputZeroButtonStyle}"
                    Clicked="OnKeyPressed" />
            </Grid>
        </VerticalStackLayout>
    </telerik:RadBorder>
</toolkit:Popup>