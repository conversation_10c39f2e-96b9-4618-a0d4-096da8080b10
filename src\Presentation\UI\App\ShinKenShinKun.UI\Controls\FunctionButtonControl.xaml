﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    x:Class="ShinKenShinKun.UI.FunctionButtonControl"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:Name="this">
    <telerik:RadButton
        Command="{Binding Command, Source={Reference this}}"
        CommandParameter="{Binding CommandParameter, Source={Reference this}}"
        Style="{Binding Style, Source={Reference this}, TargetNullValue={Static ui:Styles.FunctionButtonDefaultStyle}}"
        Text="{Binding Text, Source={Reference this}}" />
</ContentView>