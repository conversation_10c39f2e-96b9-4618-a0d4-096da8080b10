<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="FunctionDataSet" targetNamespace="http://tempuri.org/FunctionDataSet.xsd" xmlns:mstns="http://tempuri.org/FunctionDataSet.xsd" xmlns="http://tempuri.org/FunctionDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupConnectionString" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClickSettingTableAdapter" GeneratorDataComponentClassName="ClickSettingTableAdapter" Name="ClickSetting" UserDataComponentName="ClickSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, 
                      MedicalItemSetting.ClickId, ClickSwitchMaster.DispStr1, 
                      ClickSwitchMaster.DispStr2, ClickSwitchMaster.DispStr3, 
                      ClickSwitchMaster.DispStr4, ClickSwitchMaster.DispStr5, 
                      ClickSwitchMaster.DispStr6, ClickSwitchMaster.DispStr7, 
                      ClickSwitchMaster.DispStr8, ClickSwitchMaster.DispStr9, 
                      ClickSwitchMaster.DispStr10
FROM            MedicalItemSetting INNER JOIN
                      ClickSwitchMaster ON 
                      MedicalItemSetting.ClickId = ClickSwitchMaster.ClickId
ORDER BY     MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="ClickId" DataSetColumn="ClickId" />
              <Mapping SourceColumn="DispStr1" DataSetColumn="DispStr1" />
              <Mapping SourceColumn="DispStr2" DataSetColumn="DispStr2" />
              <Mapping SourceColumn="DispStr3" DataSetColumn="DispStr3" />
              <Mapping SourceColumn="DispStr4" DataSetColumn="DispStr4" />
              <Mapping SourceColumn="DispStr5" DataSetColumn="DispStr5" />
              <Mapping SourceColumn="DispStr6" DataSetColumn="DispStr6" />
              <Mapping SourceColumn="DispStr7" DataSetColumn="DispStr7" />
              <Mapping SourceColumn="DispStr8" DataSetColumn="DispStr8" />
              <Mapping SourceColumn="DispStr9" DataSetColumn="DispStr9" />
              <Mapping SourceColumn="DispStr10" DataSetColumn="DispStr10" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, 
                      MedicalItemSetting.ClickId, ClickSwitchMaster.DispStr1, 
                      ClickSwitchMaster.DispStr2, ClickSwitchMaster.DispStr3, 
                      ClickSwitchMaster.DispStr4, ClickSwitchMaster.DispStr5, 
                      ClickSwitchMaster.DispStr6, ClickSwitchMaster.DispStr7, 
                      ClickSwitchMaster.DispStr8, ClickSwitchMaster.DispStr9, 
                      ClickSwitchMaster.DispStr10
FROM            MedicalItemSetting INNER JOIN
                      ClickSwitchMaster ON 
                      MedicalItemSetting.ClickId = ClickSwitchMaster.ClickId
WHERE           (MedicalItemSetting.MedicalCheckNo = @MedicalCheckNo) AND 
                      (MedicalItemSetting.ItemNo = @ItemNo)
ORDER BY     MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="FunctionDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="FunctionDataSet" msprop:Generator_DataSetName="FunctionDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ClickSetting" msprop:Generator_UserTableName="ClickSetting" msprop:Generator_RowDeletedName="ClickSettingRowDeleted" msprop:Generator_TableClassName="ClickSettingDataTable" msprop:Generator_RowChangedName="ClickSettingRowChanged" msprop:Generator_RowClassName="ClickSettingRow" msprop:Generator_RowChangingName="ClickSettingRowChanging" msprop:Generator_RowEvArgName="ClickSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="ClickSettingRowChangeEventHandler" msprop:Generator_TablePropName="ClickSetting" msprop:Generator_TableVarName="tableClickSetting" msprop:Generator_RowDeletingName="ClickSettingRowDeleting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="ClickId" msprop:Generator_UserColumnName="ClickId" msprop:Generator_ColumnPropNameInRow="ClickId" msprop:Generator_ColumnVarNameInTable="columnClickId" msprop:Generator_ColumnPropNameInTable="ClickIdColumn" type="xs:short" />
              <xs:element name="DispStr1" msprop:Generator_UserColumnName="DispStr1" msprop:Generator_ColumnPropNameInRow="DispStr1" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr1" msprop:Generator_ColumnPropNameInTable="DispStr1Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr2" msprop:Generator_UserColumnName="DispStr2" msprop:Generator_ColumnPropNameInRow="DispStr2" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr2" msprop:Generator_ColumnPropNameInTable="DispStr2Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr3" msprop:Generator_UserColumnName="DispStr3" msprop:Generator_ColumnPropNameInRow="DispStr3" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr3" msprop:Generator_ColumnPropNameInTable="DispStr3Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr4" msprop:Generator_UserColumnName="DispStr4" msprop:Generator_ColumnPropNameInRow="DispStr4" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr4" msprop:Generator_ColumnPropNameInTable="DispStr4Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr5" msprop:Generator_UserColumnName="DispStr5" msprop:Generator_ColumnPropNameInRow="DispStr5" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr5" msprop:Generator_ColumnPropNameInTable="DispStr5Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr6" msprop:Generator_UserColumnName="DispStr6" msprop:Generator_ColumnPropNameInRow="DispStr6" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr6" msprop:Generator_ColumnPropNameInTable="DispStr6Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr7" msprop:Generator_UserColumnName="DispStr7" msprop:Generator_ColumnPropNameInRow="DispStr7" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr7" msprop:Generator_ColumnPropNameInTable="DispStr7Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr8" msprop:Generator_UserColumnName="DispStr8" msprop:Generator_ColumnPropNameInRow="DispStr8" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr8" msprop:Generator_ColumnPropNameInTable="DispStr8Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr9" msprop:Generator_UserColumnName="DispStr9" msprop:Generator_ColumnPropNameInRow="DispStr9" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr9" msprop:Generator_ColumnPropNameInTable="DispStr9Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispStr10" msprop:Generator_UserColumnName="DispStr10" msprop:Generator_ColumnPropNameInRow="DispStr10" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnDispStr10" msprop:Generator_ColumnPropNameInTable="DispStr10Column" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClickSetting" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
    </xs:unique>
  </xs:element>
</xs:schema>