﻿namespace ShinKenShinKun.Utils;

public class Utils
{
    public static string GetIpPcAddress()
    {
        string ip = "";
        try
        {
            IPHostEntry ipHost = ipHost = Dns.GetHostEntry(Dns.GetHostName());
            foreach (IPAddress ipa in ipHost.AddressList)
            {
                if (ipa.AddressFamily == AddressFamily.InterNetwork)
                {
                    return ipa.ToString();
                }
            }
        }
        catch { }
        return ip;
    }
}