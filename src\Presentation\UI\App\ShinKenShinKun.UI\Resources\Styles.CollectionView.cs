﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ItemBarCollectionStyle => CreateStyle<RadCollectionViewItemView>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Grey60)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius16)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0);

    public static Style ItemNoteMasterCollectionStyle => CreateStyle<RadCollectionViewItemView>()
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadCornerRadius0)
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(VisualStateManager.VisualStateGroupsProperty, ItemNoteMasterCollectionVisualStateGroups());

    public static Style FunctionButtonsItemStyle => CreateStyle<RadCollectionViewItemView>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.Grey60)
        .Set(RadCollectionViewItemView.CornerRadiusProperty, Dimens.RadCornerRadius10)
        .Set(RadCollectionViewItemView.BorderThicknessProperty, Dimens.RadBorderThickness0);
}
