﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// EyeGroupSelectService の概要の説明です
    /// </summary>
    public class EyeGroupSelectService
    {
        private DispSettingService service;
        private Logger logger = null;
        public EyeGroupSelectService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }

        #region EyeGroupSelectテーブル全取得
        /// <summary>
        /// EyeGroupSelectテーブル全取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] GetEyeGroupSelectAll()
        {
            try
            {
                DispSettingDataSet.EyeGroupSelectDataTable tmpTable = service.GetEyeGroupSelectAll().EyeGroupSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeGroupSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;

            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeGroupSelectテーブル取得（GroupIDをKey）
        /// <summary>
        /// EyeGroupSelectテーブル取得（GroupIDをKey）
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeGroupSelectId(string groupId)
        {
            try
            {
                short groupIdSht = Convert.ToInt16(groupId);
                DispSettingDataSet.EyeGroupSelectDataTable tmpTable = service.GetEyeGroupSelectId(groupIdSht).EyeGroupSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeGroupSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;

            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeGroupSelectテーブル取得（MedicalCheckNoをKey）
        /// <summary>
        /// EyeGroupSelectテーブル取得（MedicalCheckNoをKey）
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeGroupMedicalCheckNo(string formId)
        {
            try
            {
                short formIdSht = Convert.ToInt16(formId);
                DispSettingDataSet.EyeGroupSelectDataTable tmpTable =
                    service.GetEyeGroupSelectMedicalCheckNo(formIdSht).EyeGroupSelect;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeGroupSelectRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;

            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region PluralRecord設定
        /// <summary>
        /// PluralRecord設定
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        private PluralRecord SetPluralRecord(DispSettingDataSet.EyeGroupSelectRow row)
        {
            List<string> rList = new List<string>();
            rList.Add(row.EyeGroupId.ToString());
            // GroupID設定
            rList.Add(row.GroupId.ToString());
            // MedicalCheckNo設定
            rList.Add(row.MedicalCheckNo.ToString());
            // ItemNo設定
            rList.Add(row.ItemNo.ToString());
            rList.Add(row.FormId.ToString());
            PluralRecord pRec = new PluralRecord(rList.ToArray());
            return pRec;
        }
        #endregion
    }
}