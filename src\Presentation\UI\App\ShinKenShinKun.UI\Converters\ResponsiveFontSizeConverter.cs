﻿namespace ShinKenShinKun.UI;

public class ResponsiveFontSizeConverter : IValueConverter
{
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if(value is string text && parameter is FontSizeModel parameters)
        {
            int length = text.Length;
            if(length <= 3)
            {
                return parameters.BaseFontSize;
            }
            double fontSize = parameters.BaseFontSize * Math.Pow(length, -parameters.Ratio);
            return Math.Max(parameters.MinFontSize, Math.Min(parameters.MaxFontSize, fontSize));
        }
        return Dimens.FontSizeT6;
    }

    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
