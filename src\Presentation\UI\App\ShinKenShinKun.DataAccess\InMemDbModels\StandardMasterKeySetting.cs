﻿namespace ShinKenShinKun.DataAccess;

public class StandardMasterKeySetting
{
    [ForeignKey(nameof(SettingNumberPad))]
    public int SettingNumberPadId { get; set; }

    [ForeignKey(nameof(StandardMasterKey))]
    public int StandardMasterKeyId { get; set; }

    public int RowIndex { get; set; }
    public int ColIndex { get; set; }
    public int RowSpan { get; set; }
    public int ColSpan { get; set; }
    public string BackgroundColor { get; set; }
    public string TextColor { get; set; }
    public bool IsActive { get; set; }

    public SettingNumberPad SettingNumberPad { get; set; }
    public StandardMasterKey StandardMasterKey { get; set; }
}