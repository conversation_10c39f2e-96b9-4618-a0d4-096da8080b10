<?xml version="1.0" encoding="utf-8"?>

<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:utils="clr-namespace:ShinKenShinKun.Utils;assembly=ShinKenShinKun.Utils"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:Class="ShinKenShinKun.PatientInfoView"
    x:Name="this">
    <ContentView.Resources>
        <ResourceDictionary>
            <ui:MarkedTextConverter
                x:Key="MarkedTextConverter" />
            <DataTemplate
                x:Key="NoteItemTemplate">
                <VerticalStackLayout
                    x:DataType="x:String">
                    <Label
                        Text="{Binding .}"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}"
                        Style="{x:Static ui:Styles.LabelPatientInfo}"
                        HorizontalOptions="Start" />
                    <BoxView
                        Style="{x:Static ui:Styles.HorizontalRule}" />
                </VerticalStackLayout>
            </DataTemplate>
            <DataTemplate
                x:Key="EmptyViewTemplate">
                <VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label
                            Margin="{ui:EdgeInsets 
                                Horizontal={x:Static ui:Dimens.SpacingSm},
                                Vertical={x:Static ui:Dimens.SpacingXs}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}"
                            HorizontalOptions="Start" />
                        <BoxView
                            Style="{x:Static ui:Styles.HorizontalRule}" />
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label
                            Margin="{ui:EdgeInsets 
                                Horizontal={x:Static ui:Dimens.SpacingSm},
                                Vertical={x:Static ui:Dimens.SpacingXs}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}"
                            HorizontalOptions="Start" />
                        <BoxView
                            Style="{x:Static ui:Styles.HorizontalRule}" />
                    </VerticalStackLayout>
                    <VerticalStackLayout>
                        <Label
                            Margin="{ui:EdgeInsets 
                                Horizontal={x:Static ui:Dimens.SpacingSm},
                                Vertical={x:Static ui:Dimens.SpacingXs}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}"
                            HorizontalOptions="Start" />
                        <BoxView
                            Style="{x:Static ui:Styles.HorizontalRule}" />
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </DataTemplate>
        </ResourceDictionary>
    </ContentView.Resources>
    <Grid
        RowDefinitions="*,*,*"
        RowSpacing="{x:Static ui:Dimens.SpacingSm2 }"
        Padding="{x:Static ui:Dimens.SpacingSm2 }">
        <telerik:RadBorder
            Grid.Row="0"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <Grid
                RowDefinitions="Auto, *"
                Padding="{ui:EdgeInsets 
                    Horizontal={x:Static ui:Dimens.SpacingMd}, 
                    Vertical={x:Static ui:Dimens.SpacingSm}}">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.BorderPatientTitle}"
                    Margin="{ui:EdgeInsets 
                        Bottom={x:Static ui:Dimens.SpacingS}}"
                    WidthRequest="{x:Static ui:Dimens.Width94}"
                    HorizontalOptions="Start">
                    <Label
                        Text="{x:Static resources:AppResources.PatientInformation}"
                        Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                        HorizontalTextAlignment="Center"
                        VerticalTextAlignment="Center"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingS}}"
                        VerticalOptions="Center" />
                </telerik:RadBorder>
                <VerticalStackLayout
                    Spacing="0"
                    Grid.Row="1">
                    <Label
                        Text="{Binding ItemSelected.ClientId, Source={x:Reference this}}"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}"
                        Style="{x:Static ui:Styles.LabelPatientInfo}" />
                    <BoxView
                        Style="{x:Static ui:Styles.HorizontalRule}" />
                    <Label
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}"
                        Style="{x:Static ui:Styles.LabelPatientInfo}">
                        <Label.Text>
                            <MultiBinding
                                Converter="{StaticResource MarkedTextConverter}">
                                <Binding
                                    Path="IsItemSelected"
                                    Source="{x:Reference this}" />
                                <Binding
                                    Path="IsMaskMode"
                                    Source="{x:Reference this}" />
                                <Binding
                                    Path="ItemSelected.Name"
                                    Source="{x:Reference this}" />
                            </MultiBinding>
                        </Label.Text>
                    </Label>
                    <BoxView
                        Style="{x:Static ui:Styles.HorizontalRule}" />
                    <FlexLayout
                        JustifyContent="SpaceBetween"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingSm},
                            Vertical={x:Static ui:Dimens.SpacingXs}}">
                        <Label
                            Text="{Binding ItemSelected.BirthDay,
                                Converter={x:Static ui:AppConverters.DateToJapaneseFormat}, 
                                Source={x:Reference this}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}" />
                        <Label
                            Text="{Binding ItemSelected.Age, 
                                StringFormat={x:Static resources:AppResources.AgeStringFormat}, 
                                Source={x:Reference this}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}" />
                        <Label
                            Text="{Binding ItemSelected.SexDisplay.DisplayName, 
                                Source={x:Reference this}}"
                            Style="{x:Static ui:Styles.LabelPatientInfo}" />
                    </FlexLayout>
                    <BoxView
                        Style="{x:Static ui:Styles.HorizontalRule}" />
                </VerticalStackLayout>
            </Grid>
        </telerik:RadBorder>

        <telerik:RadBorder
            Style="{x:Static ui:Styles.FrameBorderStyle}"
            Grid.Row="1">
            <Grid
                RowDefinitions="Auto,*"
                Padding="{ui:EdgeInsets 
                    Horizontal={x:Static ui:Dimens.SpacingMd}, 
                    Vertical={x:Static ui:Dimens.SpacingSm}}">
                <telerik:RadBorder
                    Grid.Row="0"
                    Style="{x:Static ui:Styles.BorderPatientTitle}"
                    Margin="{ui:EdgeInsets 
                        Bottom={x:Static ui:Dimens.SpacingS}}"
                    WidthRequest="{x:Static ui:Dimens.Width94}"
                    HorizontalOptions="Start">
                    <Label
                        Text="{x:Static resources:AppResources.PersonalNotes}"
                        Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                        HorizontalTextAlignment="Center"
                        VerticalTextAlignment="Center"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingS}}"
                        VerticalOptions="Center" />
                </telerik:RadBorder>
                <Grid
                    Grid.Row="1">
                    <ScrollView
                        x:Name="PersonalNoteList"
                        Scrolled="PersonalNoteScrolled"
                        Orientation="Vertical">
                        <VerticalStackLayout
                            BindableLayout.ItemsSource="{Binding ItemSelected.PersonalNotes, 
                                Source={x:Reference this}}"
                            BindableLayout.ItemTemplate="{StaticResource NoteItemTemplate}"
                            BindableLayout.EmptyViewTemplate="{StaticResource EmptyViewTemplate}" />
                    </ScrollView>
                </Grid>
            </Grid>
        </telerik:RadBorder>

        <telerik:RadBorder
            Grid.Row="2"
            Style="{x:Static ui:Styles.FrameBorderStyle}">
            <Grid
                RowDefinitions="Auto,*,Auto"
                RowSpacing="{x:Static ui:Dimens.SpacingS}"
                Padding="{ui:EdgeInsets 
                    Horizontal={x:Static ui:Dimens.SpacingMd}, 
                    Vertical={x:Static ui:Dimens.SpacingSm}}">
                <telerik:RadBorder
                    Grid.Row="0"
                    Style="{x:Static ui:Styles.BorderPatientTitle}"
                    WidthRequest="{x:Static ui:Dimens.Width94}"
                    HorizontalOptions="Start">
                    <Label
                        Text="{x:Static resources:AppResources.Notes}"
                        Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                        HorizontalTextAlignment="Center"
                        VerticalTextAlignment="Center"
                        Margin="{ui:EdgeInsets 
                            Horizontal={x:Static ui:Dimens.SpacingS}}" />
                </telerik:RadBorder>
                <Grid
                    Grid.Row="1">
                    <ScrollView
                        x:Name="NoteList"
                        Scrolled="NoteScrolled"
                        Orientation="Vertical">
                        <VerticalStackLayout
                            BindableLayout.ItemsSource="{Binding ItemSelected.Notes, 
                                Source={x:Reference this}}"
                            BindableLayout.ItemTemplate="{StaticResource NoteItemTemplate}"
                            BindableLayout.EmptyViewTemplate="{StaticResource EmptyViewTemplate}" />
                    </ScrollView>
                </Grid>

                <telerik:RadBorder
                    Grid.Row="2"
                    Style="{x:Static ui:Styles.ExamineEditBorder}"
                    HorizontalOptions="End"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingXxs2}}">
                    <Label
                        Style="{x:Static ui:Styles.ExamineEditLabel}" />
                    <telerik:RadBorder.Triggers>
                        <DataTrigger
                            TargetType="telerik:RadBorder"
                            Binding="{Binding IsItemSelected, 
                                Source={x:Reference this}}"
                            Value="True">
                            <Setter
                                Property="BackgroundColor"
                                Value="{x:Static ui:AppColors.DarkBlue}" />
                        </DataTrigger>
                        <DataTrigger
                            TargetType="telerik:RadBorder"
                            Binding="{Binding IsItemSelected, 
                                Source={x:Reference this}}"
                            Value="False">
                            <Setter
                                Property="BackgroundColor"
                                Value="{x:Static ui:AppColors.LightPeriwinkle}" />
                        </DataTrigger>
                    </telerik:RadBorder.Triggers>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding ShowNoteEditPopupCommand, Source={x:Reference this}}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
            </Grid>
        </telerik:RadBorder>
    </Grid>
</ContentView>