using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class TermGuideDataRecord
    {
        private const int IPADRESS_POS = 0;
        private const int GUIDE_FLG_POS = 1;
        private const int TERM_FLG_POS = 2;
        private const int LASTUPDATE_POS = 3;
        private string FldIpAdress;
        private string FldGuideFlg;
        private string FldTermFlg;
        private string FldLastUpDate;

        public TermGuideDataRecord(string[] dbData)
        {
            this.IpAdress = dbData[IPADRESS_POS];
            this.GuideFlg = dbData[GUIDE_FLG_POS];
            this.TermFlg = dbData[TERM_FLG_POS];
            this.LastUpDate = dbData[LASTUPDATE_POS];
        }

        public string IpAdress
        {
            get
            {
                return this.FldIpAdress;
            }
            set
            {
                this.FldIpAdress = value;
            }
        }

        public string GuideFlg
        {
            get
            {
                return this.FldGuideFlg;
            }
            set
            {
                this.FldGuideFlg = value;
            }
        }

        public string TermFlg
        {
            get
            {
                return this.FldTermFlg;
            }
            set
            {
                this.FldTermFlg = value;
            }
        }

        public string LastUpDate
        {
            get
            {
                return this.FldLastUpDate;
            }
            set
            {
                this.FldLastUpDate = value;
            }
        }

    }
}
