<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DispSettingDataSet" targetNamespace="http://tempuri.org/DispSettingDataSet.xsd" xmlns:mstns="http://tempuri.org/DispSettingDataSet.xsd" xmlns="http://tempuri.org/DispSettingDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupConnectionString" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="MedicalCheckupV3ConnectionString" IsAppSettingsProperty="True" Modifier="Assembly" Name="MedicalCheckupV3ConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.MedicalCheckupV3ConnectionString.ConnectionString" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MedicalItemListTableAdapter" GeneratorDataComponentClassName="MedicalItemListTableAdapter" Name="MedicalItemList" UserDataComponentName="MedicalItemListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [MedicalItemList] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [MedicalItemList] ([MedicalCheckNo], [ItemNo], [ItemName]) VALUES (@MedicalCheckNo, @ItemNo, @ItemName)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ItemName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ItemName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, ItemName
FROM            MedicalItemList
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [MedicalItemList] SET [MedicalCheckNo] = @MedicalCheckNo, [ItemNo] = @ItemNo, [ItemName] = @ItemName WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ItemName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ItemName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="ItemName" DataSetColumn="ItemName" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT ItemName, ItemNo, MedicalCheckNo FROM MedicalItemList WHERE (MedicalCheckNo = @MedicalCheckNo) AND (ItemNo = @ItemNo) ORDER BY MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemList" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.MedicalItemList" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByNo" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, ItemName
FROM            MedicalItemList
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemList" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="PluralDispSettingTableAdapter" GeneratorDataComponentClassName="PluralDispSettingTableAdapter" Name="PluralDispSetting" UserDataComponentName="PluralDispSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.PluralDispSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [PluralDispSetting] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [PluralDispSetting] ([MedicalCheckNo], [ItemNo], [DispCount], [SelectCount]) VALUES (@MedicalCheckNo, @ItemNo, @DispCount, @SelectCount)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SelectCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SelectCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, DispCount, SelectCount
FROM            PluralDispSetting
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [PluralDispSetting] SET [MedicalCheckNo] = @MedicalCheckNo, [ItemNo] = @ItemNo, [DispCount] = @DispCount, [SelectCount] = @SelectCount WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@SelectCount" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="SelectCount" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="DispCount" DataSetColumn="DispCount" />
              <Mapping SourceColumn="SelectCount" DataSetColumn="SelectCount" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.PluralDispSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, DispCount, SelectCount
FROM            PluralDispSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo) AND (ItemNo = @ItemNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.PluralDispSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.PluralDispSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.PluralDispSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByNo" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, DispCount, SelectCount
FROM            PluralDispSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.PluralDispSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="PluralSettingTableAdapter" GeneratorDataComponentClassName="PluralSettingTableAdapter" Name="PluralSetting" UserDataComponentName="PluralSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.PluralSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [PluralSetting] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [PluralSetting] ([MedicalCheckNo], [ChildMedicalCheckNo1], [ChildMedicalCheckNo2], [ChildMedicalCheckNo3], [ChildMedicalCheckNo4], [ChildMedicalCheckNo5], [ChildMedicalCheckNo6], [ChildMedicalCheckNo7], [ChildMedicalCheckNo8], [ChildMedicalCheckNo9], [ChildMedicalCheckNo10], [ChildMedicalCheckNo11], [ChildMedicalCheckNo12], [ChildMedicalCheckNo13], [ChildMedicalCheckNo14], [ChildMedicalCheckNo15], [ChildMedicalCheckNo16], [ChildMedicalCheckNo17], [ChildMedicalCheckNo18], [ChildMedicalCheckNo19], [ChildMedicalCheckNo20], [ChildMedicalCheckNo21], [ChildMedicalCheckNo22], [ChildMedicalCheckNo23], [ChildMedicalCheckNo24], [ChildMedicalCheckNo25], [ChildMedicalCheckNo26], [ChildMedicalCheckNo27], [ChildMedicalCheckNo28], [ChildMedicalCheckNo29], [ChildMedicalCheckNo30], [ChildMedicalCheckNo31], [ChildMedicalCheckNo32], [ChildMedicalCheckNo33], [ChildMedicalCheckNo34], [ChildMedicalCheckNo35], [ChildMedicalCheckNo36], [ChildMedicalCheckNo37], [ChildMedicalCheckNo38], [ChildMedicalCheckNo39], [ChildMedicalCheckNo40], [ChildMedicalCheckNo41], [ChildMedicalCheckNo42], [ChildMedicalCheckNo43], [ChildMedicalCheckNo44], [ChildMedicalCheckNo45], [ChildMedicalCheckNo46], [ChildMedicalCheckNo47], [ChildMedicalCheckNo48], [ChildMedicalCheckNo49], [ChildMedicalCheckNo50]) VALUES (@MedicalCheckNo, @ChildMedicalCheckNo1, @ChildMedicalCheckNo2, @ChildMedicalCheckNo3, @ChildMedicalCheckNo4, @ChildMedicalCheckNo5, @ChildMedicalCheckNo6, @ChildMedicalCheckNo7, @ChildMedicalCheckNo8, @ChildMedicalCheckNo9, @ChildMedicalCheckNo10, @ChildMedicalCheckNo11, @ChildMedicalCheckNo12, @ChildMedicalCheckNo13, @ChildMedicalCheckNo14, @ChildMedicalCheckNo15, @ChildMedicalCheckNo16, @ChildMedicalCheckNo17, @ChildMedicalCheckNo18, @ChildMedicalCheckNo19, @ChildMedicalCheckNo20, @ChildMedicalCheckNo21, @ChildMedicalCheckNo22, @ChildMedicalCheckNo23, @ChildMedicalCheckNo24, @ChildMedicalCheckNo25, @ChildMedicalCheckNo26, @ChildMedicalCheckNo27, @ChildMedicalCheckNo28, @ChildMedicalCheckNo29, @ChildMedicalCheckNo30, @ChildMedicalCheckNo31, @ChildMedicalCheckNo32, @ChildMedicalCheckNo33, @ChildMedicalCheckNo34, @ChildMedicalCheckNo35, @ChildMedicalCheckNo36, @ChildMedicalCheckNo37, @ChildMedicalCheckNo38, @ChildMedicalCheckNo39, @ChildMedicalCheckNo40, @ChildMedicalCheckNo41, @ChildMedicalCheckNo42, @ChildMedicalCheckNo43, @ChildMedicalCheckNo44, @ChildMedicalCheckNo45, @ChildMedicalCheckNo46, @ChildMedicalCheckNo47, @ChildMedicalCheckNo48, @ChildMedicalCheckNo49, @ChildMedicalCheckNo50)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ChildMedicalCheckNo1, ChildMedicalCheckNo2, 
                      ChildMedicalCheckNo3, ChildMedicalCheckNo4, ChildMedicalCheckNo5, 
                      ChildMedicalCheckNo6, ChildMedicalCheckNo7, ChildMedicalCheckNo8, 
                      ChildMedicalCheckNo9, ChildMedicalCheckNo10, ChildMedicalCheckNo11, 
                      ChildMedicalCheckNo12, ChildMedicalCheckNo13, ChildMedicalCheckNo14, 
                      ChildMedicalCheckNo15, ChildMedicalCheckNo16, ChildMedicalCheckNo17, 
                      ChildMedicalCheckNo18, ChildMedicalCheckNo19, ChildMedicalCheckNo20, 
                      ChildMedicalCheckNo21, ChildMedicalCheckNo22, ChildMedicalCheckNo23, 
                      ChildMedicalCheckNo24, ChildMedicalCheckNo25, ChildMedicalCheckNo26, 
                      ChildMedicalCheckNo27, ChildMedicalCheckNo28, ChildMedicalCheckNo29, 
                      ChildMedicalCheckNo30, ChildMedicalCheckNo31, ChildMedicalCheckNo32, 
                      ChildMedicalCheckNo33, ChildMedicalCheckNo34, ChildMedicalCheckNo35, 
                      ChildMedicalCheckNo36, ChildMedicalCheckNo37, ChildMedicalCheckNo38, 
                      ChildMedicalCheckNo39, ChildMedicalCheckNo40, ChildMedicalCheckNo41, 
                      ChildMedicalCheckNo42, ChildMedicalCheckNo43, ChildMedicalCheckNo44, 
                      ChildMedicalCheckNo45, ChildMedicalCheckNo46, ChildMedicalCheckNo47, 
                      ChildMedicalCheckNo48, ChildMedicalCheckNo49, ChildMedicalCheckNo50
FROM            PluralSetting
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [PluralSetting] SET [MedicalCheckNo] = @MedicalCheckNo, [ChildMedicalCheckNo1] = @ChildMedicalCheckNo1, [ChildMedicalCheckNo2] = @ChildMedicalCheckNo2, [ChildMedicalCheckNo3] = @ChildMedicalCheckNo3, [ChildMedicalCheckNo4] = @ChildMedicalCheckNo4, [ChildMedicalCheckNo5] = @ChildMedicalCheckNo5, [ChildMedicalCheckNo6] = @ChildMedicalCheckNo6, [ChildMedicalCheckNo7] = @ChildMedicalCheckNo7, [ChildMedicalCheckNo8] = @ChildMedicalCheckNo8, [ChildMedicalCheckNo9] = @ChildMedicalCheckNo9, [ChildMedicalCheckNo10] = @ChildMedicalCheckNo10, [ChildMedicalCheckNo11] = @ChildMedicalCheckNo11, [ChildMedicalCheckNo12] = @ChildMedicalCheckNo12, [ChildMedicalCheckNo13] = @ChildMedicalCheckNo13, [ChildMedicalCheckNo14] = @ChildMedicalCheckNo14, [ChildMedicalCheckNo15] = @ChildMedicalCheckNo15, [ChildMedicalCheckNo16] = @ChildMedicalCheckNo16, [ChildMedicalCheckNo17] = @ChildMedicalCheckNo17, [ChildMedicalCheckNo18] = @ChildMedicalCheckNo18, [ChildMedicalCheckNo19] = @ChildMedicalCheckNo19, [ChildMedicalCheckNo20] = @ChildMedicalCheckNo20, [ChildMedicalCheckNo21] = @ChildMedicalCheckNo21, [ChildMedicalCheckNo22] = @ChildMedicalCheckNo22, [ChildMedicalCheckNo23] = @ChildMedicalCheckNo23, [ChildMedicalCheckNo24] = @ChildMedicalCheckNo24, [ChildMedicalCheckNo25] = @ChildMedicalCheckNo25, [ChildMedicalCheckNo26] = @ChildMedicalCheckNo26, [ChildMedicalCheckNo27] = @ChildMedicalCheckNo27, [ChildMedicalCheckNo28] = @ChildMedicalCheckNo28, [ChildMedicalCheckNo29] = @ChildMedicalCheckNo29, [ChildMedicalCheckNo30] = @ChildMedicalCheckNo30, [ChildMedicalCheckNo31] = @ChildMedicalCheckNo31, [ChildMedicalCheckNo32] = @ChildMedicalCheckNo32, [ChildMedicalCheckNo33] = @ChildMedicalCheckNo33, [ChildMedicalCheckNo34] = @ChildMedicalCheckNo34, [ChildMedicalCheckNo35] = @ChildMedicalCheckNo35, [ChildMedicalCheckNo36] = @ChildMedicalCheckNo36, [ChildMedicalCheckNo37] = @ChildMedicalCheckNo37, [ChildMedicalCheckNo38] = @ChildMedicalCheckNo38, [ChildMedicalCheckNo39] = @ChildMedicalCheckNo39, [ChildMedicalCheckNo40] = @ChildMedicalCheckNo40, [ChildMedicalCheckNo41] = @ChildMedicalCheckNo41, [ChildMedicalCheckNo42] = @ChildMedicalCheckNo42, [ChildMedicalCheckNo43] = @ChildMedicalCheckNo43, [ChildMedicalCheckNo44] = @ChildMedicalCheckNo44, [ChildMedicalCheckNo45] = @ChildMedicalCheckNo45, [ChildMedicalCheckNo46] = @ChildMedicalCheckNo46, [ChildMedicalCheckNo47] = @ChildMedicalCheckNo47, [ChildMedicalCheckNo48] = @ChildMedicalCheckNo48, [ChildMedicalCheckNo49] = @ChildMedicalCheckNo49, [ChildMedicalCheckNo50] = @ChildMedicalCheckNo50 WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ChildMedicalCheckNo50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ChildMedicalCheckNo50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ChildMedicalCheckNo1" DataSetColumn="ChildMedicalCheckNo1" />
              <Mapping SourceColumn="ChildMedicalCheckNo2" DataSetColumn="ChildMedicalCheckNo2" />
              <Mapping SourceColumn="ChildMedicalCheckNo3" DataSetColumn="ChildMedicalCheckNo3" />
              <Mapping SourceColumn="ChildMedicalCheckNo4" DataSetColumn="ChildMedicalCheckNo4" />
              <Mapping SourceColumn="ChildMedicalCheckNo5" DataSetColumn="ChildMedicalCheckNo5" />
              <Mapping SourceColumn="ChildMedicalCheckNo6" DataSetColumn="ChildMedicalCheckNo6" />
              <Mapping SourceColumn="ChildMedicalCheckNo7" DataSetColumn="ChildMedicalCheckNo7" />
              <Mapping SourceColumn="ChildMedicalCheckNo8" DataSetColumn="ChildMedicalCheckNo8" />
              <Mapping SourceColumn="ChildMedicalCheckNo9" DataSetColumn="ChildMedicalCheckNo9" />
              <Mapping SourceColumn="ChildMedicalCheckNo10" DataSetColumn="ChildMedicalCheckNo10" />
              <Mapping SourceColumn="ChildMedicalCheckNo11" DataSetColumn="ChildMedicalCheckNo11" />
              <Mapping SourceColumn="ChildMedicalCheckNo12" DataSetColumn="ChildMedicalCheckNo12" />
              <Mapping SourceColumn="ChildMedicalCheckNo13" DataSetColumn="ChildMedicalCheckNo13" />
              <Mapping SourceColumn="ChildMedicalCheckNo14" DataSetColumn="ChildMedicalCheckNo14" />
              <Mapping SourceColumn="ChildMedicalCheckNo15" DataSetColumn="ChildMedicalCheckNo15" />
              <Mapping SourceColumn="ChildMedicalCheckNo16" DataSetColumn="ChildMedicalCheckNo16" />
              <Mapping SourceColumn="ChildMedicalCheckNo17" DataSetColumn="ChildMedicalCheckNo17" />
              <Mapping SourceColumn="ChildMedicalCheckNo18" DataSetColumn="ChildMedicalCheckNo18" />
              <Mapping SourceColumn="ChildMedicalCheckNo19" DataSetColumn="ChildMedicalCheckNo19" />
              <Mapping SourceColumn="ChildMedicalCheckNo20" DataSetColumn="ChildMedicalCheckNo20" />
              <Mapping SourceColumn="ChildMedicalCheckNo21" DataSetColumn="ChildMedicalCheckNo21" />
              <Mapping SourceColumn="ChildMedicalCheckNo22" DataSetColumn="ChildMedicalCheckNo22" />
              <Mapping SourceColumn="ChildMedicalCheckNo23" DataSetColumn="ChildMedicalCheckNo23" />
              <Mapping SourceColumn="ChildMedicalCheckNo24" DataSetColumn="ChildMedicalCheckNo24" />
              <Mapping SourceColumn="ChildMedicalCheckNo25" DataSetColumn="ChildMedicalCheckNo25" />
              <Mapping SourceColumn="ChildMedicalCheckNo26" DataSetColumn="ChildMedicalCheckNo26" />
              <Mapping SourceColumn="ChildMedicalCheckNo27" DataSetColumn="ChildMedicalCheckNo27" />
              <Mapping SourceColumn="ChildMedicalCheckNo28" DataSetColumn="ChildMedicalCheckNo28" />
              <Mapping SourceColumn="ChildMedicalCheckNo29" DataSetColumn="ChildMedicalCheckNo29" />
              <Mapping SourceColumn="ChildMedicalCheckNo30" DataSetColumn="ChildMedicalCheckNo30" />
              <Mapping SourceColumn="ChildMedicalCheckNo31" DataSetColumn="ChildMedicalCheckNo31" />
              <Mapping SourceColumn="ChildMedicalCheckNo32" DataSetColumn="ChildMedicalCheckNo32" />
              <Mapping SourceColumn="ChildMedicalCheckNo33" DataSetColumn="ChildMedicalCheckNo33" />
              <Mapping SourceColumn="ChildMedicalCheckNo34" DataSetColumn="ChildMedicalCheckNo34" />
              <Mapping SourceColumn="ChildMedicalCheckNo35" DataSetColumn="ChildMedicalCheckNo35" />
              <Mapping SourceColumn="ChildMedicalCheckNo36" DataSetColumn="ChildMedicalCheckNo36" />
              <Mapping SourceColumn="ChildMedicalCheckNo37" DataSetColumn="ChildMedicalCheckNo37" />
              <Mapping SourceColumn="ChildMedicalCheckNo38" DataSetColumn="ChildMedicalCheckNo38" />
              <Mapping SourceColumn="ChildMedicalCheckNo39" DataSetColumn="ChildMedicalCheckNo39" />
              <Mapping SourceColumn="ChildMedicalCheckNo40" DataSetColumn="ChildMedicalCheckNo40" />
              <Mapping SourceColumn="ChildMedicalCheckNo41" DataSetColumn="ChildMedicalCheckNo41" />
              <Mapping SourceColumn="ChildMedicalCheckNo42" DataSetColumn="ChildMedicalCheckNo42" />
              <Mapping SourceColumn="ChildMedicalCheckNo43" DataSetColumn="ChildMedicalCheckNo43" />
              <Mapping SourceColumn="ChildMedicalCheckNo44" DataSetColumn="ChildMedicalCheckNo44" />
              <Mapping SourceColumn="ChildMedicalCheckNo45" DataSetColumn="ChildMedicalCheckNo45" />
              <Mapping SourceColumn="ChildMedicalCheckNo46" DataSetColumn="ChildMedicalCheckNo46" />
              <Mapping SourceColumn="ChildMedicalCheckNo47" DataSetColumn="ChildMedicalCheckNo47" />
              <Mapping SourceColumn="ChildMedicalCheckNo48" DataSetColumn="ChildMedicalCheckNo48" />
              <Mapping SourceColumn="ChildMedicalCheckNo49" DataSetColumn="ChildMedicalCheckNo49" />
              <Mapping SourceColumn="ChildMedicalCheckNo50" DataSetColumn="ChildMedicalCheckNo50" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.PluralSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ChildMedicalCheckNo1, ChildMedicalCheckNo2, 
                      ChildMedicalCheckNo3, ChildMedicalCheckNo4, ChildMedicalCheckNo5, 
                      ChildMedicalCheckNo6, ChildMedicalCheckNo7, ChildMedicalCheckNo8, 
                      ChildMedicalCheckNo9, ChildMedicalCheckNo10, ChildMedicalCheckNo11, 
                      ChildMedicalCheckNo12, ChildMedicalCheckNo13, ChildMedicalCheckNo14, 
                      ChildMedicalCheckNo15, ChildMedicalCheckNo16, ChildMedicalCheckNo17, 
                      ChildMedicalCheckNo18, ChildMedicalCheckNo19, ChildMedicalCheckNo20, 
                      ChildMedicalCheckNo21, ChildMedicalCheckNo22, ChildMedicalCheckNo23, 
                      ChildMedicalCheckNo24, ChildMedicalCheckNo25, ChildMedicalCheckNo26, 
                      ChildMedicalCheckNo27, ChildMedicalCheckNo28, ChildMedicalCheckNo29, 
                      ChildMedicalCheckNo30, ChildMedicalCheckNo31, ChildMedicalCheckNo32, 
                      ChildMedicalCheckNo33, ChildMedicalCheckNo34, ChildMedicalCheckNo35, 
                      ChildMedicalCheckNo36, ChildMedicalCheckNo37, ChildMedicalCheckNo38, 
                      ChildMedicalCheckNo39, ChildMedicalCheckNo40, ChildMedicalCheckNo41, 
                      ChildMedicalCheckNo42, ChildMedicalCheckNo43, ChildMedicalCheckNo44, 
                      ChildMedicalCheckNo45, ChildMedicalCheckNo46, ChildMedicalCheckNo47, 
                      ChildMedicalCheckNo48, ChildMedicalCheckNo49, ChildMedicalCheckNo50
FROM            PluralSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.PluralSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="TransformedSettingTableAdapter" GeneratorDataComponentClassName="TransformedSettingTableAdapter" Name="TransformedSetting" UserDataComponentName="TransformedSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.TransformedSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [TransformedSetting] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [TransformedSetting] ([MedicalCheckNo], [TransformedItemNo1], [TransformedItemNo2], [TransformedItemNo3], [TransformedItemNo4], [TransformedItemNo5], [TransformedItemNo6], [TransformedItemNo7], [TransformedItemNo8], [TransformedItemNo9], [TransformedItemNo10], [TransformedItemNo11], [TransformedItemNo12], [TransformedItemNo13], [TransformedItemNo14], [TransformedItemNo15], [TransformedItemNo16], [TransformedItemNo17], [TransformedItemNo18], [TransformedItemNo19], [TransformedItemNo20], [TransformedItemNo21], [TransformedItemNo22], [TransformedItemNo23], [TransformedItemNo24], [TransformedItemNo25], [TransformedItemNo26], [TransformedItemNo27], [TransformedItemNo28], [TransformedItemNo29], [TransformedItemNo30], [TransformedItemNo31], [TransformedItemNo32], [TransformedItemNo33], [TransformedItemNo34], [TransformedItemNo35], [TransformedItemNo36], [TransformedItemNo37], [TransformedItemNo38], [TransformedItemNo39], [TransformedItemNo40], [TransformedItemNo41], [TransformedItemNo42], [TransformedItemNo43], [TransformedItemNo44], [TransformedItemNo45], [TransformedItemNo46], [TransformedItemNo47], [TransformedItemNo48], [TransformedItemNo49], [TransformedItemNo50]) VALUES (@MedicalCheckNo, @TransformedItemNo1, @TransformedItemNo2, @TransformedItemNo3, @TransformedItemNo4, @TransformedItemNo5, @TransformedItemNo6, @TransformedItemNo7, @TransformedItemNo8, @TransformedItemNo9, @TransformedItemNo10, @TransformedItemNo11, @TransformedItemNo12, @TransformedItemNo13, @TransformedItemNo14, @TransformedItemNo15, @TransformedItemNo16, @TransformedItemNo17, @TransformedItemNo18, @TransformedItemNo19, @TransformedItemNo20, @TransformedItemNo21, @TransformedItemNo22, @TransformedItemNo23, @TransformedItemNo24, @TransformedItemNo25, @TransformedItemNo26, @TransformedItemNo27, @TransformedItemNo28, @TransformedItemNo29, @TransformedItemNo30, @TransformedItemNo31, @TransformedItemNo32, @TransformedItemNo33, @TransformedItemNo34, @TransformedItemNo35, @TransformedItemNo36, @TransformedItemNo37, @TransformedItemNo38, @TransformedItemNo39, @TransformedItemNo40, @TransformedItemNo41, @TransformedItemNo42, @TransformedItemNo43, @TransformedItemNo44, @TransformedItemNo45, @TransformedItemNo46, @TransformedItemNo47, @TransformedItemNo48, @TransformedItemNo49, @TransformedItemNo50)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, TransformedItemNo1, TransformedItemNo2, 
                      TransformedItemNo3, TransformedItemNo4, TransformedItemNo5, 
                      TransformedItemNo6, TransformedItemNo7, TransformedItemNo8, 
                      TransformedItemNo9, TransformedItemNo10, TransformedItemNo11, 
                      TransformedItemNo12, TransformedItemNo13, TransformedItemNo14, 
                      TransformedItemNo15, TransformedItemNo16, TransformedItemNo17, 
                      TransformedItemNo18, TransformedItemNo19, TransformedItemNo20, 
                      TransformedItemNo21, TransformedItemNo22, TransformedItemNo23, 
                      TransformedItemNo24, TransformedItemNo25, TransformedItemNo26, 
                      TransformedItemNo27, TransformedItemNo28, TransformedItemNo29, 
                      TransformedItemNo30, TransformedItemNo31, TransformedItemNo32, 
                      TransformedItemNo33, TransformedItemNo34, TransformedItemNo35, 
                      TransformedItemNo36, TransformedItemNo37, TransformedItemNo38, 
                      TransformedItemNo39, TransformedItemNo40, TransformedItemNo41, 
                      TransformedItemNo42, TransformedItemNo43, TransformedItemNo44, 
                      TransformedItemNo45, TransformedItemNo46, TransformedItemNo47, 
                      TransformedItemNo48, TransformedItemNo49, TransformedItemNo50
FROM            TransformedSetting
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [TransformedSetting] SET [MedicalCheckNo] = @MedicalCheckNo, [TransformedItemNo1] = @TransformedItemNo1, [TransformedItemNo2] = @TransformedItemNo2, [TransformedItemNo3] = @TransformedItemNo3, [TransformedItemNo4] = @TransformedItemNo4, [TransformedItemNo5] = @TransformedItemNo5, [TransformedItemNo6] = @TransformedItemNo6, [TransformedItemNo7] = @TransformedItemNo7, [TransformedItemNo8] = @TransformedItemNo8, [TransformedItemNo9] = @TransformedItemNo9, [TransformedItemNo10] = @TransformedItemNo10, [TransformedItemNo11] = @TransformedItemNo11, [TransformedItemNo12] = @TransformedItemNo12, [TransformedItemNo13] = @TransformedItemNo13, [TransformedItemNo14] = @TransformedItemNo14, [TransformedItemNo15] = @TransformedItemNo15, [TransformedItemNo16] = @TransformedItemNo16, [TransformedItemNo17] = @TransformedItemNo17, [TransformedItemNo18] = @TransformedItemNo18, [TransformedItemNo19] = @TransformedItemNo19, [TransformedItemNo20] = @TransformedItemNo20, [TransformedItemNo21] = @TransformedItemNo21, [TransformedItemNo22] = @TransformedItemNo22, [TransformedItemNo23] = @TransformedItemNo23, [TransformedItemNo24] = @TransformedItemNo24, [TransformedItemNo25] = @TransformedItemNo25, [TransformedItemNo26] = @TransformedItemNo26, [TransformedItemNo27] = @TransformedItemNo27, [TransformedItemNo28] = @TransformedItemNo28, [TransformedItemNo29] = @TransformedItemNo29, [TransformedItemNo30] = @TransformedItemNo30, [TransformedItemNo31] = @TransformedItemNo31, [TransformedItemNo32] = @TransformedItemNo32, [TransformedItemNo33] = @TransformedItemNo33, [TransformedItemNo34] = @TransformedItemNo34, [TransformedItemNo35] = @TransformedItemNo35, [TransformedItemNo36] = @TransformedItemNo36, [TransformedItemNo37] = @TransformedItemNo37, [TransformedItemNo38] = @TransformedItemNo38, [TransformedItemNo39] = @TransformedItemNo39, [TransformedItemNo40] = @TransformedItemNo40, [TransformedItemNo41] = @TransformedItemNo41, [TransformedItemNo42] = @TransformedItemNo42, [TransformedItemNo43] = @TransformedItemNo43, [TransformedItemNo44] = @TransformedItemNo44, [TransformedItemNo45] = @TransformedItemNo45, [TransformedItemNo46] = @TransformedItemNo46, [TransformedItemNo47] = @TransformedItemNo47, [TransformedItemNo48] = @TransformedItemNo48, [TransformedItemNo49] = @TransformedItemNo49, [TransformedItemNo50] = @TransformedItemNo50 WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo1" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo1" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo2" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo2" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo3" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo3" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo4" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo4" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo5" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo5" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo6" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo6" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo7" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo7" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo8" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo8" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo9" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo9" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo10" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo10" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo11" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo11" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo12" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo12" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo13" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo13" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo14" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo14" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo15" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo15" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo16" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo16" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo17" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo17" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo18" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo18" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo19" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo19" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo20" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo20" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo21" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo21" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo22" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo22" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo23" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo23" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo24" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo24" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo25" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo25" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo26" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo26" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo27" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo27" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo28" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo28" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo29" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo29" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo30" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo30" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo31" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo31" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo32" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo32" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo33" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo33" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo34" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo34" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo35" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo35" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo36" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo36" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo37" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo37" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo38" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo38" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo39" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo39" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo40" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo40" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo41" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo41" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo42" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo42" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo43" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo43" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo44" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo44" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo45" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo45" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo46" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo46" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo47" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo47" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo48" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo48" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo49" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo49" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TransformedItemNo50" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TransformedItemNo50" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="TransformedItemNo1" DataSetColumn="TransformedItemNo1" />
              <Mapping SourceColumn="TransformedItemNo2" DataSetColumn="TransformedItemNo2" />
              <Mapping SourceColumn="TransformedItemNo3" DataSetColumn="TransformedItemNo3" />
              <Mapping SourceColumn="TransformedItemNo4" DataSetColumn="TransformedItemNo4" />
              <Mapping SourceColumn="TransformedItemNo5" DataSetColumn="TransformedItemNo5" />
              <Mapping SourceColumn="TransformedItemNo6" DataSetColumn="TransformedItemNo6" />
              <Mapping SourceColumn="TransformedItemNo7" DataSetColumn="TransformedItemNo7" />
              <Mapping SourceColumn="TransformedItemNo8" DataSetColumn="TransformedItemNo8" />
              <Mapping SourceColumn="TransformedItemNo9" DataSetColumn="TransformedItemNo9" />
              <Mapping SourceColumn="TransformedItemNo10" DataSetColumn="TransformedItemNo10" />
              <Mapping SourceColumn="TransformedItemNo11" DataSetColumn="TransformedItemNo11" />
              <Mapping SourceColumn="TransformedItemNo12" DataSetColumn="TransformedItemNo12" />
              <Mapping SourceColumn="TransformedItemNo13" DataSetColumn="TransformedItemNo13" />
              <Mapping SourceColumn="TransformedItemNo14" DataSetColumn="TransformedItemNo14" />
              <Mapping SourceColumn="TransformedItemNo15" DataSetColumn="TransformedItemNo15" />
              <Mapping SourceColumn="TransformedItemNo16" DataSetColumn="TransformedItemNo16" />
              <Mapping SourceColumn="TransformedItemNo17" DataSetColumn="TransformedItemNo17" />
              <Mapping SourceColumn="TransformedItemNo18" DataSetColumn="TransformedItemNo18" />
              <Mapping SourceColumn="TransformedItemNo19" DataSetColumn="TransformedItemNo19" />
              <Mapping SourceColumn="TransformedItemNo20" DataSetColumn="TransformedItemNo20" />
              <Mapping SourceColumn="TransformedItemNo21" DataSetColumn="TransformedItemNo21" />
              <Mapping SourceColumn="TransformedItemNo22" DataSetColumn="TransformedItemNo22" />
              <Mapping SourceColumn="TransformedItemNo23" DataSetColumn="TransformedItemNo23" />
              <Mapping SourceColumn="TransformedItemNo24" DataSetColumn="TransformedItemNo24" />
              <Mapping SourceColumn="TransformedItemNo25" DataSetColumn="TransformedItemNo25" />
              <Mapping SourceColumn="TransformedItemNo26" DataSetColumn="TransformedItemNo26" />
              <Mapping SourceColumn="TransformedItemNo27" DataSetColumn="TransformedItemNo27" />
              <Mapping SourceColumn="TransformedItemNo28" DataSetColumn="TransformedItemNo28" />
              <Mapping SourceColumn="TransformedItemNo29" DataSetColumn="TransformedItemNo29" />
              <Mapping SourceColumn="TransformedItemNo30" DataSetColumn="TransformedItemNo30" />
              <Mapping SourceColumn="TransformedItemNo31" DataSetColumn="TransformedItemNo31" />
              <Mapping SourceColumn="TransformedItemNo32" DataSetColumn="TransformedItemNo32" />
              <Mapping SourceColumn="TransformedItemNo33" DataSetColumn="TransformedItemNo33" />
              <Mapping SourceColumn="TransformedItemNo34" DataSetColumn="TransformedItemNo34" />
              <Mapping SourceColumn="TransformedItemNo35" DataSetColumn="TransformedItemNo35" />
              <Mapping SourceColumn="TransformedItemNo36" DataSetColumn="TransformedItemNo36" />
              <Mapping SourceColumn="TransformedItemNo37" DataSetColumn="TransformedItemNo37" />
              <Mapping SourceColumn="TransformedItemNo38" DataSetColumn="TransformedItemNo38" />
              <Mapping SourceColumn="TransformedItemNo39" DataSetColumn="TransformedItemNo39" />
              <Mapping SourceColumn="TransformedItemNo40" DataSetColumn="TransformedItemNo40" />
              <Mapping SourceColumn="TransformedItemNo41" DataSetColumn="TransformedItemNo41" />
              <Mapping SourceColumn="TransformedItemNo42" DataSetColumn="TransformedItemNo42" />
              <Mapping SourceColumn="TransformedItemNo43" DataSetColumn="TransformedItemNo43" />
              <Mapping SourceColumn="TransformedItemNo44" DataSetColumn="TransformedItemNo44" />
              <Mapping SourceColumn="TransformedItemNo45" DataSetColumn="TransformedItemNo45" />
              <Mapping SourceColumn="TransformedItemNo46" DataSetColumn="TransformedItemNo46" />
              <Mapping SourceColumn="TransformedItemNo47" DataSetColumn="TransformedItemNo47" />
              <Mapping SourceColumn="TransformedItemNo48" DataSetColumn="TransformedItemNo48" />
              <Mapping SourceColumn="TransformedItemNo49" DataSetColumn="TransformedItemNo49" />
              <Mapping SourceColumn="TransformedItemNo50" DataSetColumn="TransformedItemNo50" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.TransformedSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, TransformedItemNo1, TransformedItemNo2, 
                      TransformedItemNo3, TransformedItemNo4, TransformedItemNo5, 
                      TransformedItemNo6, TransformedItemNo7, TransformedItemNo8, 
                      TransformedItemNo9, TransformedItemNo10, TransformedItemNo11, 
                      TransformedItemNo12, TransformedItemNo13, TransformedItemNo14, 
                      TransformedItemNo15, TransformedItemNo16, TransformedItemNo17, 
                      TransformedItemNo18, TransformedItemNo19, TransformedItemNo20, 
                      TransformedItemNo21, TransformedItemNo22, TransformedItemNo23, 
                      TransformedItemNo24, TransformedItemNo25, TransformedItemNo26, 
                      TransformedItemNo27, TransformedItemNo28, TransformedItemNo29, 
                      TransformedItemNo30, TransformedItemNo31, TransformedItemNo32, 
                      TransformedItemNo33, TransformedItemNo34, TransformedItemNo35, 
                      TransformedItemNo36, TransformedItemNo37, TransformedItemNo38, 
                      TransformedItemNo39, TransformedItemNo40, TransformedItemNo41, 
                      TransformedItemNo42, TransformedItemNo43, TransformedItemNo44, 
                      TransformedItemNo45, TransformedItemNo46, TransformedItemNo47, 
                      TransformedItemNo48, TransformedItemNo49, TransformedItemNo50
FROM            TransformedSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.TransformedSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="InputFormatSettingTableAdapter" GeneratorDataComponentClassName="InputFormatSettingTableAdapter" Name="InputFormatSetting" UserDataComponentName="InputFormatSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.InputFormatSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [InputFormatSetting] WHERE (([InputFormatId] = @Original_InputFormatId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [InputFormatSetting] ([InputFormatId], [InputFormat]) VALUES (@InputFormatId, @InputFormat)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@InputFormat" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="InputFormat" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          InputFormatId, InputFormat
FROM            InputFormatSetting
ORDER BY     InputFormatId</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [InputFormatSetting] SET [InputFormatId] = @InputFormatId, [InputFormat] = @InputFormat WHERE (([InputFormatId] = @Original_InputFormatId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@InputFormat" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="InputFormat" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="InputFormatId" DataSetColumn="InputFormatId" />
              <Mapping SourceColumn="InputFormat" DataSetColumn="InputFormat" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.InputFormatSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          InputFormatId, InputFormat
FROM            InputFormatSetting
WHERE           (InputFormatId = @InputFormatId)
ORDER BY     InputFormatId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="InputFormatId" ColumnName="InputFormatId" DataSourceName="MedicalCheckup.dbo.InputFormatSetting" DataTypeServer="smallint" DbType="Int32" Direction="Input" ParameterName="@InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DataTransformedDataTableAdapter" GeneratorDataComponentClassName="DataTransformedDataTableAdapter" Name="DataTransformedData" UserDataComponentName="DataTransformedDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          DataTransformedSetting.MedicalCheckNo, DataTransformedSetting.ItemNo, 
                      DataTransformedSetting.TransId, DataTransformedMaster.DispData, 
                      DataTransformedMaster.TransformedData
FROM            DataTransformedSetting INNER JOIN
                      DataTransformedMaster ON 
                      DataTransformedMaster.TransId = DataTransformedSetting.TransId
ORDER BY     DataTransformedSetting.MedicalCheckNo, DataTransformedSetting.ItemNo, 
                      DataTransformedSetting.TransId</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="TransId" DataSetColumn="TransId" />
              <Mapping SourceColumn="DispData" DataSetColumn="DispData" />
              <Mapping SourceColumn="TransformedData" DataSetColumn="TransformedData" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          DataTransformedSetting.MedicalCheckNo, DataTransformedSetting.ItemNo, 
                      DataTransformedSetting.TransId, DataTransformedMaster.DispData, 
                      DataTransformedMaster.TransformedData
FROM            DataTransformedSetting INNER JOIN
                      DataTransformedMaster ON 
                      DataTransformedMaster.TransId = DataTransformedSetting.TransId
WHERE           (DataTransformedSetting.MedicalCheckNo = @MedicalCheckNo) AND 
                      (DataTransformedSetting.ItemNo = @ItemNo)
ORDER BY     DataTransformedSetting.MedicalCheckNo, DataTransformedSetting.ItemNo, 
                      DataTransformedSetting.TransId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.DataTransformedSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.DataTransformedSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="DispChangeDataTableAdapter" GeneratorDataComponentClassName="DispChangeDataTableAdapter" Name="DispChangeData" UserDataComponentName="DispChangeDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId, DispChangeMaster.BeforeData, 
                      DispChangeMaster.AfterData
FROM            DispChangeSetteing INNER JOIN
                      DispChangeMaster ON 
                      DispChangeMaster.DispChangeId = DispChangeSetteing.DispChangeId
ORDER BY     DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="DispChangeId" DataSetColumn="DispChangeId" />
              <Mapping SourceColumn="BeforeData" DataSetColumn="BeforeData" />
              <Mapping SourceColumn="AfterData" DataSetColumn="AfterData" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId, DispChangeMaster.BeforeData, 
                      DispChangeMaster.AfterData
FROM            DispChangeSetteing INNER JOIN
                      DispChangeMaster ON 
                      DispChangeMaster.DispChangeId = DispChangeSetteing.DispChangeId
WHERE           (DispChangeSetteing.MedicalCheckNo = @MedicalCheckNo) AND 
                      (DispChangeSetteing.ItemNo = @ItemNo) AND 
                      (DispChangeSetteing.DispChangeId = @DispChangeId)
ORDER BY     DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.DispChangeSetteing" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.DispChangeSetteing" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="DispChangeId" ColumnName="DispChangeId" DataSourceName="MedicalCheckup.dbo.DispChangeSetteing" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@DispChangeId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="DispChangeId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByItemNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByItemNo" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByItemNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId, DispChangeMaster.BeforeData, 
                      DispChangeMaster.AfterData
FROM            DispChangeSetteing INNER JOIN
                      DispChangeMaster ON 
                      DispChangeMaster.DispChangeId = DispChangeSetteing.DispChangeId
WHERE           (DispChangeSetteing.MedicalCheckNo = @MedicalCheckNo) AND 
                      (DispChangeSetteing.ItemNo = @ItemNo)
ORDER BY     DispChangeSetteing.MedicalCheckNo, DispChangeSetteing.ItemNo, 
                      DispChangeSetteing.DispChangeId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.DispChangeSetteing" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.DispChangeSetteing" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MeDataRelationalTableAdapter" GeneratorDataComponentClassName="MeDataRelationalTableAdapter" Name="MeDataRelational" UserDataComponentName="MeDataRelationalTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MeDataRelational" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [MeDataRelational] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [MeDataRelational] ([MedicalCheckNo], [ItemNo], [MeItemCode]) VALUES (@MedicalCheckNo, @ItemNo, @MeItemCode)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MeItemCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MeItemCode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, MeItemCode
FROM            MeDataRelational
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [MeDataRelational] SET [MedicalCheckNo] = @MedicalCheckNo, [ItemNo] = @ItemNo, [MeItemCode] = @MeItemCode WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MeItemCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MeItemCode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="MeItemCode" DataSetColumn="MeItemCode" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MeDataRelational" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, MeItemCode
FROM            MeDataRelational
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MeDataRelational" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MeDataRelational" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByItem" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByItem" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByItem">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, MeItemCode
FROM            MeDataRelational
WHERE           (MedicalCheckNo = @MedicalCheckNo) AND (ItemNo = @ItemNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MeDataRelational" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.MeDataRelational" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MeDataRelational" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByMeCode" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy2" GeneratorSourceName="FillByMeCode" GetMethodModifier="Public" GetMethodName="GetDataBy2" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy2" UserSourceName="FillByMeCode">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, MeItemCode
FROM            MeDataRelational
WHERE           (MeItemCode = @MeItemNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="True" AutogeneratedName="MeItemNo" ColumnName="MeItemCode" DataSourceName="MedicalCheckup.dbo.MeDataRelational" DataTypeServer="nvarchar(8)" DbType="String" Direction="Input" ParameterName="@MeItemNo" Precision="0" ProviderType="NVarChar" Scale="0" Size="8" SourceColumn="MeItemCode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MedicalItemSettingTableAdapter" GeneratorDataComponentClassName="MedicalItemSettingTableAdapter" Name="MedicalItemSetting" UserDataComponentName="MedicalItemSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [MedicalItemSetting] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [MedicalItemSetting] ([MedicalCheckNo], [ItemNo], [InputMode], [MaxData], [MinData], [GapData], [CheckEmpty], [InputFormatId], [DispChange], [DispFormat], [WarningBackColor], [WarningForeColor], [ClickId]) VALUES (@MedicalCheckNo, @ItemNo, @InputMode, @MaxData, @MinData, @GapData, @CheckEmpty, @InputFormatId, @DispChange, @DispFormat, @WarningBackColor, @WarningForeColor, @ClickId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MaxData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MaxData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MinData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MinData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GapData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GapData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CheckEmpty" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CheckEmpty" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispChange" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispChange" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispFormat" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispFormat" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@WarningBackColor" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="WarningBackColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@WarningForeColor" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="WarningForeColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ClickId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClickId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, InputMode, MaxData, MinData, GapData, 
                      CheckEmpty, InputFormatId, DispChange, DispFormat, WarningBackColor, 
                      WarningForeColor, ClickId
FROM            MedicalItemSetting
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [MedicalItemSetting] SET [MedicalCheckNo] = @MedicalCheckNo, [ItemNo] = @ItemNo, [InputMode] = @InputMode, [MaxData] = @MaxData, [MinData] = @MinData, [GapData] = @GapData, [CheckEmpty] = @CheckEmpty, [InputFormatId] = @InputFormatId, [DispChange] = @DispChange, [DispFormat] = @DispFormat, [WarningBackColor] = @WarningBackColor, [WarningForeColor] = @WarningForeColor, [ClickId] = @ClickId WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MaxData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MaxData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MinData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MinData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GapData" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GapData" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CheckEmpty" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CheckEmpty" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InputFormatId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InputFormatId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispChange" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispChange" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispFormat" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispFormat" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@WarningBackColor" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="WarningBackColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@WarningForeColor" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="WarningForeColor" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ClickId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ClickId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="InputMode" DataSetColumn="InputMode" />
              <Mapping SourceColumn="MaxData" DataSetColumn="MaxData" />
              <Mapping SourceColumn="MinData" DataSetColumn="MinData" />
              <Mapping SourceColumn="GapData" DataSetColumn="GapData" />
              <Mapping SourceColumn="CheckEmpty" DataSetColumn="CheckEmpty" />
              <Mapping SourceColumn="InputFormatId" DataSetColumn="InputFormatId" />
              <Mapping SourceColumn="DispChange" DataSetColumn="DispChange" />
              <Mapping SourceColumn="DispFormat" DataSetColumn="DispFormat" />
              <Mapping SourceColumn="WarningBackColor" DataSetColumn="WarningBackColor" />
              <Mapping SourceColumn="WarningForeColor" DataSetColumn="WarningForeColor" />
              <Mapping SourceColumn="ClickId" DataSetColumn="ClickId" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, InputMode, MaxData, MinData, GapData, 
                      CheckEmpty, InputFormatId, DispChange, DispFormat, WarningBackColor, 
                      WarningForeColor, ClickId
FROM            MedicalItemSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo) AND (ItemNo = @ItemNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.MedicalItemSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByNo" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, ItemNo, InputMode, MaxData, MinData, GapData, 
                      CheckEmpty, InputFormatId, DispChange, DispFormat, WarningBackColor, 
                      WarningForeColor, ClickId
FROM            MedicalItemSetting
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo, ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillForSetting" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy2" GeneratorSourceName="FillForSetting" GetMethodModifier="Public" GetMethodName="GetDataBy2" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy2" UserSourceName="FillForSetting">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MI.MedicalCheckNo, MI.ItemNo, MI.InputMode, MI.MaxData, MI.MinData, 
                      MI.GapData, MI.CheckEmpty, MI.InputFormatId, MI.DispChange, MI.DispFormat, 
                      MI.WarningBackColor, MI.WarningForeColor, MI.ClickId, 
                      InputFormatSetting.InputFormat
FROM            MedicalItemSetting AS MI INNER JOIN
                      InputFormatSetting ON 
                      MI.InputFormatId = InputFormatSetting.InputFormatId
WHERE           EXISTS
                          (SELECT          FormId, MedicalCheckNo, ItemNo, DataType, 
                                                    DataIndex, DispFlg, ReadOnly
                             FROM             FormDispDetail AS FD
                             WHERE            (MI.MedicalCheckNo = MedicalCheckNo) AND 
                                                    (MI.ItemNo = ItemNo) AND (DispFlg = 1)) AND 
                      (MI.MedicalCheckNo = @MedicalCheckNo)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="FormDispItemTableAdapter" GeneratorDataComponentClassName="FormDispItemTableAdapter" Name="FormDispItem" UserDataComponentName="FormDispItemTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, MedicalItemSetting.InputMode, MedicalItemSetting.MaxData, MedicalItemSetting.MinData, 
                        MedicalItemSetting.GapData, MedicalItemSetting.CheckEmpty, MedicalItemSetting.InputFormatId, MedicalItemSetting.DispChange, MedicalItemSetting.DispFormat, 
                        MedicalItemSetting.WarningBackColor, MedicalItemSetting.WarningForeColor, MedicalItemSetting.ClickId, FormDispDetail.FormId, FormDispDetail.DataType, FormDispDetail.DataIndex, 
                        FormDispDetail.DispFlg, FormDispDetail.ReadOnly, FormDispDetail.TitleSelect, InputFormatSetting.InputFormat, FormDispDetail.ComponentType
FROM              MedicalItemSetting INNER JOIN
                        FormDispDetail ON MedicalItemSetting.MedicalCheckNo = FormDispDetail.MedicalCheckNo AND MedicalItemSetting.ItemNo = FormDispDetail.ItemNo LEFT OUTER JOIN
                        InputFormatSetting ON MedicalItemSetting.InputFormatId = InputFormatSetting.InputFormatId
ORDER BY       FormDispDetail.FormId, MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="InputMode" DataSetColumn="InputMode" />
              <Mapping SourceColumn="MaxData" DataSetColumn="MaxData" />
              <Mapping SourceColumn="MinData" DataSetColumn="MinData" />
              <Mapping SourceColumn="GapData" DataSetColumn="GapData" />
              <Mapping SourceColumn="CheckEmpty" DataSetColumn="CheckEmpty" />
              <Mapping SourceColumn="InputFormatId" DataSetColumn="InputFormatId" />
              <Mapping SourceColumn="DispChange" DataSetColumn="DispChange" />
              <Mapping SourceColumn="DispFormat" DataSetColumn="DispFormat" />
              <Mapping SourceColumn="WarningBackColor" DataSetColumn="WarningBackColor" />
              <Mapping SourceColumn="WarningForeColor" DataSetColumn="WarningForeColor" />
              <Mapping SourceColumn="ClickId" DataSetColumn="ClickId" />
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="DataType" DataSetColumn="DataType" />
              <Mapping SourceColumn="DataIndex" DataSetColumn="DataIndex" />
              <Mapping SourceColumn="DispFlg" DataSetColumn="DispFlg" />
              <Mapping SourceColumn="ReadOnly" DataSetColumn="ReadOnly" />
              <Mapping SourceColumn="TitleSelect" DataSetColumn="TitleSelect" />
              <Mapping SourceColumn="InputFormat" DataSetColumn="InputFormat" />
              <Mapping SourceColumn="ComponentType" DataSetColumn="ComponentType" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, MedicalItemSetting.InputMode, MedicalItemSetting.MaxData, MedicalItemSetting.MinData, 
                        MedicalItemSetting.GapData, MedicalItemSetting.CheckEmpty, MedicalItemSetting.InputFormatId, MedicalItemSetting.DispChange, MedicalItemSetting.DispFormat, 
                        MedicalItemSetting.WarningBackColor, MedicalItemSetting.WarningForeColor, MedicalItemSetting.ClickId, FormDispDetail.FormId, FormDispDetail.DataType, FormDispDetail.DataIndex, 
                        FormDispDetail.DispFlg, FormDispDetail.ReadOnly, FormDispDetail.TitleSelect, InputFormatSetting.InputFormat, FormDispDetail.ComponentType
FROM              MedicalItemSetting INNER JOIN
                        FormDispDetail ON MedicalItemSetting.MedicalCheckNo = FormDispDetail.MedicalCheckNo AND MedicalItemSetting.ItemNo = FormDispDetail.ItemNo LEFT OUTER JOIN
                        InputFormatSetting ON MedicalItemSetting.InputFormatId = InputFormatSetting.InputFormatId
WHERE             (FormDispDetail.FormId = @FormId)
ORDER BY       FormDispDetail.FormId, MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByDataType" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByDataType" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByDataType">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, MedicalItemSetting.InputMode, MedicalItemSetting.MaxData, MedicalItemSetting.MinData, 
                        MedicalItemSetting.GapData, MedicalItemSetting.CheckEmpty, MedicalItemSetting.InputFormatId, MedicalItemSetting.DispChange, MedicalItemSetting.DispFormat, 
                        MedicalItemSetting.WarningBackColor, MedicalItemSetting.WarningForeColor, MedicalItemSetting.ClickId, FormDispDetail.FormId, FormDispDetail.DataType, FormDispDetail.DataIndex, 
                        FormDispDetail.DispFlg, FormDispDetail.ReadOnly, FormDispDetail.TitleSelect, InputFormatSetting.InputFormat, FormDispDetail.ComponentType
FROM              MedicalItemSetting INNER JOIN
                        FormDispDetail ON MedicalItemSetting.MedicalCheckNo = FormDispDetail.MedicalCheckNo AND MedicalItemSetting.ItemNo = FormDispDetail.ItemNo LEFT OUTER JOIN
                        InputFormatSetting ON MedicalItemSetting.InputFormatId = InputFormatSetting.InputFormatId
WHERE             (FormDispDetail.FormId = @FormId) AND (FormDispDetail.DataType = @DataType)
ORDER BY       FormDispDetail.FormId, MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="DataType" ColumnName="DataType" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillDataType" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy2" GeneratorSourceName="FillDataType" GetMethodModifier="Public" GetMethodName="GetDataBy2" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy2" UserSourceName="FillDataType">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo, MedicalItemSetting.InputMode, MedicalItemSetting.MaxData, MedicalItemSetting.MinData, 
                        MedicalItemSetting.GapData, MedicalItemSetting.CheckEmpty, MedicalItemSetting.InputFormatId, MedicalItemSetting.DispChange, MedicalItemSetting.DispFormat, 
                        MedicalItemSetting.WarningBackColor, MedicalItemSetting.WarningForeColor, MedicalItemSetting.ClickId, FormDispDetail.FormId, FormDispDetail.DataType, FormDispDetail.DataIndex, 
                        FormDispDetail.DispFlg, FormDispDetail.ReadOnly, FormDispDetail.TitleSelect, InputFormatSetting.InputFormat, FormDispDetail.ComponentType
FROM              MedicalItemSetting INNER JOIN
                        FormDispDetail ON MedicalItemSetting.MedicalCheckNo = FormDispDetail.MedicalCheckNo AND MedicalItemSetting.ItemNo = FormDispDetail.ItemNo LEFT OUTER JOIN
                        InputFormatSetting ON MedicalItemSetting.InputFormatId = InputFormatSetting.InputFormatId
WHERE             (MedicalItemSetting.MedicalCheckNo = @MedicalCheckNo) AND (MedicalItemSetting.ItemNo = @ItemNo) AND (FormDispDetail.DataType = @DataType)
ORDER BY       FormDispDetail.FormId, MedicalItemSetting.MedicalCheckNo, MedicalItemSetting.ItemNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.MedicalItemSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="DataType" ColumnName="DataType" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="FormDispModeTableAdapter" GeneratorDataComponentClassName="FormDispModeTableAdapter" Name="FormDispMode" UserDataComponentName="FormDispModeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TestDatabaseK.dbo.FormDispMode" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [FormDispMode] WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [FormDispMode] ([MedicalCheckNo], [DispMode], [PluralMode], [FormId], [InitialSelectFlg]) VALUES (@MedicalCheckNo, @DispMode, @PluralMode, @FormId, @InitialSelectFlg)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@PluralMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="PluralMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InitialSelectFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InitialSelectFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, DispMode, PluralMode, FormId, InitialSelectFlg
FROM            FormDispMode
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [FormDispMode] SET [MedicalCheckNo] = @MedicalCheckNo, [DispMode] = @DispMode, [PluralMode] = @PluralMode, [FormId] = @FormId, [InitialSelectFlg] = @InitialSelectFlg WHERE (([MedicalCheckNo] = @Original_MedicalCheckNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@PluralMode" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="PluralMode" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@InitialSelectFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="InitialSelectFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="DispMode" DataSetColumn="DispMode" />
              <Mapping SourceColumn="PluralMode" DataSetColumn="PluralMode" />
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="InitialSelectFlg" DataSetColumn="InitialSelectFlg" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="TestDatabaseK.dbo.FormDispMode" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          MedicalCheckNo, DispMode, PluralMode, FormId, InitialSelectFlg
FROM            FormDispMode
WHERE           (MedicalCheckNo = @MedicalCheckNo)
ORDER BY     MedicalCheckNo</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="TestDatabaseK.dbo.FormDispMode" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="FormDispDetailTableAdapter" GeneratorDataComponentClassName="FormDispDetailTableAdapter" Name="FormDispDetail" UserDataComponentName="FormDispDetailTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.FormDispDetail" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [FormDispDetail] WHERE (([FormId] = @Original_FormId) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo) AND ([DataType] = @Original_DataType) AND ([DataIndex] = @Original_DataIndex))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_DataIndex" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataIndex" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [FormDispDetail] ([FormId], [MedicalCheckNo], [ItemNo], [DataType], [DataIndex], [DispFlg], [ReadOnly], [TitleSelect], [ComponentType]) VALUES (@FormId, @MedicalCheckNo, @ItemNo, @DataType, @DataIndex, @DispFlg, @ReadOnly, @TitleSelect, @ComponentType)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DataIndex" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataIndex" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ReadOnly" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ReadOnly" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TitleSelect" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TitleSelect" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ComponentType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ComponentType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            FormId, MedicalCheckNo, ItemNo, DataType, DataIndex, DispFlg, ReadOnly, TitleSelect, ComponentType
FROM              FormDispDetail
ORDER BY       FormId, MedicalCheckNo, ItemNo, DataType, DataIndex</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [FormDispDetail] SET [FormId] = @FormId, [MedicalCheckNo] = @MedicalCheckNo, [ItemNo] = @ItemNo, [DataType] = @DataType, [DataIndex] = @DataIndex, [DispFlg] = @DispFlg, [ReadOnly] = @ReadOnly, [TitleSelect] = @TitleSelect, [ComponentType] = @ComponentType WHERE (([FormId] = @Original_FormId) AND ([MedicalCheckNo] = @Original_MedicalCheckNo) AND ([ItemNo] = @Original_ItemNo) AND ([DataType] = @Original_DataType) AND ([DataIndex] = @Original_DataIndex))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DataIndex" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataIndex" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@DispFlg" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DispFlg" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ReadOnly" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ReadOnly" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@TitleSelect" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="TitleSelect" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="True" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@ComponentType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ComponentType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_DataIndex" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="DataIndex" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="DataType" DataSetColumn="DataType" />
              <Mapping SourceColumn="DataIndex" DataSetColumn="DataIndex" />
              <Mapping SourceColumn="DispFlg" DataSetColumn="DispFlg" />
              <Mapping SourceColumn="ReadOnly" DataSetColumn="ReadOnly" />
              <Mapping SourceColumn="TitleSelect" DataSetColumn="TitleSelect" />
              <Mapping SourceColumn="ComponentType" DataSetColumn="ComponentType" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.FormDispDetail" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            ComponentType, DataIndex, DataType, DispFlg, FormId, ItemNo, MedicalCheckNo, ReadOnly, TitleSelect
FROM              FormDispDetail
WHERE             (FormId = @FormId) AND (MedicalCheckNo = @MedicalCheckNo) AND (ItemNo = @ItemNo) AND (DataType = @DataType)
ORDER BY       FormId, MedicalCheckNo, ItemNo, DataType, DataIndex</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="MedicalCheckNo" ColumnName="MedicalCheckNo" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@MedicalCheckNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="MedicalCheckNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="ItemNo" ColumnName="ItemNo" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@ItemNo" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="ItemNo" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="DataType" ColumnName="DataType" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckup.dbo.FormDispDetail" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByDataType" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByDataType" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByDataType">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            ComponentType, DataIndex, DataType, DispFlg, FormId, ItemNo, MedicalCheckNo, ReadOnly, TitleSelect
FROM              FormDispDetail
WHERE             (FormId = @FormId) AND (DataType = @DataType)
ORDER BY       FormId, MedicalCheckNo, ItemNo, DataType, DataIndex</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="DataType" ColumnName="DataType" DataSourceName="MedicalCheckup.dbo.FormDispDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@DataType" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="DataType" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="EyeInitialSelectTableAdapter" GeneratorDataComponentClassName="EyeInitialSelectTableAdapter" Name="EyeInitialSelect" UserDataComponentName="EyeInitialSelectTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByMedicalCheckNo" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="FillByMedicalCheckNo" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="FillByMedicalCheckNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.InitialId, EI.MedicalCheckNo, EI.ItemNo, EI.Priority
FROM            EyeSetting AS ES INNER JOIN
                      EyeInitialSelect AS EI ON ES.InitialId = EI.InitialId
WHERE           (ES.FormId = @FormId)
ORDER BY     ES.FormId, EI.Priority</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="InitialId" DataSetColumn="InitialId" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
              <Mapping SourceColumn="Priority" DataSetColumn="Priority" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.InitialId, EI.MedicalCheckNo, EI.ItemNo, EI.Priority
FROM            EyeSetting AS ES INNER JOIN
                      EyeInitialSelect AS EI ON ES.InitialId = EI.InitialId
ORDER BY     ES.FormId, EI.Priority</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.InitialId, EI.MedicalCheckNo, EI.ItemNo, EI.Priority
FROM            EyeSetting AS ES INNER JOIN
                      EyeInitialSelect AS EI ON ES.InitialId = EI.InitialId
WHERE           (ES.InitialId = @InitialId)
ORDER BY     ES.FormId, EI.Priority</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="True" AutogeneratedName="InitialId" ColumnName="InitialId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@InitialId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="InitialId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="EyeGroupSelectTableAdapter" GeneratorDataComponentClassName="EyeGroupSelectTableAdapter" Name="EyeGroupSelect" UserDataComponentName="EyeGroupSelectTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeGroupId, EG.GroupId, EG.MedicalCheckNo, EG.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeGroupSelect AS EG ON ES.EyeGroupId = EG.EyeGroupId
WHERE           (ES.EyeGroupId = @EyeGroupId)
ORDER BY     ES.FormId, EG.EyeGroupId, EG.GroupId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="True" AutogeneratedName="EyeGroupId" ColumnName="EyeGroupId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@EyeGroupId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="EyeGroupId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="EyeGroupId" DataSetColumn="EyeGroupId" />
              <Mapping SourceColumn="GroupId" DataSetColumn="GroupId" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeGroupId, EG.GroupId, EG.MedicalCheckNo, EG.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeGroupSelect AS EG ON ES.EyeGroupId = EG.EyeGroupId
ORDER BY     ES.FormId, EG.EyeGroupId, EG.GroupId</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByMedicalCheckNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillByMedicalCheckNo" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillByMedicalCheckNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeGroupId, EG.GroupId, EG.MedicalCheckNo, EG.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeGroupSelect AS EG ON ES.EyeGroupId = EG.EyeGroupId
WHERE           (ES.FormId = @FormId)
ORDER BY     ES.FormId, EG.EyeGroupId, EG.GroupId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="EyeControlTableAdapter" GeneratorDataComponentClassName="EyeControlTableAdapter" Name="EyeControl" UserDataComponentName="EyeControlTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeControlId, EC.ControlId, EC.MedicalCheckNo, EC.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeControl AS EC ON ES.EyeControlId = EC.EyeControlId
ORDER BY     ES.FormId, EC.EyeControlId, EC.ControlId</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="EyeControlId" DataSetColumn="EyeControlId" />
              <Mapping SourceColumn="ControlId" DataSetColumn="ControlId" />
              <Mapping SourceColumn="MedicalCheckNo" DataSetColumn="MedicalCheckNo" />
              <Mapping SourceColumn="ItemNo" DataSetColumn="ItemNo" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeControlId, EC.ControlId, EC.MedicalCheckNo, EC.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeControl AS EC ON ES.EyeControlId = EC.EyeControlId
WHERE           (ES.EyeControlId = @EyeControlId)
ORDER BY     ES.FormId, EC.EyeControlId, EC.ControlId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="True" AutogeneratedName="EyeControlId" ColumnName="EyeControlId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@EyeControlId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="EyeControlId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByMedicalCheckNo" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillByMedicalCheckNo" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillByMedicalCheckNo">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          ES.FormId, ES.EyeControlId, EC.ControlId, EC.MedicalCheckNo, EC.ItemNo
FROM            EyeSetting AS ES INNER JOIN
                      EyeControl AS EC ON ES.EyeControlId = EC.EyeControlId
WHERE           (ES.FormId = @FormId)
ORDER BY     ES.FormId, EC.EyeControlId, EC.ControlId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="MedicalCheckup.dbo.EyeSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="FontSettingTableAdapter" GeneratorDataComponentClassName="FontSettingTableAdapter" Name="FontSetting" UserDataComponentName="FontSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="Ar1000kPackageTest.dbo.FontSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [dbo].[FontSetting] WHERE (([FormId] = @Original_FormId) AND ([Label] = @Original_Label) AND ([FontName] = @Original_FontName) AND ([FontSize] = @Original_FontSize) AND ([FontStyle] = @Original_FontStyle) AND ([FontUnit] = @Original_FontUnit) AND ([CharSet] = @Original_CharSet))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Label" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Label" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FontSize" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FontSize" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontStyle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontStyle" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontUnit" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontUnit" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_CharSet" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CharSet" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [dbo].[FontSetting] ([FormId], [Label], [FontName], [FontSize], [FontStyle], [FontUnit], [CharSet]) VALUES (@FormId, @Label, @FontName, @FontSize, @FontStyle, @FontUnit, @CharSet);
SELECT FormId, Label, FontName, FontSize, FontStyle, FontUnit, CharSet FROM FontSetting WHERE (FormId = @FormId) AND (Label = @Label)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Label" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Label" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FontSize" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FontSize" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontStyle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontStyle" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontUnit" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontUnit" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CharSet" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CharSet" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT FormId, Label, FontName, FontSize, FontStyle, FontUnit, CharSet FROM dbo.FontSetting</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [dbo].[FontSetting] SET [FormId] = @FormId, [Label] = @Label, [FontName] = @FontName, [FontSize] = @FontSize, [FontStyle] = @FontStyle, [FontUnit] = @FontUnit, [CharSet] = @CharSet WHERE (([FormId] = @Original_FormId) AND ([Label] = @Original_Label) AND ([FontName] = @Original_FontName) AND ([FontSize] = @Original_FontSize) AND ([FontStyle] = @Original_FontStyle) AND ([FontUnit] = @Original_FontUnit) AND ([CharSet] = @Original_CharSet));
SELECT FormId, Label, FontName, FontSize, FontStyle, FontUnit, CharSet FROM FontSetting WHERE (FormId = @FormId) AND (Label = @Label)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Label" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Label" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@FontSize" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FontSize" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontStyle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontStyle" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FontUnit" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontUnit" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@CharSet" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CharSet" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Label" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Label" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_FontSize" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="FontSize" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontStyle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontStyle" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FontUnit" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FontUnit" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_CharSet" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="CharSet" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="Label" DataSetColumn="Label" />
              <Mapping SourceColumn="FontName" DataSetColumn="FontName" />
              <Mapping SourceColumn="FontSize" DataSetColumn="FontSize" />
              <Mapping SourceColumn="FontStyle" DataSetColumn="FontStyle" />
              <Mapping SourceColumn="FontUnit" DataSetColumn="FontUnit" />
              <Mapping SourceColumn="CharSet" DataSetColumn="CharSet" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="Ar1000kPackageTest.dbo.FontSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          FormId, Label, FontName, FontSize, FontStyle, FontUnit, CharSet
FROM            FontSetting
WHERE           (FormId = @FormId)
ORDER BY     FormId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="Ar1000kPackageTest.dbo.FontSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="Ar1000kPackageTest.dbo.FontSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy1" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy1" GeneratorSourceName="FillBy1" GetMethodModifier="Public" GetMethodName="GetDataBy1" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy1" UserSourceName="FillBy1">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT          FormId, Label, FontName, FontSize, FontStyle, FontUnit, CharSet
FROM            FontSetting
WHERE           (FormId = @FormId) AND (Label = @Label)
ORDER BY     FormId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormId" DataSourceName="Ar1000kPackageTest.dbo.FontSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormId" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="Label" ColumnName="Label" DataSourceName="Ar1000kPackageTest.dbo.FontSetting" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@Label" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="Label" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CustomizeButtonSettingTableAdapter" GeneratorDataComponentClassName="CustomizeButtonSettingTableAdapter" Name="CustomizeButtonSetting" UserDataComponentName="CustomizeButtonSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckupV3.dbo.CustomizeButtonSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            FormId, ButtonID, PageNo, DispName, DispShape, DispPosition, DispSize, DispVisible, DispColor, AutoAction
FROM              CustomizeButtonSetting</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormId" DataSetColumn="FormId" />
              <Mapping SourceColumn="PageNo" DataSetColumn="PageNo" />
              <Mapping SourceColumn="DispName" DataSetColumn="DispName" />
              <Mapping SourceColumn="DispPosition" DataSetColumn="DispPosition" />
              <Mapping SourceColumn="DispSize" DataSetColumn="DispSize" />
              <Mapping SourceColumn="DispVisible" DataSetColumn="DispVisible" />
              <Mapping SourceColumn="AutoAction" DataSetColumn="AutoAction" />
              <Mapping SourceColumn="ButtonID" DataSetColumn="ButtonID" />
              <Mapping SourceColumn="DispShape" DataSetColumn="DispShape" />
              <Mapping SourceColumn="DispColor" DataSetColumn="DispColor" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CustomizeButtonDetailTableAdapter" GeneratorDataComponentClassName="CustomizeButtonDetailTableAdapter" Name="CustomizeButtonDetail" UserDataComponentName="CustomizeButtonDetailTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckupV3.dbo.CustomizeButtonDetail" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT            FunctionID, FormID, ButtonID, Priority, FunctionType, DispData, DispItemIndex, CalcItemFlg1, CalcItemIndex1, CalcItemFlg2, CalcItemIndex2, CalcItemFlg3, CalcItemIndex3, 
                        CalcItemFlg4, CalcItemIndex4, CalcItemFlg5, CalcItemIndex5
FROM              CustomizeButtonDetail
ORDER BY       FormID, Priority</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FormID" DataSetColumn="FormID" />
              <Mapping SourceColumn="Priority" DataSetColumn="Priority" />
              <Mapping SourceColumn="FunctionID" DataSetColumn="FunctionID" />
              <Mapping SourceColumn="DispData" DataSetColumn="DispData" />
              <Mapping SourceColumn="DispItemIndex" DataSetColumn="DispItemIndex" />
              <Mapping SourceColumn="CalcItemFlg1" DataSetColumn="CalcItemFlg1" />
              <Mapping SourceColumn="CalcItemIndex1" DataSetColumn="CalcItemIndex1" />
              <Mapping SourceColumn="CalcItemFlg2" DataSetColumn="CalcItemFlg2" />
              <Mapping SourceColumn="CalcItemIndex2" DataSetColumn="CalcItemIndex2" />
              <Mapping SourceColumn="CalcItemFlg3" DataSetColumn="CalcItemFlg3" />
              <Mapping SourceColumn="CalcItemIndex3" DataSetColumn="CalcItemIndex3" />
              <Mapping SourceColumn="CalcItemFlg4" DataSetColumn="CalcItemFlg4" />
              <Mapping SourceColumn="CalcItemIndex4" DataSetColumn="CalcItemIndex4" />
              <Mapping SourceColumn="CalcItemFlg5" DataSetColumn="CalcItemFlg5" />
              <Mapping SourceColumn="CalcItemIndex5" DataSetColumn="CalcItemIndex5" />
              <Mapping SourceColumn="ButtonID" DataSetColumn="ButtonID" />
              <Mapping SourceColumn="FunctionType" DataSetColumn="FunctionType" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="MedicalCheckupConnectionString (Web.config)" DbObjectName="MedicalCheckupV3.dbo.CustomizeButtonDetail" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT ButtonID, CalcItemFlg1, CalcItemFlg2, CalcItemFlg3, CalcItemFlg4, CalcItemFlg5, CalcItemIndex1, CalcItemIndex2, CalcItemIndex3, CalcItemIndex4, CalcItemIndex5, DispData, DispItemIndex, FormID, FunctionID, FunctionType, Priority FROM CustomizeButtonDetail WHERE (FormID = @FormId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="FormId" ColumnName="FormID" DataSourceName="MedicalCheckupV3.dbo.CustomizeButtonDetail" DataTypeServer="smallint" DbType="Int16" Direction="Input" ParameterName="@FormId" Precision="0" ProviderType="SmallInt" Scale="0" Size="2" SourceColumn="FormID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DispSettingDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DispSettingDataSet" msprop:Generator_DataSetName="DispSettingDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="MedicalItemList" msprop:Generator_UserTableName="MedicalItemList" msprop:Generator_RowDeletedName="MedicalItemListRowDeleted" msprop:Generator_RowChangedName="MedicalItemListRowChanged" msprop:Generator_RowClassName="MedicalItemListRow" msprop:Generator_RowChangingName="MedicalItemListRowChanging" msprop:Generator_RowEvArgName="MedicalItemListRowChangeEvent" msprop:Generator_RowEvHandlerName="MedicalItemListRowChangeEventHandler" msprop:Generator_TableClassName="MedicalItemListDataTable" msprop:Generator_TableVarName="tableMedicalItemList" msprop:Generator_RowDeletingName="MedicalItemListRowDeleting" msprop:Generator_TablePropName="MedicalItemList">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="ItemName" msprop:Generator_UserColumnName="ItemName" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="ItemName" msprop:Generator_ColumnPropNameInTable="ItemNameColumn" msprop:Generator_ColumnVarNameInTable="columnItemName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PluralDispSetting" msprop:Generator_UserTableName="PluralDispSetting" msprop:Generator_RowDeletedName="PluralDispSettingRowDeleted" msprop:Generator_RowChangedName="PluralDispSettingRowChanged" msprop:Generator_RowClassName="PluralDispSettingRow" msprop:Generator_RowChangingName="PluralDispSettingRowChanging" msprop:Generator_RowEvArgName="PluralDispSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="PluralDispSettingRowChangeEventHandler" msprop:Generator_TableClassName="PluralDispSettingDataTable" msprop:Generator_TableVarName="tablePluralDispSetting" msprop:Generator_RowDeletingName="PluralDispSettingRowDeleting" msprop:Generator_TablePropName="PluralDispSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="DispCount" msprop:Generator_UserColumnName="DispCount" msprop:Generator_ColumnVarNameInTable="columnDispCount" msprop:Generator_ColumnPropNameInRow="DispCount" msprop:Generator_ColumnPropNameInTable="DispCountColumn" type="xs:short" />
              <xs:element name="SelectCount" msprop:Generator_UserColumnName="SelectCount" msprop:Generator_ColumnVarNameInTable="columnSelectCount" msprop:Generator_ColumnPropNameInRow="SelectCount" msprop:Generator_ColumnPropNameInTable="SelectCountColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PluralSetting" msprop:Generator_UserTableName="PluralSetting" msprop:Generator_RowDeletedName="PluralSettingRowDeleted" msprop:Generator_RowChangedName="PluralSettingRowChanged" msprop:Generator_RowClassName="PluralSettingRow" msprop:Generator_RowChangingName="PluralSettingRowChanging" msprop:Generator_RowEvArgName="PluralSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="PluralSettingRowChangeEventHandler" msprop:Generator_TableClassName="PluralSettingDataTable" msprop:Generator_TableVarName="tablePluralSetting" msprop:Generator_RowDeletingName="PluralSettingRowDeleting" msprop:Generator_TablePropName="PluralSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ChildMedicalCheckNo1" msprop:Generator_UserColumnName="ChildMedicalCheckNo1" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo1" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo1" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo1Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo2" msprop:Generator_UserColumnName="ChildMedicalCheckNo2" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo2" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo2" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo2Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo3" msprop:Generator_UserColumnName="ChildMedicalCheckNo3" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo3" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo3" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo3Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo4" msprop:Generator_UserColumnName="ChildMedicalCheckNo4" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo4" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo4" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo4Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo5" msprop:Generator_UserColumnName="ChildMedicalCheckNo5" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo5" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo5" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo5Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo6" msprop:Generator_UserColumnName="ChildMedicalCheckNo6" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo6" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo6" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo6Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo7" msprop:Generator_UserColumnName="ChildMedicalCheckNo7" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo7" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo7" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo7Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo8" msprop:Generator_UserColumnName="ChildMedicalCheckNo8" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo8" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo8" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo8Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo9" msprop:Generator_UserColumnName="ChildMedicalCheckNo9" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo9" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo9" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo9Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo10" msprop:Generator_UserColumnName="ChildMedicalCheckNo10" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo10" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo10" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo10Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo11" msprop:Generator_UserColumnName="ChildMedicalCheckNo11" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo11" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo11" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo11Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo12" msprop:Generator_UserColumnName="ChildMedicalCheckNo12" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo12" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo12" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo12Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo13" msprop:Generator_UserColumnName="ChildMedicalCheckNo13" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo13" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo13" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo13Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo14" msprop:Generator_UserColumnName="ChildMedicalCheckNo14" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo14" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo14" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo14Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo15" msprop:Generator_UserColumnName="ChildMedicalCheckNo15" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo15" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo15" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo15Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo16" msprop:Generator_UserColumnName="ChildMedicalCheckNo16" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo16" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo16" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo16Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo17" msprop:Generator_UserColumnName="ChildMedicalCheckNo17" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo17" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo17" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo17Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo18" msprop:Generator_UserColumnName="ChildMedicalCheckNo18" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo18" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo18" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo18Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo19" msprop:Generator_UserColumnName="ChildMedicalCheckNo19" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo19" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo19" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo19Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo20" msprop:Generator_UserColumnName="ChildMedicalCheckNo20" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo20" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo20" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo20Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo21" msprop:Generator_UserColumnName="ChildMedicalCheckNo21" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo21" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo21" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo21Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo22" msprop:Generator_UserColumnName="ChildMedicalCheckNo22" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo22" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo22" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo22Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo23" msprop:Generator_UserColumnName="ChildMedicalCheckNo23" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo23" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo23" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo23Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo24" msprop:Generator_UserColumnName="ChildMedicalCheckNo24" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo24" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo24" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo24Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo25" msprop:Generator_UserColumnName="ChildMedicalCheckNo25" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo25" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo25" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo25Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo26" msprop:Generator_UserColumnName="ChildMedicalCheckNo26" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo26" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo26" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo26Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo27" msprop:Generator_UserColumnName="ChildMedicalCheckNo27" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo27" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo27" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo27Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo28" msprop:Generator_UserColumnName="ChildMedicalCheckNo28" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo28" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo28" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo28Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo29" msprop:Generator_UserColumnName="ChildMedicalCheckNo29" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo29" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo29" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo29Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo30" msprop:Generator_UserColumnName="ChildMedicalCheckNo30" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo30" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo30" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo30Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo31" msprop:Generator_UserColumnName="ChildMedicalCheckNo31" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo31" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo31" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo31Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo32" msprop:Generator_UserColumnName="ChildMedicalCheckNo32" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo32" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo32" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo32Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo33" msprop:Generator_UserColumnName="ChildMedicalCheckNo33" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo33" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo33" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo33Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo34" msprop:Generator_UserColumnName="ChildMedicalCheckNo34" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo34" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo34" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo34Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo35" msprop:Generator_UserColumnName="ChildMedicalCheckNo35" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo35" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo35" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo35Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo36" msprop:Generator_UserColumnName="ChildMedicalCheckNo36" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo36" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo36" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo36Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo37" msprop:Generator_UserColumnName="ChildMedicalCheckNo37" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo37" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo37" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo37Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo38" msprop:Generator_UserColumnName="ChildMedicalCheckNo38" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo38" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo38" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo38Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo39" msprop:Generator_UserColumnName="ChildMedicalCheckNo39" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo39" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo39" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo39Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo40" msprop:Generator_UserColumnName="ChildMedicalCheckNo40" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo40" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo40" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo40Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo41" msprop:Generator_UserColumnName="ChildMedicalCheckNo41" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo41" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo41" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo41Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo42" msprop:Generator_UserColumnName="ChildMedicalCheckNo42" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo42" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo42" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo42Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo43" msprop:Generator_UserColumnName="ChildMedicalCheckNo43" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo43" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo43" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo43Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo44" msprop:Generator_UserColumnName="ChildMedicalCheckNo44" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo44" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo44" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo44Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo45" msprop:Generator_UserColumnName="ChildMedicalCheckNo45" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo45" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo45" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo45Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo46" msprop:Generator_UserColumnName="ChildMedicalCheckNo46" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo46" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo46" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo46Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo47" msprop:Generator_UserColumnName="ChildMedicalCheckNo47" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo47" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo47" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo47Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo48" msprop:Generator_UserColumnName="ChildMedicalCheckNo48" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo48" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo48" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo48Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo49" msprop:Generator_UserColumnName="ChildMedicalCheckNo49" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo49" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo49" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo49Column" type="xs:short" minOccurs="0" />
              <xs:element name="ChildMedicalCheckNo50" msprop:Generator_UserColumnName="ChildMedicalCheckNo50" msprop:Generator_ColumnVarNameInTable="columnChildMedicalCheckNo50" msprop:Generator_ColumnPropNameInRow="ChildMedicalCheckNo50" msprop:Generator_ColumnPropNameInTable="ChildMedicalCheckNo50Column" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="TransformedSetting" msprop:Generator_UserTableName="TransformedSetting" msprop:Generator_RowDeletedName="TransformedSettingRowDeleted" msprop:Generator_RowChangedName="TransformedSettingRowChanged" msprop:Generator_RowClassName="TransformedSettingRow" msprop:Generator_RowChangingName="TransformedSettingRowChanging" msprop:Generator_RowEvArgName="TransformedSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="TransformedSettingRowChangeEventHandler" msprop:Generator_TableClassName="TransformedSettingDataTable" msprop:Generator_TableVarName="tableTransformedSetting" msprop:Generator_RowDeletingName="TransformedSettingRowDeleting" msprop:Generator_TablePropName="TransformedSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="TransformedItemNo1" msprop:Generator_UserColumnName="TransformedItemNo1" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo1" msprop:Generator_ColumnPropNameInRow="TransformedItemNo1" msprop:Generator_ColumnPropNameInTable="TransformedItemNo1Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo2" msprop:Generator_UserColumnName="TransformedItemNo2" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo2" msprop:Generator_ColumnPropNameInRow="TransformedItemNo2" msprop:Generator_ColumnPropNameInTable="TransformedItemNo2Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo3" msprop:Generator_UserColumnName="TransformedItemNo3" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo3" msprop:Generator_ColumnPropNameInRow="TransformedItemNo3" msprop:Generator_ColumnPropNameInTable="TransformedItemNo3Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo4" msprop:Generator_UserColumnName="TransformedItemNo4" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo4" msprop:Generator_ColumnPropNameInRow="TransformedItemNo4" msprop:Generator_ColumnPropNameInTable="TransformedItemNo4Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo5" msprop:Generator_UserColumnName="TransformedItemNo5" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo5" msprop:Generator_ColumnPropNameInRow="TransformedItemNo5" msprop:Generator_ColumnPropNameInTable="TransformedItemNo5Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo6" msprop:Generator_UserColumnName="TransformedItemNo6" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo6" msprop:Generator_ColumnPropNameInRow="TransformedItemNo6" msprop:Generator_ColumnPropNameInTable="TransformedItemNo6Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo7" msprop:Generator_UserColumnName="TransformedItemNo7" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo7" msprop:Generator_ColumnPropNameInRow="TransformedItemNo7" msprop:Generator_ColumnPropNameInTable="TransformedItemNo7Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo8" msprop:Generator_UserColumnName="TransformedItemNo8" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo8" msprop:Generator_ColumnPropNameInRow="TransformedItemNo8" msprop:Generator_ColumnPropNameInTable="TransformedItemNo8Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo9" msprop:Generator_UserColumnName="TransformedItemNo9" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo9" msprop:Generator_ColumnPropNameInRow="TransformedItemNo9" msprop:Generator_ColumnPropNameInTable="TransformedItemNo9Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo10" msprop:Generator_UserColumnName="TransformedItemNo10" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo10" msprop:Generator_ColumnPropNameInRow="TransformedItemNo10" msprop:Generator_ColumnPropNameInTable="TransformedItemNo10Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo11" msprop:Generator_UserColumnName="TransformedItemNo11" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo11" msprop:Generator_ColumnPropNameInRow="TransformedItemNo11" msprop:Generator_ColumnPropNameInTable="TransformedItemNo11Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo12" msprop:Generator_UserColumnName="TransformedItemNo12" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo12" msprop:Generator_ColumnPropNameInRow="TransformedItemNo12" msprop:Generator_ColumnPropNameInTable="TransformedItemNo12Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo13" msprop:Generator_UserColumnName="TransformedItemNo13" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo13" msprop:Generator_ColumnPropNameInRow="TransformedItemNo13" msprop:Generator_ColumnPropNameInTable="TransformedItemNo13Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo14" msprop:Generator_UserColumnName="TransformedItemNo14" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo14" msprop:Generator_ColumnPropNameInRow="TransformedItemNo14" msprop:Generator_ColumnPropNameInTable="TransformedItemNo14Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo15" msprop:Generator_UserColumnName="TransformedItemNo15" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo15" msprop:Generator_ColumnPropNameInRow="TransformedItemNo15" msprop:Generator_ColumnPropNameInTable="TransformedItemNo15Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo16" msprop:Generator_UserColumnName="TransformedItemNo16" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo16" msprop:Generator_ColumnPropNameInRow="TransformedItemNo16" msprop:Generator_ColumnPropNameInTable="TransformedItemNo16Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo17" msprop:Generator_UserColumnName="TransformedItemNo17" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo17" msprop:Generator_ColumnPropNameInRow="TransformedItemNo17" msprop:Generator_ColumnPropNameInTable="TransformedItemNo17Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo18" msprop:Generator_UserColumnName="TransformedItemNo18" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo18" msprop:Generator_ColumnPropNameInRow="TransformedItemNo18" msprop:Generator_ColumnPropNameInTable="TransformedItemNo18Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo19" msprop:Generator_UserColumnName="TransformedItemNo19" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo19" msprop:Generator_ColumnPropNameInRow="TransformedItemNo19" msprop:Generator_ColumnPropNameInTable="TransformedItemNo19Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo20" msprop:Generator_UserColumnName="TransformedItemNo20" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo20" msprop:Generator_ColumnPropNameInRow="TransformedItemNo20" msprop:Generator_ColumnPropNameInTable="TransformedItemNo20Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo21" msprop:Generator_UserColumnName="TransformedItemNo21" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo21" msprop:Generator_ColumnPropNameInRow="TransformedItemNo21" msprop:Generator_ColumnPropNameInTable="TransformedItemNo21Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo22" msprop:Generator_UserColumnName="TransformedItemNo22" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo22" msprop:Generator_ColumnPropNameInRow="TransformedItemNo22" msprop:Generator_ColumnPropNameInTable="TransformedItemNo22Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo23" msprop:Generator_UserColumnName="TransformedItemNo23" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo23" msprop:Generator_ColumnPropNameInRow="TransformedItemNo23" msprop:Generator_ColumnPropNameInTable="TransformedItemNo23Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo24" msprop:Generator_UserColumnName="TransformedItemNo24" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo24" msprop:Generator_ColumnPropNameInRow="TransformedItemNo24" msprop:Generator_ColumnPropNameInTable="TransformedItemNo24Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo25" msprop:Generator_UserColumnName="TransformedItemNo25" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo25" msprop:Generator_ColumnPropNameInRow="TransformedItemNo25" msprop:Generator_ColumnPropNameInTable="TransformedItemNo25Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo26" msprop:Generator_UserColumnName="TransformedItemNo26" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo26" msprop:Generator_ColumnPropNameInRow="TransformedItemNo26" msprop:Generator_ColumnPropNameInTable="TransformedItemNo26Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo27" msprop:Generator_UserColumnName="TransformedItemNo27" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo27" msprop:Generator_ColumnPropNameInRow="TransformedItemNo27" msprop:Generator_ColumnPropNameInTable="TransformedItemNo27Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo28" msprop:Generator_UserColumnName="TransformedItemNo28" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo28" msprop:Generator_ColumnPropNameInRow="TransformedItemNo28" msprop:Generator_ColumnPropNameInTable="TransformedItemNo28Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo29" msprop:Generator_UserColumnName="TransformedItemNo29" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo29" msprop:Generator_ColumnPropNameInRow="TransformedItemNo29" msprop:Generator_ColumnPropNameInTable="TransformedItemNo29Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo30" msprop:Generator_UserColumnName="TransformedItemNo30" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo30" msprop:Generator_ColumnPropNameInRow="TransformedItemNo30" msprop:Generator_ColumnPropNameInTable="TransformedItemNo30Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo31" msprop:Generator_UserColumnName="TransformedItemNo31" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo31" msprop:Generator_ColumnPropNameInRow="TransformedItemNo31" msprop:Generator_ColumnPropNameInTable="TransformedItemNo31Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo32" msprop:Generator_UserColumnName="TransformedItemNo32" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo32" msprop:Generator_ColumnPropNameInRow="TransformedItemNo32" msprop:Generator_ColumnPropNameInTable="TransformedItemNo32Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo33" msprop:Generator_UserColumnName="TransformedItemNo33" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo33" msprop:Generator_ColumnPropNameInRow="TransformedItemNo33" msprop:Generator_ColumnPropNameInTable="TransformedItemNo33Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo34" msprop:Generator_UserColumnName="TransformedItemNo34" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo34" msprop:Generator_ColumnPropNameInRow="TransformedItemNo34" msprop:Generator_ColumnPropNameInTable="TransformedItemNo34Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo35" msprop:Generator_UserColumnName="TransformedItemNo35" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo35" msprop:Generator_ColumnPropNameInRow="TransformedItemNo35" msprop:Generator_ColumnPropNameInTable="TransformedItemNo35Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo36" msprop:Generator_UserColumnName="TransformedItemNo36" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo36" msprop:Generator_ColumnPropNameInRow="TransformedItemNo36" msprop:Generator_ColumnPropNameInTable="TransformedItemNo36Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo37" msprop:Generator_UserColumnName="TransformedItemNo37" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo37" msprop:Generator_ColumnPropNameInRow="TransformedItemNo37" msprop:Generator_ColumnPropNameInTable="TransformedItemNo37Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo38" msprop:Generator_UserColumnName="TransformedItemNo38" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo38" msprop:Generator_ColumnPropNameInRow="TransformedItemNo38" msprop:Generator_ColumnPropNameInTable="TransformedItemNo38Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo39" msprop:Generator_UserColumnName="TransformedItemNo39" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo39" msprop:Generator_ColumnPropNameInRow="TransformedItemNo39" msprop:Generator_ColumnPropNameInTable="TransformedItemNo39Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo40" msprop:Generator_UserColumnName="TransformedItemNo40" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo40" msprop:Generator_ColumnPropNameInRow="TransformedItemNo40" msprop:Generator_ColumnPropNameInTable="TransformedItemNo40Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo41" msprop:Generator_UserColumnName="TransformedItemNo41" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo41" msprop:Generator_ColumnPropNameInRow="TransformedItemNo41" msprop:Generator_ColumnPropNameInTable="TransformedItemNo41Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo42" msprop:Generator_UserColumnName="TransformedItemNo42" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo42" msprop:Generator_ColumnPropNameInRow="TransformedItemNo42" msprop:Generator_ColumnPropNameInTable="TransformedItemNo42Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo43" msprop:Generator_UserColumnName="TransformedItemNo43" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo43" msprop:Generator_ColumnPropNameInRow="TransformedItemNo43" msprop:Generator_ColumnPropNameInTable="TransformedItemNo43Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo44" msprop:Generator_UserColumnName="TransformedItemNo44" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo44" msprop:Generator_ColumnPropNameInRow="TransformedItemNo44" msprop:Generator_ColumnPropNameInTable="TransformedItemNo44Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo45" msprop:Generator_UserColumnName="TransformedItemNo45" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo45" msprop:Generator_ColumnPropNameInRow="TransformedItemNo45" msprop:Generator_ColumnPropNameInTable="TransformedItemNo45Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo46" msprop:Generator_UserColumnName="TransformedItemNo46" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo46" msprop:Generator_ColumnPropNameInRow="TransformedItemNo46" msprop:Generator_ColumnPropNameInTable="TransformedItemNo46Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo47" msprop:Generator_UserColumnName="TransformedItemNo47" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo47" msprop:Generator_ColumnPropNameInRow="TransformedItemNo47" msprop:Generator_ColumnPropNameInTable="TransformedItemNo47Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo48" msprop:Generator_UserColumnName="TransformedItemNo48" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo48" msprop:Generator_ColumnPropNameInRow="TransformedItemNo48" msprop:Generator_ColumnPropNameInTable="TransformedItemNo48Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo49" msprop:Generator_UserColumnName="TransformedItemNo49" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo49" msprop:Generator_ColumnPropNameInRow="TransformedItemNo49" msprop:Generator_ColumnPropNameInTable="TransformedItemNo49Column" type="xs:short" minOccurs="0" />
              <xs:element name="TransformedItemNo50" msprop:Generator_UserColumnName="TransformedItemNo50" msprop:Generator_ColumnVarNameInTable="columnTransformedItemNo50" msprop:Generator_ColumnPropNameInRow="TransformedItemNo50" msprop:Generator_ColumnPropNameInTable="TransformedItemNo50Column" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InputFormatSetting" msprop:Generator_UserTableName="InputFormatSetting" msprop:Generator_RowDeletedName="InputFormatSettingRowDeleted" msprop:Generator_RowChangedName="InputFormatSettingRowChanged" msprop:Generator_RowClassName="InputFormatSettingRow" msprop:Generator_RowChangingName="InputFormatSettingRowChanging" msprop:Generator_RowEvArgName="InputFormatSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="InputFormatSettingRowChangeEventHandler" msprop:Generator_TableClassName="InputFormatSettingDataTable" msprop:Generator_TableVarName="tableInputFormatSetting" msprop:Generator_RowDeletingName="InputFormatSettingRowDeleting" msprop:Generator_TablePropName="InputFormatSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="InputFormatId" msprop:Generator_UserColumnName="InputFormatId" msprop:Generator_ColumnVarNameInTable="columnInputFormatId" msprop:Generator_ColumnPropNameInRow="InputFormatId" msprop:Generator_ColumnPropNameInTable="InputFormatIdColumn" type="xs:short" />
              <xs:element name="InputFormat" msprop:Generator_UserColumnName="InputFormat" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="InputFormat" msprop:Generator_ColumnPropNameInTable="InputFormatColumn" msprop:Generator_ColumnVarNameInTable="columnInputFormat" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DataTransformedData" msprop:Generator_UserTableName="DataTransformedData" msprop:Generator_RowDeletedName="DataTransformedDataRowDeleted" msprop:Generator_RowChangedName="DataTransformedDataRowChanged" msprop:Generator_RowClassName="DataTransformedDataRow" msprop:Generator_RowChangingName="DataTransformedDataRowChanging" msprop:Generator_RowEvArgName="DataTransformedDataRowChangeEvent" msprop:Generator_RowEvHandlerName="DataTransformedDataRowChangeEventHandler" msprop:Generator_TableClassName="DataTransformedDataDataTable" msprop:Generator_TableVarName="tableDataTransformedData" msprop:Generator_RowDeletingName="DataTransformedDataRowDeleting" msprop:Generator_TablePropName="DataTransformedData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="TransId" msprop:Generator_UserColumnName="TransId" msprop:Generator_ColumnVarNameInTable="columnTransId" msprop:Generator_ColumnPropNameInRow="TransId" msprop:Generator_ColumnPropNameInTable="TransIdColumn" type="xs:short" />
              <xs:element name="DispData" msprop:Generator_UserColumnName="DispData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="DispData" msprop:Generator_ColumnPropNameInTable="DispDataColumn" msprop:Generator_ColumnVarNameInTable="columnDispData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TransformedData" msprop:Generator_UserColumnName="TransformedData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="TransformedData" msprop:Generator_ColumnPropNameInTable="TransformedDataColumn" msprop:Generator_ColumnVarNameInTable="columnTransformedData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="DispChangeData" msprop:Generator_UserTableName="DispChangeData" msprop:Generator_RowDeletedName="DispChangeDataRowDeleted" msprop:Generator_RowChangedName="DispChangeDataRowChanged" msprop:Generator_RowClassName="DispChangeDataRow" msprop:Generator_RowChangingName="DispChangeDataRowChanging" msprop:Generator_RowEvArgName="DispChangeDataRowChangeEvent" msprop:Generator_RowEvHandlerName="DispChangeDataRowChangeEventHandler" msprop:Generator_TableClassName="DispChangeDataDataTable" msprop:Generator_TableVarName="tableDispChangeData" msprop:Generator_RowDeletingName="DispChangeDataRowDeleting" msprop:Generator_TablePropName="DispChangeData">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="DispChangeId" msprop:Generator_UserColumnName="DispChangeId" msprop:Generator_ColumnVarNameInTable="columnDispChangeId" msprop:Generator_ColumnPropNameInRow="DispChangeId" msprop:Generator_ColumnPropNameInTable="DispChangeIdColumn" type="xs:short" />
              <xs:element name="BeforeData" msprop:Generator_UserColumnName="BeforeData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="BeforeData" msprop:Generator_ColumnPropNameInTable="BeforeDataColumn" msprop:Generator_ColumnVarNameInTable="columnBeforeData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AfterData" msprop:Generator_UserColumnName="AfterData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="AfterData" msprop:Generator_ColumnPropNameInTable="AfterDataColumn" msprop:Generator_ColumnVarNameInTable="columnAfterData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MeDataRelational" msprop:Generator_UserTableName="MeDataRelational" msprop:Generator_RowDeletedName="MeDataRelationalRowDeleted" msprop:Generator_RowChangedName="MeDataRelationalRowChanged" msprop:Generator_RowClassName="MeDataRelationalRow" msprop:Generator_RowChangingName="MeDataRelationalRowChanging" msprop:Generator_RowEvArgName="MeDataRelationalRowChangeEvent" msprop:Generator_RowEvHandlerName="MeDataRelationalRowChangeEventHandler" msprop:Generator_TableClassName="MeDataRelationalDataTable" msprop:Generator_TableVarName="tableMeDataRelational" msprop:Generator_RowDeletingName="MeDataRelationalRowDeleting" msprop:Generator_TablePropName="MeDataRelational">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="MeItemCode" msprop:Generator_UserColumnName="MeItemCode" msprop:nullValue="_null" msprop:Generator_ColumnVarNameInTable="columnMeItemCode" msprop:Generator_ColumnPropNameInTable="MeItemCodeColumn" msprop:Generator_ColumnPropNameInRow="MeItemCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MedicalItemSetting" msprop:Generator_UserTableName="MedicalItemSetting" msprop:Generator_RowDeletedName="MedicalItemSettingRowDeleted" msprop:Generator_RowChangedName="MedicalItemSettingRowChanged" msprop:Generator_RowClassName="MedicalItemSettingRow" msprop:Generator_RowChangingName="MedicalItemSettingRowChanging" msprop:Generator_RowEvArgName="MedicalItemSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="MedicalItemSettingRowChangeEventHandler" msprop:Generator_TableClassName="MedicalItemSettingDataTable" msprop:Generator_TableVarName="tableMedicalItemSetting" msprop:Generator_RowDeletingName="MedicalItemSettingRowDeleting" msprop:Generator_TablePropName="MedicalItemSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="InputMode" msprop:Generator_UserColumnName="InputMode" msprop:Generator_ColumnVarNameInTable="columnInputMode" msprop:Generator_ColumnPropNameInRow="InputMode" msprop:Generator_ColumnPropNameInTable="InputModeColumn" type="xs:short" />
              <xs:element name="MaxData" msprop:Generator_UserColumnName="MaxData" msprop:Generator_ColumnVarNameInTable="columnMaxData" msprop:Generator_ColumnPropNameInRow="MaxData" msprop:Generator_ColumnPropNameInTable="MaxDataColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MinData" msprop:Generator_UserColumnName="MinData" msprop:Generator_ColumnVarNameInTable="columnMinData" msprop:Generator_ColumnPropNameInRow="MinData" msprop:Generator_ColumnPropNameInTable="MinDataColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GapData" msprop:Generator_UserColumnName="GapData" msprop:Generator_ColumnVarNameInTable="columnGapData" msprop:Generator_ColumnPropNameInRow="GapData" msprop:Generator_ColumnPropNameInTable="GapDataColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CheckEmpty" msprop:Generator_UserColumnName="CheckEmpty" msprop:Generator_ColumnVarNameInTable="columnCheckEmpty" msprop:Generator_ColumnPropNameInRow="CheckEmpty" msprop:Generator_ColumnPropNameInTable="CheckEmptyColumn" type="xs:short" />
              <xs:element name="InputFormatId" msprop:Generator_UserColumnName="InputFormatId" msprop:Generator_ColumnVarNameInTable="columnInputFormatId" msprop:Generator_ColumnPropNameInRow="InputFormatId" msprop:Generator_ColumnPropNameInTable="InputFormatIdColumn" type="xs:short" />
              <xs:element name="DispChange" msprop:Generator_UserColumnName="DispChange" msprop:Generator_ColumnVarNameInTable="columnDispChange" msprop:Generator_ColumnPropNameInRow="DispChange" msprop:Generator_ColumnPropNameInTable="DispChangeColumn" type="xs:short" />
              <xs:element name="DispFormat" msprop:Generator_UserColumnName="DispFormat" msprop:Generator_ColumnVarNameInTable="columnDispFormat" msprop:Generator_ColumnPropNameInRow="DispFormat" msprop:Generator_ColumnPropNameInTable="DispFormatColumn" type="xs:short" />
              <xs:element name="WarningBackColor" msprop:Generator_UserColumnName="WarningBackColor" msprop:Generator_ColumnVarNameInTable="columnWarningBackColor" msprop:Generator_ColumnPropNameInRow="WarningBackColor" msprop:Generator_ColumnPropNameInTable="WarningBackColorColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="11" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="WarningForeColor" msprop:Generator_UserColumnName="WarningForeColor" msprop:Generator_ColumnVarNameInTable="columnWarningForeColor" msprop:Generator_ColumnPropNameInRow="WarningForeColor" msprop:Generator_ColumnPropNameInTable="WarningForeColorColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="11" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClickId" msprop:Generator_UserColumnName="ClickId" msprop:Generator_ColumnVarNameInTable="columnClickId" msprop:Generator_ColumnPropNameInRow="ClickId" msprop:Generator_ColumnPropNameInTable="ClickIdColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FormDispItem" msprop:Generator_UserTableName="FormDispItem" msprop:Generator_RowDeletedName="FormDispItemRowDeleted" msprop:Generator_RowChangedName="FormDispItemRowChanged" msprop:Generator_RowClassName="FormDispItemRow" msprop:Generator_RowChangingName="FormDispItemRowChanging" msprop:Generator_RowEvArgName="FormDispItemRowChangeEvent" msprop:Generator_RowEvHandlerName="FormDispItemRowChangeEventHandler" msprop:Generator_TableClassName="FormDispItemDataTable" msprop:Generator_TableVarName="tableFormDispItem" msprop:Generator_RowDeletingName="FormDispItemRowDeleting" msprop:Generator_TablePropName="FormDispItem">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="InputMode" msprop:Generator_UserColumnName="InputMode" msprop:Generator_ColumnPropNameInRow="InputMode" msprop:Generator_ColumnVarNameInTable="columnInputMode" msprop:Generator_ColumnPropNameInTable="InputModeColumn" type="xs:short" />
              <xs:element name="MaxData" msprop:Generator_UserColumnName="MaxData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="MaxData" msprop:Generator_ColumnPropNameInTable="MaxDataColumn" msprop:Generator_ColumnVarNameInTable="columnMaxData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MinData" msprop:Generator_UserColumnName="MinData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="MinData" msprop:Generator_ColumnPropNameInTable="MinDataColumn" msprop:Generator_ColumnVarNameInTable="columnMinData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GapData" msprop:Generator_UserColumnName="GapData" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="GapData" msprop:Generator_ColumnPropNameInTable="GapDataColumn" msprop:Generator_ColumnVarNameInTable="columnGapData" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CheckEmpty" msprop:Generator_UserColumnName="CheckEmpty" msprop:Generator_ColumnPropNameInRow="CheckEmpty" msprop:Generator_ColumnVarNameInTable="columnCheckEmpty" msprop:Generator_ColumnPropNameInTable="CheckEmptyColumn" type="xs:short" />
              <xs:element name="InputFormatId" msprop:Generator_UserColumnName="InputFormatId" msprop:Generator_ColumnPropNameInRow="InputFormatId" msprop:Generator_ColumnVarNameInTable="columnInputFormatId" msprop:Generator_ColumnPropNameInTable="InputFormatIdColumn" type="xs:short" />
              <xs:element name="DispChange" msprop:Generator_UserColumnName="DispChange" msprop:Generator_ColumnPropNameInRow="DispChange" msprop:Generator_ColumnVarNameInTable="columnDispChange" msprop:Generator_ColumnPropNameInTable="DispChangeColumn" type="xs:short" />
              <xs:element name="DispFormat" msprop:Generator_UserColumnName="DispFormat" msprop:Generator_ColumnPropNameInRow="DispFormat" msprop:Generator_ColumnVarNameInTable="columnDispFormat" msprop:Generator_ColumnPropNameInTable="DispFormatColumn" type="xs:short" />
              <xs:element name="WarningBackColor" msprop:Generator_UserColumnName="WarningBackColor" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="WarningBackColor" msprop:Generator_ColumnPropNameInTable="WarningBackColorColumn" msprop:Generator_ColumnVarNameInTable="columnWarningBackColor" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="11" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="WarningForeColor" msprop:Generator_UserColumnName="WarningForeColor" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="WarningForeColor" msprop:Generator_ColumnPropNameInTable="WarningForeColorColumn" msprop:Generator_ColumnVarNameInTable="columnWarningForeColor" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="11" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClickId" msprop:Generator_UserColumnName="ClickId" msprop:Generator_ColumnPropNameInRow="ClickId" msprop:Generator_ColumnVarNameInTable="columnClickId" msprop:Generator_ColumnPropNameInTable="ClickIdColumn" type="xs:short" />
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="DataType" msprop:Generator_UserColumnName="DataType" msprop:Generator_ColumnPropNameInRow="DataType" msprop:Generator_ColumnVarNameInTable="columnDataType" msprop:Generator_ColumnPropNameInTable="DataTypeColumn" type="xs:short" />
              <xs:element name="DataIndex" msprop:Generator_UserColumnName="DataIndex" msprop:Generator_ColumnPropNameInRow="DataIndex" msprop:Generator_ColumnVarNameInTable="columnDataIndex" msprop:Generator_ColumnPropNameInTable="DataIndexColumn" type="xs:short" />
              <xs:element name="DispFlg" msprop:Generator_UserColumnName="DispFlg" msprop:Generator_ColumnPropNameInRow="DispFlg" msprop:Generator_ColumnVarNameInTable="columnDispFlg" msprop:Generator_ColumnPropNameInTable="DispFlgColumn" type="xs:short" />
              <xs:element name="ReadOnly" msprop:Generator_UserColumnName="ReadOnly" msprop:Generator_ColumnPropNameInRow="ReadOnly" msprop:Generator_ColumnVarNameInTable="columnReadOnly" msprop:Generator_ColumnPropNameInTable="ReadOnlyColumn" type="xs:short" />
              <xs:element name="TitleSelect" msprop:Generator_UserColumnName="TitleSelect" msprop:Generator_ColumnPropNameInRow="TitleSelect" msprop:Generator_ColumnVarNameInTable="columnTitleSelect" msprop:Generator_ColumnPropNameInTable="TitleSelectColumn" type="xs:short" />
              <xs:element name="InputFormat" msprop:Generator_UserColumnName="InputFormat" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="InputFormat" msprop:Generator_ColumnPropNameInTable="InputFormatColumn" msprop:Generator_ColumnVarNameInTable="columnInputFormat" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ComponentType" msprop:Generator_UserColumnName="ComponentType" msprop:Generator_ColumnPropNameInRow="ComponentType" msprop:Generator_ColumnVarNameInTable="columnComponentType" msprop:Generator_ColumnPropNameInTable="ComponentTypeColumn" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FormDispMode" msprop:Generator_UserTableName="FormDispMode" msprop:Generator_RowDeletedName="FormDispModeRowDeleted" msprop:Generator_RowChangedName="FormDispModeRowChanged" msprop:Generator_RowClassName="FormDispModeRow" msprop:Generator_RowChangingName="FormDispModeRowChanging" msprop:Generator_RowEvArgName="FormDispModeRowChangeEvent" msprop:Generator_RowEvHandlerName="FormDispModeRowChangeEventHandler" msprop:Generator_TableClassName="FormDispModeDataTable" msprop:Generator_TableVarName="tableFormDispMode" msprop:Generator_RowDeletingName="FormDispModeRowDeleting" msprop:Generator_TablePropName="FormDispMode">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="DispMode" msprop:Generator_UserColumnName="DispMode" msprop:Generator_ColumnPropNameInRow="DispMode" msprop:Generator_ColumnVarNameInTable="columnDispMode" msprop:Generator_ColumnPropNameInTable="DispModeColumn" type="xs:short" />
              <xs:element name="PluralMode" msprop:Generator_UserColumnName="PluralMode" msprop:Generator_ColumnPropNameInRow="PluralMode" msprop:Generator_ColumnVarNameInTable="columnPluralMode" msprop:Generator_ColumnPropNameInTable="PluralModeColumn" type="xs:short" />
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="InitialSelectFlg" msprop:Generator_UserColumnName="InitialSelectFlg" msprop:Generator_ColumnPropNameInRow="InitialSelectFlg" msprop:Generator_ColumnVarNameInTable="columnInitialSelectFlg" msprop:Generator_ColumnPropNameInTable="InitialSelectFlgColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FormDispDetail" msprop:Generator_UserTableName="FormDispDetail" msprop:Generator_RowDeletedName="FormDispDetailRowDeleted" msprop:Generator_RowChangedName="FormDispDetailRowChanged" msprop:Generator_RowClassName="FormDispDetailRow" msprop:Generator_RowChangingName="FormDispDetailRowChanging" msprop:Generator_RowEvArgName="FormDispDetailRowChangeEvent" msprop:Generator_RowEvHandlerName="FormDispDetailRowChangeEventHandler" msprop:Generator_TableClassName="FormDispDetailDataTable" msprop:Generator_TableVarName="tableFormDispDetail" msprop:Generator_RowDeletingName="FormDispDetailRowDeleting" msprop:Generator_TablePropName="FormDispDetail">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="DataType" msprop:Generator_UserColumnName="DataType" msprop:Generator_ColumnPropNameInRow="DataType" msprop:Generator_ColumnVarNameInTable="columnDataType" msprop:Generator_ColumnPropNameInTable="DataTypeColumn" type="xs:short" />
              <xs:element name="DataIndex" msprop:Generator_UserColumnName="DataIndex" msprop:Generator_ColumnPropNameInRow="DataIndex" msprop:Generator_ColumnVarNameInTable="columnDataIndex" msprop:Generator_ColumnPropNameInTable="DataIndexColumn" type="xs:short" />
              <xs:element name="DispFlg" msprop:Generator_UserColumnName="DispFlg" msprop:Generator_ColumnPropNameInRow="DispFlg" msprop:Generator_ColumnVarNameInTable="columnDispFlg" msprop:Generator_ColumnPropNameInTable="DispFlgColumn" type="xs:short" />
              <xs:element name="ReadOnly" msprop:Generator_UserColumnName="ReadOnly" msprop:Generator_ColumnPropNameInRow="ReadOnly" msprop:Generator_ColumnVarNameInTable="columnReadOnly" msprop:Generator_ColumnPropNameInTable="ReadOnlyColumn" type="xs:short" />
              <xs:element name="TitleSelect" msprop:Generator_UserColumnName="TitleSelect" msprop:Generator_ColumnPropNameInRow="TitleSelect" msprop:Generator_ColumnVarNameInTable="columnTitleSelect" msprop:Generator_ColumnPropNameInTable="TitleSelectColumn" type="xs:short" />
              <xs:element name="ComponentType" msprop:Generator_UserColumnName="ComponentType" msprop:Generator_ColumnPropNameInRow="ComponentType" msprop:Generator_ColumnVarNameInTable="columnComponentType" msprop:Generator_ColumnPropNameInTable="ComponentTypeColumn" type="xs:short" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="EyeInitialSelect" msprop:Generator_UserTableName="EyeInitialSelect" msprop:Generator_RowDeletedName="EyeInitialSelectRowDeleted" msprop:Generator_RowChangedName="EyeInitialSelectRowChanged" msprop:Generator_RowClassName="EyeInitialSelectRow" msprop:Generator_RowChangingName="EyeInitialSelectRowChanging" msprop:Generator_RowEvArgName="EyeInitialSelectRowChangeEvent" msprop:Generator_RowEvHandlerName="EyeInitialSelectRowChangeEventHandler" msprop:Generator_TableClassName="EyeInitialSelectDataTable" msprop:Generator_TableVarName="tableEyeInitialSelect" msprop:Generator_RowDeletingName="EyeInitialSelectRowDeleting" msprop:Generator_TablePropName="EyeInitialSelect">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="InitialId" msprop:Generator_UserColumnName="InitialId" msprop:Generator_ColumnPropNameInRow="InitialId" msprop:Generator_ColumnVarNameInTable="columnInitialId" msprop:Generator_ColumnPropNameInTable="InitialIdColumn" type="xs:short" minOccurs="0" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
              <xs:element name="Priority" msprop:Generator_UserColumnName="Priority" msprop:Generator_ColumnPropNameInRow="Priority" msprop:Generator_ColumnVarNameInTable="columnPriority" msprop:Generator_ColumnPropNameInTable="PriorityColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="EyeGroupSelect" msprop:Generator_UserTableName="EyeGroupSelect" msprop:Generator_RowDeletedName="EyeGroupSelectRowDeleted" msprop:Generator_RowChangedName="EyeGroupSelectRowChanged" msprop:Generator_RowClassName="EyeGroupSelectRow" msprop:Generator_RowChangingName="EyeGroupSelectRowChanging" msprop:Generator_RowEvArgName="EyeGroupSelectRowChangeEvent" msprop:Generator_RowEvHandlerName="EyeGroupSelectRowChangeEventHandler" msprop:Generator_TableClassName="EyeGroupSelectDataTable" msprop:Generator_TableVarName="tableEyeGroupSelect" msprop:Generator_RowDeletingName="EyeGroupSelectRowDeleting" msprop:Generator_TablePropName="EyeGroupSelect">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="EyeGroupId" msprop:Generator_UserColumnName="EyeGroupId" msprop:Generator_ColumnPropNameInRow="EyeGroupId" msprop:Generator_ColumnVarNameInTable="columnEyeGroupId" msprop:Generator_ColumnPropNameInTable="EyeGroupIdColumn" type="xs:short" minOccurs="0" />
              <xs:element name="GroupId" msprop:Generator_UserColumnName="GroupId" msprop:Generator_ColumnPropNameInRow="GroupId" msprop:Generator_ColumnVarNameInTable="columnGroupId" msprop:Generator_ColumnPropNameInTable="GroupIdColumn" type="xs:short" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="EyeControl" msprop:Generator_UserTableName="EyeControl" msprop:Generator_RowDeletedName="EyeControlRowDeleted" msprop:Generator_RowChangedName="EyeControlRowChanged" msprop:Generator_RowClassName="EyeControlRow" msprop:Generator_RowChangingName="EyeControlRowChanging" msprop:Generator_RowEvArgName="EyeControlRowChangeEvent" msprop:Generator_RowEvHandlerName="EyeControlRowChangeEventHandler" msprop:Generator_TableClassName="EyeControlDataTable" msprop:Generator_TableVarName="tableEyeControl" msprop:Generator_RowDeletingName="EyeControlRowDeleting" msprop:Generator_TablePropName="EyeControl">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="EyeControlId" msprop:Generator_UserColumnName="EyeControlId" msprop:Generator_ColumnPropNameInRow="EyeControlId" msprop:Generator_ColumnVarNameInTable="columnEyeControlId" msprop:Generator_ColumnPropNameInTable="EyeControlIdColumn" type="xs:short" minOccurs="0" />
              <xs:element name="ControlId" msprop:Generator_UserColumnName="ControlId" msprop:Generator_ColumnPropNameInRow="ControlId" msprop:Generator_ColumnVarNameInTable="columnControlId" msprop:Generator_ColumnPropNameInTable="ControlIdColumn" type="xs:short" />
              <xs:element name="MedicalCheckNo" msprop:Generator_UserColumnName="MedicalCheckNo" msprop:Generator_ColumnPropNameInRow="MedicalCheckNo" msprop:Generator_ColumnVarNameInTable="columnMedicalCheckNo" msprop:Generator_ColumnPropNameInTable="MedicalCheckNoColumn" type="xs:short" />
              <xs:element name="ItemNo" msprop:Generator_UserColumnName="ItemNo" msprop:Generator_ColumnPropNameInRow="ItemNo" msprop:Generator_ColumnVarNameInTable="columnItemNo" msprop:Generator_ColumnPropNameInTable="ItemNoColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="FontSetting" msprop:Generator_UserTableName="FontSetting" msprop:Generator_RowDeletedName="FontSettingRowDeleted" msprop:Generator_RowChangedName="FontSettingRowChanged" msprop:Generator_RowClassName="FontSettingRow" msprop:Generator_RowChangingName="FontSettingRowChanging" msprop:Generator_RowEvArgName="FontSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="FontSettingRowChangeEventHandler" msprop:Generator_TableClassName="FontSettingDataTable" msprop:Generator_TableVarName="tableFontSetting" msprop:Generator_RowDeletingName="FontSettingRowDeleting" msprop:Generator_TablePropName="FontSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="Label" msprop:Generator_UserColumnName="Label" msprop:Generator_ColumnVarNameInTable="columnLabel" msprop:Generator_ColumnPropNameInRow="Label" msprop:Generator_ColumnPropNameInTable="LabelColumn" type="xs:short" />
              <xs:element name="FontName" msprop:Generator_UserColumnName="FontName" msprop:Generator_ColumnVarNameInTable="columnFontName" msprop:Generator_ColumnPropNameInRow="FontName" msprop:Generator_ColumnPropNameInTable="FontNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FontSize" msprop:Generator_UserColumnName="FontSize" msprop:Generator_ColumnVarNameInTable="columnFontSize" msprop:Generator_ColumnPropNameInRow="FontSize" msprop:Generator_ColumnPropNameInTable="FontSizeColumn" type="xs:short" />
              <xs:element name="FontStyle" msprop:Generator_UserColumnName="FontStyle" msprop:Generator_ColumnVarNameInTable="columnFontStyle" msprop:Generator_ColumnPropNameInRow="FontStyle" msprop:Generator_ColumnPropNameInTable="FontStyleColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FontUnit" msprop:Generator_UserColumnName="FontUnit" msprop:Generator_ColumnVarNameInTable="columnFontUnit" msprop:Generator_ColumnPropNameInRow="FontUnit" msprop:Generator_ColumnPropNameInTable="FontUnitColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CharSet" msprop:Generator_UserColumnName="CharSet" msprop:Generator_ColumnVarNameInTable="columnCharSet" msprop:Generator_ColumnPropNameInRow="CharSet" msprop:Generator_ColumnPropNameInTable="CharSetColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CustomizeButtonSetting" msprop:Generator_UserTableName="CustomizeButtonSetting" msprop:Generator_RowDeletedName="CustomizeButtonSettingRowDeleted" msprop:Generator_RowChangedName="CustomizeButtonSettingRowChanged" msprop:Generator_RowClassName="CustomizeButtonSettingRow" msprop:Generator_RowChangingName="CustomizeButtonSettingRowChanging" msprop:Generator_RowEvArgName="CustomizeButtonSettingRowChangeEvent" msprop:Generator_RowEvHandlerName="CustomizeButtonSettingRowChangeEventHandler" msprop:Generator_TableClassName="CustomizeButtonSettingDataTable" msprop:Generator_TableVarName="tableCustomizeButtonSetting" msprop:Generator_RowDeletingName="CustomizeButtonSettingRowDeleting" msprop:Generator_TablePropName="CustomizeButtonSetting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormId" msprop:Generator_UserColumnName="FormId" msprop:Generator_ColumnVarNameInTable="columnFormId" msprop:Generator_ColumnPropNameInRow="FormId" msprop:Generator_ColumnPropNameInTable="FormIdColumn" type="xs:short" />
              <xs:element name="PageNo" msprop:Generator_UserColumnName="PageNo" msprop:Generator_ColumnVarNameInTable="columnPageNo" msprop:Generator_ColumnPropNameInRow="PageNo" msprop:Generator_ColumnPropNameInTable="PageNoColumn" type="xs:short" />
              <xs:element name="DispName" msprop:Generator_UserColumnName="DispName" msprop:Generator_ColumnVarNameInTable="columnDispName" msprop:Generator_ColumnPropNameInRow="DispName" msprop:Generator_ColumnPropNameInTable="DispNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispPosition" msprop:Generator_UserColumnName="DispPosition" msprop:Generator_ColumnVarNameInTable="columnDispPosition" msprop:Generator_ColumnPropNameInRow="DispPosition" msprop:Generator_ColumnPropNameInTable="DispPositionColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispSize" msprop:Generator_UserColumnName="DispSize" msprop:Generator_ColumnVarNameInTable="columnDispSize" msprop:Generator_ColumnPropNameInRow="DispSize" msprop:Generator_ColumnPropNameInTable="DispSizeColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispVisible" msprop:Generator_UserColumnName="DispVisible" msprop:Generator_ColumnVarNameInTable="columnDispVisible" msprop:Generator_ColumnPropNameInRow="DispVisible" msprop:Generator_ColumnPropNameInTable="DispVisibleColumn" type="xs:short" />
              <xs:element name="AutoAction" msprop:Generator_UserColumnName="AutoAction" msprop:Generator_ColumnVarNameInTable="columnAutoAction" msprop:Generator_ColumnPropNameInRow="AutoAction" msprop:Generator_ColumnPropNameInTable="AutoActionColumn" type="xs:short" />
              <xs:element name="ButtonID" msprop:Generator_UserColumnName="ButtonID" msprop:Generator_ColumnVarNameInTable="columnButtonID" msprop:Generator_ColumnPropNameInRow="ButtonID" msprop:Generator_ColumnPropNameInTable="ButtonIDColumn" type="xs:short" />
              <xs:element name="DispShape" msprop:Generator_UserColumnName="DispShape" msprop:Generator_ColumnVarNameInTable="columnDispShape" msprop:Generator_ColumnPropNameInRow="DispShape" msprop:Generator_ColumnPropNameInTable="DispShapeColumn" type="xs:short" />
              <xs:element name="DispColor" msprop:Generator_UserColumnName="DispColor" msprop:Generator_ColumnVarNameInTable="columnDispColor" msprop:Generator_ColumnPropNameInRow="DispColor" msprop:Generator_ColumnPropNameInTable="DispColorColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CustomizeButtonDetail" msprop:Generator_UserTableName="CustomizeButtonDetail" msprop:Generator_RowDeletedName="CustomizeButtonDetailRowDeleted" msprop:Generator_RowChangedName="CustomizeButtonDetailRowChanged" msprop:Generator_RowClassName="CustomizeButtonDetailRow" msprop:Generator_RowChangingName="CustomizeButtonDetailRowChanging" msprop:Generator_RowEvArgName="CustomizeButtonDetailRowChangeEvent" msprop:Generator_RowEvHandlerName="CustomizeButtonDetailRowChangeEventHandler" msprop:Generator_TableClassName="CustomizeButtonDetailDataTable" msprop:Generator_TableVarName="tableCustomizeButtonDetail" msprop:Generator_RowDeletingName="CustomizeButtonDetailRowDeleting" msprop:Generator_TablePropName="CustomizeButtonDetail">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FormID" msprop:Generator_UserColumnName="FormID" msprop:Generator_ColumnVarNameInTable="columnFormID" msprop:Generator_ColumnPropNameInRow="FormID" msprop:Generator_ColumnPropNameInTable="FormIDColumn" type="xs:short" />
              <xs:element name="Priority" msprop:Generator_UserColumnName="Priority" msprop:Generator_ColumnVarNameInTable="columnPriority" msprop:Generator_ColumnPropNameInRow="Priority" msprop:Generator_ColumnPropNameInTable="PriorityColumn" type="xs:short" />
              <xs:element name="FunctionID" msprop:Generator_UserColumnName="FunctionID" msprop:Generator_ColumnVarNameInTable="columnFunctionID" msprop:Generator_ColumnPropNameInRow="FunctionID" msprop:Generator_ColumnPropNameInTable="FunctionIDColumn" type="xs:short" />
              <xs:element name="DispData" msprop:Generator_UserColumnName="DispData" msprop:Generator_ColumnVarNameInTable="columnDispData" msprop:Generator_ColumnPropNameInRow="DispData" msprop:Generator_ColumnPropNameInTable="DispDataColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DispItemIndex" msprop:Generator_UserColumnName="DispItemIndex" msprop:Generator_ColumnVarNameInTable="columnDispItemIndex" msprop:Generator_ColumnPropNameInRow="DispItemIndex" msprop:Generator_ColumnPropNameInTable="DispItemIndexColumn" type="xs:short" />
              <xs:element name="CalcItemFlg1" msprop:Generator_UserColumnName="CalcItemFlg1" msprop:Generator_ColumnVarNameInTable="columnCalcItemFlg1" msprop:Generator_ColumnPropNameInRow="CalcItemFlg1" msprop:Generator_ColumnPropNameInTable="CalcItemFlg1Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemIndex1" msprop:Generator_UserColumnName="CalcItemIndex1" msprop:Generator_ColumnVarNameInTable="columnCalcItemIndex1" msprop:Generator_ColumnPropNameInRow="CalcItemIndex1" msprop:Generator_ColumnPropNameInTable="CalcItemIndex1Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemFlg2" msprop:Generator_UserColumnName="CalcItemFlg2" msprop:Generator_ColumnVarNameInTable="columnCalcItemFlg2" msprop:Generator_ColumnPropNameInRow="CalcItemFlg2" msprop:Generator_ColumnPropNameInTable="CalcItemFlg2Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemIndex2" msprop:Generator_UserColumnName="CalcItemIndex2" msprop:Generator_ColumnVarNameInTable="columnCalcItemIndex2" msprop:Generator_ColumnPropNameInRow="CalcItemIndex2" msprop:Generator_ColumnPropNameInTable="CalcItemIndex2Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemFlg3" msprop:Generator_UserColumnName="CalcItemFlg3" msprop:Generator_ColumnVarNameInTable="columnCalcItemFlg3" msprop:Generator_ColumnPropNameInRow="CalcItemFlg3" msprop:Generator_ColumnPropNameInTable="CalcItemFlg3Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemIndex3" msprop:Generator_UserColumnName="CalcItemIndex3" msprop:Generator_ColumnVarNameInTable="columnCalcItemIndex3" msprop:Generator_ColumnPropNameInRow="CalcItemIndex3" msprop:Generator_ColumnPropNameInTable="CalcItemIndex3Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemFlg4" msprop:Generator_UserColumnName="CalcItemFlg4" msprop:Generator_ColumnVarNameInTable="columnCalcItemFlg4" msprop:Generator_ColumnPropNameInRow="CalcItemFlg4" msprop:Generator_ColumnPropNameInTable="CalcItemFlg4Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemIndex4" msprop:Generator_UserColumnName="CalcItemIndex4" msprop:Generator_ColumnVarNameInTable="columnCalcItemIndex4" msprop:Generator_ColumnPropNameInRow="CalcItemIndex4" msprop:Generator_ColumnPropNameInTable="CalcItemIndex4Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemFlg5" msprop:Generator_UserColumnName="CalcItemFlg5" msprop:Generator_ColumnVarNameInTable="columnCalcItemFlg5" msprop:Generator_ColumnPropNameInRow="CalcItemFlg5" msprop:Generator_ColumnPropNameInTable="CalcItemFlg5Column" type="xs:short" minOccurs="0" />
              <xs:element name="CalcItemIndex5" msprop:Generator_UserColumnName="CalcItemIndex5" msprop:Generator_ColumnVarNameInTable="columnCalcItemIndex5" msprop:Generator_ColumnPropNameInRow="CalcItemIndex5" msprop:Generator_ColumnPropNameInTable="CalcItemIndex5Column" type="xs:short" minOccurs="0" />
              <xs:element name="ButtonID" msprop:Generator_UserColumnName="ButtonID" msprop:Generator_ColumnVarNameInTable="columnButtonID" msprop:Generator_ColumnPropNameInRow="ButtonID" msprop:Generator_ColumnPropNameInTable="ButtonIDColumn" type="xs:short" />
              <xs:element name="FunctionType" msprop:Generator_UserColumnName="FunctionType" msprop:Generator_ColumnVarNameInTable="columnFunctionType" msprop:Generator_ColumnPropNameInRow="FunctionType" msprop:Generator_ColumnPropNameInTable="FunctionTypeColumn" type="xs:short" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MedicalItemList" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
    </xs:unique>
    <xs:unique name="PluralDispSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:PluralDispSetting" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
    </xs:unique>
    <xs:unique name="PluralSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:PluralSetting" />
      <xs:field xpath="mstns:MedicalCheckNo" />
    </xs:unique>
    <xs:unique name="TransformedSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:TransformedSetting" />
      <xs:field xpath="mstns:MedicalCheckNo" />
    </xs:unique>
    <xs:unique name="InputFormatSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:InputFormatSetting" />
      <xs:field xpath="mstns:InputFormatId" />
    </xs:unique>
    <xs:unique name="DataTransformedData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:DataTransformedData" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
      <xs:field xpath="mstns:TransId" />
    </xs:unique>
    <xs:unique name="DispChangeData_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:DispChangeData" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
      <xs:field xpath="mstns:DispChangeId" />
    </xs:unique>
    <xs:unique name="MeDataRelational_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MeDataRelational" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
    </xs:unique>
    <xs:unique name="MedicalItemSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MedicalItemSetting" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
    </xs:unique>
    <xs:unique name="FormDispMode_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:FormDispMode" />
      <xs:field xpath="mstns:MedicalCheckNo" />
    </xs:unique>
    <xs:unique name="FormDispDetail_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:FormDispDetail" />
      <xs:field xpath="mstns:FormId" />
      <xs:field xpath="mstns:MedicalCheckNo" />
      <xs:field xpath="mstns:ItemNo" />
      <xs:field xpath="mstns:DataType" />
      <xs:field xpath="mstns:DataIndex" />
    </xs:unique>
    <xs:unique name="FontSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:FontSetting" />
      <xs:field xpath="mstns:FormId" />
      <xs:field xpath="mstns:Label" />
    </xs:unique>
    <xs:unique name="CustomizeButtonSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CustomizeButtonSetting" />
      <xs:field xpath="mstns:FormId" />
      <xs:field xpath="mstns:ButtonID" />
    </xs:unique>
    <xs:unique name="CustomizeButtonDetail_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CustomizeButtonDetail" />
      <xs:field xpath="mstns:FormID" />
      <xs:field xpath="mstns:ButtonID" />
      <xs:field xpath="mstns:Priority" />
    </xs:unique>
  </xs:element>
</xs:schema>