﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{2B88F456-8E47-42AE-98E8-97AF81599801}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>ShinKenShinKunServer</RootNamespace>
    <AssemblyName>ShinKenShinKunServer</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44345</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BCrypt.Net-Next, Version=*******, Culture=neutral, PublicKeyToken=1e11be04b6288443, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\packages\BCrypt.Net-Next.3.3.2\lib\net472\BCrypt.Net-Next.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform">
      <HintPath>..\..\..\..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.1\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="index.html" />
    <Content Include="PublicService.asmx" />
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Constants.cs" />
    <Compile Include="DataSets\DispSettingDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DispSettingDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\FunctionDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>FunctionDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\GuideSupportDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GuideSupportDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\MedicalDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MedicalDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\MessageDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MessageDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\RemarksDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RemarksDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\UserDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>UserDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Dtos\LoginRecord.cs" />
    <Compile Include="Dtos\PluralRecord.cs" />
    <Compile Include="Enums\CommandStatus.cs" />
    <Compile Include="Enums\LoginState.cs" />
    <Compile Include="Enums\LoginStatus.cs" />
    <Compile Include="Enums\RegisterStatus.cs" />
    <Compile Include="Enums\Role.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PublicService.asmx.cs">
      <DependentUpon>PublicService.asmx</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Services\CallMonitorService.cs" />
    <Compile Include="Services\CallMonitorSettingService.cs" />
    <Compile Include="Services\CheckItemService.cs" />
    <Compile Include="Services\ClickSwitchSettingService.cs" />
    <Compile Include="Services\ClientAddInformation2Service.cs" />
    <Compile Include="Services\ClientAddInformationService.cs" />
    <Compile Include="Services\ClientRemarksService.cs" />
    <Compile Include="Services\ClientService.cs" />
    <Compile Include="Services\ConciergeControlService.cs" />
    <Compile Include="Services\ConciergeMemberListService.cs" />
    <Compile Include="Services\ConciergeTimeGroupListService.cs" />
    <Compile Include="Services\CustomizeButtonSettingService.cs" />
    <Compile Include="Services\DataTransformedService.cs" />
    <Compile Include="Services\DispChangeSettingService.cs" />
    <Compile Include="Services\DispSettingService.cs" />
    <Compile Include="Services\EyeControlService.cs" />
    <Compile Include="Services\EyeGroupSelectService.cs" />
    <Compile Include="Services\EyeInitialSelectService.cs" />
    <Compile Include="Services\FontSettingService.cs" />
    <Compile Include="Services\FormDispSettingService.cs" />
    <Compile Include="Services\FormModeDispService.cs" />
    <Compile Include="Services\FunctionDataSetService.cs" />
    <Compile Include="Services\GetDataStateService.cs" />
    <Compile Include="Services\GuideGroupService.cs" />
    <Compile Include="Services\GuideSettingService.cs" />
    <Compile Include="Services\GuideSupportService.cs" />
    <Compile Include="Services\IndividualControlService.cs" />
    <Compile Include="Services\IpTermSetService.cs" />
    <Compile Include="Services\LiteTerminalService.cs" />
    <Compile Include="Services\MachineService.cs" />
    <Compile Include="Services\MeDataRelationalService.cs" />
    <Compile Include="Services\MedicalCheckItemStateService.cs" />
    <Compile Include="Services\MedicalCheckReasonDataService.cs" />
    <Compile Include="Services\MedicalItemListService.cs" />
    <Compile Include="Services\MedicalItemSettingService.cs" />
    <Compile Include="Services\MeMachinesService.cs" />
    <Compile Include="Services\MessageService.cs" />
    <Compile Include="Services\NeedCheckupItemService.cs" />
    <Compile Include="Services\NeedCheckupService.cs" />
    <Compile Include="Services\NextService.cs" />
    <Compile Include="Services\OldClientService.cs" />
    <Compile Include="Services\PluralDispSettingService.cs" />
    <Compile Include="Services\PluralSetting.cs" />
    <Compile Include="Services\RemarksItemListService.cs" />
    <Compile Include="Services\RemarksService.cs" />
    <Compile Include="Services\Service.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Services\SetDataStateService.cs" />
    <Compile Include="Services\SoundSetService.cs" />
    <Compile Include="Services\TransformedSettingService.cs" />
    <Compile Include="Services\UserService.cs" />
    <Compile Include="Utils\Exceptions.cs" />
    <Compile Include="Utils\Logger.cs" />
    <Compile Include="Utils\Security.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DataSets\DispSettingDataSet.xsc">
      <DependentUpon>DispSettingDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\DispSettingDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DispSettingDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\DispSettingDataSet.xss">
      <DependentUpon>DispSettingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\FunctionDataSet.xsc">
      <DependentUpon>FunctionDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\FunctionDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>FunctionDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\FunctionDataSet.xss">
      <DependentUpon>FunctionDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\GuideSupportDataSet.xsc">
      <DependentUpon>GuideSupportDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\GuideSupportDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>GuideSupportDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\GuideSupportDataSet.xss">
      <DependentUpon>GuideSupportDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\MedicalDataSet.xsc">
      <DependentUpon>MedicalDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\MedicalDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>MedicalDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\MedicalDataSet.xss">
      <DependentUpon>MedicalDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\RemarksDataSet.xsc">
      <DependentUpon>RemarksDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\MessageDataSet.xsc">
      <DependentUpon>MessageDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\MessageDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>MessageDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\MessageDataSet.xss">
      <DependentUpon>MessageDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\RemarksDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>RemarksDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\RemarksDataSet.xss">
      <DependentUpon>RemarksDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DataSets\UserDataSet.xsc">
      <DependentUpon>UserDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DataSets\UserDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>UserDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="DataSets\UserDataSet.xss">
      <DependentUpon>UserDataSet.xsd</DependentUpon>
    </Content>
    <None Include="packages.config" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>0</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:8999/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>