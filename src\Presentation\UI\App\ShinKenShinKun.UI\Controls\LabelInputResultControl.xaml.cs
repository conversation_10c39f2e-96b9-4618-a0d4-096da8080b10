namespace ShinKenShinKun.UI;

/// <summary>
/// A customizable control that combines a label, input, and result display functionality.
/// Inherits from Grid and provides various display modes and interaction options.
/// </summary>
/// <remarks>
/// This control supports different types of interactions and display modes:
/// - Input mode for text entry
/// - Classification mode for cycling through predefined categories
/// - Multiple display states (Default, Hidden, GrayedOut)
/// </remarks>
/// <example>
/// Usage in XAML:
/// <code>
/// <local:LabelInputResultControl
///     Title="Sample Title"
///     Value="Initial Value"
///     Type="Input"
///     DisplayMode="Default"/>
/// </code>
/// </example>
/// <seealso cref="Grid"/>
public partial class LabelInputResultControl : Grid
{
    public static readonly BindableProperty TitleProperty = BindableProperty.Create(
       nameof(Title),
       typeof(string),
       typeof(LabelInputResultControl),
       string.Empty,
       BindingMode.OneWay);

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
       nameof(Value),
       typeof(string),
       typeof(LabelInputResultControl),
       default(string),
       BindingMode.TwoWay);

    public static readonly BindableProperty DisplayModeProperty = BindableProperty.Create(
       nameof(DisplayMode),
       typeof(DisplayMode),
       typeof(LabelInputResultControl),
       default(DisplayMode),
       BindingMode.OneWay,
       propertyChanged: OnDisplayModeChanged);

    private static void OnDisplayModeChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.CreateUI();
        }
    }

    public static readonly BindableProperty CategoriesProperty = BindableProperty.Create(
       nameof(Categories),
       typeof(IList<string>),
       typeof(LabelInputResultControl),
       new List<string>(),
       BindingMode.OneWay,
       propertyChanged: OnCategoriesChanged);

    private static void OnCategoriesChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control && newValue is IList<string> list)
        {
            control.CreateUI();
        }
    }

    public static readonly BindableProperty TypeProperty = BindableProperty.Create(
       nameof(Type),
       typeof(LabelResultType),
       typeof(LabelInputResultControl),
       LabelResultType.None,
       BindingMode.OneWay,
       propertyChanged: OnLabelResultTypeChanged);

    private static void OnLabelResultTypeChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control && newValue is LabelResultType type && type != LabelResultType.None)
        {
            control.CreateUI();
        }
    }

    public LabelInputResultControl()
    {
        InitializeComponent();
    }

    /// <summary>
    /// Gets or sets the title text displayed in the control.
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    /// <summary>
    /// Gets or sets the display mode of the control.
    /// Controls visibility and appearance states.
    /// </summary>
    public DisplayMode DisplayMode
    {
        get => (DisplayMode)GetValue(DisplayModeProperty);
        set => SetValue(DisplayModeProperty, value);
    }

    /// <summary>
    /// Gets or sets the main value displayed in the control.
    /// Supports two-way binding.
    /// </summary>
    public string Value
    {
        get => (string)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }

    /// <summary>
    /// Gets or sets the list of available categories for classification mode.
    /// </summary>
    public IList<string> Categories
    {
        get => (IList<string>)GetValue(CategoriesProperty);
        set => SetValue(CategoriesProperty, value);
    }

    /// <summary>
    /// Gets or sets the type of the control (Input or Classification).
    /// Determines the behavior when interacted with.
    /// </summary>
    public LabelResultType Type
    {
        get => (LabelResultType)GetValue(TypeProperty);
        set => SetValue(TypeProperty, value);
    }

    public int SettingNumberPadId { get; set; }
    public string InputFormat { get; set; }

    private void CreateUI()
    {
        EnsureInitialized();

        grid.Children.Clear();
        var label = CreateLabel();
        ApplyDisplayMode(label);
        grid.Add(label);
    }

    private void EnsureInitialized()
    {
        if (Type == LabelResultType.Classify)
        {
            Value = Categories.FirstOrDefault() ?? string.Empty;
        }
    }

    private Label CreateLabel()
    {
        var label = new Label
        {
            Style = Styles.InputResultLabel,
            FontAttributes = Type == LabelResultType.Input
                ? FontAttributes.Bold
                : FontAttributes.None,
            FontFamily = Type == LabelResultType.Input
                ? FontNames.NotoSansJPBold
                : FontNames.NotoSansJPRegular
        };

        label.SetBinding(Label.TextProperty, new Binding(nameof(Value), source: this));
        return label;
    }

    private void ApplyDisplayMode(Label label)
    {
        switch (DisplayMode)
        {
            case DisplayMode.Hidden:
                this.IsVisible = false;
                break;

            case DisplayMode.GrayedOut:
                ApplyGrayedOutStyle(label);
                break;

            default:
            case DisplayMode.Default:
                break;
        }
    }

    private void ApplyGrayedOutStyle(Label label)
    {
        var grayColor = AppColors.Gray300;
        boxView.BackgroundColor = grayColor;
        title.TextColor = grayColor;
        label.TextColor = grayColor;

        var behavior = new IconTintColorBehavior { TintColor = grayColor };
        image.Behaviors.Add(behavior);
    }

    private void TapGestureRecognizer_Tapped(object sender, TappedEventArgs e)
    {
        if (DisplayMode == DisplayMode.GrayedOut)
        {
            return;
        }
        if (Type == LabelResultType.Input)
        {
            var popupService = Application.Current?.Handler.MauiContext.Services.GetService<IPopupService>();
            popupService.ShowPopup<NumberPadViewModel>(vm =>
            {
                vm.IsStandard = true;
                vm.Text = Value;
                vm.Comfirm = new(ChangeValue);
                vm.SettingNumberPadId = SettingNumberPadId;
                vm.InputFormat = InputFormat;
                vm.Title = Title;
            });
        }
        else if (Type == LabelResultType.Classify)
        {
            if (Categories.Count == 0)
            {
                return;
            }
            var index = Categories.IndexOf(Value);
            var indexNextItem = Categories.Count > index + 1 ? ++index : 0;

            ChangeValue(Categories[indexNextItem]);
        }
    }

    private void ChangeValue(string value)
    {
        Value = value;
    }
}