namespace ShinKenShinKun.UI;

/// <summary>
/// A customizable control that combines a label, input, and result display functionality.
/// Inherits from Grid and provides various display modes and interaction options.
/// </summary>
/// <remarks>
/// This control supports different types of interactions and display modes:
/// - Input mode for text entry
/// - Classification mode for cycling through predefined categories
/// - Multiple display states (Default, Hidden, GrayedOut)
/// </remarks>
/// <example>
/// Usage in XAML:
/// <code>
/// <local:LabelInputResultControl
///     Title="Sample Title"
///     Value="Initial Value"
///     Type="Input"
///     DisplayMode="Default"/>
/// </code>
/// </example>
/// <seealso cref="Grid"/>
public partial class LabelInputResultControl : Grid
{
    public static readonly BindableProperty TitleProperty = BindableProperty.Create(
       nameof(Title),
       typeof(string),
       typeof(LabelInputResultControl),
       string.Empty,
       BindingMode.OneWay);

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
       nameof(Value),
       typeof(string),
       typeof(LabelInputResultControl),
       default(string),
       BindingMode.TwoWay);

    public static readonly BindableProperty DisplayModeProperty = BindableProperty.Create(
       nameof(DisplayMode),
       typeof(DisplayMode),
       typeof(LabelInputResultControl),
       default(DisplayMode),
       BindingMode.OneWay,
       propertyChanged: OnDisplayModeChanged);

    private static void OnDisplayModeChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.CreateUI();
        }
    }

    public static readonly BindableProperty CategoriesProperty = BindableProperty.Create(
       nameof(Categories),
       typeof(IList<string>),
       typeof(LabelInputResultControl),
       new List<string>(),
       BindingMode.OneWay,
       propertyChanged: OnCategoriesChanged);

    private static void OnCategoriesChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control && newValue is IList<string> list)
        {
            control.CreateUI();
        }
    }

    public static readonly BindableProperty TypeProperty = BindableProperty.Create(
       nameof(Type),
       typeof(LabelResultType),
       typeof(LabelInputResultControl),
       LabelResultType.None,
       BindingMode.OneWay,
       propertyChanged: OnLabelResultTypeChanged);

    public static readonly BindableProperty LabelColorProperty = BindableProperty.Create(
       nameof(LabelColor),
       typeof(string),
       typeof(LabelInputResultControl),
       string.Empty,
       BindingMode.OneWay,
       propertyChanged: OnLabelColorChanged);

    public static readonly BindableProperty IsReadOnlyProperty = BindableProperty.Create(
       nameof(IsReadOnly),
       typeof(bool),
       typeof(LabelInputResultControl),
       false,
       BindingMode.OneWay,
       propertyChanged: OnIsReadOnlyChanged);

    public static readonly BindableProperty TextColorProperty = BindableProperty.Create(
       nameof(TextColor),
       typeof(string),
       typeof(LabelInputResultControl),
       string.Empty,
       BindingMode.OneWay,
       propertyChanged: OnTextColorChanged);

    public static readonly BindableProperty UseDirectInputProperty = BindableProperty.Create(
       nameof(UseDirectInput),
       typeof(bool),
       typeof(LabelInputResultControl),
       false,
       BindingMode.OneWay,
       propertyChanged: OnUseDirectInputChanged);

    private static void OnLabelResultTypeChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control && newValue is LabelResultType type && type != LabelResultType.None)
        {
            control.CreateUI();
        }
    }

    private static void OnLabelColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.ApplyLabelColor();
        }
    }

    private static void OnIsReadOnlyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.ApplyReadOnlyState();
        }
    }

    private static void OnTextColorChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.ApplyTextColor();
        }
    }

    private static void OnUseDirectInputChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelInputResultControl control)
        {
            control.CreateUI();
        }
    }

    public LabelInputResultControl()
    {
        InitializeComponent();
    }

    /// <summary>
    /// Gets or sets the title text displayed in the control.
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    /// <summary>
    /// Gets or sets the display mode of the control.
    /// Controls visibility and appearance states.
    /// </summary>
    public DisplayMode DisplayMode
    {
        get => (DisplayMode)GetValue(DisplayModeProperty);
        set => SetValue(DisplayModeProperty, value);
    }

    /// <summary>
    /// Gets or sets the main value displayed in the control.
    /// Supports two-way binding.
    /// </summary>
    public string Value
    {
        get => (string)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }

    /// <summary>
    /// Gets or sets the list of available categories for classification mode.
    /// </summary>
    public IList<string> Categories
    {
        get => (IList<string>)GetValue(CategoriesProperty);
        set => SetValue(CategoriesProperty, value);
    }

    /// <summary>
    /// Gets or sets the type of the control (Input or Classification).
    /// Determines the behavior when interacted with.
    /// </summary>
    public LabelResultType Type
    {
        get => (LabelResultType)GetValue(TypeProperty);
        set => SetValue(TypeProperty, value);
    }

    /// <summary>
    /// Gets or sets the color of the label/title text.
    /// </summary>
    public string LabelColor
    {
        get => (string)GetValue(LabelColorProperty);
        set => SetValue(LabelColorProperty, value);
    }

    /// <summary>
    /// Gets or sets whether the control is read-only.
    /// </summary>
    public bool IsReadOnly
    {
        get => (bool)GetValue(IsReadOnlyProperty);
        set => SetValue(IsReadOnlyProperty, value);
    }

    /// <summary>
    /// Gets or sets the color of the value text.
    /// </summary>
    public string TextColor
    {
        get => (string)GetValue(TextColorProperty);
        set => SetValue(TextColorProperty, value);
    }

    /// <summary>
    /// Gets or sets whether to use direct keyboard input instead of NumberPad popup.
    /// </summary>
    public bool UseDirectInput
    {
        get => (bool)GetValue(UseDirectInputProperty);
        set => SetValue(UseDirectInputProperty, value);
    }

    public int SettingNumberPadId { get; set; }
    public string InputFormat { get; set; }

    private Label? valueLabel;
    private Entry? valueEntry;

    private void CreateUI()
    {
        EnsureInitialized();

        grid.Children.Clear();

        if (UseDirectInput)
        {
            valueEntry = CreateEntry();
            ApplyLabelColor();
            ApplyTextColor();
            ApplyReadOnlyState();
            grid.Add(valueEntry);
        }
        else
        {
            valueLabel = CreateLabel();
            ApplyDisplayMode(valueLabel);
            ApplyLabelColor();
            ApplyTextColor();
            ApplyReadOnlyState();
            grid.Add(valueLabel);
        }
    }

    private void EnsureInitialized()
    {
        if (Type == LabelResultType.Classify)
        {
            Value = Categories.FirstOrDefault() ?? string.Empty;
        }
    }

    private Label CreateLabel()
    {
        var label = new Label
        {
            Style = Styles.InputResultLabel,
            FontAttributes = Type == LabelResultType.Input
                ? FontAttributes.Bold
                : FontAttributes.None,
            FontFamily = Type == LabelResultType.Input
                ? FontNames.NotoSansJPBold
                : FontNames.NotoSansJPRegular
        };

        label.SetBinding(Label.TextProperty, new Binding(nameof(Value), source: this));
        return label;
    }

    private Entry CreateEntry()
    {
        var entry = new Entry
        {
            Style = Styles.EntryStyle,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Center,
            FontFamily = FontNames.NotoSansJPRegular,
            BackgroundColor = Colors.Transparent,
            Margin = new Thickness(0)
        };

        entry.SetBinding(Entry.TextProperty, new Binding(nameof(Value), source: this, mode: BindingMode.TwoWay));
        return entry;
    }

    private void ApplyDisplayMode(Label label)
    {
        switch (DisplayMode)
        {
            case DisplayMode.Hidden:
                this.IsVisible = false;
                break;

            case DisplayMode.GrayedOut:
                ApplyGrayedOutStyle(label);
                break;

            default:
            case DisplayMode.Default:
                break;
        }
    }

    private void ApplyGrayedOutStyle(Label label)
    {
        var grayColor = AppColors.Gray300;
        boxView.BackgroundColor = grayColor;
        title.TextColor = grayColor;
        label.TextColor = grayColor;

        var behavior = new IconTintColorBehavior { TintColor = grayColor };
        image.Behaviors.Add(behavior);
    }

    private void TapGestureRecognizer_Tapped(object sender, TappedEventArgs e)
    {
        if (DisplayMode == DisplayMode.GrayedOut || IsReadOnly || UseDirectInput)
        {
            return;
        }
        if (Type == LabelResultType.Input)
        {
            var popupService = Application.Current?.Handler.MauiContext.Services.GetService<IPopupService>();
            popupService.ShowPopup<NumberPadViewModel>(vm =>
            {
                vm.IsStandard = true;
                vm.Text = Value;
                vm.Comfirm = new(ChangeValue);
                vm.SettingNumberPadId = SettingNumberPadId;
                vm.InputFormat = InputFormat;
                vm.Title = Title;
            });
        }
        else if (Type == LabelResultType.Classify)
        {
            if (Categories.Count == 0)
            {
                return;
            }
            var index = Categories.IndexOf(Value);
            var indexNextItem = Categories.Count > index + 1 ? ++index : 0;

            ChangeValue(Categories[indexNextItem]);
        }
    }

    private void ChangeValue(string value)
    {
        Value = value;
    }

    private void ApplyLabelColor()
    {
        if (!string.IsNullOrEmpty(LabelColor) && title != null)
        {
            try
            {
                title.TextColor = Color.FromArgb(LabelColor);
            }
            catch
            {
                // Fallback to default color if parsing fails
                title.TextColor = AppColors.LightBlue;
            }
        }
    }

    private void ApplyReadOnlyState()
    {
        if (UseDirectInput)
        {
            if (valueEntry != null)
            {
                valueEntry.IsReadOnly = IsReadOnly;
            }
            // Hide the arrow icon for direct input
            if (image != null)
            {
                image.IsVisible = false;
            }
        }
        else
        {
            if (image != null)
            {
                image.IsVisible = !IsReadOnly;
            }

            if (grid != null)
            {
                grid.IsEnabled = !IsReadOnly;
            }
        }
    }

    private void ApplyTextColor()
    {
        if (!string.IsNullOrEmpty(TextColor))
        {
            try
            {
                var color = Color.FromArgb(TextColor);
                if (valueLabel != null)
                {
                    valueLabel.TextColor = color;
                }
                if (valueEntry != null)
                {
                    valueEntry.TextColor = color;
                }
            }
            catch
            {
                // Fallback to default color if parsing fails
                var fallbackColor = AppColors.LightBlue;
                if (valueLabel != null)
                {
                    valueLabel.TextColor = fallbackColor;
                }
                if (valueEntry != null)
                {
                    valueEntry.TextColor = fallbackColor;
                }
            }
        }
    }
}