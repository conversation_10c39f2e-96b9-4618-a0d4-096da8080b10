namespace ShinKenShinKun;

public partial class LoginPage : BasePage
{
    public LoginPage(LoginPageViewModel vm)
    {
        InitializeComponent();
        BindingContext = vm;
    }

    private void TapGestureRecognizer_Tapped(object sender, TappedEventArgs e)
    {
        passwordEntry.IsPassword = !passwordEntry.IsPassword;
        showHidePassWord.Source = passwordEntry.IsPassword
            ? Icons.Visibility
            : Icons.VisibilityOff;
    }
}