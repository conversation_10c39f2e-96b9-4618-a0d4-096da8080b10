﻿namespace ShinKenShinKun.CoreMVVM;

public abstract partial class BaseViewModel : ObservableRecipient
{
    [ObservableProperty]
    protected bool isBusy;

    [ObservableProperty]
    protected CompomentStatusModel compomentStatus;

    protected IAppNavigator AppNavigator { get; }

    protected BaseViewModel(IAppNavigator appNavigator)
    {
        AppNavigator = appNavigator;
    }

    public virtual Task OnAppearingAsync()
    {
        var name = GetType().FullName;
        Log.Information($"{GetType().Name}.{nameof(OnAppearingAsync)}");

        Shell.Current.Navigated += OnShellNavigated;

        return Task.CompletedTask;
    }

    public virtual Task OnDisappearingAsync()
    {
        Log.Information($"{GetType().Name}.{nameof(OnDisappearingAsync)}");

        Shell.Current.Navigated -= OnShellNavigated;

        return Task.CompletedTask;
    }

    [RelayCommand]
    protected virtual Task BackAsync() => AppNavigator.GoBackAsync(data: GetType().FullName);

    private void OnShellNavigated(object sender, ShellNavigatedEventArgs e)
    {
        string route = e.Current.Location.ToString();
        string lastSegment = route.Split('/').Last(s => !string.IsNullOrEmpty(s));
        CompomentStatus = CompomentHelper.GetCompomentStatus(lastSegment);
    }
}