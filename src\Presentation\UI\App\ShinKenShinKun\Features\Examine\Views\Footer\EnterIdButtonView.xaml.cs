namespace ShinKenShinKun;

public partial class EnterIdButtonView : RadBorder
{
    public EnterIdButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty EnterIdCommandProperty = BindableProperty.Create(
        nameof(EnterIdCommand),
        typeof(IRelayCommand),
        typeof(EnterIdButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand EnterIdCommand
    {
        get => (IRelayCommand)GetValue(EnterIdCommandProperty);
        set => SetValue(EnterIdCommandProperty, value);
    }
}