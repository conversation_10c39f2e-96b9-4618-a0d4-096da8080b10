﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.MedicalDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{
    public class LiteTerminalService
    {
        /// <summary>
        //データセットクラスを使用する
        /// </summary>
        MedicalDataSet medicalDataset = new MedicalDataSet();

        /// <summary>
        /// データ読み書き用クラス
        /// </summary>
        private Service service = null;
        private Logger logger = null;

        #region コンストラクタ
        /// <summary>
        /// コンストラクタ
        /// </summary>
        public LiteTerminalService()
        {
            //デザインされたコンポーネントを使用する場合、次の行をコメントを解除してください 
            //InitializeComponent();
            service = new Service();
        }
        #endregion

        #region StrGetLiteTerminalData
        /// <summary>
        /// LiteTerminalSettingの1レコードと検査種別を取得する。
        /// </summary>
        /// <remarks>
        /// 渡された端末名をキーに、該当するひとつの端末情報と
        /// 対応する検査種別をPermissionIPから取得する。
        /// 該当するデータがなければnullを返す。
        /// </remarks>
        /// <param name="liteTerminalID">端末ID</param>
        /// <returns>String[]</returns>
        public string[] StrGetLiteTerminalData(string liteTerminalID)
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();

                // LiteTerminalSettingsのレコードを取得
                medicalDataset = service.GetLiteTerminalSettingsControl(liteTerminalID);

                if (medicalDataset != null)
                {
                    // 取得した行のコレクションを取得
                    DataRowCollection rowcollection = medicalDataset.Tables["LiteTerminalSettings"].Rows;

                    // データがあれば
                    if (rowcollection.Count != 0)
                    {
                        // 取得したレコード配列に追加
                        DataRow row = rowcollection[0];
                        array.Add(row["LiteTerminalID"].ToString());
                        array.Add(row["IPAddress"].ToString());
                        array.Add(row["SubnetMask"].ToString());
                        array.Add(row["DefaultGateWay"].ToString());
                        array.Add(row["ServerIPAddress"].ToString());
                        array.Add(row["ServerIPPort"].ToString());
                        array.Add(row["SerialSettingS1"].ToString());
                        array.Add(row["SerialSettingS2"].ToString());
                        array.Add(row["SerialSettingS3"].ToString());
                        array.Add(row["SerialBufferSizeS1"].ToString());
                        array.Add(row["SerialBufferSizeS2"].ToString());
                        array.Add(row["SerialBufferSizeS3"].ToString());
                        array.Add(row["SerialReadInterval"].ToString());
                        array.Add(row["SerialReadTimeout"].ToString());
                        array.Add(((DateTime)row["LastSettingDate"]).ToString("yyyyMMddhhmmss"));
                        array.Add(row["ProgramMode"].ToString());
                        array.Add(row["MedicalCheckNo"].ToString());
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

                // ArrayListからString配列に変換
                string[] retData = new string[array.Count];
                Array.Copy(array.ToArray(), retData, array.Count);
                return retData;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region StrGetLiteTerminalDataAll
        /// <summary>
        /// LiteTerminalSettingの全レコードと検査種別を取得する。
        /// </summary>
        /// <remarks>
        /// LiteTerminalSettingに登録されているすべての端末情報と
        /// それぞれに対応する検査種別をPermissionIPから取得する。
        /// 該当するデータがなければnullを返す。
        /// </remarks>
        /// <returns>PluralRecord[]</returns>
        public PluralRecord[] StrGetLiteTerminalDataAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();

                // LiteTerminalSettingsの全レコードを取得
                medicalDataset = service.GetLiteTerminalSettingsControlPlural();

                //// 取得したレコード数をチェックする。
                int recordCount = medicalDataset.Tables["LiteTerminalSettings"].Rows.Count;

                if (recordCount != 0)
                {
                    // 複数レコード返却クラスの配列を作成する
                    PluralRecord[] pRecord = new PluralRecord[recordCount];

                    // レコード数分、処理を繰り返す
                    for (int i = 0; i < recordCount; i++)
                    {
                        DataRow row = medicalDataset.Tables["LiteTerminalSettings"].Rows[i];

                        // 検索した情報を配列に追加していく
                        array.Add(row["LiteTerminalID"].ToString());
                        array.Add(row["IPAddress"].ToString());
                        array.Add(row["SubnetMask"].ToString());
                        array.Add(row["DefaultGateWay"].ToString());
                        array.Add(row["ServerIPAddress"].ToString());
                        array.Add(row["ServerIPPort"].ToString());
                        array.Add(row["SerialSettingS1"].ToString());
                        array.Add(row["SerialSettingS2"].ToString());
                        array.Add(row["SerialSettingS3"].ToString());
                        array.Add(row["SerialBufferSizeS1"].ToString());
                        array.Add(row["SerialBufferSizeS2"].ToString());
                        array.Add(row["SerialBufferSizeS3"].ToString());
                        array.Add(row["SerialReadInterval"].ToString());
                        array.Add(row["SerialReadTimeout"].ToString());
                        array.Add(((DateTime)row["LastSettingDate"]).ToString("yyyyMMddhhmmss"));
                        array.Add(row["ProgramMode"].ToString());
                        array.Add(row["MedicalCheckNo"].ToString());

                        // ArrayListからString配列に変換
                        string[] retData = new string[array.Count];
                        Array.Copy(array.ToArray(), retData, array.Count);
                        pRecord[i] = new PluralRecord(retData);
                        array.Clear();
                    }
                    return pRecord;
                }
                return null;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region StrSetLiteMachineSingleData
        /// <summary>
        /// LiteMachineSettingの１レコードを変更する。
        /// </summary>
        /// <param name="befClientID"></param>
        /// <param name="nowTime"></param>
        /// <returns></returns>
        public bool StrSetLiteTerminalData(string befClientID, DateTime nowTime)
        {
            try
            {
                // LiteMachineSettingsのレコードを取得
                medicalDataset = service.GetLiteTerminalSettingsControlPlural();
                DataRow row = medicalDataset.Tables["LiteTerminalSettings"].Rows.Find(new object[] { befClientID });
                if (row != null)
                {
                    row["LastSettingDate"] = nowTime;
                    //端末情報の更新を行う。
                    LiteTerminalSettingsTableAdapter adapterLiteTerminal
                         = new LiteTerminalSettingsTableAdapter();
                    adapterLiteTerminal.Update(medicalDataset.LiteTerminalSettings);
                    return true;
                }
                return false;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
                return false;
            }
        }
        #endregion
    }
}