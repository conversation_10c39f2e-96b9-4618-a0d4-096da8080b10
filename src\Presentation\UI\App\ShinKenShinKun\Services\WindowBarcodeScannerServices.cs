﻿#if WINDOWS
using CommunityToolkit.Mvvm.Messaging;
using Microsoft.UI.Xaml.Input;
using Windows.System;

namespace ShinKenShinKun;

public class WindowBarcodeScannerServices : IBarcodeScannerServices
{
    public event Action<string>? OnBarcodeScanned;
    private readonly StringBuilder _barcodeBuffer = new();
    private Window? mauiWindow;

    public void StartListening()
    {
        mauiWindow = Application.Current.Windows[0];
        if(mauiWindow?.Handler?.PlatformView is Microsoft.UI.Xaml.Window nativeWindow && nativeWindow.Content != null)
        {
            nativeWindow.Content.KeyDown += OnKeyDown;
        }
    }

    public void StopListening()
    {
        if(mauiWindow?.Handler?.PlatformView is Microsoft.UI.Xaml.Window nativeWindow && nativeWindow.Content != null)
        {
            nativeWindow.Content.KeyDown -= OnKeyDown;
        }
        WeakReferenceMessenger.Default.Unregister<object>(this);
    }

    private void OnKeyDown(object sender, KeyRoutedEventArgs args)
    {
        var key = VirtualKeyToChar(args.Key);
        ProcessKeyInput(key);
    }

    private void ProcessKeyInput(string key)
    {
        if(key == "Enter") // Barcode completed
        {
            string barcode = _barcodeBuffer.ToString();
            _barcodeBuffer.Clear();

            if(!string.IsNullOrWhiteSpace(barcode))
            {
                OnBarcodeScanned?.Invoke(barcode);
            }
        }
        else
        {
            _barcodeBuffer.Append(key);
        }
    }

    private static string VirtualKeyToChar(VirtualKey key)
    {
        return key switch
        {
            >= VirtualKey.A and <= VirtualKey.Z => key.ToString(),
            >= VirtualKey.Number0 and <= VirtualKey.Number9 => ((int)key - (int)VirtualKey.Number0).ToString(),
            VirtualKey.Space => " ",
            VirtualKey.Enter => "Enter",
            VirtualKey.Tab => "Tab",
            VirtualKey.Back => "Backspace",
            _ => string.Empty
        };
    }
}
#endif
