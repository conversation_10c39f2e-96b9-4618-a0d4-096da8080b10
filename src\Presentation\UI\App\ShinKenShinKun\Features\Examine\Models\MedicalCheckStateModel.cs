﻿namespace ShinKenShinKun;

public partial class MedicalCheckStateModel : BaseModel
{
    [ObservableProperty]
    private string medicalCheckName;

    [ObservableProperty]
    private TestStatusModel testStatus;

    public MedicalCheckStateModel Clone()
    {
        return new MedicalCheckStateModel
        {
            MedicalCheckName = this.MedicalCheckName,
            TestStatus = this.TestStatus
        };
    }
}