namespace ShinKenShinKun;

public partial class PatientPendingTableView : ContentView
{
    public static readonly BindableProperty CheckAllClientCommandProperty = BindableProperty.Create(
        nameof(CheckAllClientCommand),
        typeof(IRelayCommand),
        typeof(PatientPendingTableView),
        default(IRelayCommand),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty CheckClientInformationCommandProperty = BindableProperty.Create(
        nameof(CheckClientInformationCommand),
        typeof(IRelayCommand),
        typeof(PatientPendingTableView),
        default(IRelayCommand),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty ClientInformationsProperty = BindableProperty.Create(
        nameof(ClientInformations),
        typeof(ObservableCollection<ClientInformationCheckedModel>),
        typeof(PatientPendingTableView),
        default(ObservableCollection<ClientInformationCheckedModel>),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty IsMaskModeProperty = BindableProperty.Create(
        nameof(IsMaskMode),
        typeof(bool),
        typeof(PatientPendingTableView),
        default(bool),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty IsSelectAllClientProperty = BindableProperty.Create(
        nameof(IsSelectAllClient),
        typeof(bool),
        typeof(PatientPendingTableView),
        default(bool),
        BindingMode.TwoWay
    );

    public PatientPendingTableView()
    {
        InitializeComponent();
    }

    public IRelayCommand CheckAllClientCommand
    {
        get => (IRelayCommand)GetValue(CheckAllClientCommandProperty);
        set => SetValue(CheckAllClientCommandProperty, value);
    }

    public IRelayCommand CheckClientInformationCommand
    {
        get => (IRelayCommand)GetValue(CheckClientInformationCommandProperty);
        set => SetValue(CheckClientInformationCommandProperty, value);
    }

    public ObservableCollection<ClientInformationCheckedModel> ClientInformations
    {
        get =>
            (ObservableCollection<ClientInformationCheckedModel>)
                GetValue(ClientInformationsProperty);
        set => SetValue(ClientInformationsProperty, value);
    }

    public bool IsMaskMode
    {
        get => (bool)GetValue(IsMaskModeProperty);
        set => SetValue(IsMaskModeProperty, value);
    }

    public bool IsSelectAllClient
    {
        get => (bool)GetValue(IsSelectAllClientProperty);
        set => SetValue(IsSelectAllClientProperty, value);
    }
}