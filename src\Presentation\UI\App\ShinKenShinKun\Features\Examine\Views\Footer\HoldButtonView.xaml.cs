namespace ShinKenShinKun;

public partial class HoldButtonView : RadBorder
{
    public HoldButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty HoldCommandProperty = BindableProperty.Create(
        nameof(HoldCommand),
        typeof(IRelayCommand),
        typeof(HoldButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand HoldCommand
    {
        get => (IRelayCommand)GetValue(HoldCommandProperty);
        set => SetValue(HoldCommandProperty, value);
    }
}