﻿namespace ShinKenShinKun.InterfaceLibrary;

public class MeConnectNonSettingException : Exception
{
    #region 変数
    private int FldErrorCode;
    private string FldMsg;
    #endregion

    #region MeConnectNonSettingException
    public MeConnectNonSettingException()
    {
    }
    #endregion
    #region MeConnectNonSettingException
    public MeConnectNonSettingException(string msg)
    {
        FldMsg = msg;
    }
    #endregion
    #region Msg
    public string Msg
    {
        get
        {
            return FldMsg;
        }
    }
    #endregion
    #region MeConnectNonSettingException
    public MeConnectNonSettingException(int errorCode)
    {
        ErrorCode = errorCode;
    }
    #endregion
    #region ErrorCode
    public int ErrorCode
    {
        set
        {
            FldErrorCode = value;
        }
        get
        {
            return FldErrorCode;
        }
    }
    #endregion
}
