namespace ShinKenShinKun;

public partial class FooterView : Grid
{
	public FooterView()
	{
		InitializeComponent();
	}

    #region Bindable Properties
    public static readonly BindableProperty CompomentStatusProperty = BindableProperty.Create(
        nameof(CompomentStatus),
        typeof(CompomentStatusModel),
        typeof(FooterView),
        null,
        BindingMode.TwoWay);

    public static readonly BindableProperty BackCommandProperty = BindableProperty.Create(
        nameof(BackCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty EnterIdCommandProperty = BindableProperty.Create(
        nameof(EnterIdCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty HoldCommandProperty = BindableProperty.Create(
        nameof(HoldCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty PauseCommandProperty = BindableProperty.Create(
        nameof(PauseCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkPauseTestsCommandProperty = BindableProperty.Create(
        nameof(BulkPauseTestsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty RegisterCommandProperty = BindableProperty.Create(
        nameof(RegisterCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkReleaseHoldTestsCommandProperty = BindableProperty.Create(
        nameof(BulkReleaseHoldTestsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkCancelPauseTestsCommandProperty = BindableProperty.Create(
        nameof(BulkCancelPauseTestsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkRegisterHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkRegisterHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkReleaseHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkReleaseHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty ChangeGuidanceCommandProperty = BindableProperty.Create(
        nameof(ChangeGuidanceCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty BulkHoldTestsCommandProperty = BindableProperty.Create(
        nameof(BulkHoldTestsCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty LogoutCommandProperty = BindableProperty.Create(
        nameof(LogoutCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty FinishCommandProperty = BindableProperty.Create(
        nameof(FinishCommand),
        typeof(IRelayCommand),
        typeof(FooterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty ClientSelectedProperty = BindableProperty.Create(
        nameof(ClientSelected),
        typeof(ClientInformationModel),
        typeof(ChangeGuidanceButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty IsBackButtonEnabledProperty = BindableProperty.Create(
        nameof(IsBackButtonEnabled),
        typeof(bool),
        typeof(ChangeGuidanceButtonView),
        true,
        defaultBindingMode: BindingMode.TwoWay);
    #endregion

    #region Properties
    public CompomentStatusModel CompomentStatus
    {
        get => (CompomentStatusModel)GetValue(CompomentStatusProperty);
        set => SetValue(CompomentStatusProperty, value);
    }

    public IRelayCommand BackCommand
    {
        get => (IRelayCommand)GetValue(BackCommandProperty);
        set => SetValue(BackCommandProperty, value);
    }

    public IRelayCommand EnterIdCommand
    {
        get => (IRelayCommand)GetValue(EnterIdCommandProperty);
        set => SetValue(EnterIdCommandProperty, value);
    }

    public IRelayCommand HoldCommand
    {
        get => (IRelayCommand)GetValue(HoldCommandProperty);
        set => SetValue(HoldCommandProperty, value);
    }

    public IRelayCommand PauseCommand
    {
        get => (IRelayCommand)GetValue(PauseCommandProperty);
        set => SetValue(PauseCommandProperty, value);
    }

    public IRelayCommand BulkPauseTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkPauseTestsCommandProperty);
        set => SetValue(BulkPauseTestsCommandProperty, value);
    }

    public IRelayCommand RegisterCommand
    {
        get => (IRelayCommand)GetValue(RegisterCommandProperty);
        set => SetValue(RegisterCommandProperty, value);
    }

    public IRelayCommand BulkReleaseHoldTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkReleaseHoldTestsCommandProperty);
        set => SetValue(BulkReleaseHoldTestsCommandProperty, value);
    }

    public IRelayCommand BulkCancelPauseTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkCancelPauseTestsCommandProperty);
        set => SetValue(BulkCancelPauseTestsCommandProperty, value);
    }

    public IRelayCommand BulkHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkHoldPatientsCommandProperty);
        set => SetValue(BulkHoldPatientsCommandProperty, value);
    }

    public IRelayCommand BulkRegisterHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkRegisterHoldPatientsCommandProperty);
        set => SetValue(BulkRegisterHoldPatientsCommandProperty, value);
    }

    public IRelayCommand BulkReleaseHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkReleaseHoldPatientsCommandProperty);
        set => SetValue(BulkReleaseHoldPatientsCommandProperty, value);
    }

    public IRelayCommand ChangeGuidanceCommand
    {
        get => (IRelayCommand)GetValue(ChangeGuidanceCommandProperty);
        set => SetValue(ChangeGuidanceCommandProperty, value);
    }

    public IRelayCommand BulkHoldTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkHoldTestsCommandProperty);
        set => SetValue(BulkHoldTestsCommandProperty, value);
    }

    public IRelayCommand LogoutCommand
    {
        get => (IRelayCommand)GetValue(LogoutCommandProperty);
        set => SetValue(LogoutCommandProperty, value);
    }

    public IRelayCommand FinishCommand
    {
        get => (IRelayCommand)GetValue(FinishCommandProperty);
        set => SetValue(FinishCommandProperty, value);
    }

    public bool IsBackButtonEnabled
    {
        get => (bool)GetValue(IsBackButtonEnabledProperty);
        set => SetValue(IsBackButtonEnabledProperty, value);
    }

    public ClientInformationModel ClientSelected
    {
        get => (ClientInformationModel)GetValue(ClientSelectedProperty);
        set => SetValue(ClientSelectedProperty, value);
    }
    #endregion
}