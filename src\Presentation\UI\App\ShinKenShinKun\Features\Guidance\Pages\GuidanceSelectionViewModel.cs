namespace ShinKenShinKun;
public partial class GuidanceSelectionViewModel(
    IAppNavigator appNavigator,
    IFileSystemService fileSystemService,
    IAppSettingServices appSettingServices,
    ISessionServices sessionServices,
    IMapper mapper) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObservableProperties

    [ObservableProperty] private ObservableCollection<ClientInformationModel> clientInformations;
    [ObservableProperty] private ClientInformationModel? clientSelected;
    [ObservableProperty] private string medicalMachineName;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string userName;
    [ObservableProperty] private ObservableCollection<GuideItem> guideItems;
    [ObservableProperty] private GuideItem selectedGuideItem;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;
    [ObservableProperty] private bool isHomeButtonEnabled = false;
    [ObservableProperty] private bool isBackButtonEnabled = false;
    #endregion ObservableProperties

    #region RelayCommand
    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    [RelayCommand]
    private async Task SelectButton(GuideItem guideItem)
    {
        switch(guideItem.Status)
        {
            case 1:
                await GeneralProcess(Format(AppResources.ConfirmNavigationMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 2:
                break;
            case 3:
                await GeneralProcess(Format(AppResources.PendingNavigationMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 4:
                await GeneralProcess(Format(AppResources.RequiredTestNotCompletedMessage, guideItem.MedicalCheckName), guideItem);
                break;
            case 5:
                break;
            default:
                break;
        }
    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    #endregion RelayCommand

    #region Override Method

    protected override void OnInit(IDictionary<string, object> query)
    {
        base.OnInit(query);
        UserName = sessionServices.UserName;
        IsLogin = appSettingServices.AppSettings.IsLogin;
        var tuple = query.GetData<Tuple<ClientInformationModel, string>>();
        ClientSelected = tuple?.Item1;
        MedicalMachineName = tuple?.Item2 ?? appSettingServices.AppSettings.DefaultMedicalCheck;
    }

    public override Task OnAppearingAsync()
    {
        IsMaskMode = sessionServices.IsMaskMode;
        GuideItems = mapper.Map<ObservableCollection<GuideItem>>(fileSystemService.ReadGuidanceExcelFile());
        SelectedGuideItem = GuideItems.First();
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        return base.OnAppearingAsync();
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion Override Method

    #region PublicMethod
    #endregion PublicMethod

    #region Private Method

    private async Task GeneralProcess(string title, GuideItem guideItem)
    {
        if(await AppNavigator.ShowConfirmationDialog(title))
        {
            await AppNavigator.NavigateAsync($"{UriHelper.GoBackSegment}/{UriHelper.GoBackSegment}", false, MedicalMachineName);
        }
    }


    #endregion Private Method
}
