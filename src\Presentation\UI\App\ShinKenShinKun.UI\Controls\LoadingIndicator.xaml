<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePopup
    x:Class="ShinKenShinKun.UI.LoadingIndicator"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    Color="Transparent"
    CanBeDismissedByTappingOutsideOfPopup="False"
    x:Name="this">
    <Grid>
        <telerik:RadBusyIndicator
            IsBusy="True"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            AnimationContentHeightRequest="{x:Static ui:Dimens.Height200}"
            AnimationContentWidthRequest="{x:Static ui:Dimens.Width200}"
            AnimationContentColor="{x:Static ui:AppColors.CobaltBlue}">
        </telerik:RadBusyIndicator>
    </Grid>
</mvvm:BasePopup>