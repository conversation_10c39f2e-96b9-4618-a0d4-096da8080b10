global using AutoMapper;
global using CommunityToolkit.Maui;
global using CommunityToolkit.Maui.Core;
global using CommunityToolkit.Maui.Core.Extensions;
global using CommunityToolkit.Maui.Views;
global using CommunityToolkit.Mvvm.ComponentModel;
global using CommunityToolkit.Mvvm.Input;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.Logging;
global using Serilog;
global using Serilog.Events;
global using ShinKenShinKun.CoreMVVM;
global using ShinKenShinKun.DataAccess;
global using ShinKenShinKun.UI;
global using ShinKenShinKun.Utils;
global using ShinKenShinKun.Utils.Resources;
global using Syncfusion.Maui.Toolkit.Hosting;
global using System.Collections;
global using System.Collections.ObjectModel;
global using System.Diagnostics;
global using System.Text;
global using System.Text.RegularExpressions;
global using System.Windows.Input;
global using Telerik.Maui;
global using Telerik.Maui.Controls;
global using Telerik.Maui.Controls.Compatibility;
global using static System.String;
global using System.Arctec.Ar1000k.DataBase.ArctecServer;
global using FFImageLoading.Maui;
global using System.ComponentModel.DataAnnotations;
global using System.Arctec.Ar1000k.DataBase;