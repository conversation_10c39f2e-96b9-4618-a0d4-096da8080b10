﻿{
  "IsLogin": true,
  "DefaultScreen": "MC-002",
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=***************;Initial Catalog=GenkiPlazaDatabase2;Persist Security Info=True;User ID=sa;Password=*************;TrustServerCertificate=True"
  },
  "DefaultMedicalCheck": "計測",
  "MonitoringSystemFolderPath": "C:\\MonitoringSystem\\ControllerMonitor.exe",
  "MonitoringSystemWorkingFolderPath": "C:\\MonitoringSystem",
  "DataAccessControl": {
    "TimeOut": 30,
    "ServerUrl": "http://localhost:8999/PublicService.asmx"
  },
  "LogSettings": {
    "LogPath": "C:\\Arctec\\ShinKenShinKun\\Log",
    "LogName": "Log"
  },
  "MedicalCheckProgressSetting": [
    {
      "Status": 0,
      "StatusName": "未",
      "BackgroundColor": "#F7C92E",
      "TextColor": "#FFFFFF"
    },
    {
      "Status": 1,
      "StatusName": "済",
      "BackgroundColor": "#5766C1",
      "TextColor": "#FFFFFF"
    },
    {
      "Status": 2,
      "StatusName": "外",
      "BackgroundColor": "#A6A6A6",
      "TextColor": "#FFFFFF"
    },
    {
      "Status": 3,
      "StatusName": "保",
      "BackgroundColor": "#E8E8E8",
      "TextColor": "#000000"
    },
    {
      "Status": 4,
      "StatusName": "次",
      "BackgroundColor": "#CF37BC",
      "TextColor": "#FFFFFF"
    },
    {
      "Status": 5,
      "StatusName": "止",
      "BackgroundColor": "#F92F1A",
      "TextColor": "#FFFFFF"
    }
  ],
  "AreaSettings": [
    {
      "AreaId": "0",
      "ScreenId": "5",
      "Order": 0,
      "Height": 50,
      "Margin": "10,5,10,5",
      "HorizontalOptions": "Fill",
      "VerticalOptions": "Start",
      "RowRatios": "1",
      "ColumnRatios": "1",
      "ComponentLayouts": [
        {
          "ComponentId": "0",
          "AreaId": "0",
          "RowIndex": 0,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "0,0,0,0",
          "VerticalOptions": "Center",
          "HorizontalOptions": "Start",
          "ComponentType": 0,
          "Text": "過去値表示ラベル"
        }
      ]
    },
    {
      "AreaId": "1",
      "ScreenId": "5",
      "Order": 1,
      "Height": 300,
      "Margin": "10,5,10,5",
      "HorizontalOptions": "Fill",
      "VerticalOptions": "Start",
      "RowRatios": "1,1,1",
      "ColumnRatios": "1,1,1",
      "ComponentLayouts": [
        {
          "ComponentId": "1",
          "AreaId": "1",
          "RowIndex": 0,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 1,
          "Title": "Name",
          "Value": "HAHA",
          "TextColor": "#000000",
          "LabelColor": "#5A9BD5",
          "IsReadOnly": false,
          "LabelResultType": 0,
          "UseDirectInput": true,
          "TextAlignment": 1,
          "MinLength": 2,
          "MaxLength": 10
        },
        {
          "ComponentId": "2",
          "AreaId": "1",
          "RowIndex": 0,
          "ColumnIndex": 1,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "MedicalCheckItemNo",
          "Value": "a1",
          "TextColor": "#000000",
          "LabelColor": "#5A9BD5",
          "IsReadOnly": false
        },
        {
          "ComponentId": "3",
          "AreaId": "1",
          "RowIndex": 0,
          "ColumnIndex": 2,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "MedicalCheckNo",
          "Value": "2",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "4",
          "AreaId": "1",
          "RowIndex": 1,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "糖",
          "Value": "Hihi",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "5",
          "AreaId": "1",
          "RowIndex": 1,
          "ColumnIndex": 1,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "沈渣",
          "Value": "a2",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "6",
          "AreaId": "1",
          "RowIndex": 1,
          "ColumnIndex": 2,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "代謝1",
          "Value": "a3",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "7",
          "AreaId": "1",
          "RowIndex": 2,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "潜血",
          "Value": "Hoho",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "8",
          "AreaId": "1",
          "RowIndex": 2,
          "ColumnIndex": 1,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 3,
          "Title": "比重",
          "Value": "lolo",
          "TextColor": "#000000"
        },
        {
          "ComponentId": "9",
          "AreaId": "1",
          "RowIndex": 2,
          "ColumnIndex": 2,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 1,
          "Title": "代謝2",
          "Value": "a4",
          "TextColor": "#000000"
        }
      ]
    },
    {
      "AreaId": "2",
      "ScreenId": "5",
      "Order": 2,
      "Height": 50,
      "Margin": "10,5,10,5",
      "HorizontalOptions": "Fill",
      "VerticalOptions": "Start",
      "RowRatios": "1",
      "ColumnRatios": "1,1",
      "ComponentLayouts": [
        {
          "ComponentId": "5",
          "AreaId": "2",
          "RowIndex": 0,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "0,0,10,10",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 2,
          "Text": ""
        },
        {
          "ComponentId": "6",
          "AreaId": "2",
          "RowIndex": 0,
          "ColumnIndex": 1,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "10,0,0,10",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 2,
          "Text": ""
        }
      ]
    },
    {
      "AreaId": "4",
      "ScreenId": "5",
      "Order": 4,
      "Height": 100,
      "Margin": "10,5,10,5",
      "HorizontalOptions": "Fill",
      "VerticalOptions": "Start",
      "RowRatios": "1",
      "ColumnRatios": "1,1",
      "ComponentLayouts": [
        {
          "ComponentId": "7",
          "AreaId": "4",
          "RowIndex": 0,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "0,0,10,0",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 4,
          "Text": "眼圧右",
          "StatusList": [
            {
              "StatusColor": "#F7C92E",
              "StatusName": "未"
            },
            {
              "StatusColor": "#F92F1A",
              "StatusName": "未"
            }
          ],
          "CurrentState": {
            "StatusColor": "#F7C92E",
            "StatusName": "未"
          }
        },
        {
          "ComponentId": "8",
          "AreaId": "4",
          "RowIndex": 0,
          "ColumnIndex": 1,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "10,0,0,0",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 4,
          "Text": "眼圧左",
          "StatusList": [
            {
              "StatusColor": "#F7C92E",
              "StatusName": "未"
            },
            {
              "StatusColor": "#F92F1A",
              "StatusName": "未"
            }
          ],
          "CurrentState": {
            "StatusColor": "#F7C92E",
            "StatusName": "未"
          }
        }
      ]
    },
    {
      "AreaId": "5",
      "ScreenId": "5",
      "Order": 5,
      "Height": 100,
      "Margin": "10,5,10,5",
      "HorizontalOptions": "Fill",
      "VerticalOptions": "End",
      "RowRatios": "1",
      "ColumnRatios": "1,1,1,1",
      "ComponentLayouts": [
        {
          "ComponentId": "13",
          "AreaId": "6",
          "RowIndex": 0,
          "ColumnIndex": 0,
          "RowSpan": 1,
          "ColumnSpan": 1,
          "Margin": "5,5,5,5",
          "HorizontalOptions": "Fill",
          "VerticalOptions": "Fill",
          "ComponentType": 5,
          "Text": "Submithaha",
          "BackgroundColor": "#FF6B35"
        }
      ]
    }
  ]
}