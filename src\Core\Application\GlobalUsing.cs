﻿global using System.Reflection;
global using ShinKenShinKun.Application.Common.Exceptions;
global using ShinKenShinKun.Application.Common.Interfaces;
global using ShinKenShinKun.Application.Common.Security;
global using MediatR;
global using MediatR.Pipeline;
global using Microsoft.Extensions.Logging;
global using System.Diagnostics;
global using FluentValidation;
global using ValidationException = ShinKenShinKun.Application.Common.Exceptions.ValidationException;
global using FluentValidation.Results;
global using Microsoft.EntityFrameworkCore;
global using ShinKenShinKun.Shared;
global using ShinKenShinKun.Domain.Entities;
global using ShinKenShinKun.Application.Common.Models;
global using AutoMapper;
global using AutoMapper.QueryableExtensions;
global using ShinKenShinKun.Application.Common.Mappings;
global using ShinKenShinKun.Domain.Events;
global using ShinKenShinKun.Domain.Enums;
global using ShinKenShinKun.Application.Common.Extensions;
global using ShinKenShinKun.Domain.ValueObjects;
global using ShinKenShinKun.Application.Common.Behaviours;