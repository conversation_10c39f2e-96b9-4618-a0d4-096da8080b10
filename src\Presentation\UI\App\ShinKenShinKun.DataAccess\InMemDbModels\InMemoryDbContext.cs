﻿namespace ShinKenShinKun.DataAccess;

public class InMemoryDbContext(DbContextOptions<InMemoryDbContext> options) : DbContext(options)
{
    public DbSet<CustomMasterKey> CustomMasterKeys { get; set; }

    public DbSet<CustomMasterKeySetting> CustomMasterKeySettings { get; set; }

    public DbSet<SettingNumberPad> SettingNumberPads { get; set; }

    public DbSet<StandardMasterKey> StandardMasterKeys { get; set; }

    public DbSet<StandardMasterKeySetting> StandardMasterKeySettings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // CustomMasterKey
        modelBuilder.Entity<CustomMasterKey>(entity =>
        {
            entity.HasKey(e => e.CustomKeyId);

            entity.Property(e => e.KeyName).HasMaxLength(200);
            entity.Property(e => e.Text).HasMaxLength(500);
            entity.Property(e => e.Value).HasMaxLength(500);
            entity.Property(e => e.Action).HasMaxLength(500);

            entity
                .HasMany(e => e.CustomMasterKeySettings)
                .WithOne(e => e.CustomMasterKey)
                .HasForeignKey(e => e.CustomMasterKeyId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // CustomMasterKeySetting
        modelBuilder.Entity<CustomMasterKeySetting>(entity =>
        {
            entity.HasKey(e => new { e.SettingNumberPadId, e.CustomMasterKeyId });

            entity.Property(e => e.BackgroundColor).HasMaxLength(100);
            entity.Property(e => e.TextColor).HasMaxLength(100);

            entity
                .HasOne(e => e.SettingNumberPad)
                .WithMany(e => e.CustomMasterKeySettings)
                .HasForeignKey(e => e.SettingNumberPadId)
                .OnDelete(DeleteBehavior.Cascade);

            entity
                .HasOne(e => e.CustomMasterKey)
                .WithMany(e => e.CustomMasterKeySettings)
                .HasForeignKey(e => e.CustomMasterKeyId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // SettingNumberPad
        modelBuilder.Entity<SettingNumberPad>(entity =>
        {
            entity.HasKey(e => e.SettingNumberPadId);

            entity.Property(e => e.SettingNumberPadName).HasMaxLength(200);

            entity
                .HasMany(e => e.StandarMasterKeySettings)
                .WithOne(e => e.SettingNumberPad)
                .HasForeignKey(e => e.SettingNumberPadId)
                .OnDelete(DeleteBehavior.Cascade);

            entity
                .HasMany(e => e.CustomMasterKeySettings)
                .WithOne(e => e.SettingNumberPad)
                .HasForeignKey(e => e.SettingNumberPadId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // StandardMasterKey
        modelBuilder.Entity<StandardMasterKey>(entity =>
        {
            entity.HasKey(e => e.StandardKeyId);

            entity.Property(e => e.KeyName).HasMaxLength(200);

            entity
                .HasMany(e => e.StandarMasterKeySettings)
                .WithOne(e => e.StandardMasterKey)
                .HasForeignKey(e => e.StandardMasterKeyId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // StandardMasterKeySetting
        modelBuilder.Entity<StandardMasterKeySetting>(entity =>
        {
            entity.HasKey(e => new { e.SettingNumberPadId, e.StandardMasterKeyId });

            entity.Property(e => e.BackgroundColor).HasMaxLength(100);
            entity.Property(e => e.TextColor).HasMaxLength(100);

            entity
                .HasOne(e => e.SettingNumberPad)
                .WithMany(e => e.StandarMasterKeySettings)
                .HasForeignKey(e => e.SettingNumberPadId)
                .OnDelete(DeleteBehavior.Cascade);

            entity
                .HasOne(e => e.StandardMasterKey)
                .WithMany(e => e.StandarMasterKeySettings)
                .HasForeignKey(e => e.StandardMasterKeyId)
                .OnDelete(DeleteBehavior.Cascade);
        });

    }
}