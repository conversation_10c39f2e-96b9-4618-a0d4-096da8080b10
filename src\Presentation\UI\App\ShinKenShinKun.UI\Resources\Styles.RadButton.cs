﻿using Microsoft.Maui.Layouts;
using static Microsoft.Maui.Controls.Button;

namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style BackRadButtonStyle => CreateStyle<RadButton>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.GrayBack)
        .Set(ContentLayoutProperty, new ButtonContentLayout(ButtonContentLayout.ImagePosition.Top, 0))
        .Set(CornerRadiusProperty, Dimens.RadCornerRadius3)
        .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Subtitle, typeof(RadButton)))
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Start)
        .Set(ImageSourceProperty, Icons.Undo40X40)
        .Set(TextProperty, AppResources.Back)
        .Set(TextColorProperty, AppColors.White)
        .Set(TextTransformProperty, TextTransform.Uppercase)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height100)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width100);

    public static Style HomeRadButtonStyle => CreateStyle<RadButton>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.DarkBlue)
        .Set(BorderColorProperty, AppColors.White)
        .Set(RadButton.BorderThicknessProperty, Dimens.RadBorderThickness3)
        .Set(ContentLayoutProperty, new ButtonContentLayout(ButtonContentLayout.ImagePosition.Top, 0))
        .Set(CornerRadiusProperty, Dimens.RadCornerRadius3)
        .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Micro, typeof(RadButton)))
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Start)
        .Set(ImageSourceProperty, Icons.Home40X40)
        .Set(TextProperty, AppResources.Home)
        .Set(TextTransformProperty, TextTransform.Uppercase);

    public static Style LoginButtonStyle => CreateStyle<RadButton>()
       .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
       .Set(TextColorProperty, AppColors.White)
       .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Medium, typeof(RadButton)))
       .Set(View.HorizontalOptionsProperty, LayoutOptions.Center)
       .Set(View.VerticalOptionsProperty, LayoutOptions.Center)
       .Set(VisualElement.BackgroundColorProperty, AppColors.HanBlue);

    public static Style MedicalChecklistRadButtonStyle => CreateStyle<RadButton>()
       .Set(PaddingProperty, Dimens.Spacing10)
       .Set(View.MarginProperty, Dimens.Spacing5)
       .Set(CornerRadiusProperty, Dimens.RadBorderCornerRadius5)
       .Set(FlexLayout.BasisProperty, new FlexBasis(0.25f, true))
       .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
       .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(RadButton)))
       .Set(View.HorizontalOptionsProperty, LayoutOptions.Fill);

    public static Style ShutdownButtonStyle => CreateStyle<RadButton>()
        .Set(View.MarginProperty,
            new Thickness(
                left: 0,
                top: 0,
                right: Dimens.Spacing10,
                bottom: Dimens.Spacing5
            ))
        .Set(PaddingProperty, 0)
        .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Micro, typeof(RadButton)))
        .Set(ImageSourceProperty, Icons.PowerOff40X40)
        .Set(TextColorProperty, AppColors.White)
        .Set(RadButton.HorizontalContentAlignmentProperty, TextAlignment.Center)
        .Set(RadButton.VerticalContentAlignmentProperty, TextAlignment.Center)
        .Set(View.HorizontalOptionsProperty, LayoutOptions.End)
        .Set(VisualElement.BackgroundColorProperty, AppColors.HanBlue)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height90)
        .Set(VisualElement.WidthRequestProperty, Dimens.Width90)
        .Set(ContentLayoutProperty, new ButtonContentLayout(ButtonContentLayout.ImagePosition.Top, Dimens.Spacing5));

    public static Style MenuItemButtonStyle => CreateStyle<RadButton>()
        .Set(RadButton.HeightRequestProperty, Dimens.Width350)
        .Set(RadButton.PaddingProperty, Dimens.Spacing10)
        .Set(RadButton.MarginProperty, Dimens.Spacing5)
        .Set(RadButton.CornerRadiusProperty, Dimens.RadBorderCornerRadius28);

    public static Style IdInputButtonStyle => CreateStyle<RadButton>()
    .Set(RadButton.BackgroundColorProperty, AppColors.White)
    .Set(RadButton.WidthRequestProperty, Dimens.Width100)
    .Set(RadButton.HeightRequestProperty, Dimens.Height80)
    .Set(RadButton.BorderColorProperty, AppColors.Gray82)
    .Set(RadButton.FontSizeProperty, Dimens.FontSizeT3)
    .Set(RadButton.CornerRadiusProperty, 4)
    .Set(RadButton.TextColorProperty, AppColors.Black)
    .Set(RadButton.BorderWidthProperty, 1);

    public static Style IdInputEnterButtonStyle => CreateStyle<RadButton>()
    .Set(RadButton.BackgroundColorProperty, AppColors.DarkBlue)
    .Set(RadButton.BorderColorProperty, AppColors.MidnightExpressBlue)
    .Set(RadButton.WidthRequestProperty, Dimens.Width100)
    .Set(RadButton.FontSizeProperty, Dimens.FontSizeT3)
    .Set(RadButton.CornerRadiusProperty, 4)
    .Set(RadButton.TextColorProperty, AppColors.White)
    .Set(RadButton.BorderWidthProperty, 1);

    public static Style IdInputZeroButtonStyle => CreateStyle<RadButton>()
    .Set(RadButton.BackgroundColorProperty, AppColors.White)
    .Set(RadButton.HeightRequestProperty, Dimens.Height80)
    .Set(RadButton.BorderColorProperty, AppColors.Gray82)
    .Set(RadButton.FontSizeProperty, Dimens.FontSizeT3)
    .Set(RadButton.CornerRadiusProperty, 4)
    .Set(RadButton.TextColorProperty, AppColors.Black)
    .Set(RadButton.BorderWidthProperty, 1);

    public static Style MedicalChecklistStopRadButtonStyle => CreateStyle<RadButton>()
      .Set(PaddingProperty, Dimens.Spacing0)
      .Set(View.MarginProperty, new Thickness(left: 0, top: 0, right: Dimens.Spacing1, bottom: Dimens.Spacing1))
      .Set(CornerRadiusProperty, 5)
      .Set(VisualElement.BackgroundColorProperty, AppColors.White)
      .Set(BorderColorProperty, AppColors.Transparent)
      .Set(RadButton.BorderThicknessProperty, Dimens.RadBorderThickness2)
      .Set(FlexLayout.BasisProperty, new FlexBasis(0.25f, true))
      .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
      .Set(TextColorProperty, AppColors.Black)
      .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Subtitle, typeof(RadButton)))
      .Set(View.HorizontalOptionsProperty, LayoutOptions.Fill);

    public static Style StopExamineFunctionRadButtonStyle => CreateStyle<RadButton>()
      .Set(VisualElement.BackgroundColorProperty, AppColors.White)
      .Set(FontFamilyProperty, FontNames.NotoSansJPRegular)
      .Set(TextColorProperty, AppColors.Black)
      .Set(VisualElement.ShadowProperty, new Shadow { Opacity = 0.5f, Radius = 2, Offset = new(2, 2) })
      .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Medium, typeof(RadButton)));

    public static Style FunctionButtonDefaultStyle => CreateStyle<RadButton>()
        .Set(VisualElement.BackgroundColorProperty, AppColors.SpaceCadet)
        .Set(FontSizeProperty, Device.GetNamedSize(NamedSize.Small, typeof(RadButton)))
        .Set(TextColorProperty, AppColors.White)
        .Set(CornerRadiusProperty, Dimens.RadCornerRadius3);

    public static Style NumericPadRadButtonStyle => CreateStyle<RadButton>()
        .Set(RadButton.BackgroundColorProperty, AppColors.RipeMango)
        .Set(RadButton.FontSizeProperty, Device.GetNamedSize(NamedSize.Large, typeof(RadButton)))
        .Set(RadButton.TextColorProperty, Colors.White)
        .Set(RadButton.ShadowProperty, Styles.NumericPadButtonShadowStyle);
}