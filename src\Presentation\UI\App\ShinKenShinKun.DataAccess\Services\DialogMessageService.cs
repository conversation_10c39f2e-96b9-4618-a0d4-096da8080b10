﻿namespace ShinKenShinKun.DataAccess;

public class DialogMessageService(InMemoryDbContext inMemoryDb, IMapper mapper, DataAccessControl dataAccessControl) : IDialogMessageService
{
    public List<DialogMessageDto> GetAllMessage()
    {
        try
        {
            var res = dataAccessControl.GetAllMessage();
            var messages = System.Arctec.Ar1000k.DataBase.ParseUtility.MapDataTableToModel<Message>(res);
            return mapper.Map<List<DialogMessageDto>>(messages);
        }
        catch (Exception ex)
        {
            Log.Error("DialogMessageService", $"GetAllMessage failed: {ex.Message} {ex.StackTrace}");
            return [];
        }
    }
}