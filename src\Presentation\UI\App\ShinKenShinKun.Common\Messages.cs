﻿//using System.Collections;
//using System.Drawing;
//using System.Reflection;


//namespace Kenshinkun.Common
//{
//    /// <summary>
//    /// Currently pending for custom MAUI message box
//    /// </summary>
//    public class Messages
//    {
//        #region 変数
//        private static Messages Instance;
//        private DataTable ResourceTable;
//        private const string MESSAGE_FILENAME = "MessageResource.xml";
//        private const string TABLE_NAME = "Message";
//        private const string COLUMN_MESSAGE_ID = "ID";
//        private const string COLUMN_MESSAGE_TEXT = "Text";
//        private const string COLUMN_MESSAGE_BUTTONS = "Buttons";
//        private const string COLUMN_MESSAGE_ICON = "Icon";
//        // メッセージリソースの取得に失敗した場合のメッセージ
//        private const string NOT_FOUND_FILE_MESSAGE = "{0}が見つかりませんでした。";
//        private const string NOT_FOUND_FILE_CAPTION = "MSG0001-E";
//        private const MessageBoxButtons NOT_FOUND_FILE_BUTTONS = MessageBoxButtons.OK;
//        private const MessageBoxIcon NOT_FOUND_FILE_ICON = MessageBoxIcon.Hand;
//        private const string IO_ERROR_FILE_MESSAGE = "{0}のファイル入出力でエラーが発生しました。";
//        private const string IO_ERROR_FILE_CAPTION = "MSG0002-E";
//        private const MessageBoxButtons IO_ERROR_FILE_BUTTONS = MessageBoxButtons.OK;
//        private const ArMessageBoxIcon.Icon IO_ERROR_FILE_ICON = ArMessageBoxIcon.Icon.Error;
//        private const string NOT_FOUND_RESOURCE_MESSAGE = "{0}に該当するメッセージリソースが存在しませんでした。";
//        private const string NOT_FOUND_RESOURCE_CAPTION = "MSG0003-E";
//        private const MessageBoxButtons NOT_FOUND_RESOURCE_BUTTONS = MessageBoxButtons.OK;
//        private const ArMessageBoxIcon.Icon NOT_FOUND_RESOURCE_ICON = ArMessageBoxIcon.Icon.Error;
//        // 設定値が無い場合のDefaul設定…
//        private const string CAPTION_DEFAULT = "";
//        private const MessageBoxButtons BUTTONS_DEFAULT = MessageBoxButtons.OK;
//        private const MessageBoxIcon ICON_DEFAULT = MessageBoxIcon.None;
//        private const string LOG_TITTLE = "【Messages】";
//        private ClientLogger Logger;
//        /// <summary>
//        /// 音声出力設定
//        /// </summary>
//        private string BeepSoundSetting = "0";
//        // 音声を出力しない
//        public const string NOT_SOUND = "0";
//        // 警告のみ出力する
//        public const string ONLY_WARNING = "1";
//        // エラーのみ出力する
//        public const string ONLY_ERROR = "2";
//        // 警告とエラーのみ出力する
//        public const string ONLY_WARN_ERROR = "3";
//        // 全てを出力する
//        public const string ALL_SOUND = "4";
//        #endregion

//        #region コンストラクタ
//        private Messages(string FilePath)
//        {
//            Logger = ClientLogger.GetInstance();
//            try
//            {
//                Configration Conf = new Configration(FilePath);
//                ResourceTable = Conf.GetTable(TABLE_NAME);
//            }
//            catch (FileNotFoundException e)
//            {
//                ArrayList ArgStr = new ArrayList();
//                ArgStr.Add(FilePath);
//                throw e;
//            }
//        }
//        #endregion

//        #region インスタンス取得
//        public static Messages GetInstance()
//        {
//            if (Instance == null)
//            {
//                Assembly asm = Assembly.GetExecutingAssembly();
//                OperatingSystem osInfo = Environment.OSVersion;
//                bool? IsXpModeFlg = null;
//                // 使用マシンがＸＰの場合
//                if (osInfo.Version.Major == 5 && osInfo.Version.Minor == 1)
//                {
//                    IsXpModeFlg = true;
//                }
//                // 使用マシンがWin7の場合
//                else if (osInfo.Version.Major == 6 && osInfo.Version.Minor >= 1)
//                {
//                    IsXpModeFlg = true;
//                }
//                else
//                {
//                    IsXpModeFlg = false;
//                }
//                string xmlFilePath = Path.GetDirectoryName(System.Reflection.Assembly.GetEntryAssembly().GetName().CodeBase);
//                //string xmlFilePath = Directory.GetCurrentDirectory();
//                if (IsXpModeFlg.HasValue && IsXpModeFlg.Value)
//                {
//                    xmlFilePath = Directory.GetCurrentDirectory();
//                }
//                xmlFilePath = Path.Combine(xmlFilePath, MESSAGE_FILENAME);
//                Instance = new Messages(xmlFilePath);
//            }
//            return Instance;
//        }
//        #endregion

//        #region メッセージ表示（引数３）
//        public MessageDialogResult ShowDialog(string MessageCode, ArrayList Args)
//        {
//            // 結果ダイアログ(OKボタンが押されたものとして初期化します。)
//            MessageDialogResult Result = MessageDialogResult.OK;
//            if (string.IsNullOrEmpty(MessageCode))
//            {
//                return Result;
//            }
//            // 引数で指定されたMessageIDで抽出…
//            DataRow[] Rows;
//            // リソースから検索
//            Rows = ResourceTable.Select(COLUMN_MESSAGE_ID + " = '" + MessageCode + "'");
//            // １件も見つからなかった場合は何もしない。
//            if (Rows.Length == 0)
//            {
//                ArrayList ArgStr = new ArrayList();
//                ArgStr.Add(MessageCode);
//                Result = ArMessageBox.Show(NOT_FOUND_RESOURCE_BUTTONS,
//                                            NOT_FOUND_RESOURCE_ICON,
//                                            Replace(NOT_FOUND_RESOURCE_MESSAGE, ArgStr),
//                                            NOT_FOUND_RESOURCE_CAPTION);
//                return Result;
//            }
//            // 初期値
//            string MessageText = "";
//            string Caption = CAPTION_DEFAULT;
//            MessageBoxButtons Buttons = MessageBoxButtons.OK;
//            ArMessageBoxIcon.Icon Icon = ArMessageBoxIcon.Icon.Message;
//            MessageText = Rows[0][COLUMN_MESSAGE_TEXT].ToString();
//            // 置き換え文字を置き換えます。
//            if (Args != null)
//            {
//                MessageText = Replace(MessageText, Args);
//            }
//            // キャプションにはメッセージコードが入ります。
//            Caption = MessageCode;
//            // Buttonsの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_BUTTONS) == false && Rows[0][COLUMN_MESSAGE_BUTTONS].ToString().Trim().Length > 0)
//            {
//                int buttonNo = int.Parse(Rows[0][COLUMN_MESSAGE_BUTTONS].ToString());
//                Buttons = (MessageBoxButtons)buttonNo;
//            }
//            // Iconの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_ICON) == false && Rows[0][COLUMN_MESSAGE_ICON].ToString().Trim().Length > 0)
//            {
//                int iconNo = int.Parse(Rows[0][COLUMN_MESSAGE_ICON].ToString());
//                Icon = (ArMessageBoxIcon.Icon)iconNo;
//            }
//            MessageText = MessageText.Replace("\\n", "\n");
//            // MessageBox表示
//            Result = ArMessageBox.Show(Buttons, Icon, MessageText, Caption);
//            Rows = null;
//            // ダイアログの戻り値をリターンする。
//            return Result;
//        }
//        #endregion

//        #region メッセージ表示（引数４）エラー詳細
//        public MessageDialogResult ShowDialog(string MessageCode, ArrayList Args, string error)
//        {
//            // 結果ダイアログ(OKボタンが押されたものとして初期化します。)
//            MessageDialogResult Result = MessageDialogResult.OK;
//            if (string.IsNullOrEmpty(MessageCode))
//            {
//                return Result;
//            }
//            // 引数で指定されたMessageIDで抽出…
//            DataRow[] Rows;
//            // リソースから検索
//            Rows = ResourceTable.Select(COLUMN_MESSAGE_ID + " = '" + MessageCode + "'");
//            // １件も見つからなかった場合は何もしない。
//            if (Rows.Length == 0)
//            {
//                ArrayList ArgStr = new ArrayList();
//                ArgStr.Add(MessageCode);
//                Result = ArMessageBox.Show(NOT_FOUND_RESOURCE_BUTTONS,
//                                                NOT_FOUND_RESOURCE_ICON,
//                                                Replace(NOT_FOUND_RESOURCE_MESSAGE, ArgStr),
//                                                NOT_FOUND_RESOURCE_CAPTION);
//                return Result;
//            }
//            // 初期値
//            string MessageText = "";
//            string Caption = CAPTION_DEFAULT;
//            MessageBoxButtons Buttons = MessageBoxButtons.OK;
//            ArMessageBoxIcon.Icon Icon = ArMessageBoxIcon.Icon.Message;
//            MessageText = Rows[0][COLUMN_MESSAGE_TEXT].ToString();
//            // 置き換え文字を置き換えます。
//            if (Args != null)
//            {
//                MessageText = Replace(MessageText, Args);
//            }
//            // キャプションにはメッセージコードが入ります。
//            Caption = MessageCode;
//            // Buttonsの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_BUTTONS) == false && Rows[0][COLUMN_MESSAGE_BUTTONS].ToString().Trim().Length > 0)
//            {
//                int buttonNo = int.Parse(Rows[0][COLUMN_MESSAGE_BUTTONS].ToString());
//                Buttons = (MessageBoxButtons)buttonNo;
//            }
//            // Iconの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_ICON) == false && Rows[0][COLUMN_MESSAGE_ICON].ToString().Trim().Length > 0)
//            {
//                int iconNo = int.Parse(Rows[0][COLUMN_MESSAGE_ICON].ToString());
//                Icon = (ArMessageBoxIcon.Icon)iconNo;
//            }
//            MessageText = MessageText.Replace("\\n", "\n");
//            // MessageBox表示
//            Result = ArMessageBox.Show(Buttons, Icon, MessageText, Caption, error);
//            Rows = null;
//            // ダイアログの戻り値をリターンする。
//            return Result;
//        }
//        #endregion

//        #region メッセージ表示（引数５）
//        public MessageDialogResult ShowDialog(string MessageCode, ArrayList Args, Font arMsgFont, Color arMsgColor)
//        {
//            // 結果ダイアログ(OKボタンが押されたものとして初期化します。)
//            MessageDialogResult Result = MessageDialogResult.OK;
//            if (string.IsNullOrEmpty(MessageCode))
//            {
//                return Result;
//            }
//            // 引数で指定されたMessageIDで抽出…
//            DataRow[] Rows;
//            // リソースから検索
//            Rows = ResourceTable.Select(COLUMN_MESSAGE_ID + " = '" + MessageCode + "'");
//            // １件も見つからなかった場合は何もしない。
//            if (Rows.Length == 0)
//            {
//                ArrayList ArgStr = new ArrayList();
//                ArgStr.Add(MessageCode);
//                Result = ArMessageBox.Show(NOT_FOUND_RESOURCE_BUTTONS,
//                                            NOT_FOUND_RESOURCE_ICON,
//                                            Replace(NOT_FOUND_RESOURCE_MESSAGE, ArgStr),
//                                            NOT_FOUND_RESOURCE_CAPTION);
//                return Result;
//            }
//            // 初期値
//            string MessageText = "";
//            string Caption = CAPTION_DEFAULT;
//            MessageBoxButtons Buttons = MessageBoxButtons.OK;
//            ArMessageBoxIcon.Icon Icon = ArMessageBoxIcon.Icon.Message;
//            MessageText = Rows[0][COLUMN_MESSAGE_TEXT].ToString();
//            // 置き換え文字を置き換えます。
//            if (Args != null)
//            {
//                MessageText = Replace(MessageText, Args);
//            }
//            // キャプションにはメッセージコードが入ります。
//            Caption = MessageCode;
//            // Buttonsの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_BUTTONS) == false && Rows[0][COLUMN_MESSAGE_BUTTONS].ToString().Trim().Length > 0)
//            {
//                int buttonNo = int.Parse(Rows[0][COLUMN_MESSAGE_BUTTONS].ToString());
//                Buttons = (MessageBoxButtons)buttonNo;
//            }
//            // Iconの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_ICON) == false && Rows[0][COLUMN_MESSAGE_ICON].ToString().Trim().Length > 0)
//            {
//                int iconNo = int.Parse(Rows[0][COLUMN_MESSAGE_ICON].ToString());
//                Icon = (ArMessageBoxIcon.Icon)iconNo;
//            }
//            MessageText = MessageText.Replace("\\n", "\n");
//            Result = ArMessageBox.Show(Buttons, Icon, MessageText, Caption, arMsgFont, arMsgColor);
//            Rows = null;
//            return Result;
//        }
//        #endregion

//        #region メッセージ表示（引数６）
//        public MessageDialogResult ShowDialog(
//            string MessageCode,
//            ArrayList Args,
//            Font arMsgFont,
//            Color backColor,
//            Color foreColor
//            )
//        {
//            // 結果ダイアログ(OKボタンが押されたものとして初期化します。)
//            MessageDialogResult Result = MessageDialogResult.OK;
//            if (string.IsNullOrEmpty(MessageCode))
//            {
//                return Result;
//            }
//            // 引数で指定されたMessageIDで抽出…
//            DataRow[] Rows;
//            // リソースから検索
//            Rows = ResourceTable.Select(COLUMN_MESSAGE_ID + " = '" + MessageCode + "'");
//            // １件も見つからなかった場合は何もしない。
//            if (Rows.Length == 0)
//            {
//                ArrayList ArgStr = new ArrayList();
//                ArgStr.Add(MessageCode);
//                Result = ArMessageBox.Show(NOT_FOUND_RESOURCE_BUTTONS,
//                                            NOT_FOUND_RESOURCE_ICON,
//                                            Replace(NOT_FOUND_RESOURCE_MESSAGE, ArgStr),
//                                            NOT_FOUND_RESOURCE_CAPTION);
//                return Result;
//            }
//            // 初期値
//            string MessageText = "";
//            string Caption = CAPTION_DEFAULT;
//            MessageBoxButtons Buttons = MessageBoxButtons.OK;
//            ArMessageBoxIcon.Icon Icon = ArMessageBoxIcon.Icon.Message;
//            MessageText = Rows[0][COLUMN_MESSAGE_TEXT].ToString();
//            // 置き換え文字を置き換えます。
//            if (Args != null)
//            {
//                MessageText = Replace(MessageText, Args);
//            }
//            // キャプションにはメッセージコードが入ります。
//            Caption = MessageCode;
//            // Buttonsの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_BUTTONS) == false && Rows[0][COLUMN_MESSAGE_BUTTONS].ToString().Trim().Length > 0)
//            {
//                int buttonNo = int.Parse(Rows[0][COLUMN_MESSAGE_BUTTONS].ToString());
//                Buttons = (MessageBoxButtons)buttonNo;
//            }
//            // Iconの設定…
//            if (Rows[0].IsNull(COLUMN_MESSAGE_ICON) == false && Rows[0][COLUMN_MESSAGE_ICON].ToString().Trim().Length > 0)
//            {
//                int iconNo = int.Parse(Rows[0][COLUMN_MESSAGE_ICON].ToString());
//                Icon = (ArMessageBoxIcon.Icon)iconNo;
//            }
//            MessageText = MessageText.Replace("\\n", "\n");
//            Result = ArMessageBox.Show(
//                                    Buttons,
//                                    Icon,
//                                    MessageText,
//                                    Caption,
//                                    arMsgFont,
//                                    backColor,
//                                    foreColor
//                                    );
//            Rows = null;
//            // ダイアログの戻り値をリターンする。
//            return Result;
//        }
//        #endregion

//        #region メッセージ表示(メッセージは直書)
//        /// <summary>
//        /// メッセージ表示(メッセージは直書)
//        /// </summary>
//        /// <param name="MessageText">メッセージ</param>
//        /// <param name="buttonNo">ボタン数</param>
//        /// <param name="iconNo">アイコンタイプ</param>
//        ///-----buttonNo-----
//        ///1 OK
//        ///2 OK キャンセル
//        ///3 中止 再試行 無視
//        ///4 はい　いいえ　キャンセル
//        ///5 はい　いいえ　
//        ///-----iconNo-----
//        ///1 info
//        ///2 Warning
//        ///3 Error
//        //////////////////////////
//        public MessageDialogResult ShowDialog_Custom(string MessageText, int buttonNo, int iconNo, ArrayList Args)
//        {
//            // 結果ダイアログ(OKボタンが押されたものとして初期化します。)
//            MessageDialogResult Result = MessageDialogResult.OK;
//            string Caption = "";
//            MessageBoxButtons Buttons = (MessageBoxButtons)buttonNo;
//            ArMessageBoxIcon.Icon Icon = (ArMessageBoxIcon.Icon)iconNo;
//            MessageText = MessageText.Replace("\\n", "\n");
//            Result = ArMessageBox.Show(Buttons, Icon, MessageText, Caption);
//            return Result;
//        }
//        #endregion

//        #region 文字列置き換え
//        private string Replace(string MessageText, ArrayList Args)
//        {
//            // Argsの数だけ{数値}を置き換えます。
//            for (int i = 0; i < Args.Count; i++)
//            {
//                MessageText = System.Text.RegularExpressions.Regex.Replace(MessageText, "\\{" + i + "\\}", Args[i].ToString());
//            }
//            return MessageText;
//        }
//        #endregion
//    }
//}
