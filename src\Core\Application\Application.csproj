﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>ShinKenShinKun.Application</RootNamespace>
        <AssemblyName>ShinKenShinKun.Application</AssemblyName>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
        <PackageReference Include="MediatR" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Domain\Domain.csproj" />
        <ProjectReference Include="..\..\Common\Shared\Shared.csproj" />
    </ItemGroup>

</Project>
