<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.PatientPendingPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:DataType="app:PatientPendingPageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsEnabled="False" IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid BackgroundColor="{x:Static ui:AppColors.HeaderColor}" RowDefinitions="80, *, Auto">
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.PatientPendingScreen}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <Grid
            Grid.Row="1"
            Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing16},
                                    Vertical={Static ui:Dimens.SpacingSm}}"
            BackgroundColor="{Static ui:AppColors.AzureishWhite}">
            <telerik:RadBorder
                Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing16},
                                        Vertical={Static ui:Dimens.Spacing16}}"
                BackgroundColor="{Static ui:AppColors.White}"
                CornerRadius="6"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">
                <telerik:RadBorder.Shadow>
                    <Shadow
                        Opacity="0.3"
                        Radius="5"
                        Offset="5,5" />
                </telerik:RadBorder.Shadow>
                <Grid ColumnDefinitions="0.2*,0.8*" ColumnSpacing="{Static ui:Dimens.Spacing16}">
                    <telerik:RadBorder
                        Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.Spacing20},
                                                Horizontal={Static ui:Dimens.SpacingXxs}}"
                        BackgroundColor="{Static ui:AppColors.LightNormalGray}"
                        CornerRadius="{Static ui:Dimens.Spacing5}">
                        <Grid
                            HorizontalOptions="Fill"
                            RowDefinitions="auto,*,auto"
                            RowSpacing="{Static ui:Dimens.Spacing20}"
                            VerticalOptions="Fill">
                            <Grid ColumnDefinitions="auto,*">
                                <Grid Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing20}}">
                                    <telerik:RadBorder
                                        BackgroundColor="{Static ui:AppColors.White}"
                                        CornerRadius="{Static ui:Dimens.RadCornerRadius7}"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center">
                                        <telerik:RadCheckBox
                                            Command="{Binding CheckAllMedicalChecklistCommand}"
                                            HorizontalOptions="Center"
                                            IsChecked="{Binding IsSelectAllMedicalChecklist}"
                                            VerticalOptions="Center" />
                                    </telerik:RadBorder>
                                </Grid>
                                <Grid Grid.Column="1" Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing20}}">
                                    <Label
                                        FontFamily="{Static ui:FontNames.NotoSansJPRegular}"
                                        FontSize="Medium"
                                        HorizontalOptions="Start"
                                        Text="{Static resources:AppResources.MedicalChecklist}"
                                        TextColor="{Static ui:AppColors.White}"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Grid>
                            <ScrollView Grid.Row="1">
                                <VerticalStackLayout
                                    BindableLayout.ItemsSource="{Binding MedicalCheckLists}"
                                    HorizontalOptions="Fill"
                                    Spacing="{Static ui:Dimens.Spacing2}"
                                    VerticalOptions="Fill">
                                    <BindableLayout.ItemTemplate>
                                        <DataTemplate x:DataType="app:MedicalCheckListCheckedModel">
                                            <Grid ColumnDefinitions="auto,*" ColumnSpacing="{Static ui:Dimens.Spacing2}">
                                                <Grid Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing20}, Vertical={Static ui:Dimens.Spacing10}}" BackgroundColor="{Static ui:AppColors.White}">
                                                    <telerik:RadCheckBox
                                                        BackgroundColor="{Static ui:AppColors.Transparent}"
                                                        Command="{Binding CheckMedicalChecklistCommand, Source={RelativeSource AncestorType={Type app:PatientPendingPageViewModel}}}"
                                                        HorizontalOptions="Center"
                                                        IsChecked="{Binding IsChecked}"
                                                        VerticalOptions="Center" />
                                                </Grid>
                                                <Grid
                                                    Grid.Column="1"
                                                    Padding="{ui:EdgeInsets Horizontal={Static ui:Dimens.Spacing20},
                                                                            Vertical={Static ui:Dimens.Spacing10}}"
                                                    BackgroundColor="{Static ui:AppColors.White}">
                                                    <Label
                                                        FontFamily="{Static ui:FontNames.NotoSansJPRegular}"
                                                        FontSize="Medium"
                                                        HorizontalOptions="Start"
                                                        Text="{Binding MedicalChecklist.Name}"
                                                        TextColor="{Static ui:AppColors.Black}"
                                                        VerticalOptions="Center" />
                                                </Grid>
                                            </Grid>
                                        </DataTemplate>
                                    </BindableLayout.ItemTemplate>
                                </VerticalStackLayout>
                            </ScrollView>
                            <telerik:RadButton
                                Grid.Row="2"
                                BackgroundColor="{Static ui:AppColors.SilverFoil}"
                                CornerRadius="20"
                                FontFamily="{Static ui:FontNames.NotoSansJPRegular}"
                                FontSize="Medium"
                                ImageSource="{Static ui:Icons.Search24X24}"
                                Text="{Static resources:AppResources.Search}"
                                TextColor="{Static ui:AppColors.White}" />
                        </Grid>
                    </telerik:RadBorder>
                    <telerik:RadBorder
                        Grid.Column="1"
                        Padding="{ui:EdgeInsets Vertical={Static ui:Dimens.SpacingS},
                                                Horizontal={Static ui:Dimens.SpacingXxs}}"
                        BackgroundColor="{Static ui:AppColors.AzureishWhiteBlue}"
                        CornerRadius="5">
                        <app:PatientPendingTableView
                            CheckAllClientCommand="{Binding CheckAllClientCommand}"
                            CheckClientInformationCommand="{Binding CheckClientInformationCommand}"
                            ClientInformations="{Binding ClientInformations}"
                            HorizontalOptions="Fill"
                            IsMaskMode="{Binding IsMaskMode}"
                            IsSelectAllClient="{Binding IsSelectAllClient}"
                            VerticalOptions="Fill" />
                    </telerik:RadBorder>
                </Grid>
            </telerik:RadBorder>
        </Grid>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            ClientSelected="{Binding ClientSelected}"
            BackCommand="{Binding BackCommand}" />
    </Grid>
</mvvm:BasePage>