﻿namespace ShinKenShinKun;

public static class AppDictionaryHelpers
{
    public static readonly Dictionary<string, SexModel> SexDictionary = new()
    {
        { "0", new SexModel { SexType = Sex.NoDefine, DisplayName = AppResources.NoDefine } },
        { "1", new SexModel { SexType = Sex.Male, DisplayName = AppResources.Male } },
        { "2", new SexModel { SexType = Sex.Female, DisplayName = AppResources.Female } },
    };

    public static readonly Dictionary<string, TestStatusModel> TestStatusDictionary = new()
    {
        { AppResources.Unchecked, new TestStatusModel {
            MedicalState = MedicalState.Unchecked,
            DisplayName = AppResources.Unchecked,
            StatusColor = AppColors.White}
        },
        { AppResources.Completed, new TestStatusModel {
            MedicalState = MedicalState.Completed,
            DisplayName = AppResources.Completed,
            StatusColor = AppColors.RoyalBlue}
        },
        { AppResources.Excluded, new TestStatusModel {
            MedicalState = MedicalState.Excluded,
            DisplayName = AppResources.Excluded,
            StatusColor = AppColors.Grey40}
        },
        { AppResources.Pending, new TestStatusModel {
            MedicalState = MedicalState.Pending,
            DisplayName = AppResources.Pending,
            StatusColor = AppColors.Grey40}
        },
        { AppResources.Stopped, new TestStatusModel {
            MedicalState = MedicalState.Stopped,
            DisplayName = AppResources.Stopped,
            StatusColor = AppColors.Grey40}
        },
        { AppResources.Next, new TestStatusModel {
            MedicalState = MedicalState.Next,
            DisplayName = AppResources.Next,
            StatusColor = AppColors.Grey40}
        },
    };

    //TODO: Remap route when had designed page
    public static readonly Dictionary<string, string> DefaultSreenCodeDictionary = new()
    {
        {AppConstants.TestSelectionScreenCode, RouterName.TestSelectionPage },
        {AppConstants.ExamineBeforeAuthScreenCode, RouterName.ExamineBeforeAuthPage },
        {AppConstants.IndividualProgressScreenCode, RouterName.IndividualProgressPage },
        {AppConstants.PatientPendingScreenCode, RouterName.PatientPendingPage },
        {AppConstants.GuidanceAdjustmentScreenCode, RouterName.PatientGuidanceAdjustmentPage },
        {AppConstants.StopExamineScreenCode, RouterName.StopExaminePage },
        {AppConstants.OverallProgressScreenCode, string.Empty },
    };
}