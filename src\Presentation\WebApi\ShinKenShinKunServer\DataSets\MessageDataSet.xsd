﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="MessageDataSet" targetNamespace="http://tempuri.org/MessageDataSet.xsd" xmlns:mstns="http://tempuri.org/MessageDataSet.xsd" xmlns="http://tempuri.org/MessageDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="GenkiPlazaDatabase2ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="GenkiPlazaDatabase2ConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.GenkiPlazaDatabase2ConnectionString.ConnectionString" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="Web.config" AppSettingsPropertyName="GenkiPlazaDatabase2ConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="GenkiPlazaDatabase2ConnectionString (Web.config)" ParameterPrefix="@" PropertyReference="AppConfig.System.Configuration.ConfigurationManager.0.ConnectionStrings.GenkiPlazaDatabase2ConnectionString.ConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MessagesTableAdapter" GeneratorDataComponentClassName="MessagesTableAdapter" Name="Messages" UserDataComponentName="MessagesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="GenkiPlazaDatabase2ConnectionString (Web.config)" DbObjectName="GenkiPlazaDatabase2.dbo.Messages" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Messages] WHERE (([MessegeId] = @Original_MessegeId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_MessegeId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessegeId" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Messages] ([MessegeId], [MessageCode], [LanguageCode], [MessageType], [MessageTitle], [MessageBody], [ButtonType], [DefaltButtonIndex], [IsVisible], [AutoSelectedButtonIndex]) VALUES (@MessegeId, @MessageCode, @LanguageCode, @MessageType, @MessageTitle, @MessageBody, @ButtonType, @DefaltButtonIndex, @IsVisible, @AutoSelectedButtonIndex)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessegeId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessegeId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessageCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessageCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LanguageCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LanguageCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessageType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessageType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MessageTitle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MessageTitle" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MessageBody" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MessageBody" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ButtonType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ButtonType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@DefaltButtonIndex" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="DefaltButtonIndex" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsVisible" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsVisible" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@AutoSelectedButtonIndex" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="AutoSelectedButtonIndex" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT MessegeId, MessageCode, LanguageCode, MessageType, MessageTitle, MessageBody, ButtonType, DefaltButtonIndex, IsVisible, AutoSelectedButtonIndex FROM dbo.Messages</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Messages] SET [MessegeId] = @MessegeId, [MessageCode] = @MessageCode, [LanguageCode] = @LanguageCode, [MessageType] = @MessageType, [MessageTitle] = @MessageTitle, [MessageBody] = @MessageBody, [ButtonType] = @ButtonType, [DefaltButtonIndex] = @DefaltButtonIndex, [IsVisible] = @IsVisible, [AutoSelectedButtonIndex] = @AutoSelectedButtonIndex WHERE (([MessegeId] = @Original_MessegeId))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessegeId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessegeId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessageCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessageCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@LanguageCode" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="LanguageCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MessageType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessageType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MessageTitle" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MessageTitle" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MessageBody" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MessageBody" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@ButtonType" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="ButtonType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@DefaltButtonIndex" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="DefaltButtonIndex" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@IsVisible" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="IsVisible" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Byte" Direction="Input" ParameterName="@AutoSelectedButtonIndex" Precision="0" ProviderType="TinyInt" Scale="0" Size="0" SourceColumn="AutoSelectedButtonIndex" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_MessegeId" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MessegeId" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MessegeId" DataSetColumn="MessegeId" />
              <Mapping SourceColumn="MessageCode" DataSetColumn="MessageCode" />
              <Mapping SourceColumn="LanguageCode" DataSetColumn="LanguageCode" />
              <Mapping SourceColumn="MessageType" DataSetColumn="MessageType" />
              <Mapping SourceColumn="MessageTitle" DataSetColumn="MessageTitle" />
              <Mapping SourceColumn="MessageBody" DataSetColumn="MessageBody" />
              <Mapping SourceColumn="ButtonType" DataSetColumn="ButtonType" />
              <Mapping SourceColumn="DefaltButtonIndex" DataSetColumn="DefaltButtonIndex" />
              <Mapping SourceColumn="IsVisible" DataSetColumn="IsVisible" />
              <Mapping SourceColumn="AutoSelectedButtonIndex" DataSetColumn="AutoSelectedButtonIndex" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="MessageDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="MessageDataSet" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="MessageDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Messages" msprop:Generator_RowEvHandlerName="MessagesRowChangeEventHandler" msprop:Generator_RowDeletedName="MessagesRowDeleted" msprop:Generator_RowDeletingName="MessagesRowDeleting" msprop:Generator_RowEvArgName="MessagesRowChangeEvent" msprop:Generator_TablePropName="Messages" msprop:Generator_RowChangedName="MessagesRowChanged" msprop:Generator_RowChangingName="MessagesRowChanging" msprop:Generator_TableClassName="MessagesDataTable" msprop:Generator_RowClassName="MessagesRow" msprop:Generator_TableVarName="tableMessages" msprop:Generator_UserTableName="Messages">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MessegeId" msprop:Generator_ColumnPropNameInRow="MessegeId" msprop:Generator_ColumnPropNameInTable="MessegeIdColumn" msprop:Generator_ColumnVarNameInTable="columnMessegeId" msprop:Generator_UserColumnName="MessegeId">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MessageCode" msprop:Generator_ColumnPropNameInRow="MessageCode" msprop:Generator_ColumnPropNameInTable="MessageCodeColumn" msprop:Generator_ColumnVarNameInTable="columnMessageCode" msprop:Generator_UserColumnName="MessageCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LanguageCode" msprop:Generator_ColumnPropNameInRow="LanguageCode" msprop:Generator_ColumnPropNameInTable="LanguageCodeColumn" msprop:Generator_ColumnVarNameInTable="columnLanguageCode" msprop:Generator_UserColumnName="LanguageCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MessageType" msprop:Generator_ColumnPropNameInRow="MessageType" msprop:Generator_ColumnPropNameInTable="MessageTypeColumn" msprop:Generator_ColumnVarNameInTable="columnMessageType" msprop:Generator_UserColumnName="MessageType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MessageTitle" msprop:Generator_ColumnPropNameInRow="MessageTitle" msprop:Generator_ColumnPropNameInTable="MessageTitleColumn" msprop:Generator_ColumnVarNameInTable="columnMessageTitle" msprop:Generator_UserColumnName="MessageTitle" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MessageBody" msprop:Generator_ColumnPropNameInRow="MessageBody" msprop:Generator_ColumnPropNameInTable="MessageBodyColumn" msprop:Generator_ColumnVarNameInTable="columnMessageBody" msprop:Generator_UserColumnName="MessageBody" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ButtonType" msprop:Generator_ColumnPropNameInRow="ButtonType" msprop:Generator_ColumnPropNameInTable="ButtonTypeColumn" msprop:Generator_ColumnVarNameInTable="columnButtonType" msprop:Generator_UserColumnName="ButtonType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DefaltButtonIndex" msprop:Generator_ColumnPropNameInRow="DefaltButtonIndex" msprop:Generator_ColumnPropNameInTable="DefaltButtonIndexColumn" msprop:Generator_ColumnVarNameInTable="columnDefaltButtonIndex" msprop:Generator_UserColumnName="DefaltButtonIndex" type="xs:unsignedByte" minOccurs="0" />
              <xs:element name="IsVisible" msprop:Generator_ColumnPropNameInRow="IsVisible" msprop:Generator_ColumnPropNameInTable="IsVisibleColumn" msprop:Generator_ColumnVarNameInTable="columnIsVisible" msprop:Generator_UserColumnName="IsVisible" type="xs:boolean" />
              <xs:element name="AutoSelectedButtonIndex" msprop:Generator_ColumnPropNameInRow="AutoSelectedButtonIndex" msprop:Generator_ColumnPropNameInTable="AutoSelectedButtonIndexColumn" msprop:Generator_ColumnVarNameInTable="columnAutoSelectedButtonIndex" msprop:Generator_UserColumnName="AutoSelectedButtonIndex" type="xs:unsignedByte" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Messages" />
      <xs:field xpath="mstns:MessegeId" />
    </xs:unique>
  </xs:element>
</xs:schema>