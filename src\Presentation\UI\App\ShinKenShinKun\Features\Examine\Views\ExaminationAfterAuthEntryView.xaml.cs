namespace ShinKenShinKun;
public partial class ExaminationAfterAuthEntryView : ContentView
{
    public ExaminationAfterAuthEntryView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty LabelProperty = BindableProperty.Create(
    nameof(Label),
    typeof(string),
    typeof(ExaminationAfterAuthEntryView),
    string.Empty,
    BindingMode.TwoWay);

    public string Label
    {
        get => (string)GetValue(LabelProperty);
        set => SetValue(LabelProperty, value);
    }

    public static readonly BindableProperty ColorProperty = BindableProperty.Create(
    nameof(Color),
    typeof(Color),
    typeof(ExaminationAfterAuthEntryView),
    default(Color),
    BindingMode.TwoWay);

    public Color Color
    {
        get => (Color)GetValue(ColorProperty);
        set => SetValue(ColorProperty, value);
    }

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
    nameof(Value),
    typeof(double),
    typeof(ExaminationAfterAuthEntryView),
    default(double),
    BindingMode.TwoWay);

    public double Value
    {
        get => (double)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }

}