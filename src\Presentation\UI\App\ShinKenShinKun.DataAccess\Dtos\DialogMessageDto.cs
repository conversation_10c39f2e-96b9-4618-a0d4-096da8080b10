﻿namespace ShinKenShinKun.DataAccess;

public partial class DialogMessageDto : BaseModel
{
    [ObservableProperty]
    private string messegeId;

    [ObservableProperty]
    private string messageCode;

    [ObservableProperty]
    private string languageCode;

    [ObservableProperty]
    private MessageType messageType;

    [ObservableProperty]
    private string? messageTitle;

    [ObservableProperty]
    private string? messageBody;

    [ObservableProperty]
    private MessageButtonType buttonType;

    [ObservableProperty]
    private byte? defaltButtonIndex;

    [ObservableProperty]
    private bool isVisible;

    [ObservableProperty]
    private byte autoSelectedButtonIndex;
}