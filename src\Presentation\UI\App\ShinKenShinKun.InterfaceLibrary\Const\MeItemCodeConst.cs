﻿namespace ShinKenShinKun.InterfaceLibrary;
public class MeItemCodeConst
{
    #region 身長体重用検査項目コード
    /// <summary>
    /// 身長コード
    /// </summary>
    public static readonly string BODY_RESULT_HEIGHT_CODE = "0101";
    /// <summary>
    /// 体重コード
    /// </summary>
    public static readonly string BODY_RESULT_WEIGHT_CODE = "0102";
    /// <summary>
    /// 体脂肪コード
    /// </summary>
    public static readonly string BODY_RESULT_BODY_FAT_CODE = "0103";
    /// <summary>
    /// 肥満度コード
    /// </summary>
    public static readonly string BODY_RESULT_PONDERAL_INDEX_CODE = "0104";
    /// <summary>
    /// ＢＭＩコード
    /// </summary>
    public static readonly string BODY_RESULT_BMI_CODE = "0105";
    /// <summary>
    /// 標準体重コード
    /// </summary>
    public static readonly string BODY_RESULT_STANDARD_WEIGHT_CODE = "0106";

    /// <summary>
    /// 脂肪量コード
    /// </summary>
    public static readonly string BODY_RESULT_FAT_VOLUME_CODE = "0107";
    /// <summary>
    /// 除脂肪量コード
    /// </summary>
    public static readonly string BODY_RESULT_EXCLUSION_FAT_VOLUME_CODE = "0108";
    /// <summary>
    /// 筋肉量コード
    /// </summary>
    public static readonly string BODY_RESULT_MUSCLE_VOLUME_CODE = "0109";
    /// <summary>
    /// 全身筋肉スコアコード
    /// </summary>
    public static readonly string BODY_RESULT_MUSCLE_SCORE_CODE = "0110";
    /// <summary>
    /// 推定骨量コード
    /// </summary>
    public static readonly string BODY_RESULT_BMD_CODE = "0111";
    /// <summary>
    /// 体水分量コード
    /// </summary>
    public static readonly string BODY_RESULT_WATER_VOLUME_CODE = "0112";
    /// <summary>
    /// 体水分率コード
    /// </summary>
    public static readonly string BODY_RESULT_WATER_PERCENT_CODE = "0113";
    /// <summary>
    /// 内臓脂肪レベルコード
    /// </summary>
    public static readonly string BODY_RESULT_METABOLIC_CODE = "0114";
    /// <summary>
    /// 脚点コード
    /// </summary>
    public static readonly string BODY_RESULT_FOOT_POINT_CODE = "0115";
    /// <summary>
    /// 基礎代謝量コード
    /// </summary>
    public static readonly string BODY_RESULT_BASAL_METABOLISM_VOLUME_CODE = "0116";
    /// <summary>
    /// 基礎代謝判定コード
    /// </summary>
    public static readonly string BODY_RESULT_BASAL_METABOLISM_JUDGE_CODE = "0117";
    /// <summary>
    /// 体内年齢コード
    /// </summary>
    public static readonly string BODY_RESULT_BODY_AGE_CODE = "0118";
    /// <summary>
    /// 腹囲コード
    /// </summary>
    public static readonly string BODY_RESULT_ABD_CODE = "0119";

    #endregion

    #region 血圧用検査項目コード
    /// <summary>
    /// 最高血圧コード
    /// </summary>
    public static readonly string BLOODPRE_RESULT_MAX_CODE = "0201";
    /// <summary>
    /// 最低血圧コード
    /// </summary>
    public static readonly string BLOODPRE_RESULT_MIN_CODE = "0202";
    /// <summary>
    /// 脈拍
    /// </summary>
    public static readonly string BLOODPRE_RESULT_PULSE_CODE = "0203";
    #endregion

    #region 視力用検査項目コード
    /// <summary>
    /// 遠点右コード
    /// </summary>
    public static readonly string EYE_RESULT_FAR_R_CODE = "0301";
    /// <summary>
    /// 遠点左コード
    /// </summary>
    public static readonly string EYE_RESULT_FAR_L_CODE = "0302";
    /// <summary>
    /// 遠点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_FAR_BOTH_CODE = "0303";
    /// <summary>
    /// 近点右コード
    /// </summary>
    public static readonly string EYE_RESULT_NEAR_R_CODE = "0304";
    /// <summary>
    /// 近点左コード
    /// </summary>
    public static readonly string EYE_RESULT_NEAR_L_CODE = "0305";
    /// <summary>
    /// 近点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_NEAR_BOTH_CODE = "0306";
    /// <summary>
    /// 裸眼遠点右コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_FAR_R_CODE = "0307";
    /// <summary>
    /// 裸眼遠点左コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_FAR_L_CODE = "0308";
    /// <summary>
    /// 裸眼遠点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_FAR_BOTH_CODE = "0309";
    /// <summary>
    /// 裸眼近点右コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_NEAR_R_CODE = "0310";
    /// <summary>
    /// 裸眼近点左コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_NEAR_L_CODE = "0311";
    /// <summary>
    /// 裸眼近点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_NAKED_NEAR_BOTH_CODE = "0312";
    /// <summary>
    /// 矯正遠点右コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_FAR_R_CODE = "0313";
    /// <summary>
    /// 矯正遠点左コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_FAR_L_CODE = "0314";
    /// <summary>
    /// 矯正遠点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_FAR_BOTH_CODE = "0315";
    /// <summary>
    /// 矯正近点右コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_NEAR_R_CODE = "0316";
    /// <summary>
    /// 矯正近点左コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_NEAR_L_CODE = "0317";
    /// <summary>
    /// 矯正近点両目コード
    /// </summary>
    public static readonly string EYE_RESULT_CORR_NEAR_BOTH_CODE = "0318";
    /// <summary>
    /// 遠点矯正コード
    /// </summary>
    public static readonly string EYE_RESULT_FAR_CORRECT_CODE = "0319";
    /// <summary>
    /// 近点矯正コード
    /// </summary>
    public static readonly string EYE_RESULT_NEAR_CORRECT_CODE = "0320";
    #endregion

    #region 眼圧用検査項目コード
    /// <summary>
    /// 眼圧１回目右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_1_CODE = "0401";
    /// <summary>
    /// 眼圧１回目左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_1_CODE = "0402";
    /// <summary>
    /// 眼圧２回目右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_2_CODE = "0403";
    /// <summary>
    /// 眼圧２回目左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_2_CODE = "0404";
    /// <summary>
    /// 眼圧３回目右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_3_CODE = "0405";
    /// <summary>
    /// 眼圧３回目左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_3_CODE = "0406";
    /// <summary>
    /// 眼圧４回目右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_4_CODE = "0407";
    /// <summary>
    /// 眼圧４回目左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_4_CODE = "0408";
    /// <summary>
    /// 眼圧平均値右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_AVERAGE_CODE = "0409";
    /// <summary>
    /// 眼圧平均値左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_AVERAGE_CODE = "0410";
    /// <summary>
    /// 眼圧補正値右コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_R_REVISION_CODE = "0411";
    /// <summary>
    /// 眼圧補正値左コード
    /// </summary>
    public static readonly string EYEPRE_RESULT_L_REVISION_CODE = "0412";
    #endregion

    #region 聴力用検査項目コード
    /// <summary>
    /// 選別１０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_SCREEN_1000_R_CODE = "0501";
    /// <summary>
    /// 選別１０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_SCREEN_1000_L_CODE = "0502";
    /// <summary>
    /// 選別４０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_SCREEN_4000_R_CODE = "0503";
    /// <summary>
    /// 選別４０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_SCREEN_4000_L_CODE = "0504";
    /// <summary>
    /// 閾値１２５Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_125_R_CODE = "0505";
    /// <summary>
    /// 閾値１２５Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_125_L_CODE = "0506";
    /// <summary>
    /// 閾値２５０Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_250_R_CODE = "0507";
    /// <summary>
    /// 閾値２５０Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_250_L_CODE = "0508";
    /// <summary>
    /// 閾値５００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_500_R_CODE = "0509";
    /// <summary>
    /// 閾値５００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_500_L_CODE = "0510";
    /// <summary>
    /// 閾値７５０Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_750_R_CODE = "0511";
    /// <summary>
    /// 閾値７５０Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_750_L_CODE = "0512";
    /// <summary>
    /// 閾値１０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_1000_R_CODE = "0513";
    /// <summary>
    /// 閾値１０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_1000_L_CODE = "0514";
    /// <summary>
    /// 閾値１５００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_1500_R_CODE = "0515";
    /// <summary>
    /// 閾値１５００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_1500_L_CODE = "0516";
    /// <summary>
    /// 閾値２０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_2000_R_CODE = "0517";
    /// <summary>
    /// 閾値２０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_2000_L_CODE = "0518";
    /// <summary>
    /// 閾値３０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_3000_R_CODE = "0519";
    /// <summary>
    /// 閾値３０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_3000_L_CODE = "0520";
    /// <summary>
    /// 閾値４０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_4000_R_CODE = "0521";
    /// <summary>
    /// 閾値４０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_4000_L_CODE = "0522";
    /// <summary>
    /// 閾値６０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_6000_R_CODE = "0523";
    /// <summary>
    /// 閾値６０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_6000_L_CODE = "0524";
    /// <summary>
    /// 閾値８０００Ｈｚ右コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_8000_R_CODE = "0525";
    /// <summary>
    /// 閾値８０００Ｈｚ左コード
    /// </summary>
    public static readonly string EAR_RESULT_MONITOR_8000_L_CODE = "0526";
    /// <summary>
    /// 測定方法コード
    /// </summary>
    public static readonly string EAR_RESULT_DATAKIND_CODE = "0527";
    #endregion

    #region 肺機能用検査項目コード
    /// <summary>
    /// 肺活量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_VC_CODE = "0601";
    /// <summary>
    /// 予測肺活量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PRE_VC_CODE = "0602";
    /// <summary>
    /// ％肺活量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_VC_CODE = "0603";
    /// <summary>
    /// 努力性肺活量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_FVC_CODE = "0604";
    /// <summary>
    /// ％努力性肺活量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_FVC_CODE = "0605";
    /// <summary>
    /// １秒量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_FEV1_CODE = "0606";
    /// <summary>
    /// ％１秒量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_FEV1_CODE = "0607";
    /// <summary>
    /// １秒率（Ｇ）コード
    /// </summary>
    public static readonly string LUNGS_RESULT_FEV1_PERCENT_G_CODE = "0608";
    /// <summary>
    /// １秒率（Ｔ）コード
    /// </summary>
    public static readonly string LUNGS_RESULT_FEV1_PERCENT_T_CODE = "0609";
    /// <summary>
    /// ＰＥＦＲコード
    /// </summary>
    public static readonly string LUNGS_RESULT_PEFR_CODE = "0610";
    /// <summary>
    /// Ｖ７５コード
    /// </summary>
    public static readonly string LUNGS_RESULT_V75_CODE = "0611";
    /// <summary>
    /// ％Ｖ７５コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_V75_CODE = "0612";
    /// <summary>
    /// Ｖ５０コード
    /// </summary>
    public static readonly string LUNGS_RESULT_V50_CODE = "0613";
    /// <summary>
    /// ％Ｖ５０コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_V50_CODE = "0614";
    /// <summary>
    /// Ｖ２５コード
    /// </summary>
    public static readonly string LUNGS_RESULT_V25_CODE = "0615";
    /// <summary>
    /// ％Ｖ２５コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_V25_CODE = "0616";
    /// <summary>
    /// Ｖ５０／Ｖ２５コード
    /// </summary>
    public static readonly string LUNGS_RESULT_V50_PER_V25_CODE = "0617";
    /// <summary>
    /// Ｖ７５／ＨＴコード
    /// </summary>
    public static readonly string LUNGS_RESULT_V75_PER_HT_CODE = "0618";
    /// <summary>
    /// Ｖ５０／ＨＴコード
    /// </summary>
    public static readonly string LUNGS_RESULT_V50_PER_HT_CODE = "0619";
    /// <summary>
    /// Ｖ２５／ＨＴコード
    /// </summary>
    public static readonly string LUNGS_RESULT_V25_PER_HT_CODE = "0620";
    /// <summary>
    /// ％Ｖ２５／ＨＴコード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_V25_PER_HT_CODE = "0621";
    /// <summary>
    /// １秒量予測値コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PFEV1_CODE = "0622";
    /// <summary>
    /// １回換気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_TV_CODE = "0623";
    /// <summary>
    /// 努力性肺活量予測値
    /// </summary>
    public static readonly string LUNGS_RESULT_PRE_FVC_CODE = "0624";
    /// <summary>
    /// 肺年齢
    /// </summary>
    public static readonly string LUNGS_RESULT_AGE_CODE = "0625";
    /// <summary>
    /// 最大換気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_MVV_CODE = "0626";
    /// <summary>
    /// １秒率（Ｇ）予測値コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PRE_FEV1_PERCENT_G_CODE = "0627";
    /// <summary>
    /// 最大中間呼気流量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_MMEF_CODE = "0628";
    /// <summary>
    /// １秒量予測率コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PERCENT_PRE_FEV1_CODE = "0629";
    /// <summary>
    /// 塵肺法判定コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PNEUMOCONIOSIS_CODE = "0630";
    /// <summary>
    /// 予備吸気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_IRV_CODE = "0631";
    /// <summary>
    /// 予備呼気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_ERV_CODE = "0632";
    /// <summary>
    /// PEF到達時間コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PEFtime_CODE = "0633";
    /// <summary>
    /// 予測最大換気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_PRE_MVV_CODE = "0634";
    /// <summary>
    /// 分時換気量コード
    /// </summary>
    public static readonly string LUNGS_RESULT_MV_CODE = "0635";
    /// <summary>
    /// ％VC判定コード
    /// </summary>
    public static readonly string LUNGS_RESULT_F_PERCENT_VC_CODE = "0636";
    /// <summary>
    /// 1秒率判定コード
    /// </summary>
    public static readonly string LUNGS_RESULT_F_FEV1_PERCENT_CODE = "0637";
    /// <summary>
    /// V25/身長判定コード
    /// </summary>
    public static readonly string LUNGS_RESULT_F_V25_PER_HT_CODE = "0638";

    #endregion

    #region 心電図用検査項目コード
    /// <summary>
    /// ミネソタ１コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_1_CODE = "0801";
    /// <summary>
    /// ミネソタ２コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_2_CODE = "0802";
    /// <summary>
    /// ミネソタ３コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_3_CODE = "0803";
    /// <summary>
    /// ミネソタ４コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_4_CODE = "0804";
    /// <summary>
    /// ミネソタ５コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_5_CODE = "0805";
    /// <summary>
    /// 所見１コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_1_CODE = "0806";
    /// <summary>
    /// 所見２コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_2_CODE = "0807";
    /// <summary>
    /// 所見３コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_3_CODE = "0808";
    /// <summary>
    /// 所見４コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_4_CODE = "0809";
    /// <summary>
    /// 所見５コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_5_CODE = "0810";
    /// <summary>
    /// 心拍数コード
    /// </summary>
    public static readonly string HEART_RESULT_HEARTRATE_CODE = "0811";

    /// <summary>
    /// Ｒ－Ｒコード
    /// </summary>
    public static readonly string HEART_RESULT_R_R_CODE = "0812";
    /// <summary>
    /// ＰＲコード
    /// </summary>
    public static readonly string HEART_RESULT_PR_CODE = "0813";
    /// <summary>
    /// ＱＲＳコード
    /// </summary>
    public static readonly string HEART_RESULT_QRS_CODE = "0814";
    /// <summary>
    /// ＡＸＩＳコード
    /// </summary>
    public static readonly string HEART_RESULT_AXIS_CODE = "0815";
    /// <summary>
    /// ＱＴコード
    /// </summary>
    public static readonly string HEART_RESULT_QT_CODE = "0816";
    /// <summary>
    /// ＱＴｃコード
    /// </summary>
    public static readonly string HEART_RESULT_QTc_CODE = "0817";
    /// <summary>
    /// ＲＶ５，６コード
    /// </summary>
    public static readonly string HEART_RESULT_RV56_CODE = "0818";
    /// <summary>
    /// ＳＶ１コード
    /// </summary>
    public static readonly string HEART_RESULT_SV1_CODE = "0819";
    /// <summary>
    /// ＲＶ５，６＋ＳＶ１コード
    /// </summary>
    public static readonly string HEART_RESULT_RV56_SV1_CODE = "0820";

    /// <summary>
    /// ミネソタ６コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_6_CODE = "0821";
    /// <summary>
    /// ミネソタ７コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_7_CODE = "0822";
    /// <summary>
    /// ミネソタ８コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_8_CODE = "0823";
    /// <summary>
    /// ミネソタ９コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_9_CODE = "0824";
    /// <summary>
    /// 所見６コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_6_CODE = "0825";
    /// <summary>
    /// 所見７コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_7_CODE = "0826";
    /// <summary>
    /// 所見８コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_8_CODE = "0827";
    /// <summary>
    /// 所見９コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_9_CODE = "0828";

    /// <summary>
    /// ミネソタ１０コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_10_CODE = "0829";
    /// <summary>
    /// ミネソタ１１コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_11_CODE = "0830";
    /// <summary>
    /// ミネソタ１２コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_12_CODE = "0831";
    /// <summary>
    /// ミネソタ１３コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_13_CODE = "0832";
    /// <summary>
    /// ミネソタ１４コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_14_CODE = "0833";
    /// <summary>
    /// ミネソタ１５コード
    /// </summary>
    public static readonly string HEART_RESULT_MINNESOTA_15_CODE = "0834";
    /// <summary>
    /// 所見１０コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_10_CODE = "0835";
    /// <summary>
    /// 所見１１コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_11_CODE = "0836";
    /// <summary>
    /// 所見１２コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_12_CODE = "0837";
    /// <summary>
    /// 所見１３コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_13_CODE = "0838";
    /// <summary>
    /// 所見１４コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_14_CODE = "0839";
    /// <summary>
    /// 所見１５コード
    /// </summary>
    public static readonly string HEART_RESULT_OPINION_15_CODE = "0840";

    /// <summary>
    /// 負荷所見１コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_1_CODE = "0841";
    /// <summary>
    /// 負荷所見２コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_2_CODE = "0842";
    /// <summary>
    /// 負荷所見３コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_3_CODE = "0843";
    /// <summary>
    /// 負荷所見４コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_4_CODE = "0844";
    /// <summary>
    /// 負荷所見５コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_5_CODE = "0845";
    /// <summary>
    /// 負荷所見６コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_6_CODE = "0846";
    /// <summary>
    /// 負荷所見７コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_7_CODE = "0847";
    /// <summary>
    /// 負荷所見８コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_8_CODE = "0848";
    /// <summary>
    /// 負荷所見９コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_OPINION_9_CODE = "0849";

    /// <summary>
    /// 負荷ミネソタ１コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_1_CODE = "0850";
    /// <summary>
    /// 負荷ミネソタ２コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_2_CODE = "0851";
    /// <summary>
    /// 負荷ミネソタ３コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_3_CODE = "0852";
    /// <summary>
    /// 負荷ミネソタ４コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_4_CODE = "0853";
    /// <summary>
    /// 負荷ミネソタ５コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_5_CODE = "0854";
    /// <summary>
    /// 負荷ミネソタ６コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_6_CODE = "0855";
    /// <summary>
    /// 負荷ミネソタ７コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_7_CODE = "0856";
    /// <summary>
    /// 負荷ミネソタ８コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_8_CODE = "0857";
    /// <summary>
    /// 負荷ミネソタ９コード
    /// </summary>
    public static readonly string HEART_RESULT_STRESS_MINNESOTA_9_CODE = "0858";

    /// <summary>
    /// 検査種別コード
    /// </summary>
    public static readonly string HEART_RESULT_DATAKIND_CODE = "0859";
    /// <summary>
    /// 経過時間コード
    /// </summary>
    public static readonly string HEART_RESULT_DATETIME_CODE = "0860";

    /// <summary>
    /// 負荷判定コード
    /// </summary>
    public static readonly string HEART_RESULT_PRESSURE_CODE = "0861";
    /// <summary>
    /// グレードコード
    /// </summary>
    public static readonly string HEART_RESULT_GRADE_CODE = "0862";

    #endregion

    #region 腹囲用検査項目コード
    /// <summary>
    /// 腹囲コード
    /// </summary>
    public static readonly string ABDOMEN_RESULT_CODE = "0701";
    #endregion

    #region 骨密度用検査項目コード
    /// <summary>
    /// ＯＳＩコード
    /// </summary>
    public static readonly string BONE_RESULT_OSI_CODE = "0901";
    /// <summary>
    /// Ｔスコアコード
    /// </summary>
    public static readonly string BONE_RESULT_T_SCORE_CODE = "0902";
    /// <summary>
    /// Ｔスコア（％）コード
    /// </summary>
    public static readonly string BONE_RESULT_T_SCORE_PERCENT_CODE = "0903";
    /// <summary>
    /// Ｚスコアコード
    /// </summary>
    public static readonly string BONE_RESULT_Z_SCORE_CODE = "0904";
    /// <summary>
    /// Ｚスコア（％）コード
    /// </summary>
    public static readonly string BONE_RESULT_Z_SCORE_PERCENT_CODE = "0905";
    /// <summary>
    /// 判定区分コード
    /// </summary>
    public static readonly string BONE_RESULT_JUDGE_CODE = "0906";
    /// <summary>
    /// SOSコード
    /// </summary>
    public static readonly string BONE_RESULT_SOS_CODE = "0907";
    /// <summary>
    /// 骨幅コード
    /// </summary>
    public static readonly string BONE_RESULT_BORN_WIDTH_CODE = "0908";


    #endregion

    #region 脈波用検査項目コード
    /// <summary>
    /// Ｒ－ＣＡＶＩコード
    /// </summary>
    public static readonly string PWV_RESULT_R_CAVI_CODE = "1001";
    /// <summary>
    /// Ｌ－ＣＡＶＩコード
    /// </summary>
    public static readonly string PWV_RESULT_L_CAVI_CODE = "1002";
    /// <summary>
    /// Ｒ－ＡＢＩコード
    /// </summary>
    public static readonly string PWV_RESULT_R_ABI_CODE = "1003";
    /// <summary>
    /// Ｌ－ＡＢＩコード
    /// </summary>
    public static readonly string PWV_RESULT_L_ABI_CODE = "1004";
    /// <summary>
    /// 右上腕最高血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ARM_BLOODPRE_MAX_CODE = "1005";
    /// <summary>
    /// 右上腕最低血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ARM_BLOODPRE_MIN_CODE = "1006";
    /// <summary>
    /// 左上腕最高血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ARM_BLOODPRE_MAX_CODE = "1007";
    /// <summary>
    /// 左上腕最低血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ARM_BLOODPRE_MIN_CODE = "1008";
    /// <summary>
    /// 右足首最高血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ANKLE_BLOODPRE_MAX_CODE = "1009";
    /// <summary>
    /// 右足首最低血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ANKLE_BLOODPRE_MIN_CODE = "1010";
    /// <summary>
    /// 左足首最高血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ANKLE_BLOODPRE_MAX_CODE = "1011";
    /// <summary>
    /// 左足首最低血圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ANKLE_BLOODPRE_MIN_CODE = "1012";
    /// <summary>
    /// 右上腕脈圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ARM_BLOODPRE_GAP_CODE = "1013";
    /// <summary>
    /// 左上腕脈圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ARM_BLOODPRE_GAP_CODE = "1014";
    /// <summary>
    /// 右足首脈圧コード
    /// </summary>
    public static readonly string PWV_RESULT_R_ANKLE_BLOODPRE_GAP_CODE = "1015";
    /// <summary>
    /// 左足首脈圧コード
    /// </summary>
    public static readonly string PWV_RESULT_L_ANKLE_BLOODPRE_GAP_CODE = "1016";
    /// <summary>
    /// 血管年齢Ｒ－ＣＡＶＩコード
    /// </summary>
    public static readonly string PWV_RESULT_VESSEL_R_CAVI_CODE = "1017";
    /// <summary>
    /// 血管年齢Ｌ－ＣＡＶＩコード
    /// </summary>
    public static readonly string PWV_RESULT_VESSEL_L_CAVI_CODE = "1018";
    /// <summary>
    /// 心拍数コード
    /// </summary>
    public static readonly string PWV_RESULT_HEARTRATE_CODE = "1019";
    /// <summary>
    /// DiagRankコード
    /// </summary>
    public static readonly string PWV_RESULT_DIAGRANK_CODE = "1020";

    #endregion

    #region 尿検査用検査項目コード
    /// <summary>
    /// 尿糖コード
    /// </summary>
    public static readonly string URINE_RESULT_GLUCOSE_CODE = "1101";
    /// <summary>
    /// 尿蛋白コード
    /// </summary>
    public static readonly string URINE_RESULT_PROTEIN_CODE = "1102";
    /// <summary>
    /// 尿潜血コード
    /// </summary>
    public static readonly string URINE_RESULT_BLOOD_CODE = "1103";
    /// <summary>
    /// ケトン体コード
    /// </summary>
    public static readonly string URINE_RESULT_KETONE_CODE = "1104";
    /// <summary>
    /// ビリルビンコード
    /// </summary>
    public static readonly string URINE_RESULT_BILIRUBIN_CODE = "1105";
    /// <summary>
    /// ウロビリノーゲンコード
    /// </summary>
    public static readonly string URINE_RESULT_UROBILINOGEN_CODE = "1106";
    /// <summary>
    /// ｐＨコード
    /// </summary>
    public static readonly string URINE_RESULT_PH_CODE = "1107";
    /// <summary>
    /// 亜硝酸塩
    /// </summary>
    public static readonly string URINE_RESULT_NITRO_CODE = "1108";
    /// <summary>
    /// 白血球
    /// </summary>
    public static readonly string URINE_RESULT_LEUKOCYTE_CODE = "1109";
    /// <summary>
    /// 比重
    /// </summary>
    public static readonly string URINE_RESULT_GRAVITY_CODE = "1110";
    /// <summary>
    /// サンプルNO
    /// </summary>
    public static readonly string URINE_RESULT_SAMPLENUM_CODE = "1111";
    /// <summary>
    /// バーコードNO
    /// </summary>
    public static readonly string URINE_RESULT_BARCODE_CODE = "1112";
    #endregion

    #region 体脂肪用検査項目コード
    /// <summary>
    /// 体脂肪コード
    /// </summary>
    public static readonly string FAT_RESULT_CODE = "1201";
    #endregion

    #region オートレフ用検査項目コード

    /// <summary>
    /// 右眼代表値　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_MAIN_CODE = "1301";
    /// <summary>
    /// 右眼代表値　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_MAIN_CODE = "1302";
    /// <summary>
    /// 右眼代表値　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_MAIN_CODE = "1303";
    /// <summary>
    /// 右眼１回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_1_CODE = "1304";
    /// <summary>
    /// 右眼１回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_1_CODE = "1305";
    /// <summary>
    /// 右眼１回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_1_CODE = "1306";
    /// <summary>
    /// 右眼１回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_1_CODE = "1307";
    /// <summary>
    /// 右眼２回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_2_CODE = "1308";
    /// <summary>
    /// 右眼２回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_2_CODE = "1309";
    /// <summary>
    /// 右眼２回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_2_CODE = "1310";
    /// <summary>
    /// 右眼２回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_2_CODE = "1311";
    /// <summary>
    /// 右眼３回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_3_CODE = "1312";
    /// <summary>
    /// 右眼３回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_3_CODE = "1313";
    /// <summary>
    /// 右眼３回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_3_CODE = "1314";
    /// <summary>
    /// 右眼３回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_3_CODE = "1315";
    /// <summary>
    /// 右眼４回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_4_CODE = "1316";
    /// <summary>
    /// 右眼４回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_4_CODE = "1317";
    /// <summary>
    /// 右眼４回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_4_CODE = "1318";
    /// <summary>
    /// 右眼４回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_4_CODE = "1319";
    /// <summary>
    /// 右眼５回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_5_CODE = "1320";
    /// <summary>
    /// 右眼５回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_5_CODE = "1321";
    /// <summary>
    /// 右眼５回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_5_CODE = "1322";
    /// <summary>
    /// 右眼５回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_5_CODE = "1323";
    /// <summary>
    /// 右眼６回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_6_CODE = "1324";
    /// <summary>
    /// 右眼６回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_6_CODE = "1325";
    /// <summary>
    /// 右眼６回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_6_CODE = "1326";
    /// <summary>
    /// 右眼６回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_6_CODE = "1327";
    /// <summary>
    /// 右眼７回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_7_CODE = "1328";
    /// <summary>
    /// 右眼７回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_7_CODE = "1329";
    /// <summary>
    /// 右眼７回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_7_CODE = "1330";
    /// <summary>
    /// 右眼７回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_7_CODE = "1331";
    /// <summary>
    /// 右眼８回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_8_CODE = "1332";
    /// <summary>
    /// 右眼８回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_8_CODE = "1333";
    /// <summary>
    /// 右眼８回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_8_CODE = "1334";
    /// <summary>
    /// 右眼８回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_8_CODE = "1335";
    /// <summary>
    /// 右眼９回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_9_CODE = "1336";
    /// <summary>
    /// 右眼９回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_9_CODE = "1337";
    /// <summary>
    /// 右眼９回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_9_CODE = "1338";
    /// <summary>
    /// 右眼９回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_9_CODE = "1339";
    /// <summary>
    /// 右眼１０回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_SPH_10_CODE = "1340";
    /// <summary>
    /// 右眼１０回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_CYL_10_CODE = "1341";
    /// <summary>
    /// 右眼１０回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_AXIS_10_CODE = "1342";
    /// <summary>
    /// 右眼１０回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_R_TRUST_10_CODE = "1343";
    /// <summary>
    /// 左眼代表値　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_MAIN_CODE = "1344";
    /// <summary>
    /// 左眼代表値　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_MAIN_CODE = "1345";
    /// <summary>
    /// 左眼代表値　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_MAIN_CODE = "1346";
    /// <summary>
    /// 左眼１回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_1_CODE = "1347";
    /// <summary>
    /// 左眼１回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_1_CODE = "1348";
    /// <summary>
    /// 左眼１回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_1_CODE = "1349";
    /// <summary>
    /// 左眼１回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_1_CODE = "1350";
    /// <summary>
    /// 左眼２回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_2_CODE = "1351";
    /// <summary>
    /// 左眼２回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_2_CODE = "1352";
    /// <summary>
    /// 左眼２回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_2_CODE = "1353";
    /// <summary>
    /// 左眼２回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_2_CODE = "1354";
    /// <summary>
    /// 左眼３回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_3_CODE = "1355";
    /// <summary>
    /// 左眼３回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_3_CODE = "1356";
    /// <summary>
    /// 左眼３回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_3_CODE = "1357";
    /// <summary>
    /// 左眼３回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_3_CODE = "1358";
    /// <summary>
    /// 左眼４回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_4_CODE = "1359";
    /// <summary>
    /// 左眼４回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_4_CODE = "1360";
    /// <summary>
    /// 左眼４回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_4_CODE = "1361";
    /// <summary>
    /// 左眼４回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_4_CODE = "1362";
    /// <summary>
    /// 左眼５回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_5_CODE = "1363";
    /// <summary>
    /// 左眼５回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_5_CODE = "1364";
    /// <summary>
    /// 左眼５回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_5_CODE = "1365";
    /// <summary>
    /// 左眼５回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_5_CODE = "1366";
    /// <summary>
    /// 左眼６回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_6_CODE = "1367";
    /// <summary>
    /// 左眼６回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_6_CODE = "1368";
    /// <summary>
    /// 左眼６回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_6_CODE = "1369";
    /// <summary>
    /// 左眼６回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_6_CODE = "1370";
    /// <summary>
    /// 左眼７回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_7_CODE = "1371";
    /// <summary>
    /// 左眼７回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_7_CODE = "1372";
    /// <summary>
    /// 左眼７回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_7_CODE = "1373";
    /// <summary>
    /// 左眼７回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_7_CODE = "1374";
    /// <summary>
    /// 左眼８回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_8_CODE = "1375";
    /// <summary>
    /// 左眼８回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_8_CODE = "1376";
    /// <summary>
    /// 左眼８回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_8_CODE = "1377";
    /// <summary>
    /// 左眼８回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_8_CODE = "1378";
    /// <summary>
    /// 左眼９回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_9_CODE = "1379";
    /// <summary>
    /// 左眼９回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_9_CODE = "1380";
    /// <summary>
    /// 左眼９回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_9_CODE = "1381";
    /// <summary>
    /// 左眼９回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_9_CODE = "1382";
    /// <summary>
    /// 左眼１０回目　他覚屈折度測定値　SPHコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_SPH_10_CODE = "1383";
    /// <summary>
    /// 左眼１０回目　他覚屈折度測定値　CYLコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_CYL_10_CODE = "1384";
    /// <summary>
    /// 左眼１０回目　他覚屈折度測定値　AXISコード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_AXIS_10_CODE = "1385";
    /// <summary>
    /// 左眼１０回目　他覚屈折度測定値　信頼係数コード
    /// </summary>
    public static readonly string REFRACT_RESULT_L_TRUST_10_CODE = "1386";

    #endregion
}
