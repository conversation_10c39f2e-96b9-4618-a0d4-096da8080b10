using System;
using System.Collections.Generic;
using System.Text;

namespace System.Arctec.Ar1000k.DataBase
{
    public class IniGuideMasterRecord
    {
        private const int IPADRESS_POS = 0;
        private const int INIGUIDE_FLG_POS = 1;
        private const int LASTUPDATE_POS = 2;
        private string FldIpAdress;
        private string FldIniGuideFlg;
        private string FldLastUpDate;

        public IniGuideMasterRecord(string[] dbData)
        {
            this.IpAdress = dbData[IPADRESS_POS];
            this.IniGuideFlg = dbData[INIGUIDE_FLG_POS];
            this.LastUpDate = dbData[LASTUPDATE_POS];
        }

        public string IpAdress
        {
            get
            {
                return this.FldIpAdress;
            }
            set
            {
                this.FldIpAdress = value;
            }
        }

        public string IniGuideFlg
        {
            get
            {
                return this.FldIniGuideFlg;
            }
            set
            {
                this.FldIniGuideFlg = value;
            }
        }

        public string LastUpDate
        {
            get
            {
                return this.FldLastUpDate;
            }
            set
            {
                this.FldLastUpDate = value;
            }
        }
    }
}
