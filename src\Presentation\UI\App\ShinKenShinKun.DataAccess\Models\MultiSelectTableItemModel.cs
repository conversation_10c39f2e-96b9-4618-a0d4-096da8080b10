﻿namespace ShinKenShinKun.DataAccess;

public partial class MultiSelectTableItemModel : BaseModel
{
    [ObservableProperty] private string multiSelectTestItemId;
    [ObservableProperty] private string componentId;
    [ObservableProperty] private int testTypeNumber;
    [ObservableProperty] private int testItemNumber;
    [ObservableProperty] private string title;
    [ObservableProperty] private int noOrderDisplay;
    [ObservableProperty] private string pressEvent;
    [ObservableProperty] private string numbericKeypadSettingID;
    [ObservableProperty] private string pressSwitchingSettingID;
    [ObservableProperty] private string medicalCheckDataId;
}