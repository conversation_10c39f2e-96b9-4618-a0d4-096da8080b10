namespace ShinKenShinKun;

public partial class BulkPauseTestsButtonView : RadBorder
{
    public BulkPauseTestsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkPauseTestsCommandProperty = BindableProperty.Create(
        nameof(BulkPauseTestsCommand),
        typeof(IRelayCommand),
        typeof(BulkPauseTestsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkPauseTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkPauseTestsCommandProperty);
        set => SetValue(BulkPauseTestsCommandProperty, value);
    }
}