﻿namespace ShinKenShinKun;

public interface IGuidanceAdjustmentService
{
    ObservableCollection<ClientInformationModel> LoadDataClientInformations();
    ObservableCollection<ClientInformationModel> ClientInformationByClientId(string clientId, MedicalCheckModel medicalCheck);
    ObservableCollection<ClientInformationModel> ClientInformationByMedicalCheck(string clientId, MedicalCheckModel medicalCheck);
    ObservableCollection<MedicalCheckModel> GetMedicalCheckDivisions();
}
