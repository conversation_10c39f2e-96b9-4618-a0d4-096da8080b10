﻿using Color = Microsoft.Maui.Graphics.Color;

namespace ShinKenShinKun.Utils;

public class CheckColor
{
    public static Color GetColorRgb(string name)
    {
        switch (name.ToLower())
        {
            case "white":
                return Color.FromRgb(255, 255, 255);

            case "whitesmoke":
                return Color.FromRgb(245, 245, 245);

            case "gainsboro":
                return Color.FromRgb(220, 220, 220);

            case "lightgrey":
                return Color.FromRgb(211, 211, 211);

            case "silver":
                return Color.FromRgb(192, 192, 192);

            case "darkgray":
                return Color.FromRgb(169, 169, 169);

            case "gray":
                return Color.FromRgb(128, 128, 128);

            case "dimgray":
                return Color.FromRgb(105, 105, 105);

            case "black":
                return Color.FromRgb(0, 0, 0);

            case "red":
                return Color.FromRgb(255, 0, 0);

            case "orangered":
                return Color.FromRgb(255, 69, 0);

            case "tomato":
                return Color.FromRgb(255, 99, 71);

            case "coral":
                return Color.FromRgb(255, 127, 80);

            case "salmon":
                return Color.FromRgb(250, 128, 114);

            case "lightsalmon":
                return Color.FromRgb(255, 160, 122);

            case "darksalmon":
                return Color.FromRgb(233, 150, 122);

            case "peru":
                return Color.FromRgb(205, 133, 63);

            case "saddlebrown":
                return Color.FromRgb(139, 69, 19);

            case "sienna":
                return Color.FromRgb(160, 82, 45);

            case "chocolate":
                return Color.FromRgb(210, 105, 30);

            case "sandybrown":
                return Color.FromRgb(244, 164, 96);

            case "darkred":
                return Color.FromRgb(139, 0, 0);

            case "maroon":
                return Color.FromRgb(128, 0, 0);

            case "brown":
                return Color.FromRgb(165, 42, 42);

            case "firebrick":
                return Color.FromRgb(178, 34, 34);

            case "crimson":
                return Color.FromRgb(188, 6, 12);

            case "indianred":
                return Color.FromRgb(205, 92, 92);

            case "lightcoral":
                return Color.FromRgb(240, 128, 128);

            case "rosybrown":
                return Color.FromRgb(188, 143, 143);

            case "palevioletred":
                return Color.FromRgb(219, 112, 147);

            case "deeppink":
                return Color.FromRgb(255, 20, 147);

            case "hotpink":
                return Color.FromRgb(255, 105, 180);

            case "lightpink":
                return Color.FromRgb(255, 182, 193);

            case "pink":
                return Color.FromRgb(255, 192, 203);

            case "mistyrose":
                return Color.FromRgb(255, 228, 225);

            case "linen":
                return Color.FromRgb(250, 240, 230);

            case "seashell":
                return Color.FromRgb(255, 245, 238);

            case "lavenderblush":
                return Color.FromRgb(255, 240, 245);

            case "snow":
                return Color.FromRgb(255, 250, 250);

            case "yellow":
                return Color.FromRgb(255, 255, 0);

            case "gold":
                return Color.FromRgb(255, 215, 0);

            case "orange":
                return Color.FromRgb(255, 165, 0);

            case "darkorange":
                return Color.FromRgb(255, 140, 0);

            case "goldenrod":
                return Color.FromRgb(218, 165, 32);

            case "darkgoldenrod":
                return Color.FromRgb(184, 134, 11);

            case "darkkhaki":
                return Color.FromRgb(189, 183, 107);

            case "burlywood":
                return Color.FromRgb(222, 184, 135);

            case "tan":
                return Color.FromRgb(210, 180, 140);

            case "khaki":
                return Color.FromRgb(240, 230, 140);

            case "peachpuff":
                return Color.FromRgb(255, 218, 185);

            case "navajowhite":
                return Color.FromRgb(255, 222, 173);

            case "palegoldenrod":
                return Color.FromRgb(238, 232, 170);

            case "moccasin":
                return Color.FromRgb(255, 228, 181);

            case "wheat":
                return Color.FromRgb(245, 222, 179);

            case "bisque":
                return Color.FromRgb(255, 228, 196);

            case "blanchedalmond":
                return Color.FromRgb(255, 235, 205);

            case "papayawhip":
                return Color.FromRgb(255, 239, 213);

            case "cornsilk":
                return Color.FromRgb(255, 248, 220);

            case "lightyellow":
                return Color.FromRgb(255, 255, 224);

            case "lightgoldenrodyellow":
                return Color.FromRgb(250, 250, 210);

            case "lemonchiffon":
                return Color.FromRgb(255, 250, 205);

            case "antiquewhite":
                return Color.FromRgb(250, 235, 215);

            case "beige":
                return Color.FromRgb(245, 245, 220);

            case "oldlace":
                return Color.FromRgb(253, 245, 230);

            case "ivory":
                return Color.FromRgb(255, 255, 240);

            case "floralwhite":
                return Color.FromRgb(255, 250, 240);

            case "greenyellow":
                return Color.FromRgb(173, 255, 47);

            case "yellowgreen":
                return Color.FromRgb(154, 205, 50);

            case "olive":
                return Color.FromRgb(128, 128, 0);

            case "darkolivegreen":
                return Color.FromRgb(85, 107, 47);

            case "olivedrab":
                return Color.FromRgb(107, 142, 35);

            case "chartreuse":
                return Color.FromRgb(127, 255, 0);

            case "lawngreen":
                return Color.FromRgb(124, 252, 0);

            case "lime":
                return Color.FromRgb(0, 255, 0);

            case "limegreen":
                return Color.FromRgb(50, 205, 50);

            case "forestgreen":
                return Color.FromRgb(34, 139, 34);

            case "green":
                return Color.FromRgb(0, 128, 0);

            case "darkgreen":
                return Color.FromRgb(0, 100, 0);

            case "seagreen":
                return Color.FromRgb(46, 139, 87);

            case "mediumseagreen":
                return Color.FromRgb(60, 179, 113);

            case "darkseagreen":
                return Color.FromRgb(143, 188, 143);

            case "lightgreen":
                return Color.FromRgb(144, 238, 144);

            case "palegreen":
                return Color.FromRgb(152, 251, 152);

            case "springgreen":
                return Color.FromRgb(0, 255, 127);

            case "mediumspringgreen":
                return Color.FromRgb(0, 250, 154);

            case "honeydew":
                return Color.FromRgb(240, 255, 240);

            case "mintcream":
                return Color.FromRgb(245, 255, 250);

            case "azure":
                return Color.FromRgb(240, 255, 255);

            case "lightcyan":
                return Color.FromRgb(224, 255, 255);

            case "aliceblue":
                return Color.FromRgb(240, 248, 255);

            case "darkslategray":
                return Color.FromRgb(47, 79, 79);

            case "darkslatenavy":
                return Color.FromRgb(47, 47, 79);

            case "darkslatered":
                return Color.FromRgb(147, 47, 47);

            case "steelblue":
                return Color.FromRgb(70, 130, 180);

            case "mediumaquamarine":
                return Color.FromRgb(102, 205, 170);

            case "aquamarine":
                return Color.FromRgb(127, 255, 212);

            case "mediumturquoise":
                return Color.FromRgb(72, 209, 204);

            case "turquoise":
                return Color.FromRgb(64, 224, 208);

            case "lightseagreen":
                return Color.FromRgb(32, 178, 170);

            case "darkcyan":
                return Color.FromRgb(0, 139, 139);

            case "teal":
                return Color.FromRgb(0, 128, 128);

            case "cadetblue":
                return Color.FromRgb(95, 158, 160);

            case "darkturquoise":
                return Color.FromRgb(0, 206, 209);

            case "aqua":
                return Color.FromRgb(0, 255, 255);

            case "cyan":
                return Color.FromRgb(0, 255, 255);

            case "lightblue":
                return Color.FromRgb(173, 216, 230);

            case "powderblue":
                return Color.FromRgb(176, 224, 230);

            case "paleturquoise":
                return Color.FromRgb(175, 238, 238);

            case "skyblue":
                return Color.FromRgb(135, 206, 235);

            case "lightskyblue":
                return Color.FromRgb(135, 206, 250);

            case "deepskyblue":
                return Color.FromRgb(0, 191, 255);

            case "dodgerblue":
                return Color.FromRgb(30, 144, 255);

            case "ghostwhite":
                return Color.FromRgb(248, 248, 255);

            case "lavender":
                return Color.FromRgb(230, 230, 250);

            case "lightsteelblue":
                return Color.FromRgb(176, 196, 222);

            case "slategray":
                return Color.FromRgb(112, 128, 144);

            case "lightslategray":
                return Color.FromRgb(119, 136, 153);

            case "indigo":
                return Color.FromRgb(75, 0, 130);

            case "darkslateblue":
                return Color.FromRgb(72, 61, 139);

            case "midnightblue":
                return Color.FromRgb(25, 25, 112);

            case "navy":
                return Color.FromRgb(0, 0, 128);

            case "darkblue":
                return Color.FromRgb(0, 0, 139);

            case "slateblue":
                return Color.FromRgb(106, 90, 205);

            case "mediumslateblue":
                return Color.FromRgb(123, 104, 238);

            case "cornflowerblue":
                return Color.FromRgb(100, 149, 237);

            case "royalblue":
                return Color.FromRgb(65, 105, 225);

            case "mediumblue":
                return Color.FromRgb(0, 0, 205);

            case "blue":
                return Color.FromRgb(0, 0, 255);

            case "thistle":
                return Color.FromRgb(216, 191, 216);

            case "plum":
                return Color.FromRgb(221, 160, 221);

            case "orchid":
                return Color.FromRgb(218, 112, 214);

            case "violet":
                return Color.FromRgb(238, 130, 238);

            case "fuchsia":
                return Color.FromRgb(255, 0, 255);

            case "magenta":
                return Color.FromRgb(255, 0, 255);

            case "mediumpurple":
                return Color.FromRgb(147, 112, 219);

            case "mediumorchid":
                return Color.FromRgb(186, 85, 211);

            case "darkorchid":
                return Color.FromRgb(153, 50, 204);

            case "blueviolet":
                return Color.FromRgb(138, 43, 226);

            case "darkviolet":
                return Color.FromRgb(148, 0, 211);

            case "purple":
                return Color.FromRgb(128, 0, 128);

            case "darkmagenta":
                return Color.FromRgb(139, 0, 139);

            case "mediumvioletred":
                return Color.FromRgb(199, 21, 133);

            case "midnightblack":
                return Color.FromRgb(50, 50, 50);

            case "midnightgray":
                return Color.FromRgb(75, 75, 75);

            default:
                return Color.FromRgb(255, 255, 255);
        }
    }
}