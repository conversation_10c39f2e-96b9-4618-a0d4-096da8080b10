﻿namespace ShinKenShinKun.Utils;

public enum SoundType
{
    Default = 0,
    Once,
    Double,
    Triple,
    Forth,
    Fifth
}

public class CheckSound
{
    static CheckSound()
    {
    }

    public static void Speak()
    {
        Speak(SoundType.Default);
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="versionStr"></param>
    /// <returns></returns>
    public static SoundType GetSoundVersion(string versionStr)
    {
        if (versionStr == "Once")
        {
            return SoundType.Once;
        }
        else if (versionStr == "Double")
        {
            return SoundType.Double;
        }
        else if (versionStr == "Triple")
        {
            return SoundType.Triple;
        }
        else if (versionStr == "Forth")
        {
            return SoundType.Forth;
        }
        else if (versionStr == "Fifth")
        {
            return SoundType.Fifth;
        }
        else
        {
            return SoundType.Default;
        }
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="type"></param>
    public static void Speak(SoundType type)
    {
        //if (type == Type.Default)
        //{
        //    Console.Beep(1000, 150);
        //    Console.Beep(1500, 150);
        //    Console.Beep(2000, 150);
        //}
        //else if (type == Type.Once )
        //{
        //    Console.Beep(1500, 300);
        //}

        //else if (type == Type.Double)
        //{
        //    Console.Beep(1500, 200);
        //    Console.Beep(1500, 300);
        //}
        //else if (type == Type.Triple)
        //{
        //    Console.Beep(1500, 150);
        //    Console.Beep(1500, 150);
        //    Console.Beep(1500, 150);
        //}
        //else if (type == Type.Forth)
        //{
        //    Console.Beep(1000, 150);
        //    Console.Beep(1500, 150);
        //    Console.Beep(2000, 150);
        //    Console.Beep(2500, 150);
        //}
        //else if (type == Type.Fifth)
        //{
        //    Console.Beep(1000, 150);
        //    Console.Beep(1500, 150);
        //    Console.Beep(2000, 150);
        //    Console.Beep(2500, 150);
        //    Console.Beep(3000, 150);
        //}
    }

    //private class DefineMusicScale
    //{
    //    private Hashtable scaleMap = new Hashtable();

    //    private enum ScaleEnmu
    //    {
    //        SCALE_DO = 0,
    //        SCALE_RE,
    //        SCALE_MI,
    //        SCALE_FA,
    //        SCALE_SO,
    //        SCALE_RA,
    //        SCALE_SI,
    //        SCALE_DOU
    //    };

    //    private const int DO_SOUND = 1000;
    //    private const int RE_SOUND = 1250;
    //    private const int MI_SOUND = 1500;
    //    private const int FA_SOUND = 1750;
    //    private const int SO_SOUND = 2000;
    //    private const int RA_SOUND = 2250;
    //    private const int SI_SOUND = 2500;
    //    private const int DOU_SOUND = 2750;

    //    private DefineMusicScale()
    //    {
    //    }
    //}
}