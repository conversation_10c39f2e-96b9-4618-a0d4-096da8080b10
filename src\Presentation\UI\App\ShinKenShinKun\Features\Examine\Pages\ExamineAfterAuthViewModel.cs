﻿namespace ShinKenShinKun;

public partial class ExamineAfterAuthViewModel(
    IAppNavigator appNavigator,
    ISessionServices sessionServices,
    IExamineService examineService,
    IPopupService popupService,
    IAppSettingServices appSettingServices) : NavigationAwareBaseViewModel(appNavigator)
{
    #region ObervableProperty

    [ObservableProperty] private ObservableCollection<ClientInformationModel> clientInformations;
    [ObservableProperty] private ObservableCollection<string> functionButtons;
    [ObservableProperty] private ObservableCollection<ExamineAfterAuthModel> patientBodyInfo = new();
    [ObservableProperty] private ClientInformationModel clientSelected;
    [ObservableProperty] private string medicalMachineName;
    [ObservableProperty] private bool isLogin;
    [ObservableProperty] private bool isMaskMode;
    [ObservableProperty] private string userName;
    [ObservableProperty] private ScrollPositionModel noteScroll;
    [ObservableProperty] private ScrollPositionModel personalNoteScroll;
    [ObservableProperty] private ObservableCollection<AreaModel> areas;

    #endregion ObervableProperty

    #region Override Method

    protected override void OnInit(IDictionary<string, object> query)
    {
        var data = query.GetData<Tuple<ClientInformationModel, string>>();
        IsLogin = appSettingServices.AppSettings.IsLogin;
        ClientSelected = data.Item1;
        MedicalMachineName = data.Item2 ?? appSettingServices.AppSettings.DefaultMedicalCheck;
        IsLogin = appSettingServices.AppSettings.IsLogin;
        UserName = sessionServices.UserName;
        Areas = examineService.GetAreaDatas();
        PatientInfoGenerate();

        FunctionButtons = [
        "F1",
        "F2",
        "F3",
        "F4",
        "F5",
        "F6",
        "F7",
        "F8",];
    }

    public override Task OnAppearingAsync()
    {
        sessionServices.NoteViewScroll.ScrollY = 0;
        sessionServices.PersonalNoteViewScroll.ScrollY = 0;
        IsMaskMode = sessionServices.IsMaskMode;
        NoteScroll = sessionServices.NoteViewScroll;
        PersonalNoteScroll = sessionServices.PersonalNoteViewScroll;
        return base.OnAppearingAsync();
    }

    public override Task OnDisappearingAsync()
    {
        sessionServices.NoteViewScroll = NoteScroll;
        sessionServices.PersonalNoteViewScroll = PersonalNoteScroll;
        return base.OnDisappearingAsync();
    }

    #endregion Override Method

    #region Private Method

    private void PatientInfoGenerate()
    {
        PatientBodyInfo = [
            new ExamineAfterAuthModel { Id = 1, Name = "身長", NewValue = 170.0,OldValue=170.0, IsAfter = false },
            new ExamineAfterAuthModel { Id = 2, Name = "体重", NewValue = 60.0,OldValue=60.0, IsAfter = false },
            new ExamineAfterAuthModel { Id = 3, Name = "体脂肪", NewValue = 20.0,OldValue=20.0, IsAfter = false  },
            new ExamineAfterAuthModel { Id = 11, Name = "右裸眼", NewValue = 0.5,OldValue = 0.5, IsAfter = true },
            new ExamineAfterAuthModel { Id = 12, Name = "左裸眼", NewValue = 0.5,OldValue = 0.5, IsAfter = false },
            new ExamineAfterAuthModel { Id = 13, Name = "右矯正", NewValue = 1.0,OldValue = 1.0, IsAfter = false },
            new ExamineAfterAuthModel { Id = 13, Name = "左矯正", NewValue = 1.0,OldValue = 1.0, IsAfter = false },
        ];
    }

    #endregion Private Method

    #region RelayCommand

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    [RelayCommand]
    private async Task GoHome()
    {
        await AppNavigator.NavigateAsync(RouterName.HomePageRoot);
    }

    [RelayCommand]
    private async Task GoToGuidanceSelection()
    {
        popupService.ShowPopup<LoadingIndicatorViewModel>();

        await Task.Delay(1000);
        Tuple<ClientInformationModel, string> tuple = new(ClientSelected, MedicalMachineName);
        await AppNavigator.NavigateAsync(RouterName.GuidanceSelectionPage, false, tuple);
        popupService.ClosePopup();

    }

    [RelayCommand]
    private async Task ShowNoteEditPopup()
    {
        if(ClientSelected != null)
        {
            var popup = new NoteEditPopup();
            popup.Note = ClientSelected.Note;
            await AppNavigator.ShowPopupAsync(popup);
        }
    }

    protected override Task BackAsync()
    {
        return AppNavigator.GoBackAsync(false, MedicalMachineName);
    }

    [RelayCommand]
    private async Task GoToPendingTestSelection()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.PendingTestSelectionPage, false, ClientSelected);
    }

    [RelayCommand]
    private async Task GoToStopTestSelection()
    {
        if(ClientSelected == null)
        {
            return;
        }
        await AppNavigator.NavigateAsync(RouterName.StopTestSelectionPage, false, ClientSelected);
    }

    #endregion RelayCommand
}