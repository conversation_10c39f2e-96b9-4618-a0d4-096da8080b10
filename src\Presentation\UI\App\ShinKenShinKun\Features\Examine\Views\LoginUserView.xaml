<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.LoginUserView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <Grid
        IsVisible="{Binding IsVisible, Source={x:Reference this}}"
        HorizontalOptions="End"
        VerticalOptions="Center"
        Margin="{ui:EdgeInsets 
            Right={x:Static ui:Dimens.SpacingS}}"
        RowSpacing="{x:Static ui:Dimens.SpacingXs}"
        RowDefinitions="auto, 1">
        <HorizontalStackLayout
            Spacing="10">
            <Image
                Aspect="Fill"
                Source="{x:Static ui:Icons.Personal}"
                HeightRequest="32"
                WidthRequest="32" />
            <Label
                Style="{x:Static ui:Styles.NotoSansThinStyle}"
                Text="{Binding LoginUser, Source={x:Reference this}}"
                FontSize="{x:Static ui:Dimens.FontSizeT3}"
                VerticalTextAlignment="Center" />
        </HorizontalStackLayout>
        <Border
            Grid.Row="1"
            HeightRequest="1"
            Stroke="{x:Static ui:AppColors.White}"
            StrokeThickness="1"
            StrokeDashArray="4,2"
            BackgroundColor="Transparent"
            HorizontalOptions="Fill"
            VerticalOptions="Center" />   
    </Grid>
</ContentView>
