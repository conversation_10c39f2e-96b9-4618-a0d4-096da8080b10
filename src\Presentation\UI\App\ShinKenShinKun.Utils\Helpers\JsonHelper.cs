﻿using System.Text.Json;

namespace ShinKenShinKun.Utils;

public static class JsonHelper
{
    private static readonly JsonSerializerOptions UnsafeRelaxedJsonEscaping = new()
    {
        WriteIndented = true,
        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
    };

    public static string ConvertTo<PERSON>son(object items)
    {
        var options = UnsafeRelaxedJsonEscaping;
        return JsonSerializer.Serialize(items, options);
    }
}