﻿namespace ShinKenShinKun.DataAccess;

public class CustomMasterKey
{
    [Key]
    public int CustomKeyId { get; set; }

    public string KeyName { get; set; }
    public string Text { get; set; }
    public string Value { get; set; }
    public KeyAction Action { get; set; }

    [InverseProperty(nameof(CustomMasterKeySetting.CustomMasterKey))]
    public ICollection<CustomMasterKeySetting> CustomMasterKeySettings { get; set; }
}