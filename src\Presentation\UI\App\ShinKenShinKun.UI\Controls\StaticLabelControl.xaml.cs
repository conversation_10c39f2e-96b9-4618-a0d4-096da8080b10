namespace ShinKenShinKun.UI;

public partial class StaticLabelControl : ContentView
{
    public StaticLabelControl() => InitializeComponent();

    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        nameof(Text),
        typeof(string),
        typeof(StaticLabelControl),
        string.Empty,
        BindingMode.TwoWay);

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }
}