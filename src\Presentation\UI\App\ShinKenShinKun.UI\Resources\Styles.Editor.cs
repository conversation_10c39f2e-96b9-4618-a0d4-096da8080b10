﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style EditorStyle => CreateStyle<Editor>()
        .Set(Editor.TextColorProperty, Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.White : AppColors.Black)
        .Set(Editor.BackgroundColorProperty, AppColors.Transparent)
        .Set(Editor.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Editor.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Editor.MinimumHeightRequestProperty, 44)
        .Set(Editor.MinimumWidthRequestProperty, 44);
}
