﻿namespace ShinKenShinKun.DataAccess;

public class NumberPadServices(InMemoryDbContext inMemoryDbContext, IMapper mapper)
    : INumberPadServices
{
    public SettingNumberPadDto GetNumberPadSettingById(int id)
    {
        return mapper.Map<SettingNumberPadDto>(
            inMemoryDbContext
                .SettingNumberPads.Include(x => x.CustomMasterKeySettings)
                .ThenInclude(x => x.CustomMasterKey)
                .Include(x => x.StandarMasterKeySettings)
                .ThenInclude(x => x.StandardMasterKey)
                .FirstOrDefault(x => x.SettingNumberPadId == id)
        );
    }

    public List<SettingNumberPadDto> GetAllNumberPadSetting()
    {
        var res = inMemoryDbContext
            .SettingNumberPads.Include(x => x.CustomMasterKeySettings)
            .ThenInclude(x => x.CustomMasterKey)
            .Include(x => x.StandarMasterKeySettings)
            .ThenInclude(x => x.StandardMasterKey);
        return mapper.Map<List<SettingNumberPadDto>>(res.ToList());
    }
}