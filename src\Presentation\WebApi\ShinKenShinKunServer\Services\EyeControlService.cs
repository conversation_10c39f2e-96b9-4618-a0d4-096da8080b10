﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// EyControlService の概要の説明です
    /// </summary>
    public class EyeControlService
    {
        private DispSettingService service;
        private Logger logger = null;

        public EyeControlService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
            service = new DispSettingService();
        }

        #region EyeControlテーブル全取得
        /// <summary>
        /// EyeControlテーブル全取得
        /// </summary>
        /// <remarks>
        /// 
        /// </remarks>
        /// <returns></returns>
        public PluralRecord[] GetEyeControlAll()
        {
            try
            {
                DispSettingDataSet.EyeControlDataTable tmpTable =
                    service.GetEyeControlAll().EyeControl;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeControlRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeControlテーブル取得（ControlIDをKey）
        /// <summary>
        /// EyeControlテーブル取得（ControlIDをKey）
        /// </summary>
        /// <param name="controlId"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeControlId(string controlId)
        {
            try
            {
                short controlIdSht = Convert.ToInt16(controlId);
                DispSettingDataSet.EyeControlDataTable tmpTable =
                    service.GetEyeControlId(controlIdSht).EyeControl;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeControlRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region EyeControlテーブル取得（MedicalCheckNoをKey）
        /// <summary>
        /// EyeControlテーブル取得（MedicalCheckNoをKey）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public PluralRecord[] GetEyeControlMedicalCheckNo(string formId)
        {
            try
            {
                short formIdSht = Convert.ToInt16(formId);
                DispSettingDataSet.EyeControlDataTable tmpTable =
                    service.GetEyeControlMedicalCheckNo(formIdSht).EyeControl;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.EyeControlRow row in tmpTable)
                    {
                        PluralRecord pRec = SetPluralRecord(row);
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region PluralRecord設定関数
        /// <summary>
        /// PluralRecord設定関数
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        private PluralRecord SetPluralRecord(DispSettingDataSet.EyeControlRow row)
        {
            List<string> rList = new List<string>();
            rList.Add(row.EyeControlId.ToString());
            //ControlID設定
            rList.Add(row.ControlId.ToString());
            // MedicalCheckNo設定
            rList.Add(row.MedicalCheckNo.ToString());
            // ItemNo設定
            rList.Add(row.ItemNo.ToString());
            rList.Add(row.FormId.ToString());
            PluralRecord pRec = new PluralRecord(rList.ToArray());
            return pRec;
        }
        #endregion
    }
}