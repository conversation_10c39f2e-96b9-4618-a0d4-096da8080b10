﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style EntryStyle => CreateStyle<Entry>()
        .Set(Entry.TextColorProperty, Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.White : AppColors.Black)
        .Set(Entry.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Entry.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Entry.MinimumHeightRequestProperty, 44)
        .Set(Entry.MinimumWidthRequestProperty, 44);

    public static Style EntryTemplateStyle => CreateStyle<Entry>()
        .Set(Entry.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Entry.TextColorProperty, AppColors.Black)
        .Set(Entry.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Entry.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Entry.ClearButtonVisibilityProperty, ClearButtonVisibility.Never)
        .Set(Entry.TextColorProperty, AppColors.Black);

    public static Style MultiSelectTableEntryStyle => CreateStyle<Entry>()
        .Set(Entry.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Entry.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Entry.TextColorProperty, AppColors.Black)
        .Set(Entry.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Entry.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Entry.KeyboardProperty, Keyboard.Numeric)
        .Set(Entry.ClearButtonVisibilityProperty, ClearButtonVisibility.Never)
        .Set(Entry.TextColorProperty, AppColors.LightBlue);
}
