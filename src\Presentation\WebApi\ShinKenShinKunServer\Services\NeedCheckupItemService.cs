﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.MedicalDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Transactions;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// NeedCheckupItemService の概要の説明です
    /// </summary>
    public class NeedCheckupItemService
    {
        private MedicalDataSet MedicalDataSet = new MedicalDataSet();
        private Service srv;
        private Logger logger = null;

        private readonly string MEDICAL_CHECK_NO_COLUMN = "MedicalCheckNo";
        private readonly string ITEM_NO_COLUMN = "ItemNo";
        private readonly string NEED_CHECK_NO_COLUMN = "NeedCheckNo";
        private readonly string NEED_ITEM_NO_COLUMN = "NeedItemNo";

        public NeedCheckupItemService()
        {
            srv = new Service();
        }

        #region 必須検査項目（項目毎設定）の全取得
        /// <summary>
        /// 必須検査項目（項目毎設定）の全取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] GetNeedCheckupItemAll()
        {
            try
            {
                MedicalDataSet = srv.GetNeedCheckupItemAll();
                // 取得したレコード数のチェック
                int retCount = MedicalDataSet.NeedCheckupItem.Rows.Count;
                PluralRecord[] pRecord = new PluralRecord[retCount];
                ArrayList itemArray = new ArrayList();
                // レコード数分、処理を繰り返す
                for (int i = 0; i < retCount; i++)
                {
                    DataRow row = MedicalDataSet.NeedCheckupItem.Rows[i];
                    if (row != null)
                    {
                        itemArray.Add(row[MEDICAL_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                    }
                    string[] itemList = new string[itemArray.Count];
                    Array.Copy(itemArray.ToArray(), itemList, itemArray.Count);
                    pRecord[i] = new PluralRecord(itemList);
                    itemArray.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        public PluralRecord[] GetNeedCheckupItem(string sMedicalCheckNo)
        {
            try
            {
                short medicalCheckNo;
                if (!short.TryParse(sMedicalCheckNo, out medicalCheckNo))
                {
                    return null;
                }
                MedicalDataSet = srv.GetNeedCheckupItem(medicalCheckNo);
                // 取得したレコード数のチェック
                int retCount = MedicalDataSet.NeedCheckupItem.Rows.Count;
                PluralRecord[] pRecord = new PluralRecord[retCount];
                ArrayList itemArray = new ArrayList();
                // レコード数分、処理を繰り返す
                for (int i = 0; i < retCount; i++)
                {
                    DataRow row = MedicalDataSet.NeedCheckupItem.Rows[i];
                    if (row != null)
                    {
                        itemArray.Add(row[MEDICAL_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                    }
                    string[] itemList = new string[itemArray.Count];
                    Array.Copy(itemArray.ToArray(), itemList, itemArray.Count);
                    pRecord[i] = new PluralRecord(itemList);
                    itemArray.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetNeedCheckupItem(string sMedicalCheckNo, string sItemNo)
        {
            try
            {
                short medicalCheckNo;
                short itemNo;
                if (!(short.TryParse(sMedicalCheckNo, out medicalCheckNo)
                    && short.TryParse(sItemNo, out itemNo)))
                {
                    return null;
                }
                MedicalDataSet = srv.GetNeedCheckupItem(medicalCheckNo, itemNo);
                // 取得したレコード数のチェック
                int retCount = MedicalDataSet.NeedCheckupItem.Rows.Count;
                PluralRecord[] pRecord = new PluralRecord[retCount];
                ArrayList itemArray = new ArrayList();
                // レコード数分、処理を繰り返す
                for (int i = 0; i < retCount; i++)
                {
                    DataRow row = MedicalDataSet.NeedCheckupItem.Rows[i];
                    if (row != null)
                    {
                        itemArray.Add(row[MEDICAL_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_CHECK_NO_COLUMN].ToString().PadLeft(1, ' '));
                        itemArray.Add(row[NEED_ITEM_NO_COLUMN].ToString().PadLeft(1, ' '));
                    }
                    string[] itemList = new string[itemArray.Count];
                    Array.Copy(itemArray.ToArray(), itemList, itemArray.Count);
                    pRecord[i] = new PluralRecord(itemList);
                    itemArray.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public bool SetNeedCheckupItem(DataTable updateTbl)
        {
            try
            {
                if (updateTbl != null)
                {
                    using (TransactionScope tx = new TransactionScope())
                    {
                        MedicalDataSet = new MedicalDataSet();
                        foreach (DataRow row in updateTbl.Rows)
                        {
                            MedicalDataSet.NeedCheckupItemRow newRow = MedicalDataSet.NeedCheckupItem.NewNeedCheckupItemRow();
                            newRow.BeginEdit();
                            newRow.MedicalCheckNo = short.Parse(row["MedicalCheckNo"].ToString());
                            newRow.ItemNo = short.Parse(row["ItemNo"].ToString());
                            newRow.NeedCheckNo = short.Parse(row["NeedCheckNo"].ToString());
                            newRow.NeedItemNo = short.Parse(row["NeedItemNo"].ToString());
                            newRow.EndEdit();
                            MedicalDataSet.NeedCheckupItem.AddNeedCheckupItemRow(newRow);
                        }
                        NeedCheckupItemTableAdapter adapter =
                            new NeedCheckupItemTableAdapter();
                        adapter.DeleteQuery();
                        adapter.Update(MedicalDataSet.NeedCheckupItem);
                        tx.Complete();
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
    }
}