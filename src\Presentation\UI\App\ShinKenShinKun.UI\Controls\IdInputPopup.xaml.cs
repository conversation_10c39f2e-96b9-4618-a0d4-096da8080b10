namespace ShinKenShinKun.UI;

public partial class IdInputPopup : Popup
{
    public IdInputPopup()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty TitleProperty = BindableProperty.Create(
    nameof(Title),
    typeof(string),
    typeof(IdInputPopup),
    string.Empty,
    BindingMode.TwoWay);

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public static readonly BindableProperty InputValueProperty = BindableProperty.Create(
    nameof(InputValue),
    typeof(string),
    typeof(IdInputPopup),
    string.Empty,
    BindingMode.TwoWay);

    public string InputValue
    {
        get => (string)GetValue(InputValueProperty);
        set => SetValue(InputValueProperty, value);
    }

    public static readonly BindableProperty ConfirmCommandProperty = BindableProperty.Create(
    nameof(ConfirmCommand),
    typeof(ICommand),
    typeof(IdInputPopup),
    default(ICommand),
    BindingMode.TwoWay);

    public ICommand ConfirmCommand
    {
        get => (ICommand)GetValue(ConfirmCommandProperty);
        set => SetValue(ConfirmCommandProperty, value);
    }

    public static readonly BindableProperty CancelCommandProperty = BindableProperty.Create(
    nameof(CancelCommand),
    typeof(ICommand),
    typeof(IdInputPopup),
    default(ICommand),
    BindingMode.TwoWay);

    public ICommand CancelCommand
    {
        get => (ICommand)GetValue(CancelCommandProperty);
        set => SetValue(CancelCommandProperty, value);
    }

    public static readonly BindableProperty IsOpenProperty = BindableProperty.Create(
    nameof(IsOpen),
    typeof(bool),
    typeof(IdInputPopup),
    false,
    BindingMode.TwoWay);

    public bool IsOpen
    {
        get => (bool)GetValue(IsOpenProperty);
        set => SetValue(IsOpenProperty, value);
    }

    private void OnClose(object sender, EventArgs e)
    {
        this.Close();
    }

    private void OnKeyPressed(object sender, EventArgs e)
    {
        if (sender is Button button && InputValue.Length < 10)
        {
            InputValue += button.Text;
        }
    }

    private void OnDelete(object sender, EventArgs e)
    {
        InputValue = "";
    }

    private void OnBackspace(object sender, EventArgs e)
    {
        if (InputValue.Length > 0)
        {
            InputValue = InputValue.Substring(0, InputValue.Length - 1);
        }
    }
}