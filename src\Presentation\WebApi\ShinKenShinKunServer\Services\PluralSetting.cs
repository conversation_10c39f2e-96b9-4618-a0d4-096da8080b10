﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// PluralSetting の概要の説明です

    /// </summary>
    public class PluralSetting
    {
        private DispSettingService service;
        private Logger logger = null;

        public PluralSetting()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetPluralSettingAll()
        {
            try
            {
                DispSettingDataSet.PluralSettingDataTable tmpTable =
                    service.GetPluralSettingAll().PluralSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.PluralSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        for (int i = 1; i <= 50; i++)
                        {
                            if (row["ChildMedicalCheckNo" + i.ToString()] == null)
                            {
                                rList.Add(null);
                            }
                            else
                            {
                                rList.Add(row["ChildMedicalCheckNo" + i.ToString()].ToString());
                            }
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }

                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetPlurakSetting(
            string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.PluralSettingDataTable tmpTable =
                    service.GetPluralSetting(medicalCheckNoSht).PluralSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.PluralSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        for (int i = 1; i <= 50; i++)
                        {
                            if (row["ChildMedicalCheckNo" + i.ToString()] == null)
                            {
                                rList.Add(null);
                            }
                            else
                            {
                                rList.Add(row["ChildMedicalCheckNo" + i.ToString()].ToString());
                            }
                        }
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }

                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}