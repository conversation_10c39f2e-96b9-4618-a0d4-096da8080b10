<?xml version="1.0" encoding="utf-8"?>

<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.PatientListView"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    x:Name="this">
    <ScrollView
        VerticalOptions="Center"
        Orientation="Horizontal">
        <HorizontalStackLayout
            BindableLayout.ItemsSource="{Binding ItemsSource, 
                Source={x:Reference this}}">
            <BindableLayout.ItemTemplate>
                <DataTemplate
                    x:DataType="app:MedicalCheckStateModel">
                    <Grid
                        RowDefinitions="*, auto">
                        <telerik:RadBorder
                            Grid.Row="0"
                            Style="{x:Static ui:Styles.HeaderExamineStatusBorderStyle}"
                            BorderThickness="{ui:EdgeInsets 
                                Horizontal={x:Static ui:Dimens.Thickness1},
                                Top={x:Static ui:Dimens.Thickness2},
                                Bottom={x:Static ui:Dimens.Thickness1}}">
                            <Label
                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                TextColor="{x:Static ui:AppColors.Black}"
                                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                                VerticalTextAlignment="Center"
                                HorizontalTextAlignment="Center"
                                MaxLines="2"
                                LineBreakMode="WordWrap"
                                Padding="{ui:EdgeInsets 
                                    Horizontal={x:Static ui:Dimens.SpacingSm},
                                    Vertical={x:Static ui:Dimens.SpacingSm}}"
                                Text="{Binding MedicalCheckName}" />
                        </telerik:RadBorder>
                        <telerik:RadBorder
                            Grid.Row="1"
                            Style="{x:Static ui:Styles.ValueExamineStatusBorderStyle}"
                            BackgroundColor="{Binding TestStatus.StatusColor}"
                            BorderThickness="{ui:EdgeInsets 
                                Horizontal={x:Static ui:Dimens.Thickness1},
                                Top={x:Static ui:Dimens.Thickness1},
                                Bottom={x:Static ui:Dimens.Thickness2}}">
                            <Label
                                VerticalTextAlignment="Center"
                                HorizontalTextAlignment="Center"
                                Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                                FontSize="{x:Static ui:Dimens.FontSizeT5}"
                                Text="{Binding TestStatus.DisplayName}"
                                TextColor="{x:Static ui:AppColors.Black}" />
                        </telerik:RadBorder>
                    </Grid>
                </DataTemplate>
            </BindableLayout.ItemTemplate>
        </HorizontalStackLayout>
    </ScrollView>
</ContentView>