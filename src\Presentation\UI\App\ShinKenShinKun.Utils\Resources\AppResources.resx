﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Completed" xml:space="preserve">
    <value>済</value>
  </data>
  <data name="Excluded" xml:space="preserve">
    <value>外</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>女</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>男</value>
  </data>
  <data name="NoDefine" xml:space="preserve">
    <value>不</value>
  </data>
  <data name="Pending" xml:space="preserve">
    <value>保</value>
  </data>
  <data name="QuitAppTitle" xml:space="preserve">
    <value>アプリケーションを終了しますか？</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="LoginScreenTitle" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>いいえ</value>
  </data>
  <data name="Offline" xml:space="preserve">
    <value>Offline</value>
  </data>
  <data name="Online" xml:space="preserve">
    <value>Online</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Shutdown" xml:space="preserve">
    <value>終了</value>
  </data>
  <data name="ShutdownDeviceTitle" xml:space="preserve">
    <value>コンピュータをシャットダウンしますか？</value>
  </data>
  <data name="SystemName" xml:space="preserve">
    <value>けんしんくん</value>
  </data>
  <data name="UserId" xml:space="preserve">
    <value>ログインID</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>はい</value>
  </data>
  <data name="KanaName" xml:space="preserve">
    <value>カナ指名</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>年齢</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>性別</value>
  </data>
  <data name="Reserved" xml:space="preserve">
    <value>確保</value>
  </data>
  <data name="WaitingTime" xml:space="preserve">
    <value>待ち時間</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>生年月日</value>
  </data>
  <data name="NoData" xml:space="preserve">
    <value>データなし</value>
  </data>
  <data name="HomeTitle" xml:space="preserve">
    <value>けんしんくんHOME</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="LogoutMessage" xml:space="preserve">
    <value>ログアウトしますか？</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="TestSelectionPageHeader" xml:space="preserve">
    <value>検査画面選択</value>
  </data>
  <data name="ConfirmNavigationMessage" xml:space="preserve">
    <value>{0}に誘導しますか？</value>
  </data>
  <data name="PendingNavigationMessage" xml:space="preserve">
    <value>{0}は保留中です。　　
{0}に誘導しますか？</value>
  </data>
  <data name="RequiredTestNotCompletedMessage" xml:space="preserve">
    <value>{0}は必須検査が終了していません。
{0}に誘導しますか？</value>
  </data>
  <data name="FirstCandidateTitle" xml:space="preserve">
    <value>第一候補</value>
  </data>
  <data name="AgeStringFormat" xml:space="preserve">
    <value>{0}歳</value>
  </data>
  <data name="GuideOpen" xml:space="preserve">
    <value>誘導開始</value>
  </data>
  <data name="GuideStop" xml:space="preserve">
    <value>誘導中断</value>
  </data>
  <data name="ReserveNoConfirmTitle" xml:space="preserve">
    <value>他の受診者を確保済みです。
他の受診者の確保を解除してから確保を行ってください。</value>
  </data>
  <data name="ReserveCancelTitle" xml:space="preserve">
    <value>確保を解除しますがよろしいですか？</value>
  </data>
  <data name="ReserveNoCanceltitle" xml:space="preserve">
    <value>他の端末で確保されているため、確保できません。</value>
  </data>
  <data name="PatientInformation" xml:space="preserve">
    <value>受診者情報</value>
  </data>
  <data name="PersonalNotes" xml:space="preserve">
    <value>個人特記</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>備考</value>
  </data>
  <data name="DatetimeStringFormat" xml:space="preserve">
    <value>{0:yyyy/MM/dd}</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>更新</value>
  </data>
  <data name="InputClientIdButton" xml:space="preserve">
    <value>ID入力</value>
  </data>
  <data name="PendingButton" xml:space="preserve">
    <value>保留</value>
  </data>
  <data name="StopButton" xml:space="preserve">
    <value>中止</value>
  </data>
  <data name="Mask" xml:space="preserve">
    <value>マスク</value>
  </data>
  <data name="ExaminationScreen" xml:space="preserve">
    <value>検査画面</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>すべて</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Height" xml:space="preserve">
    <value>身長</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>体重</value>
  </data>
  <data name="BodyFat" xml:space="preserve">
    <value>体脂肪</value>
  </data>
  <data name="RightNakedEye" xml:space="preserve">
    <value>右裸眼</value>
  </data>
  <data name="LeftNakedEye" xml:space="preserve">
    <value>左裸眼</value>
  </data>
  <data name="RightEye" xml:space="preserve">
    <value>右矯正</value>
  </data>
  <data name="LeftEye" xml:space="preserve">
    <value>左矯正</value>
  </data>
  <data name="MaskText" xml:space="preserve">
    <value>＊＊＊＊＊＊＊</value>
  </data>
  <data name="EDIT" xml:space="preserve">
    <value>EDIT</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="CanNotRunExternalSystem" xml:space="preserve">
    <value>状況確認モニタジュシステムの起動に失敗しました。もう一度「全体進捗」を押してください。</value>
  </data>
  <data name="MaskOn" xml:space="preserve">
    <value>ON</value>
  </data>
  <data name="MaskOff" xml:space="preserve">
    <value>OFF</value>
  </data>
  <data name="HomeButtonLabel" xml:space="preserve">
    <value>HOME</value>
  </data>
  <data name="GuideOpenDialogTitle" xml:space="preserve">
    <value>の誘導を開始しますか？</value>
  </data>
  <data name="GuideStopDialogTitle" xml:space="preserve">
    <value>の誘導を中断しますか？</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>ON</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>OFF</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>追加</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>閉じる</value>
  </data>
  <data name="OrderNote" xml:space="preserve">
    <value>備考 {0}</value>
  </data>
  <data name="GuidanceSettingTitle" xml:space="preserve">
    <value>誘導変更</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状態</value>
  </data>
  <data name="Done" xml:space="preserve">
    <value>済</value>
  </data>
  <data name="Person" xml:space="preserve">
    <value>人</value>
  </data>
  <data name="TestStatus" xml:space="preserve">
    <value>検査状況</value>
  </data>
  <data name="PendingAll" xml:space="preserve">
    <value>一括保留</value>
  </data>
  <data name="ChangeGuide" xml:space="preserve">
    <value>誘導変更</value>
  </data>
  <data name="Entire" xml:space="preserve">
    <value>全て</value>
  </data>
  <data name="NameMedicalCategory" xml:space="preserve">
    <value>表示検査</value>
  </data>
  <data name="GuidanceDestination" xml:space="preserve">
    <value>誘導先</value>
  </data>
  <data name="GuidanceAdjustment" xml:space="preserve">
    <value>誘導調整</value>
  </data>
  <data name="ReceiptNumber" xml:space="preserve">
    <value>受付番号</value>
  </data>
  <data name="InExamination" xml:space="preserve">
    <value>検査中</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>検索</value>
  </data>
  <data name="BodyMassIndex" xml:space="preserve">
    <value>BMI</value>
  </data>
  <data name="Sight" xml:space="preserve">
    <value>視力</value>
  </data>
  <data name="BloodPressure" xml:space="preserve">
    <value>血圧</value>
  </data>
  <data name="HighestBloodPressure1" xml:space="preserve">
    <value>最高血圧1</value>
  </data>
  <data name="HightestBloodPressure2" xml:space="preserve">
    <value>最高血圧2</value>
  </data>
  <data name="LowestBloodPressure1" xml:space="preserve">
    <value>最低血圧1</value>
  </data>
  <data name="LowestBloodPressure2" xml:space="preserve">
    <value>最低血圧2</value>
  </data>
  <data name="Pulse1" xml:space="preserve">
    <value>脈拍1</value>
  </data>
  <data name="Pulse2" xml:space="preserve">
    <value>脈拍2</value>
  </data>
  <data name="HearingSelection" xml:space="preserve">
    <value>聴力選別</value>
  </data>
  <data name="HearingThreshold" xml:space="preserve">
    <value>聴力閾値</value>
  </data>
  <data name="Right1000Hz" xml:space="preserve">
    <value>1000Hz右</value>
  </data>
  <data name="Left1000Hz" xml:space="preserve">
    <value>1000Hz左</value>
  </data>
  <data name="Right4000Hz" xml:space="preserve">
    <value>4000Hz右</value>
  </data>
  <data name="Left4000Hz" xml:space="preserve">
    <value>4000Hz左</value>
  </data>
  <data name="Right250Hz" xml:space="preserve">
    <value>250Hz右</value>
  </data>
  <data name="Left250Hz" xml:space="preserve">
    <value>250Hz左</value>
  </data>
  <data name="Right500Hz" xml:space="preserve">
    <value>500Hz右</value>
  </data>
  <data name="Left500Hz" xml:space="preserve">
    <value>500Hz左</value>
  </data>
  <data name="Right2000Hz" xml:space="preserve">
    <value>2000Hz右</value>
  </data>
  <data name="Left2000Hz" xml:space="preserve">
    <value>2000Hz左</value>
  </data>
  <data name="Right8000Hz" xml:space="preserve">
    <value>8000Hz右</value>
  </data>
  <data name="Left8000Hz" xml:space="preserve">
    <value>8000Hz左</value>
  </data>
  <data name="Measurement" xml:space="preserve">
    <value>計測</value>
  </data>
  <data name="Progress" xml:space="preserve">
    <value>進捗</value>
  </data>
  <data name="TestResult" xml:space="preserve">
    <value>検査結果</value>
  </data>
  <data name="IndividualProgress" xml:space="preserve">
    <value>個別進捗</value>
  </data>
  <data name="StopExamineScreen" xml:space="preserve">
    <value>中止検査選択</value>
  </data>
  <data name="PatientPendingScreen" xml:space="preserve">
    <value>受診者一括保留</value>
  </data>
  <data name="MedicalChecklist" xml:space="preserve">
    <value>検査種別</value>
  </data>
  <data name="CancelPending" xml:space="preserve">
    <value>保留解除</value>
  </data>
  <data name="Stopped" xml:space="preserve">
    <value>止</value>
  </data>
  <data name="Next" xml:space="preserve">
    <value>次</value>
  </data>
  <data name="Unchecked" xml:space="preserve">
    <value>未</value>
  </data>
  <data name="CancelAll" xml:space="preserve">
    <value>一括解除</value>
  </data>
  <data name="SelectionPendingTitle" xml:space="preserve">
    <value>保留検査選択</value>
  </data>
  <data name="SelectReasonForCancellation" xml:space="preserve">
    <value>中止理由選択</value>
  </data>
  <data name="SelectTheTestType" xml:space="preserve">
    <value>検査種別選択</value>
  </data>
  <data name="InspectionItemSelection" xml:space="preserve">
    <value>検査項目選択</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>クリア</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>すべて選択</value>
  </data>
  <data name="SelectionStopTitle" xml:space="preserve">
    <value>中止検査選択</value>
  </data>
  <data name="StopAll" xml:space="preserve">
    <value>一括中止</value>
  </data>
  <data name="Guidance" xml:space="preserve">
    <value>誘導</value>
  </data>
  <data name="Plan" xml:space="preserve">
    <value>予定</value>
  </data>
  <data name="NotYet" xml:space="preserve">
    <value>未</value>
  </data>
  <data name="NumberOf" xml:space="preserve">
    <value>{0}回目</value>
  </data>
  <data name="AverageValue" xml:space="preserve">
    <value>平均値</value>
  </data>
  <data name="OldValue" xml:space="preserve">
    <value>過去値</value>
  </data>
  <data name="Backspace" xml:space="preserve">
    <value>一文字削除</value>
  </data>
  <data name="ClearText" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="TurnBack" xml:space="preserve">
    <value>戻る</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>確定</value>
  </data>
  <data name="AppSettingErrorTitle" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="AppSettingErrorMessage" xml:space="preserve">
    <value>新けんしんくんの起動に失敗しました。設定ファイルを確認し、アプリを再起動してください。</value>
  </data>
  <data name="Ok" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="LoginIdEmptyInvalid" xml:space="preserve">
    <value>ログインIDを入力してください。</value>
  </data>
  <data name="PasswordEmptyInvalid" xml:space="preserve">
    <value>パスワードを入力してください。</value>
  </data>
  <data name="LoginIdOrPasswordInvalid" xml:space="preserve">
    <value>ログインIDまたはパスワードが不正です。</value>
  </data>
  <data name="LoginIdLabel" xml:space="preserve">
    <value>ログインユーザ</value>
  </data>
  <data name="LogFormat" xml:space="preserve">
    <value>[{0}] User: {1} | IP: {2} | Status: {3}</value>
  </data>
  <data name="ConnectionErrors" xml:space="preserve">
    <value>新けんしんくんサ ー バとの接続時にエラ ー が発生しました。ネットワ ー ク環境を確認してくたさい。</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>登録</value>
  </data>
</root>