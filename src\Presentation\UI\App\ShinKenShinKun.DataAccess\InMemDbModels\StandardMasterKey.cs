﻿namespace ShinKenShinKun.DataAccess;

public class StandardMasterKey
{
    [Key]
    public int StandardKeyId { get; set; }

    public string KeyName { get; set; }
    public string Text { get; set; }
    public string Value { get; set; }
    public KeyAction Action { get; set; }

    [InverseProperty(nameof(StandardMasterKeySetting.StandardMasterKey))]
    public ICollection<StandardMasterKeySetting> StandarMasterKeySettings { get; set; }
}