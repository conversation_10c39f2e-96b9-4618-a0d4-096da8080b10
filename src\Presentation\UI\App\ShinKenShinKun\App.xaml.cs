﻿namespace ShinKenShinKun;

public partial class App : Application
{
#if WINDOWS
    private Exception _lastFirstChanceException;
#endif
    private readonly IAppNavigator appNavigator;
    private readonly IAppSettingServices appSettingServices;

    public App(IAppNavigator appNavigator, IAppSettingServices appSettingServices)
    {
        TelerikLocalizationManager.Manager.ResourceManager = AppResources.ResourceManager;
        InitializeComponent();
        this.appNavigator = appNavigator;
        this.appSettingServices = appSettingServices;
#if WINDOWS
        // Handle exceptions from Microsoft.UI.Xaml
        Microsoft.UI.Xaml.Application.Current.UnhandledException += UIUnhandledException;

        WriteEventLogEntry();

        LogThreadConflicts();
#endif
        // Handle unobserved task exceptions
        TaskScheduler.UnobservedTaskException += UnobservedTaskException;

        // Handle UI thread exceptions
        AppDomain.CurrentDomain.UnhandledException += UnhandledException;

        // Log application start and stop events
        AppDomain.CurrentDomain.ProcessExit += ProcessExit;

        AppDomain.CurrentDomain.DomainUnload += DomainUnload;
    }

    private async Task HandleNavigateToDefaultSetting(IAppNavigator appNavigator)
    {
        await appNavigator.NavigateAsync(
            RouteHelpers.GetRouteDefault(appSettingServices.AppSettings)
        );
        if (
            !appSettingServices.AppSettings.IsLogin
            && appSettingServices.AppSettings.DefaultScreen
                == AppConstants.OverallProgressScreenCode
        )
        {
            await RouteHelpers.RunMonitoringSystem(appSettingServices.AppSettings);
        }
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        return new Window(new AppShell());
    }

    protected override void OnStart()
    {
        base.OnStart();
        // Check the app is already running
        var isError = appSettingServices.IsErrorReadingAppSettings();
        if(isError)
        {
            appNavigator.NavigateAsync(RouterName.BlankPage);
            return;
        }

        // Handle application start logic here
        _ = HandleNavigateToDefaultSetting(appNavigator);
    }

#if WINDOWS
    private void LogThreadConflicts()
    {
        AppDomain.CurrentDomain.FirstChanceException += (sender, e) =>
        {
            if (
                e.Exception
                is ThreadAbortException
                    or ThreadStateException
                    or ThreadInterruptedException
            )
            {
                Log.Error(e.Exception, AppConstants.ThreadConflict);
            }
            else if (e.Exception is ObjectDisposedException)
            {
                Log.Error(e.Exception, AppConstants.ObjectDisposedException);
            }
            else if (
                !e.Exception.Message.Contains(AppConstants.ResourceEnd)
                && !e.Exception.Message.Contains(AppConstants.URI)
            )
            {
                Log.Error(e.Exception, AppConstants.FirstChanceException);
            }
        };
    }

    private void WriteEventLogEntry()
    {
        try
        {
            using var eventLog = new EventLog(AppConstants.Application);
            foreach (EventLogEntry item in eventLog.Entries)
            {
                if (
                    item.EntryType == EventLogEntryType.Error
                    && item.Source == AppConstants.ApplicationError
                    && item.Message.Contains(AppConstants.AppSource)
                    && (DateTime.Now - item.TimeWritten).Days < 1
                )
                {
                    Log.Error(
                        $"{AppConstants.ApplicationError}:\n{item.TimeWritten} \n{item.Message}"
                    );
                }
            }
            eventLog.EntryWritten += (sender, e) =>
            {
                Log.Error(
                    $"{AppConstants.EventViewerEntry}\n{e.Entry.TimeWritten}\n{e.Entry.Message}"
                );
            };
            eventLog.EnableRaisingEvents = true;
        }
        catch (Exception ex)
        {
            Log.Error(ex, AppConstants.FailedEventViewer);
        }
    }

    private void UIUnhandledException(
        object sender,
        Microsoft.UI.Xaml.UnhandledExceptionEventArgs e
    )
    {
        var exception = e.Exception;

        if (exception.StackTrace is null)
        {
            exception = _lastFirstChanceException;
        }

        if (e.Exception is AccessViolationException)
        {
            Log.Error(e.Exception, AppConstants.AccessViolation);
        }
        else
        {
            Log.Error(e.Exception, AppConstants.UnhandledException);
        }
        e.Handled = true;
    }
#endif

    private void UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception ex)
        {
            Log.Error(ex, AppConstants.UnhandledException);
        }
        else
        {
            Log.Error($"{AppConstants.UnhandledException}: {e.ExceptionObject}");
        }
    }

    private void UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        Log.Error(e.Exception, AppConstants.UnobservedTask);
        e.SetObserved();
    }

    private void DomainUnload(object? sender, EventArgs e)
    {
        Log.Information(AppConstants.ApplicationDomainUnload);
    }

    private void ProcessExit(object? sender, EventArgs e)
    {
        Log.Information(AppConstants.ApplicationExit);
    }
}
