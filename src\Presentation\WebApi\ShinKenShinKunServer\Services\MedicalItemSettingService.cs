﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.DispSettingDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MedicalItemSettingService の概要の説明です

    /// </summary>
    public class MedicalItemSettingService
    {
        private DispSettingService service;
        private Logger logger = null;
        public MedicalItemSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }
        public PluralRecord[] GetMedicalItemSettingAll()
        {
            try
            {
                DispSettingDataSet.MedicalItemSettingDataTable tmpTable =
                    service.GetMedicalItemSettingAll().MedicalItemSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        if (!row.IsMaxDataNull())
                        {
                            rList.Add(row.MaxData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsMinDataNull())
                        {
                            rList.Add(row.MinData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsGapDataNull())
                        {
                            rList.Add(row.GapData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add(row.CheckEmpty.ToString());
                        //rList.Add(row.InputFormatId.ToString());
                        rList.Add(row["InputFormat"].ToString());
                        rList.Add("0");
                        rList.Add("0");
                        //rList.Add(row.DispIndex.ToString());
                        //rList.Add(row.DataType.ToString());
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        if (!row.IsWarningBackColorNull())
                        {
                            rList.Add(row.WarningBackColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsWarningForeColorNull())
                        {
                            rList.Add(row.WarningForeColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMedicalItemSettingNo(
            string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.MedicalItemSettingDataTable tmpTable =
                    service.GetMedicalItemSettinNo(medicalCheckNoShr).MedicalItemSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        if (!row.IsMaxDataNull())
                        {
                            rList.Add(row.MaxData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsMinDataNull())
                        {
                            rList.Add(row.MinData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsGapDataNull())
                        {
                            rList.Add(row.GapData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add(row.CheckEmpty.ToString());
                        //rList.Add(row.InputFormatId.ToString());
                        //rList.Add(row.DispIndex.ToString());
                        //rList.Add(row.DataType.ToString());
                        rList.Add(row["InputFormat"].ToString());
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        if (!row.IsWarningBackColorNull())
                        {
                            rList.Add(row.WarningBackColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsWarningForeColorNull())
                        {
                            rList.Add(row.WarningForeColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMedicalItemSetting(
            string medicalCheckNo,
            string itemNo
            )
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                short itemNoShr = Convert.ToInt16(itemNo);
                //short dataTypeShr = System.Convert.ToInt16(dataType);
                DispSettingDataSet.MedicalItemSettingDataTable tmpTable =
                    service.GetMedicalItemSetting(medicalCheckNoShr, itemNoShr).MedicalItemSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        if (!row.IsMaxDataNull())
                        {
                            rList.Add(row.MaxData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsMinDataNull())
                        {
                            rList.Add(row.MinData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsGapDataNull())
                        {
                            rList.Add(row.GapData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add(row.CheckEmpty.ToString());
                        //rList.Add(row.InputFormatId.ToString());
                        //rList.Add(row.DispIndex.ToString());
                        //rList.Add(row.DataType.ToString());
                        rList.Add(row["InputFormat"].ToString());
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        if (!row.IsWarningBackColorNull())
                        {
                            rList.Add(row.WarningBackColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsWarningForeColorNull())
                        {
                            rList.Add(row.WarningForeColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        //public PluralRecord[] GetMedicalItemSettingDataType(
        //   string medicalCheckNo,
        //   string dataType
        //   )
        //{
        //    try
        //    {
        //        short medicalCheckNoShr = System.Convert.ToInt16(medicalCheckNo);
        //        short dataTypeShr = System.Convert.ToInt16(dataType);
        //        DispSettingDataSet.FormDispItemDataTable tmpTable =
        //            this.service.GetFormDispItemDataType(medicalCheckNoShr, dataTypeShr).MedicalItemSetting;
        //        if (tmpTable != null)
        //        {
        //            List<PluralRecord> pRecordList = new List<PluralRecord>();
        //            foreach (DispSettingDataSet.FormDispItemRow row in tmpTable)
        //            {
        //                List<string> rList = new List<string>();
        //                rList.Add(row.MedicalCheckNo.ToString());
        //                rList.Add(row.ItemNo.ToString());
        //                rList.Add(row.InputMode.ToString());
        //                rList.Add(row.MaxData);
        //                rList.Add(row.MinData);
        //                rList.Add(row.GapData);
        //                rList.Add(row.CheckEmpty.ToString());
        //                rList.Add(row.InputFormat);
        //                rList.Add(row.DataIndex.ToString());
        //                rList.Add(row.DataType.ToString());
        //                //rList.Add(row.DispIndex.ToString());
        //                //rList.Add(row.DataType.ToString());
        //                rList.Add(row.DispChange.ToString());
        //                rList.Add(row.DispFormat.ToString());
        //                rList.Add(row.WarningBackColor);
        //                rList.Add(row.WarningForeColor);
        //                rList.Add(row.DispFlg.ToString());
        //                rList.Add(row.ReadOnly.ToString());
        //                PluralRecord pRec = new PluralRecord(rList.ToArray());
        //                pRecordList.Add(pRec);
        //            }
        //            if (pRecordList.Count != 0)
        //            {
        //                return pRecordList.ToArray();
        //            }
        //        }
        //        return null;
        //    }
        //    catch (SqlException se)
        //    {
        //        throw se;
        //    }
        //    catch (FormatException fe)
        //    {
        //        throw fe;
        //    }
        //    catch (Exception exp)
        //    {
        //        this.logger = Logger.GetInstance();
        //        this.logger.logWrite(exp.ToString());
        //        throw exp;
        //    }
        //}

        public PluralRecord[] GetMedicalItemSettingMenu(string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.MedicalItemSettingDataTable tmpTable =
                    service.GetMedicalItemSettingMenu(medicalCheckNoShr).MedicalItemSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.MedicalItemSettingRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.InputMode.ToString());
                        if (!row.IsMaxDataNull())
                        {
                            rList.Add(row.MaxData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsMinDataNull())
                        {
                            rList.Add(row.MinData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsGapDataNull())
                        {
                            rList.Add(row.GapData);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add(row.CheckEmpty.ToString());
                        //rList.Add(row.InputFormatId.ToString());
                        rList.Add(row["InputFormat"].ToString());
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add(row.DispChange.ToString());
                        rList.Add(row.DispFormat.ToString());
                        if (!row.IsWarningBackColorNull())
                        {
                            rList.Add(row.WarningBackColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        if (!row.IsWarningForeColorNull())
                        {
                            rList.Add(row.WarningForeColor);
                        }
                        else
                        {
                            rList.Add(string.Empty);
                        }
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        rList.Add("0");
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public bool SetMedicalItemSettingMenu(string medicalCheckNo, List<string[]> updateValue, int updateType)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.MedicalItemSettingDataTable tmpTable =
                    service.GetMedicalItemSettingMenu(medicalCheckNoShr).MedicalItemSetting;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();

                    DispSettingDataSet.MedicalItemSettingRow row = null;
                    for (int count = 0; count < tmpTable.Count; count++)
                    {
                        row = tmpTable[count];
                        // 結果アラート設定の場合
                        if (updateType == 0)
                        {
                            row.MaxData = updateValue[count][0];
                            row.MinData = updateValue[count][1];
                        }
                        // 差分値設定の場合
                        else if (updateType == 1)
                        {
                            row.GapData = updateValue[count][0];
                        }
                        // 空白値設定の場合
                        else if (updateType == 2)
                        {
                            row.CheckEmpty = short.Parse(updateValue[count][0].ToString());
                        }
                    }
                    //健診状況の更新を行う。
                    MedicalItemSettingTableAdapter medicalItemSetTableAdapter
                        = new MedicalItemSettingTableAdapter();
                    medicalItemSetTableAdapter.Update(tmpTable);
                }
                return true;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

    }
}