<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.UI.InputControl"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <ContentView.Resources>
        <ControlTemplate
            x:Key="InputControlTemplate">
            <telerik:RadBorder
                BackgroundColor="{TemplateBinding BackgroundColor}">
                <Grid>
                    <Entry
                        Keyboard="{TemplateBinding Keyboard}"
                        MaxLength="{TemplateBinding MaxLength}"
                        Style="{x:Static ui:Styles.EntryTemplateStyle}"
                        Placeholder="{TemplateBinding Placeholder}"
                        Text="{TemplateBinding Text}" />
                </Grid>
            </telerik:RadBorder>
        </ControlTemplate>
    </ContentView.Resources>
    <telerik:RadBorder
        VerticalOptions="Fill"
        BorderThickness="{Binding BorderThickness, Source={x:Reference this}}"
        CornerRadius="{Binding CornerRadius, Source={x:Reference this}}"
        BorderColor="{Binding BorderColor, Source={x:Reference this}}">
        <telerik:RadEntry
            MaxLength="{Binding MaxLength, Source={x:Reference this}}"
            BackgroundColor="{Binding BackgroundColorInputControl, Source={x:Reference this}}"
            Style="{x:Static ui:Styles.InputRadEntryStyle}"
            ControlTemplate="{StaticResource InputControlTemplate}"
            Keyboard="{Binding Keyboard, Source={x:Reference this}}"
            TextChanged="RadEntryTextChanged"
            Unfocused="RadEntryUnfocused"
            Text="{Binding Text, Source={x:Reference this}, Mode=TwoWay}" />
    </telerik:RadBorder>
</ContentView>