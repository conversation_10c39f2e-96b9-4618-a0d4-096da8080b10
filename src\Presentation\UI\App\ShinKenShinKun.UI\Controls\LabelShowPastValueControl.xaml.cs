namespace ShinKenShinKun.UI;

public partial class LabelShowPastValueControl : Grid
{
    public LabelShowPastValueControl()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty DisplayModeProperty = BindableProperty.Create(
        nameof(DisplayMode),
        typeof(DisplayMode),
        typeof(LabelShowPastValueControl),
        DisplayMode.Default,
        BindingMode.OneWay,
        propertyChanged: OnAnyPropertyChanged);

    public static readonly BindableProperty TitleProperty = BindableProperty.Create(
        nameof(Title),
        typeof(string),
        typeof(LabelShowPastValueControl),
        string.Empty,
        BindingMode.OneWay);

    public static readonly BindableProperty ValueProperty = BindableProperty.Create(
        nameof(Value),
        typeof(string),
        typeof(LabelShowPastValueControl),
        string.Empty,
        BindingMode.OneWay,
        propertyChanged: OnAnyPropertyChanged);

    public DisplayMode DisplayMode
    {
        get => (DisplayMode)GetValue(DisplayModeProperty);
        set => SetValue(DisplayModeProperty, value);
    }

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public string Value
    {
        get => (string)GetValue(ValueProperty);
        set => SetValue(ValueProperty, value);
    }

    private static void OnAnyPropertyChanged(BindableObject bindable, object oldValue, object newValue)
    {
        if (bindable is LabelShowPastValueControl control)
        {
            control.ApplyDisplayLogic();
        }
    }

    private void ApplyDisplayLogic()
    {
        switch (DisplayMode)
        {
            case DisplayMode.Hidden:
                this.IsVisible = false;
                break;

            case DisplayMode.GrayedOut:
                this.IsVisible = true;
                this.Opacity = 0.5;
                this.IsEnabled = false;
                break;

            default:
                this.IsVisible = true;
                this.Opacity = 1.0;
                this.IsEnabled = true;
                break;
        }
    }
}