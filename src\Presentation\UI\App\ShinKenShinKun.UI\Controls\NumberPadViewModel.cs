﻿namespace ShinKenShinKun.UI;

public partial class NumberPadViewModel(
    IAppNavigator appNavigator,
    INumberPadServices numberPadServices,
    IKeyboardHandlerServices keyboardHandlerServices
) : BaseViewModel(appNavigator)
{
    public RelayCommand<string> Comfirm;

    public IKeyboardHandlerServices KeyboardHandlerServices { get; set; }

    public SettingNumberPadDto SettingNumberPad;

    [ObservableProperty]
    private string text;

    [ObservableProperty]
    private string title;

    public string InputFormat { get; set; }
    public bool IsStandard { get; set; }
    public int SettingNumberPadId { get; set; }

    public override async Task OnAppearingAsync()
    {
        await base.OnAppearingAsync();
        KeyboardHandlerServices = keyboardHandlerServices;
        SettingNumberPad = numberPadServices.GetNumberPadSettingById(SettingNumberPadId);
        IsStandard = SettingNumberPad.IsStandard;
    }

    public void StartListening()
    {
        KeyboardHandlerServices.StartListening();
    }

    public void StopListening()
    {
        KeyboardHandlerServices.StopListening();
    }
}