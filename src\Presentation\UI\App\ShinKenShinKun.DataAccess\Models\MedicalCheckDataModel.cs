﻿namespace ShinKenShinKun.DataAccess;

public partial class MedicalCheckDataModel : BaseModel
{
    [ObservableProperty] private string id;
    [ObservableProperty] private DateTime medicalCheckDate;
    [ObservableProperty] private string clientID;
    [ObservableProperty] private string division;
    [ObservableProperty] private int medicalCheckNo;
    [ObservableProperty] private int hostTransfer;
    [ObservableProperty] private int receiveOrder;
    [ObservableProperty] private string termID;
    [ObservableProperty] private DateTime startTime;
    [ObservableProperty] private DateTime endTime;
    [ObservableProperty] private double average;
    [ObservableProperty] private double data1;
    [ObservableProperty] private double data2;
    [ObservableProperty] private double data3;
    [ObservableProperty] private double data4;
    [ObservableProperty] private double data5;
}