namespace ShinKenShinKun;

public partial class ExaminationFunctionButtonView : ContentView
{
    public static readonly BindableProperty MenuButtonsProperty = BindableProperty.Create(
        nameof(MenuButtons),
        typeof(ObservableCollection<string>),
        typeof(ExaminationFunctionButtonView));

    public ExaminationFunctionButtonView()
    {
        InitializeComponent();
    }

    public ObservableCollection<string> MenuButtons
    {
        get => (ObservableCollection<string>)GetValue(MenuButtonsProperty);
        set => SetValue(MenuButtonsProperty, value);
    }
}