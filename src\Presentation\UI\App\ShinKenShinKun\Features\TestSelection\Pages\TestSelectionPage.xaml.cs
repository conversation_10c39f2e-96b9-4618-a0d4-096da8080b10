namespace ShinKenShinKun;

public partial class TestSelectionPage : BasePage
{
    public TestSelectionPage(TestSelectionPageViewModel vm)
    {
        InitializeComponent();
        BindingContext = vm;
        vm.hiddenEntryFocus += HiddenEntryFocus;
    }
    private void HiddenEntryFocus()
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            HiddenEntry?.Focus();
        });
    }
}