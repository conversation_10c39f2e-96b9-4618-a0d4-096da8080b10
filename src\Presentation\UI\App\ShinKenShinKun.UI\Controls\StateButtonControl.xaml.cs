namespace ShinKenShinKun.UI;

public partial class StateButtonControl : ContentView
{
    public static readonly BindableProperty BorderStyleProperty = BindableProperty.Create(
        nameof(BorderStyle),
        typeof(Style),
        typeof(StateButtonControl),
        default(Style),
        BindingMode.TwoWay);

    public static readonly BindableProperty CommandParameterProperty = BindableProperty.Create(
        nameof(CommandParameter),
        typeof(object),
        typeof(StateButtonControl),
        default,
        BindingMode.TwoWay
    );

    public static readonly BindableProperty CommandProperty = BindableProperty.Create(
        nameof(Command),
        typeof(ICommand),
        typeof(StateButtonControl),
        default(ICommand),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty CurrentStateProperty = BindableProperty.Create(
        nameof(CurrentState),
        typeof(ButtonState),
        typeof(StateButtonControl),
        default(ButtonState),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty StateStyleProperty = BindableProperty.Create(
        nameof(StateStyle),
        typeof(Style),
        typeof(StateButtonControl),
        default(Style),
        BindingMode.TwoWay);

    public static readonly BindableProperty StatusListProperty = BindableProperty.Create(
        nameof(StatusList),
        typeof(IList<ButtonState>),
        typeof(StateButtonControl),
        default(IList<ButtonState>),
        BindingMode.TwoWay
    );

    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        nameof(Text),
        typeof(string),
        typeof(StateButtonControl),
        string.Empty,
        BindingMode.TwoWay
    );

    public static readonly BindableProperty TextStyleProperty = BindableProperty.Create(
        nameof(TextStyle),
        typeof(Style),
        typeof(StateButtonControl),
        default(Style),
        BindingMode.TwoWay);

    public StateButtonControl()
    {
        InitializeComponent();
        var tapGesture = new TapGestureRecognizer();
        tapGesture.Tapped += OnTapped;
        GestureRecognizers.Add(tapGesture);
    }

    public Style BorderStyle
    {
        get => (Style)GetValue(BorderStyleProperty);
        set => SetValue(BorderStyleProperty, value);
    }

    public ICommand Command
    {
        get => (ICommand)GetValue(CommandProperty);
        set => SetValue(CommandProperty, value);
    }

    public object CommandParameter
    {
        get => GetValue(CommandParameterProperty);
        set => SetValue(CommandParameterProperty, value);
    }

    public ButtonState CurrentState
    {
        get => (ButtonState)GetValue(CurrentStateProperty);
        set => SetValue(CurrentStateProperty, value);
    }

    public Style StateStyle
    {
        get => (Style)GetValue(StateStyleProperty);
        set => SetValue(StateStyleProperty, value);
    }

    public IList<ButtonState> StatusList
    {
        get => (IList<ButtonState>)GetValue(StatusListProperty);
        set => SetValue(StatusListProperty, value);
    }

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    public Style TextStyle
    {
        get => (Style)GetValue(TextStyleProperty);
        set => SetValue(TextStyleProperty, value);
    }

    private void OnTapped(object sender, EventArgs e)
    {
        if (StatusList == null || StatusList.Count == 0)
            return;

        int currentIndex = StatusList.IndexOf(CurrentState);
        int nextIndex = (currentIndex + 1) % StatusList.Count;

        CurrentState = StatusList[nextIndex];

        if (Command?.CanExecute(CommandParameter) == true)
        {
            Command.Execute(CommandParameter);
        }
    }
}