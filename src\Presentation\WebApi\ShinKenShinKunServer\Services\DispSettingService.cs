﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.DispSettingDataSetTableAdapters;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// DispSettingService の概要の説明です

    /// </summary>
    public class DispSettingService
    {
        /// <summary>
        /// データセットクラス
        /// </summary>
        private DispSettingDataSet DispDataSet = new DispSettingDataSet();

        #region TableAdapterクラス

        /// <summary>
        /// ClickSwitchSetting
        /// </summary>
        //private ClickSwitchSettingTableAdapter clickSwitchTableAdapter
        //    = new ClickSwitchSettingTableAdapter();
        //private ClickSwitchDatasTableAdapter clickSwitchTableAdapter
        //    = new ClickSwitchDatasTableAdapter();
        /// <summary>
        /// TransformedSetting
        /// </summary>
        //private DataTransformedSettingTableAdapter dataTransSettingTableAdapter
        //    = new DataTransformedSettingTableAdapter();
        private DataTransformedDataTableAdapter dataTransSettingTableAdapter
        = new DataTransformedDataTableAdapter();
        /// <summary>
        /// DispChangeSetting
        /// </summary>
        //private DispChangeSetteingTableAdapter dispChangeTableAdapter
        //    = new DispChangeSetteingTableAdapter();
        private DispChangeDataTableAdapter dispChangeTableAdapter
            = new DispChangeDataTableAdapter();
        /// <summary>
        /// MedicalItemList
        /// </summary>
        private MedicalItemListTableAdapter medicalItemTableAdapter
            = new MedicalItemListTableAdapter();
        /// <summary>
        /// MedicalItemSetting
        /// </summary>
        private MedicalItemSettingTableAdapter medicalItemSetTableAdapter
            = new MedicalItemSettingTableAdapter();

        /// <summary>
        /// FormDispMode
        /// </summary>
        private FormDispModeTableAdapter dispModeTableAdapter
            = new FormDispModeTableAdapter();

        /// <summary>
        /// DispDetail
        /// </summary>
        private FormDispDetailTableAdapter dispDetailTableAdapter
            = new FormDispDetailTableAdapter();

        /// <summary>
        /// FormDispItem
        /// </summary>
        private FormDispItemTableAdapter formDispItemTableAdapter
            = new FormDispItemTableAdapter();

        /// <summary>
        /// TransformedSetting
        /// </summary>
        private TransformedSettingTableAdapter transSetTableAdapter
             = new TransformedSettingTableAdapter();

        /// <summary>
        /// PluralDispSetting
        /// </summary>
        private PluralDispSettingTableAdapter pluralDispSetTableAdapter
            = new PluralDispSettingTableAdapter();
        /// <summary>
        /// PluralSetting
        /// </summary>
        private PluralSettingTableAdapter pluralSetTableAdapter
            = new PluralSettingTableAdapter();

        /// <summary>
        /// EyeControl
        /// </summary>
        private EyeControlTableAdapter eyeControlTableAdapter
            = new EyeControlTableAdapter();
        /// <summary>
        /// EyeInitialSelect
        /// </summary>
        private EyeInitialSelectTableAdapter eyeInitialSelectTableAdapter
            = new EyeInitialSelectTableAdapter();
        /// <summary>
        /// EyeGroupSelect
        /// </summary>
        private EyeGroupSelectTableAdapter eyeGroupSelectTableAdapter
            = new EyeGroupSelectTableAdapter();

        //private ReceiveOrderSettingTableAdapter receiveOrderTableAdapter
        //    = new ReceiveOrderSettingTableAdapter();

        private MeDataRelationalTableAdapter meDataRelationalTableAdapter
            = new MeDataRelationalTableAdapter();

        /// <summary>
        /// FontSetting
        /// </summary>
        private FontSettingTableAdapter fontSettingTableAdapter
            = new FontSettingTableAdapter();

        /// <summary>
        /// CustomizeButtonSetting
        /// </summary>
        private CustomizeButtonSettingTableAdapter customizeButtonSettingTableAdapter
            = new CustomizeButtonSettingTableAdapter();

        /// <summary>
        /// CustomizeButtonDetail
        /// </summary>
        private CustomizeButtonDetailTableAdapter customizeButtonDetailTableAdapter
            = new CustomizeButtonDetailTableAdapter();

        #endregion

        public DispSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
        }

        #region ClickSwitchSetting

        #region ClickSwitchSettingテーブル全取得

        ///// <summary>
        ///// ClickSwitchSettingテーブルの全データ取得

        ///// </summary>
        ///// <returns></returns>
        //public DispSettingDataSet GetClickSwitchSettingAll()
        //{
        //    //int count = this.clickSwitchTableAdapter.Fill(this.DispDataSet.ClickSwitchSetting);
        //    int count = this.clickSwitchTableAdapter.Fill(this.DispDataSet.ClickSwitchDatas);
        //    return this.DispDataSet;
        //}
        #endregion

        #region ClickSwitchSettingテーブル取得（Key指定）

        ///// <summary>
        ///// ClickSwitchSettingテーブル取得（Key指定）

        ///// </summary>
        ///// <param name="medicalCheckNo"></param>
        ///// <param name="itemNo"></param>
        ///// <returns></returns>
        //public DispSettingDataSet GetClickSwitchSetting(short medicalCheckNo, short itemNo)
        //{
        //    //int count = this.clickSwitchTableAdapter.FillBy(this.DispDataSet.ClickSwitchSetting, medicalCheckNo, itemNo);
        //    int count = this.clickSwitchTableAdapter.FillBy(this.DispDataSet.ClickSwitchDatas, medicalCheckNo, itemNo);
        //    return this.DispDataSet;
        //}
        #endregion

        #endregion

        #region DataTranseformedSetting

        #region DataTranseformedSetteingテーブル全取得

        /// <summary>
        /// DataTranseformedSettingテーブル全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetDataTranseformedSettingAll()
        {
            //int count = this.dataTransSettingTableAdapter.Fill(this.DispDataSet.DataTransformedSetting);
            int count = dataTransSettingTableAdapter.Fill(DispDataSet.DataTransformedData);
            return DispDataSet;
        }
        #endregion

        #region DataTranseformedSettingテーブル取得(KEY指定）

        /// <summary>
        /// DataTranseformedSettingテーブル取得

        /// (MedicalCheckNo、MedicalCheckItemをKEYとする）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="medicalCheckItem"></param>
        /// <returns></returns>
        public DispSettingDataSet GetDataTransformedSetteing(short medicalCheckNo, short itemNo)
        {
            //int count =
            //    this.dataTransSettingTableAdapter.FillByItemNo(this.DispDataSet.DataTransformedSetting, medicalCheckNo, itemNo);
            int count =
                dataTransSettingTableAdapter.FillBy(DispDataSet.DataTransformedData, medicalCheckNo, itemNo);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region DispChangeSetting

        #region DispChangeSettingテーブル全取得

        /// <summary>
        /// DispChangeSettingテーブル全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetDispChangeSettingAll()
        {
            //int count = this.dispChangeTableAdapter.Fill(this.DispDataSet.DispChangeSetteing);
            int count = dispChangeTableAdapter.Fill(DispDataSet.DispChangeData);
            return DispDataSet;
        }
        #endregion

        #region DispChangeSettingテーブル取得(Key指定）

        /// <summary>
        /// DispChangeSettingテーブル取得(Key指定)
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetDispChangeSetting(short medicalCheckNo, short itemNo)
        {
            //int count = this.dispChangeTableAdapter.FillByItemNo(this.DispDataSet.DispChangeSetteing, medicalCheckNo, itemNo);
            int count = dispChangeTableAdapter.FillByItemNo(DispDataSet.DispChangeData, medicalCheckNo, itemNo);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region MedicalItemList

        #region MedicalItemListテーブル全取得

        /// <summary>
        /// MedicalItemListテーブル全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetMedicalItemListAll()
        {
            int count = medicalItemTableAdapter.Fill(DispDataSet.MedicalItemList);
            return DispDataSet;
        }
        #endregion

        #region MedicalItemListテーブル取得(Key指定）

        /// <summary>
        /// MedicalItemListテーブル取得（Key指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMedicalItemList(short medicalCheckNo, short itemNo)
        {
            int count = medicalItemTableAdapter.FillBy(DispDataSet.MedicalItemList, medicalCheckNo, itemNo);
            return DispDataSet;
        }
        #endregion

        public DispSettingDataSet GetMedicalItemListNo(short medicalCheckNo)
        {
            int count = medicalItemTableAdapter.FillByNo(DispDataSet.MedicalItemList, medicalCheckNo);
            return DispDataSet;
        }

        #endregion

        #region MedicalItemSetting

        #region MedicalItemSettingテーブル全取得

        /// <summary>
        /// MedicalItemSettingテーブル全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetMedicalItemSettingAll()
        {
            int count = medicalItemSetTableAdapter.Fill(DispDataSet.MedicalItemSetting);
            return DispDataSet;
        }
        #endregion

        #region MedicalItemSettingテーブル取得(Key指定）

        /// <summary>
        /// MedicalItemSettingテーブル取得(Key指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMedicalItemSetting(short medicalCheckNo, short itemNo)
        {
            int count = medicalItemSetTableAdapter.FillBy(DispDataSet.MedicalItemSetting, medicalCheckNo, itemNo);
            return DispDataSet;
        }
        #endregion

        public DispSettingDataSet GetMedicalItemSettinNo(short medicalCheckNo)
        {
            int count = medicalItemSetTableAdapter.FillByNo(DispDataSet.MedicalItemSetting, medicalCheckNo);
            return DispDataSet;
        }
        #endregion

        public DispSettingDataSet GetFormDispItemDataAll()
        {
            int count = formDispItemTableAdapter.Fill(DispDataSet.FormDispItem);
            return DispDataSet;
        }

        public DispSettingDataSet GetFormDispItemDataType(short pMedicalCheckNo, short dataType)
        {
            int count = formDispItemTableAdapter.FillByDataType(
                DispDataSet.FormDispItem,
                pMedicalCheckNo,
                dataType);
            return DispDataSet;
        }

        public DispSettingDataSet GetFormDispItemDataType(short formId, short itemNo, short dataType)
        {
            int count = formDispItemTableAdapter.FillDataType(
                DispDataSet.FormDispItem,
                formId,
                itemNo,
                dataType);
            return DispDataSet;
        }

        public DispSettingDataSet GetFormDispItemNo(short formId)
        {
            int count = formDispItemTableAdapter.FillBy(DispDataSet.FormDispItem, formId);
            return DispDataSet;
        }

        #region MedicalItemSettingテーブル取得(設定画面用）

        /// <summary>
        /// MedicalItemSettingテーブル取得(設定画面用）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMedicalItemSettingMenu(short medicalCheckNo)
        {
            int count = medicalItemSetTableAdapter.FillForSetting(
                DispDataSet.MedicalItemSetting, medicalCheckNo);
            return DispDataSet;
        }
        #endregion

        #region FormDispMode

        #region FormDispModeテーブルの全取得

        /// <summary>
        /// FormDispModeテーブルの全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetFormDispModeAll()
        {
            int count = dispModeTableAdapter.Fill(DispDataSet.FormDispMode);
            return DispDataSet;
        }
        #endregion

        #region FormDispModeテーブルの取得（Key指定）

        /// <summary>
        /// FormDispModeテーブルの取得（Key指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetFormDispMode(short medicalCheckNo)
        {
            int count = dispModeTableAdapter.FillBy(DispDataSet.FormDispMode, medicalCheckNo);
            return DispDataSet;
        }
        #endregion
        #endregion

        #region PluralDispSetting

        #region PluralDispSettingの全取得

        /// <summary>
        /// PluralDispSettingの全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetPluralDispSettingAll()
        {
            int count = pluralDispSetTableAdapter.Fill(DispDataSet.PluralDispSetting);
            return DispDataSet;
        }
        #endregion

        #region PluralDispSettingの取得（KEY指定）

        /// <summary>
        /// PluralDispSettingの取得（KEY指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetPluralDispSetting(short medicalCheckNo, short itemNo)
        {
            int count =
                pluralDispSetTableAdapter.FillBy(DispDataSet.PluralDispSetting, medicalCheckNo, itemNo);
            return DispDataSet;
        }
        #endregion

        public DispSettingDataSet GetPluralDispSettingNo(short medicalCheckNo)
        {
            int count = pluralDispSetTableAdapter.FillByNo(DispDataSet.PluralDispSetting, medicalCheckNo);
            return DispDataSet;
        }
        #endregion

        #region PluralSetting

        #region PluralSettingの全取得

        /// <summary>
        /// PluralSettingの全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetPluralSettingAll()
        {
            int count = pluralSetTableAdapter.Fill(DispDataSet.PluralSetting);
            return DispDataSet;
        }
        #endregion

        #region PluralSettingの取得（Key指定）

        /// <summary>
        /// PluralSettingの取得（Key指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetPluralSetting(short medicalCheckNo)
        {
            int count = pluralSetTableAdapter.FillBy(DispDataSet.PluralSetting, medicalCheckNo);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region TransformedSetting

        #region TransformedSettingの全取得

        /// <summary>
        /// TransformedSettingの全取得

        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetTransformedSettingAll()
        {
            int count = transSetTableAdapter.Fill(DispDataSet.TransformedSetting);
            return DispDataSet;
        }
        #endregion

        #region TransformedSettingの取得（Key指定）

        /// <summary>
        /// TransformedSettingの取得（Key指定）

        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetTransformedSetting(short medicalCheckNo)
        {
            int count = transSetTableAdapter.FillBy(DispDataSet.TransformedSetting, medicalCheckNo);
            return DispDataSet;
        }
        #endregion
        #endregion

        #region EyeInitialSelect

        #region EyeInitialSelectテーブル全取得
        /// <summary>
        /// EyeInitialSelectテーブル全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetEyeInitialSelectAll()
        {
            int count = eyeInitialSelectTableAdapter.Fill(DispDataSet.EyeInitialSelect);
            return DispDataSet;
        }
        #endregion

        #region EyeInitialSelectテーブル取得（MedicalCheckがKey）
        /// <summary>
        /// EyeInitialSelectテーブル取得（MedicalCheckNoがKey）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeInitialSelectMedicalNo(short formId)
        {
            int count = eyeInitialSelectTableAdapter.FillByMedicalCheckNo(DispDataSet.EyeInitialSelect, formId);
            return DispDataSet;
        }
        #endregion

        #region EyeInitialSelectテーブル取得（InitialIdがKey）
        /// <summary>
        /// EyeInitialSelectテーブル取得（InitialIdがKey）
        /// </summary>
        /// <param name="initialId"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeInitialSelectId(short initialId)
        {
            int count = eyeInitialSelectTableAdapter.FillBy(DispDataSet.EyeInitialSelect, initialId);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region EyeGroupSelect
        #region EyeGroupSelectテーブル全取得
        /// <summary>
        /// EyeGroupSelectテーブル全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetEyeGroupSelectAll()
        {
            int count = eyeGroupSelectTableAdapter.Fill(DispDataSet.EyeGroupSelect);
            return DispDataSet;
        }
        #endregion

        #region EyeGroupSelectテーブル取得
        /// <summary>
        /// EyeGroupSelectテーブル取得（MedicalCheckNoをKey）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeGroupSelectMedicalCheckNo(short formId)
        {
            int count = eyeGroupSelectTableAdapter.FillByMedicalCheckNo(DispDataSet.EyeGroupSelect, formId);
            return DispDataSet;
        }
        #endregion

        #region EyeGroupSelectテーブルの取得（GroupIdをKey）
        /// <summary>
        /// EyeGroupSelectテーブルの取得（GroupIdをKey）
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeGroupSelectId(short groupId)
        {
            int count = eyeGroupSelectTableAdapter.FillBy(DispDataSet.EyeGroupSelect, groupId);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region EyeControl

        #region EyeControlテーブルの全取得
        /// <summary>
        /// EyeControlテーブルの全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetEyeControlAll()
        {
            int count = eyeControlTableAdapter.Fill(DispDataSet.EyeControl);
            return DispDataSet;
        }
        #endregion

        #region EyeControlテーブルの取得（MedicalCheckNoをKey）
        /// <summary>
        /// EyeControlテーブルの取得（MedicalCheckNoをKey）
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeControlMedicalCheckNo(short formId)
        {
            int count = eyeControlTableAdapter.FillByMedicalCheckNo(DispDataSet.EyeControl, formId);
            return DispDataSet;
        }
        #endregion

        #region EyeControlテーブルの取得（ControlIdをKey）
        /// <summary>
        /// EyeControlテーブルの取得（ControlIdをKey）
        /// </summary>
        /// <param name="controlId"></param>
        /// <returns></returns>
        public DispSettingDataSet GetEyeControlId(short controlId)
        {
            int count = eyeControlTableAdapter.FillBy(DispDataSet.EyeControl, controlId);
            return DispDataSet;
        }
        #endregion

        //#region ReceiveOrderSettingテーブルの全取得
        ///// <summary>
        ///// ReceiveOrderSettingテーブルの全取得
        ///// </summary>
        ///// <returns></returns>
        //public DispSettingDataSet GetReceiveOrderSettingAll()
        //{
        //    int count = this.receiveOrderTableAdapter.Fill(this.DispDataSet.ReceiveOrderSetting);
        //    return this.DispDataSet;
        //}
        //#endregion

        //#region ReceiveOrderSettingNoテーブルの取得(pMedicalCheckNoをKey）
        ///// <summary>
        ///// ReceiveOrderSettingNoテーブルの取得(pMedicalCheckNoをKey）
        ///// </summary>
        ///// <param name="pMedicalCheckNo"></param>
        ///// <returns></returns>
        //public DispSettingDataSet GetReceiveOrderSettingNo(short pMedicalCheckNo)
        //{
        //    int count = this.receiveOrderTableAdapter.FillBy(this.DispDataSet.ReceiveOrderSetting, pMedicalCheckNo);
        //    return this.DispDataSet;
        //}
        //#endregion

        #endregion

        #region MeDataRelationalテーブルの全取得
        /// <summary>
        /// MeDataRelationalテーブルの全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetMeDataRelationalAll()
        {
            int count = meDataRelationalTableAdapter.Fill(DispDataSet.MeDataRelational);
            return DispDataSet;
        }
        #endregion

        #region MeDataRelationテーブル（MedicalChekcNo指定）
        /// <summary>
        /// MeDataRelationalのテーブルから、MedicalCheckNoが
        /// 一致するでーたを取得する。
        /// </summary>
        /// <param name="medicalCheckNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMeDataRelationalNo(short medicalCheckNo)
        {
            int count = meDataRelationalTableAdapter.FillBy(DispDataSet.MeDataRelational, medicalCheckNo);
            return DispDataSet;
        }
        #endregion

        #region MeDataRelationalのテーブル取得（MedicalCheccNo、ItemNo）
        /// <summary>
        /// MeDataRelationalのテーブルから、MedicalCheckNo、ItemNoが
        /// 一致するデータを取得する。
        /// </summary>
        /// <param name="medicalNo"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMeDataRelationalItemNo(short medicalNo, short itemNo)
        {
            int count = meDataRelationalTableAdapter.FillByItem(DispDataSet.MeDataRelational, medicalNo, itemNo);
            return DispDataSet;
        }
        #endregion

        #region MeDataRelationalのテーブル取得（MeItemCode)
        /// <summary>
        /// MeDataRelationalのテーブルから、MeItemCodeが一致するデータを取得する。
        /// </summary>
        /// <param name="meItemCode"></param>
        /// <returns></returns>
        public DispSettingDataSet GetMeDataRelationalMeCode(string meItemCode)
        {
            int count = meDataRelationalTableAdapter.FillByMeCode(DispDataSet.MeDataRelational, meItemCode);
            return DispDataSet;
        }
        #endregion

        #region FontSetting

        #region FontSettingテーブル全取得
        /// <summary>
        /// FontSettingテーブル全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetFontSettingAll()
        {
            int count = fontSettingTableAdapter.Fill(DispDataSet.FontSetting);
            return DispDataSet;
        }
        #endregion

        #region FontSettingテーブル取得（Key:FormId）
        /// <summary>
        /// FontSettingテーブル取得（Key:FormId）
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetFontSettingFormId(short formId)
        {
            int count = fontSettingTableAdapter.FillBy(DispDataSet.FontSetting, formId);
            return DispDataSet;
        }
        #endregion

        #region FontSettingテーブル取得（Key:FormId & Label）
        /// <summary>
        /// FontSettingテーブル取得（Key:FormId & Label）
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetFontSettingFormLabel(short formId, short label)
        {
            int count = fontSettingTableAdapter.FillBy1(DispDataSet.FontSetting, formId, label);
            return DispDataSet;
        }
        #endregion

        #endregion

        #region CustomizeButtonSetting
        /// <summary>
        /// CustomizeButtonSettingテーブル全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetCustomizeButtonSettingAll()
        {
            int count = customizeButtonSettingTableAdapter.Fill(DispDataSet.CustomizeButtonSetting);
            return DispDataSet;
        }
        #endregion

        #region CustomizeButtonDetail
        /// <summary>
        /// GetCustomizeButtonDetailテーブル全取得
        /// </summary>
        /// <returns></returns>
        public DispSettingDataSet GetCustomizeButtonDetailAll()
        {
            int count = customizeButtonDetailTableAdapter.Fill(DispDataSet.CustomizeButtonDetail);
            return DispDataSet;
        }
        /// <summary>
        /// GetCustomizeButtonDetailテーブル取得（Key:FormID）
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        public DispSettingDataSet GetCustomizeButtonDetailbyFormId(short formId)
        {
            int count = customizeButtonDetailTableAdapter.FillBy(DispDataSet.CustomizeButtonDetail, formId);
            return DispDataSet;
        }
        #endregion
    }
}