﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// DispChangeSettingService の概要の説明です

    /// </summary>
    public class DispChangeSettingService
    {
        private DispSettingService service;
        private Logger logger = null;
        public DispChangeSettingService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetDispChangeSettingAll()
        {
            try
            {
                //DispSettingDataSet.DispChangeSetteingDataTable tmpTable
                //= this.service.GetDispChangeSettingAll().DispChangeSetteing;
                DispSettingDataSet.DispChangeDataDataTable tmpTable
                = service.GetDispChangeSettingAll().DispChangeData;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.DispChangeDataRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispChangeId.ToString());
                        rList.Add(row.BeforeData);
                        rList.Add(row.AfterData);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }

                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetdispChangeSetting(
            string medicalCheckNo,
            string itemNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                short itemNoSht = Convert.ToInt16(itemNo);
                //DispSettingDataSet.DispChangeSetteingDataTable tmpTable
                //   = this.service.GetDispChangeSetting(medicalCheckNoSht, itemNoSht).DispChangeSetteing;
                DispSettingDataSet.DispChangeDataDataTable tmpTable
                   = service.GetDispChangeSetting(medicalCheckNoSht, itemNoSht).DispChangeData;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    //foreach (DispSettingDataSet.DispChangeSetteingRow row in tmpTable)
                    foreach (DispSettingDataSet.DispChangeDataRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.DispChangeId.ToString());
                        rList.Add(row.BeforeData);
                        rList.Add(row.AfterData);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }

                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}