namespace ShinKenShinKun;
public partial class IndividualSectionView : ContentView
{
    public IndividualSectionView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty SectionNameProperty = BindableProperty.Create(
        nameof(SectionName),
        typeof(string),
        typeof(IndividualSectionView),
        "",
        BindingMode.TwoWay);

    public string SectionName
    {
        get => (string)GetValue(SectionNameProperty);
        set => SetValue(SectionNameProperty, value);
    }

    public static readonly BindableProperty SpanCountProperty = BindableProperty.Create(
        nameof(SpanCount),
        typeof(int),
        typeof(IndividualSectionView),
        3,
        BindingMode.TwoWay);

    public int SpanCount
    {
        get => (int)GetValue(SpanCountProperty);
        set => SetValue(SpanCountProperty, value);
    }

    public static readonly BindableProperty ItemSourceProperty = BindableProperty.Create(
        nameof(ItemSource),
        typeof(ObservableCollection<IndividualPatientModel>),
        typeof(IndividualSectionView),
        new ObservableCollection<IndividualPatientModel>(),
        BindingMode.TwoWay);

    public ObservableCollection<IndividualPatientModel> ItemSource
    {
        get => (ObservableCollection<IndividualPatientModel>)GetValue(ItemSourceProperty);
        set => SetValue(ItemSourceProperty, value);
    }
}