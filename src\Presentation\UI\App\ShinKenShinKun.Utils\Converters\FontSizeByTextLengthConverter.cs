﻿namespace ShinKenShinKun.Utils;

public class FontSizeByTextLengthConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        string result = value.ToString();
        if (result.Length > 8)
        {
            return 15;
        }
        return 23;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}