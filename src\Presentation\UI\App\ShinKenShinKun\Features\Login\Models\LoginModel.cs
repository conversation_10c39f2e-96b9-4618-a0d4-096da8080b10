﻿namespace ShinKenShinKun;

public partial class LoginModel : BaseFormModel
{
    [ObservableProperty]
    [Required]
    [NotifyPropertyChangedFor(nameof(LoginIdErrors))]
    [NotifyDataErrorInfo]
    string loginId;

    [ObservableProperty]
    [Required]
    [NotifyPropertyChangedFor(nameof(PasswordErrors))]
    [NotifyDataErrorInfo]
    string password;

    public IEnumerable<ValidationResult> LoginIdErrors => GetErrors(nameof(LoginId));
    public IEnumerable<ValidationResult> PasswordErrors => GetErrors(nameof(Password));


    protected override string[] ValidatableAndSupportPropertyNames =>
    [
        nameof(LoginId),
        nameof(LoginIdErrors),
        nameof(Password),
        nameof(PasswordErrors)
    ];

    public void Clear()
    {
        LoginId = string.Empty;
        Password = string.Empty;
        ClearErrors();
    }
}