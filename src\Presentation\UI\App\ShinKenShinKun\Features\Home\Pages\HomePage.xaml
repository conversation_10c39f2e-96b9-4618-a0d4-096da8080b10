<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.HomePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:mtk="clr-namespace:MemoryToolkit.Maui;assembly=MemoryToolkit.Maui"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:vm="clr-namespace:ShinKenShinKun"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:Name="this"
    Title="HomePage"
    x:DataType="vm:HomePageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid
        RowDefinitions="80,*,auto"
        BackgroundColor="{Static ui:AppColors.Platinum}"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">
        <!--Hidden entry-->
        <Entry
            HorizontalOptions="End"
            x:Name="HiddenEntry"
            Opacity="0"
            HeightRequest="0"
            WidthRequest="0" />
        
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.HomeTitle}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <telerik:RadCollectionView
            x:Name="ColView"
            Grid.Row="1"
            Margin="{x:Static ui:Dimens.Spacing20}"
            ItemsSource="{Binding Menus}"
            SelectionMode="None">
            <telerik:RadCollectionView.ItemsLayout>
                <telerik:CollectionViewGridLayout
                    HorizontalItemSpacing="{x:Static ui:Dimens.Spacing20}"
                    Orientation="Vertical"
                    SpanCount="3"
                    VerticalItemSpacing="{x:Static ui:Dimens.Spacing10}" />
            </telerik:RadCollectionView.ItemsLayout>
            <telerik:RadCollectionView.ItemTemplate>
                <DataTemplate>
                    <telerik:RadButton
                        x:DataType="vm:MenuItem"
                        Command="{Binding SelectButtonCommand, Source={RelativeSource AncestorType={x:Type vm:HomePageViewModel}}}"
                        CommandParameter="{Binding Id}"
                        ContentLayout="Top,15"
                        FontSize="{x:Static ui:Dimens.FontSize50}"
                        ImageSource="{Binding IconSource}"
                        Style="{x:Static ui:Styles.MenuItemButtonStyle}"
                        Text="{Binding Text}">
                        <telerik:RadButton.Triggers>
                            <DataTrigger
                                Binding="{Binding IsSelected}"
                                TargetType="telerik:RadButton"
                                Value="False">
                                <Setter Property="BackgroundColor" Value="{x:Static ui:AppColors.TightBlue}" />
                                <Setter Property="TextColor" Value="{x:Static ui:AppColors.MediumLightBlue}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding IsSelected}"
                                TargetType="telerik:RadButton"
                                Value="True">
                                <Setter Property="BackgroundColor" Value="{x:Static ui:AppColors.DarkBlue}" />
                                <Setter Property="TextColor" Value="{x:Static ui:AppColors.OnSecondary}" />
                            </DataTrigger>
                        </telerik:RadButton.Triggers>
                    </telerik:RadButton>
                </DataTemplate>
            </telerik:RadCollectionView.ItemTemplate>
        </telerik:RadCollectionView>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            ClientSelected="{Binding ClientSelected}"
            LogoutCommand="{Binding OpenLogoutPopupCommand}"
            FinishCommand="{Binding OpenQuitAppPopupCommand}" />
    </Grid>
</mvvm:BasePage>