<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup
    xmlns:rg="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.NoteEditPopup"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    CanBeDismissedByTappingOutsideOfPopup="False"
    x:Name="this">
    <!-- Popup backdrop -->
    <telerik:RadBorder
        x:Name="MainGrid"
        BackgroundColor="{x:Static ui:AppColors.BackgroundColorPopup}">

        <telerik:RadBorder.GestureRecognizers>
            <TapGestureRecognizer
                Tapped="OnOutsideTap" />
        </telerik:RadBorder.GestureRecognizers>

        <!-- Popup body -->

        <telerik:RadBorder
            WidthRequest="{ui:DeviceEdge 
                 Edge=Width,
                 Ratio=0.7}"
            HeightRequest="{ui:DeviceEdge 
                 Edge=Height,
                 Ratio=0.7}"
            Padding="{ui:EdgeInsets 
                 Top={x:Static ui:Dimens.SpacingXl},
                 Horizontal={x:Static ui:Dimens.SpacingXl},
                 Bottom={x:Static ui:Dimens.SpacingMd}}"
            Style="{x:Static ui:Styles.NoteEditDialogBorderStyle}">
            <telerik:RadBorder.GestureRecognizers>
                <TapGestureRecognizer
                    Tapped="OnInsideTap" />
            </telerik:RadBorder.GestureRecognizers>
            <Grid
                ColumnDefinitions="3*, 2*"
                RowSpacing="{x:Static ui:Dimens.SpacingMd}"
                ColumnSpacing="{x:Static ui:Dimens.SpacingMd}"
                RowDefinitions="*, auto">
                <telerik:RadBorder
                    Grid.Row="0"
                    Grid.Column="0"
                    BackgroundColor="{x:Static ui:AppColors.LightGrayishWhite}"
                    Padding="{ui:EdgeInsets 
             Horizontal={x:Static ui:Dimens.SpacingS},
             Vertical={x:Static ui:Dimens.SpacingSm}}">
                    <ScrollView
                        x:Name="NoteScroll"
                        Orientation="Vertical">
                        <VerticalStackLayout
                            Spacing="{x:Static ui:Dimens.Spacing2}"
                            BindableLayout.ItemsSource="{Binding ClientNotes, 
                     Source={x:Reference this}}">
                            <BindableLayout.ItemTemplate>
                                <DataTemplate
                                    x:DataType="app:NoteModel">
                                    <telerik:RadBorder
                                        Padding="{ui:EdgeInsets 
                                 Vertical={x:Static ui:Dimens.SpacingXs},
                                 Horizontal={x:Static ui:Dimens.SpacingS}}"
                                        Style="{x:Static ui:Styles.PatientNotesBorderStyle}">
                                        <Grid
                                            ColumnSpacing="{x:Static ui:Dimens.SpacingS}"
                                            ColumnDefinitions="92, *, auto">
                                            <Label
                                                Grid.Column="0"
                                                x:Name="NoteIndex"
                                                Style="{x:Static ui:Styles.OrderNoteLabelStyle}"
                                                Text="{Binding Index, 
                                         StringFormat={x:Static resources:AppResources.OrderNote}}" />
                                            <Label
                                                Grid.Column="1"
                                                Style="{x:Static ui:Styles.NoteLabelStyle}"
                                                Padding="{ui:EdgeInsets 
                                         Horizontal={x:Static ui:Dimens.SpacingMd}}"
                                                Text="{Binding DisplayName}" />
                                            <telerik:RadBorder
                                                Grid.Column="2"
                                                Style="{x:Static ui:Styles.CloseNoteBorderStyle}">
                                                <Image
                                                    Aspect="AspectFit"
                                                    Source="{x:Static ui:Icons.CloseIcon}" />
                                                <telerik:RadBorder.GestureRecognizers>
                                                    <TapGestureRecognizer
                                                        Tapped="RemoveNote"
                                                        CommandParameter="{Binding .}" />
                                                </telerik:RadBorder.GestureRecognizers>
                                            </telerik:RadBorder>
                                        </Grid>
                                    </telerik:RadBorder>
                                </DataTemplate>
                            </BindableLayout.ItemTemplate>
                        </VerticalStackLayout>
                    </ScrollView>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Grid.Row="0"
                    Grid.Column="1"
                    Padding="{ui:EdgeInsets 
             Horizontal={x:Static ui:Dimens.SpacingS},
             Vertical={x:Static ui:Dimens.SpacingSm}}"
                    BackgroundColor="{x:Static ui:AppColors.LightGrayish}">
                    <ScrollView>
                        <VerticalStackLayout
                            x:Name="NoteMasterLayout"
                            BindableLayout.ItemsSource="{Binding NoteMasters, Source={x:Reference this}}">

                            <BindableLayout.ItemTemplate>
                                <DataTemplate
                                    x:DataType="app:NoteMasterModel">
                                    <Label
                                        Text="{Binding DisplayName}"
                                        TextColor="{Binding TextColor}"
                                        Background="{Binding BackgroundColor}"
                                        Style="{x:Static ui:Styles.NoteMasterLabelStyle}">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer
                                                Tapped="NoteMastersSelectionChanged" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </DataTemplate>
                            </BindableLayout.ItemTemplate>
                        </VerticalStackLayout>
                    </ScrollView>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Grid.Row="1"
                    Grid.Column="1"
                    HorizontalOptions="End"
                    Style="{x:Static ui:Styles.AddNoteBorderStyle}">
                    <VerticalStackLayout
                        Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing2}}"
                        VerticalOptions="End">
                        <Image
                            Aspect="Fill"
                            HorizontalOptions="Center"
                            HeightRequest="{x:Static ui:Dimens.Height28}"
                            WidthRequest="{x:Static ui:Dimens.Width28}"
                            Source="{x:Static ui:Icons.AddIcon}" />
                        <Label
                            Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                            FontSize="{x:Static ui:Dimens.FontSizeT5}"
                            HorizontalTextAlignment="Center"
                            Text="{x:Static resources:AppResources.Add}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Tapped="AddToNote" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
                <telerik:RadBorder
                    Grid.Row="1"
                    Grid.Column="0"
                    HorizontalOptions="Start"
                    Style="{x:Static ui:Styles.CloseEditNotePopupBorderStyle}">
                    <VerticalStackLayout
                        Padding="{ui:EdgeInsets All={x:Static ui:Dimens.Spacing2}}"
                        VerticalOptions="End">
                        <Image
                            Aspect="Fill"
                            HorizontalOptions="Center"
                            HeightRequest="{x:Static ui:Dimens.Height28}"
                            WidthRequest="{x:Static ui:Dimens.Width28}"
                            Source="{x:Static ui:Icons.BackDoorIcon}" />
                        <Label
                            Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                            FontSize="{x:Static ui:Dimens.FontSizeT5}"
                            HorizontalTextAlignment="Center"
                            Text="{x:Static resources:AppResources.Close}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Tapped="CloseNoteEditPopup" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
            </Grid>
        </telerik:RadBorder>
    </telerik:RadBorder>


</toolkit:Popup>
