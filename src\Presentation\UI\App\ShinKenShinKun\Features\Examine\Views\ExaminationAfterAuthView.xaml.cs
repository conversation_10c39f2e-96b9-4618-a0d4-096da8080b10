namespace ShinKenShinKun;

public partial class ExaminationAfterAuthView : ContentView
{
    public static readonly BindableProperty PatientBodyInfoProperty = BindableProperty.Create(
        nameof(PatientBodyInfo),
        typeof(ObservableCollection<ExamineAfterAuthModel>),
        typeof(ExaminationAfterAuthView),
        default(ObservableCollection<ExamineAfterAuthModel>),
        BindingMode.TwoWay);

    public ExaminationAfterAuthView()
    {
        InitializeComponent();
    }

    public ObservableCollection<ExamineAfterAuthModel> PatientBodyInfo
    {
        get => (ObservableCollection<ExamineAfterAuthModel>)GetValue(PatientBodyInfoProperty);
        set => SetValue(PatientBodyInfoProperty, value);
    }
}