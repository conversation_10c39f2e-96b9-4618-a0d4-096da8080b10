{"ExtendedData": {"inputs": ["http://localhost:8999/PublicService.asmx"], "collectionTypes": ["System.Array", "System.Collections.Generic.Dictionary`2"], "namespaceMappings": ["*, System.Arctec.Ar1000k.DataBase.ArctecServer"], "references": ["Microsoft.Bcl.TimeProvider, {Microsoft.Bcl.TimeProvider, 8.0.1}", "Microsoft.Extensions.ObjectPool, {Microsoft.Extensions.ObjectPool, 8.0.10}", "Microsoft.IdentityModel.Abstractions, {Microsoft.IdentityModel.Abstractions, 8.2.0}", "Microsoft.IdentityModel.Logging, {Microsoft.IdentityModel.Logging, 8.2.0}", "Microsoft.IdentityModel.Protocols.WsTrust, {Microsoft.IdentityModel.Protocols.WsTrust, 6.8.0}", "Microsoft.IdentityModel.Tokens, {Microsoft.IdentityModel.Tokens, 8.2.0}", "Microsoft.IdentityModel.Tokens.Saml, {Microsoft.IdentityModel.Tokens.Saml, 8.2.0}", "Microsoft.IdentityModel.Xml, {Microsoft.IdentityModel.Xml, 8.2.0}", "System.IO, {System.IO, 4.3.0}", "System.Runtime, {System.Runtime, 4.3.1}", "System.Security.Cryptography.Pkcs, {System.Security.Cryptography.Pkcs, 8.0.1}", "System.Security.Cryptography.Xml, {System.Security.Cryptography.Xml, 8.0.2}", "System.ServiceModel, {System.ServiceModel.Primitives, 8.1.2}", "System.ServiceModel.Duplex, {System.ServiceModel.Duplex, 6.0.0}", "System.ServiceModel.Federation, {System.ServiceModel.Federation, 8.1.2}", "System.ServiceModel.Http, {System.ServiceModel.Http, 8.1.2}", "System.ServiceModel.NetFramingBase, {System.ServiceModel.NetFramingBase, 8.1.2}", "System.ServiceModel.NetTcp, {System.ServiceModel.NetTcp, 8.1.2}", "System.ServiceModel.Primitives, {System.ServiceModel.Primitives, 8.1.2}", "System.ServiceModel.Security, {System.ServiceModel.Primitives, 8.1.2}", "System.Text.Encoding, {System.Text.Encoding, 4.3.0}", "System.Text.RegularExpressions, {System.Text.RegularExpressions, 4.3.1}", "System.Threading.Tasks, {System.Threading.Tasks, 4.3.0}", "System.Xml.ReaderWriter, {System.Xml.ReaderWriter, 4.3.0}", "System.Xml.XmlDocument, {System.Xml.XmlDocument, 4.3.0}"], "sync": true, "targetFramework": "net9.0", "typeReuseMode": "All"}}