﻿namespace ShinKenShinKun;

public static class BuildConfiguration
{
    public static MauiAppBuilder BuildConfig(this MauiAppBuilder builder)
    {
        try
        {
            builder.Configuration.AddConfiguration(
                new ConfigurationBuilder()
                    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                    .Build()
            );
        }
        catch(Exception ex)
        {
            Log.Error(ex, AppResources.Error);
        }

        return builder;
    }
}
