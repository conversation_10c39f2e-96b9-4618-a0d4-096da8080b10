﻿
namespace ShinKenShinKun;

public class GuidanceAdjustmentService(
    IFileSystemService fileSystemService,
    IMapper mapper) : IGuidanceAdjustmentService
{
    public ObservableCollection<ClientInformationModel> LoadDataClientInformations()
    {
        var data = fileSystemService.ReadClientExcelFile()
            .OrderBy(c => Int32.Parse(c.ClientId));

        if(data == null || !data.Any())
            return [];
        return mapper.Map<ObservableCollection<ClientInformationModel>>(data);
    }

    public ObservableCollection<ClientInformationModel> ClientInformationByClientId(
      string clientId, MedicalCheckModel medicalCheck)
    {
        return ReadClientInformations(clientId, medicalCheck);
    }

    public ObservableCollection<ClientInformationModel> ClientInformationByMedicalCheck(
        string clientId, MedicalCheckModel medicalCheck)
    {
        return ReadClientInformations(clientId, medicalCheck);
    }

    public ObservableCollection<ClientInformationModel> ReadClientInformations(string clientId, MedicalCheckModel medicalCheck)
    {
        var data = fileSystemService.ReadClientExcelFile()
           .Where(c => string.IsNullOrEmpty(clientId) || ulong.Parse(c.ClientId) == ulong.Parse(clientId))
           .Where(c => string.IsNullOrEmpty(medicalCheck?.Value) || c.GuideState == medicalCheck.Value)
           .OrderBy(c => ulong.Parse(c.ClientId));

        if(data == null || !data.Any())
            return [];
        return mapper.Map<ObservableCollection<ClientInformationModel>>(data);
    }
    public ObservableCollection<MedicalCheckModel> GetMedicalCheckDivisions()
    {
        var data = AppConstants.MedicalCheckNames
            .Select(x => new MedicalCheckModel { DisplayName = x, Value = x })
            .Prepend(new MedicalCheckModel { DisplayName = AppResources.Entire });
        return data.ToObservableCollection();
    }

}