﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// FormModeDispService の概要の説明です

    /// </summary>
    public class FormModeDispService
    {
        private DispSettingService service;
        private Logger logger = null;

        public FormModeDispService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetFormDispModeAll()
        {
            try
            {
                DispSettingDataSet.FormDispModeDataTable tmpTable =
                    service.GetFormDispModeAll().FormDispMode;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispModeRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.DispMode.ToString());
                        rList.Add(row.PluralMode.ToString());
                        rList.Add(row.FormId.ToString());
                        rList.Add(row.InitialSelectFlg.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetFormDispMode(
            string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                DispSettingDataSet.FormDispModeDataTable tmpTable =
                    service.GetFormDispMode(medicalCheckNoSht).FormDispMode;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (DispSettingDataSet.FormDispModeRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.DispMode.ToString());
                        rList.Add(row.PluralMode.ToString());
                        rList.Add(row.FormId.ToString());
                        rList.Add(row.InitialSelectFlg.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}