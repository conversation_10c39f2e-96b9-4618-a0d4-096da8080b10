<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePage
    x:Class="ShinKenShinKun.TestSelectionPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    Title="TestSelectionPage"
    x:DataType="app:TestSelectionPageViewModel">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsEnabled="False"
            IsVisible="False" />
    </Shell.BackButtonBehavior>
    <Grid
        RowDefinitions="80,*,auto"
        BackgroundColor="{Static ui:AppColors.Platinum}">
        <!--Hidden entry-->
        <Entry
            HorizontalOptions="End"
            x:Name="HiddenEntry"
            Opacity="0"
            HeightRequest="0"
            WidthRequest="0" />

        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.TestSelectionPageHeader}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <ScrollView
            Grid.Row="1"
            Margin="{x:Static ui:Dimens.Spacing20}">
            <FlexLayout
                AlignContent="Center"
                AlignItems="Center"
                BindableLayout.ItemsSource="{Binding MedicalChecklists}"
                Direction="Row"
                JustifyContent="Start"
                Wrap="Wrap">
                <BindableLayout.ItemTemplate>
                    <DataTemplate x:DataType="app:MedicalChecklist">
                        <telerik:RadButton
                            Command="{Binding GotoTestCommand, Source={RelativeSource AncestorType={Type app:TestSelectionPageViewModel}}}"
                            CommandParameter="{Binding .}"
                            HeightRequest="{Binding Source={RelativeSource Self}, Path=FontSize, Converter={Static ui:AppConverters.SizeMultipleConverter}, ConverterParameter=3.5}"
                            Style="{Static ui:Styles.MedicalChecklistRadButtonStyle}"
                            Text="{Binding Name}">
                            <telerik:RadButton.Triggers>
                                <DataTrigger
                                    Binding="{Binding IsSelected}"
                                    TargetType="telerik:RadButton"
                                    Value="False">
                                    <Setter Property="BackgroundColor" Value="{Static ui:AppColors.PaleCerulean}" />
                                    <Setter Property="TextColor" Value="{Static ui:AppColors.HanBlue}" />
                                </DataTrigger>
                                <DataTrigger
                                    Binding="{Binding IsSelected}"
                                    TargetType="telerik:RadButton"
                                    Value="True">
                                    <Setter Property="BackgroundColor" Value="{Static ui:AppColors.DarkBlue}" />
                                    <Setter Property="TextColor" Value="{Static ui:AppColors.White}" />
                                </DataTrigger>
                            </telerik:RadButton.Triggers>
                        </telerik:RadButton>
                    </DataTemplate>
                </BindableLayout.ItemTemplate>
            </FlexLayout>
        </ScrollView>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            ClientSelected="{Binding ClientSelected}"
            BackCommand="{Binding BackCommand}" />
    </Grid>
</mvvm:BasePage>