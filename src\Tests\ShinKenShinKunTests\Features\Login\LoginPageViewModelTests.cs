﻿namespace ShinKenShinKunTests.Features.Login;

public class LoginPageViewModelTests : UnitTestBase<LoginPageViewModel>
{
    [Fact]
    public void ChangeMode_ShouldToggleIsOnlineMode_WhenInvoked()
    {
        // Arrange
        var initialMode = Sut.IsOnlineMode;

        // Act
        Sut.ChangeModeCommand.Execute(null);

        // Assert
        Assert.NotEqual(initialMode, Sut.IsOnlineMode);
    }

    [Fact]
    public async Task GoBack_ShouldGoBackScreen_WhenInvoked()
    {
        // Arrange && Act
        await Sut.BackCommand.ExecuteAsync(null);

        // Assert
        Mocker.GetMock<IAppNavigator>().Verify(x => x.GoBackAsync(It.IsAny<bool>(), It.IsAny<object>()), Times.Once);
    }
}