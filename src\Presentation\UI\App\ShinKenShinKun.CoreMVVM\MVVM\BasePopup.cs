﻿using Popup = CommunityToolkit.Maui.Views.Popup;

namespace ShinKenShinKun.CoreMVVM;

public class BasePopup : Popup
{
    public BasePopup()
    {
        Opened += OnAppearing;
        Closed += OnDisappearing;
    }

    protected virtual void OnAppearing(object? sender, PopupOpenedEventArgs e)
    {
        if (BindingContext is BaseViewModel vm)
        {
            vm.OnAppearingAsync().FireAndForget();
        }
    }

    protected virtual void OnDisappearing(object? sender, PopupClosedEventArgs e)
    {
        if (BindingContext is BaseViewModel vm)
        {
            vm.OnDisappearingAsync().FireAndForget();
        }
    }
}