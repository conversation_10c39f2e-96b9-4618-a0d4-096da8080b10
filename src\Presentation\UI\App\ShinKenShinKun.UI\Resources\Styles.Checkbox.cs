﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style CheckboxStyle => CreateStyle<CheckBox>()
        .Set(CheckBox.ColorProperty, Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.White : AppColors.Blue)
        .Set(CheckBox.MinimumHeightRequestProperty, 44)
        .Set(CheckBox.MinimumWidthRequestProperty, 44)
        .Set(CheckBox.BackgroundColorProperty, Colors.Transparent);
}
