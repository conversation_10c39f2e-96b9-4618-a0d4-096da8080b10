namespace ShinKenShinKun;

public partial class BulkHoldPatientsButtonView : RadBorder
{
    public BulkHoldPatientsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(BulkHoldPatientsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkHoldPatientsCommandProperty);
        set => SetValue(BulkHoldPatientsCommandProperty, value);
    }
}