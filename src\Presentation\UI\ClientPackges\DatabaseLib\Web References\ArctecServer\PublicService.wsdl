<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://localhost/AR1000Server/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://localhost/AR1000Server/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://localhost/AR1000Server/">
      <s:element name="StrSetNextGuideDataHoldTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="dateTime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="clientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="division" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="childGroupFlg" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNextGuideDataHoldTimeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetNextGuideDataHoldTimeResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateOnly">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateOnlyResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetStateOnlyResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateOnlyPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="checkState" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="StrSetStateOnlyPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetStateOnlyPluralResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateTimeToNULL">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateTimeToNULLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetStateTimeToNULLResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallClientDataAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCallClientDataAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallClientDataAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfPluralRecord">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="PluralRecord" nillable="true" type="tns:PluralRecord" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="PluralRecord">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Data" type="tns:ArrayOfString" />
        </s:sequence>
      </s:complexType>
      <s:element name="StrGetCallClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallClientDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="iCallOrder" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="iStatus" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetCallClientDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteCallClientDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallExamMasterAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCallExamMasterAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallExamMasterAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallExamMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallExamMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallExamMasterResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallExamMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="sTitle" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="bStatus" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="sStartTime" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sEndTime" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iCapacity" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="iBackColor" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="sMessage" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sHeader1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sHeader2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="iMedicalCheckNos" type="tns:ArrayOfInt" />
            <s:element minOccurs="1" maxOccurs="1" name="iPanelType" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="Param1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Param2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Param3" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfInt">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="int" type="s:int" />
        </s:sequence>
      </s:complexType>
      <s:element name="StrSetCallExamMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetCallExamMasterResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallExamMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallExamMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteCallExamMasterResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallStatusAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCallStatusAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallStatusAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="iMaxWaitTime" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetCallStatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iExamId" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteCallStatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallTerminalMasterAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCallTerminalMasterAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallTerminalMasterAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallTerminalMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sTermID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCallTerminalMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCallTerminalMasterResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallTerminalMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sTermID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sIPAddress" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iIntervalTime" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="iExamIds" type="tns:ArrayOfInt" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetCallTerminalMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetCallTerminalMasterResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallTerminalMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sTermID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteCallTerminalMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteCallTerminalMasterResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSoundSettingPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSoundSettingPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSoundSettingPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSoundSettingOne">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="patternId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetSoundSettingOneResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetSoundSettingOneResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSoundSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="patternId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="soundFileName" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="repetitionCount" nillable="true" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="snoozeTime" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="volume" nillable="true" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SetSoundSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SetSoundSettingResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteSoundSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="patternId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteSoundSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteSoundSettingResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fileName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="msg" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="bOverWrite" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFCreateServerFileResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFileForEncode">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fileName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="msg" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="bOverWrite" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="encode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFileForEncodeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFCreateServerFileForEncodeResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileExist">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileExistResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFGetFileExistResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetDirectoryExist">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetDirectoryExistResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFGetDirectoryExistResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileContent">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileContentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SFGetFileContentResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileCreateDate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileCreateDateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFGetFileCreateDateResult" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileUpdateDate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileUpdateDateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFGetFileUpdateDateResult" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetFileListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SFGetFileListResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetDirectoryList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetDirectoryListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SFGetDirectoryListResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFDeleteFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sFileName" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="bRestrictFlag" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFDeleteFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SFDeleteFileResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetConditionCsvFile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sPatternName" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="colIndex" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="conditionStr" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFGetConditionCsvFileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SFGetConditionCsvFileResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFolder">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sServerPath" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SFCreateServerFolderResponse">
        <s:complexType />
      </s:element>
      <s:element name="StrStartUpdateStateTimeMore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateStateTimeMoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateStateTimeMoreResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNoCustom">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNoCustomResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataPluralCheckNoCustomResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNoTable">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sql" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNoTableResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataPluralCheckNoTableResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iMedicalCheckNo" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeMemberListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberListNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberListNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeMemberListNoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberListAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetConciergeMemberListAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeMemberListAllResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeMemberListPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetConciergeMemberListPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeMemberListPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iMedicalCheckNo" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeTimeGroupListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeTimeGroupListNoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeTimeGroupListAllResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetConciergeTimeGroupListPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeTimeGroupListPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClickSwitchSettingTableAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetClickSwitchSettingTableAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClickSwitchSettingTableAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClickSwitchSettingTable">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClickSwitchSettingTableResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClickSwitchSettingTableResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataTransformedSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetDataTransformedSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataTransformedSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataTransformedSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataTransformedSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataTransformedSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDispChangeSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetDispChangeSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDispChangeSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDispChangeSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDispChangeSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDispChangeSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemListAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMedicalItemListAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemListAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemListResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemListNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemListNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemListNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMedicalItemSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemSettingNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dataType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingDataType">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dataType" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingDataTypeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemSettingDataTypeResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingMenu">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalItemSettingMenuResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalItemSettingMenuResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetMedicalItemSettingMenu">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="updateValue" type="tns:ArrayOfArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="updateType" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="ArrayOfString" nillable="true" type="tns:ArrayOfString" />
        </s:sequence>
      </s:complexType>
      <s:element name="StrSetMedicalItemSettingMenuResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetMedicalItemSettingMenuResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFormModeDispAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetFormModeDispAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetFormModeDispAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFormModeDisp">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFormModeDispResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetFormModeDispResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralDispSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetPluralDispSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPluralDispSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralDispSettingNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralDispSettingNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPluralDispSettingNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralDispSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralDispSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPluralDispSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetPluralSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPluralSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPluralSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPluralSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTransformedSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetTransformedSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTransformedSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTransformedSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTransformedSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTransformedSettingResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRemarksData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRemarksDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientRemarksDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientRemarksInformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="commentData" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientRemarksInformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetClientRemarksInformationResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetRemarksItemListAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetRemarksItemListAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetRemarksItemListAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeGroupSelectAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetEyeGroupSelectAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeGroupSelectAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeGroupSelectId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="groupId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeGroupSelectIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeGroupSelectIdResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeGroupSelectMedicalCheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeGroupSelectMedicalCheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeGroupSelectMedicalCheckNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeControlAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetEyeControlAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeControlAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeControlId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="controlId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeControlIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeControlIdResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeControlMedicalcheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeControlMedicalcheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeControlMedicalcheckNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeInitialSelectAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetEyeInitialSelectAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeInitialSelectAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeInitialSelectId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="initialId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeInitialSelectIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeInitialSelectIdResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeInitialSelectMedicalCheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetEyeInitialSelectMedicalCheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetEyeInitialSelectMedicalCheckNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMeDataRelationalAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeDataRelationalAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeDataRelationalNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalItemNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalItemNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeDataRelationalItemNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalMeCode">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="meCode" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeDataRelationalMeCodeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeDataRelationalMeCodeResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFontSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetFontSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetFontSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFontSettingFormId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFontSettingFormIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetFontSettingFormIdResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFontSettingFormLabel">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="label" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetFontSettingFormLabelResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetFontSettingFormLabelResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCustomizeButtonSettingAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCustomizeButtonSettingAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCustomizeButtonSettingAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCustomizeButtonDetailAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetCustomizeButtonDetailAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCustomizeButtonDetailAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCustomizeButtonDetailbyFormId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="formId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCustomizeButtonDetailbyFormIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCustomizeButtonDetailbyFormIdResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetLiteTerminalData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="liteTerminalID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetLiteTerminalDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetLiteTerminalDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetLiteTerminalDataAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetLiteTerminalDataAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetLiteTerminalDataAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetLiteTerminalData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="befClientID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetLiteTerminalDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetLiteTerminalDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientAddInfor">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientAddInforResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientAddInforResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientAddInfor2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientAddInfor2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientAddInfor2Result" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientAddInfor">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="addInfo" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientAddInforResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetClientAddInforResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientAddInfor2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="addInfo" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientAddInfor2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetClientAddInfor2Result" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckReasonData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedialCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckReasonDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckReasonDataResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckItemState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckItemStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckItemStateResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateEndTimeAll">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckData" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="reasonData" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="itemState" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateEndTimeAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateEndTimeAllResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupItemAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetNeedCheckupItemAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupItemAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupItemResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupItemItemNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="itemNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupItemItemNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupItemItemNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckupItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="tbl">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckupItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetNeedCheckupItemResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIniGuideMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ipAddress" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIniGuideMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetIniGuideMasterResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIniGuideMasterAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetIniGuideMasterAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetIniGuideMasterAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTermGuideData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ipAddress" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTermGuideDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTermGuideDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTermGuideDataAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetTermGuideDataAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTermGuideDataAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetTermGuideData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ipAddress" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="GuideFlg" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TermFlg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetTermGuideDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetTermGuideDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientGuideDataWithChildGroup">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="dateTime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="clientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="division" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientGuideDataWithChildGroupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientGuideDataWithChildGroupResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetChildGroupDetailMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="childGroupId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetChildGroupDetailMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetChildGroupDetailMasterResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetChildGroupTermMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="childGroupId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetChildGroupTermMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetChildGroupTermMasterResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetExamSettingMaster">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="medicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetExamSettingMasterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetExamSettingMasterResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTargetStateNumber">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sql" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTargetStateNumberResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTargetStateNumberResult">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetAllStateFromExecDate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="execDate" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetAllStateFromExecDateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetAllStateFromExecDateResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNextGuideData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="dateTime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="clientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="division" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNextGuideDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNextGuideDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNextGuideData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="dateTime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="clientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="division" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNextGuideDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteNextGuideDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNextGuideData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="dateTime" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="clientId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="division" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="childGroupFlg" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="value" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNextGuideDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetNextGuideDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="HelloWorld">
        <s:complexType />
      </s:element>
      <s:element name="HelloWorldResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="HelloWorldResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetHostData">
        <s:complexType />
      </s:element>
      <s:element name="StrGetHostDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetHostDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMachines">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMachinesResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMachinesResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMachinesAllkeys">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMachinesAllkeysResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMachinesAllkeysResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMachinesAllPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMachinesAllPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMachinesAllPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRegistrationNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRegistrationNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientRegistrationNoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientIDDate">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="RegistrationNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientIDDateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeClientIDDateResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeClientDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRegistrationNoNoDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientRegistrationNoNoDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientRegistrationNoNoDivResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientDataNoDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeClientDataNoDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeClientDataNoDivResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralCheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataPluralCheckNoResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralAllState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataPluralAllStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataPluralAllStateResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataNoDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataNoDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataNoDivResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataRegist">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sRegistNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataRegistResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataRegistResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataRegistDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sRegistNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataRegistDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataRegistDivResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataSeachDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataSeachDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataSeachDivResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataSeachDivRegist">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sRegistNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetClientDataSeachDivRegistResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetClientDataSeachDivRegistResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataCheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataCheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataCheckNoResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="timeFlg" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetDataResponse">
        <s:complexType />
      </s:element>
      <s:element name="StrSetDataTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="timeFlg" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetDataTimeResponse">
        <s:complexType />
      </s:element>
      <s:element name="StrGetDataPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataAll">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataAllResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetStateResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetStateNext">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetStateNextResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetStateNextResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iTimeFlg" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateResponse">
        <s:complexType />
      </s:element>
      <s:element name="StrSetStateTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iTimeFlg" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetStateTimeResponse">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMedicalCheckList">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="iMedicalCheckNo" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckListResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckListResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckListNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckListNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckListNoResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckListAll">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMedicalCheckListAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckListAllResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMedicalCheckListPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetMedicalCheckListPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMedicalCheckListPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateDataStateResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataState">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateDataStateTimeResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTime">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateTimeResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeMore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeMoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateDataStateTimeMoreResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeMore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="nowTime" type="s:dateTime" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeMoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateTimeMoreResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeTrans">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="startTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="endTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="hostTransFlg" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeTransResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateDataStateTimeTransResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeTrans">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="startTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="endTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="hostTransFlg" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeTransResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateTimeTransResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeTransMore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="startTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="endTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="hostTransFlg" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrStartUpdateDataStateTimeTransMoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrStartUpdateDataStateTimeTransMoreResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeTransMore">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="startTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="endTime" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="hostTransFlg" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateTimeTransMoreResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateTimeTransMoreResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateNoTimeTrans">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="state" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="hostTransFlg" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrEndUpdateDataStateNoTimeTransResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrEndUpdateDataStateNoTimeTransResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIP">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIPResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPermissionIPResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIP_Plural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIP_PluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPermissionIP_PluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIPallkeys">
        <s:complexType />
      </s:element>
      <s:element name="StrGetPermissionIPallkeysResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPermissionIPallkeysResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetPermissionIPallPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetPermissionIPallPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetPermissionIPallPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetPermissionIP">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="beforeTermID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetPermissionIPResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetPermissionIPResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeletePermissionIP">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeletePermissionIPResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeletePermissionIPResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTerminalSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iSettingId" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTerminalSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTerminalSettingResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTerminalSettingPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetTerminalSettingPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetTerminalSettingPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetTerminalSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iSettingId" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="iContent" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetTerminalSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetTerminalSettingResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteTerminalSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sIp" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iSettingId" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteTerminalSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteTerminalSettingResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StartUpdateTermStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StartUpdateTermStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StartUpdateTermStatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EndUpdateTermStatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EndUpdateTermStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="EndUpdateTermStatusResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeOldDataResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldDataNoDiv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldDataNoDivResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeOldDataNoDivResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldCheckNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetBeforeOldCheckNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetBeforeOldCheckNoResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DatabaseLimitControl">
        <s:complexType />
      </s:element>
      <s:element name="DatabaseLimitControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DatabaseLimitControlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetDataNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetDataNameResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeCheckUpNameResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpNameNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpNameNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeCheckUpNameNoResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpNameKindNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sKindNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeCheckUpNameKindNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeCheckUpNameKindNoResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeConnectSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sDll" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iMeId" type="s:short" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetMeConnectSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetMeConnectSettingResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetMeConnectSetting">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sDll" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="iMeId" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="setting" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="medicalData" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetMeConnectSettingResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetMeConnectSettingResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckup">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetNeedCheckupPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckup">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="checkObjectNos" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetNeedCheckupResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNeedCheckup">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNeedCheckupResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteNeedCheckupResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupMedicalNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetNeedCheckupMedicalNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetNeedCheckupMedicalNoResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckupMedicalNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="checkObjectNos" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetNeedCheckupMedicalNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetNeedCheckupMedicalNoResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNeedCheckupMedicalNo">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckNo" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteNeedCheckupMedicalNoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteNeedCheckupMedicalNoResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIndividualControlPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetIndividualControlPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetIndividualControlPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIndividualControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="iPAddress" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetIndividualControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetIndividualControlResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetIndividualControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="iPAddress" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="dispCheckNos" type="tns:ArrayOfString" />
            <s:element minOccurs="0" maxOccurs="1" name="checkObjectNos" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetIndividualControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetIndividualControlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteIndividualControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="iPAddress" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteIndividualControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteIndividualControlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCheckItem">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="termID" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCheckItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCheckItemResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCheckItemPlural">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetCheckItemPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetCheckItemPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sGroupId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeControlResult" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrGetConciergeControlPlural">
        <s:complexType />
      </s:element>
      <s:element name="StrGetConciergeControlPluralResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StrGetConciergeControlPluralResult" type="tns:ArrayOfPluralRecord" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetConciergeControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sGroupId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sGroupName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sShortGroupName" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="checkObjectNos" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetConciergeControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetConciergeControlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteConciergeControl">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sGroupId" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteConciergeControlResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DeleteConciergeControlResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientData">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="sMedicalCheckDate" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sClientID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sDivision" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="regData" type="tns:ArrayOfString" />
            <s:element minOccurs="1" maxOccurs="1" name="regPos" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StrSetClientDataResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="StrSetClientDataResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="StrSetNextGuideDataHoldTimeSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetNextGuideDataHoldTime" />
  </wsdl:message>
  <wsdl:message name="StrSetNextGuideDataHoldTimeSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetNextGuideDataHoldTimeResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetStateOnlySoapIn">
    <wsdl:part name="parameters" element="tns:StrSetStateOnly" />
  </wsdl:message>
  <wsdl:message name="StrSetStateOnlySoapOut">
    <wsdl:part name="parameters" element="tns:StrSetStateOnlyResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetStateOnlyPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetStateOnlyPlural" />
  </wsdl:message>
  <wsdl:message name="StrSetStateOnlyPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetStateOnlyPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetStateTimeToNULLSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetStateTimeToNULL" />
  </wsdl:message>
  <wsdl:message name="StrSetStateTimeToNULLSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetStateTimeToNULLResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallClientDataAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallClientDataAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCallClientDataAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallClientDataAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallClientData" />
  </wsdl:message>
  <wsdl:message name="StrGetCallClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallClientDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetCallClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetCallClientData" />
  </wsdl:message>
  <wsdl:message name="StrSetCallClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetCallClientDataResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteCallClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCallClientData" />
  </wsdl:message>
  <wsdl:message name="DeleteCallClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCallClientDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallExamMasterAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallExamMasterAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCallExamMasterAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallExamMasterAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallExamMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallExamMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetCallExamMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallExamMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetCallExamMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetCallExamMaster" />
  </wsdl:message>
  <wsdl:message name="StrSetCallExamMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetCallExamMasterResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteCallExamMasterSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCallExamMaster" />
  </wsdl:message>
  <wsdl:message name="DeleteCallExamMasterSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCallExamMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallStatusAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallStatusAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCallStatusAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallStatusAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetCallStatusSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetCallStatus" />
  </wsdl:message>
  <wsdl:message name="StrSetCallStatusSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetCallStatusResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteCallStatusSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCallStatus" />
  </wsdl:message>
  <wsdl:message name="DeleteCallStatusSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCallStatusResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallTerminalMasterAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallTerminalMasterAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCallTerminalMasterAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallTerminalMasterAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCallTerminalMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCallTerminalMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetCallTerminalMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCallTerminalMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetCallTerminalMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetCallTerminalMaster" />
  </wsdl:message>
  <wsdl:message name="StrSetCallTerminalMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetCallTerminalMasterResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteCallTerminalMasterSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteCallTerminalMaster" />
  </wsdl:message>
  <wsdl:message name="DeleteCallTerminalMasterSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteCallTerminalMasterResponse" />
  </wsdl:message>
  <wsdl:message name="GetSoundSettingPluralSoapIn">
    <wsdl:part name="parameters" element="tns:GetSoundSettingPlural" />
  </wsdl:message>
  <wsdl:message name="GetSoundSettingPluralSoapOut">
    <wsdl:part name="parameters" element="tns:GetSoundSettingPluralResponse" />
  </wsdl:message>
  <wsdl:message name="GetSoundSettingOneSoapIn">
    <wsdl:part name="parameters" element="tns:GetSoundSettingOne" />
  </wsdl:message>
  <wsdl:message name="GetSoundSettingOneSoapOut">
    <wsdl:part name="parameters" element="tns:GetSoundSettingOneResponse" />
  </wsdl:message>
  <wsdl:message name="SetSoundSettingSoapIn">
    <wsdl:part name="parameters" element="tns:SetSoundSetting" />
  </wsdl:message>
  <wsdl:message name="SetSoundSettingSoapOut">
    <wsdl:part name="parameters" element="tns:SetSoundSettingResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteSoundSettingSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteSoundSetting" />
  </wsdl:message>
  <wsdl:message name="DeleteSoundSettingSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteSoundSettingResponse" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFileSoapIn">
    <wsdl:part name="parameters" element="tns:SFCreateServerFile" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFileSoapOut">
    <wsdl:part name="parameters" element="tns:SFCreateServerFileResponse" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFileForEncodeSoapIn">
    <wsdl:part name="parameters" element="tns:SFCreateServerFileForEncode" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFileForEncodeSoapOut">
    <wsdl:part name="parameters" element="tns:SFCreateServerFileForEncodeResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetFileExistSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetFileExist" />
  </wsdl:message>
  <wsdl:message name="SFGetFileExistSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetFileExistResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetDirectoryExistSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetDirectoryExist" />
  </wsdl:message>
  <wsdl:message name="SFGetDirectoryExistSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetDirectoryExistResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetFileContentSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetFileContent" />
  </wsdl:message>
  <wsdl:message name="SFGetFileContentSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetFileContentResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetFileCreateDateSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetFileCreateDate" />
  </wsdl:message>
  <wsdl:message name="SFGetFileCreateDateSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetFileCreateDateResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetFileUpdateDateSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetFileUpdateDate" />
  </wsdl:message>
  <wsdl:message name="SFGetFileUpdateDateSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetFileUpdateDateResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetFileListSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetFileList" />
  </wsdl:message>
  <wsdl:message name="SFGetFileListSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetFileListResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetDirectoryListSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetDirectoryList" />
  </wsdl:message>
  <wsdl:message name="SFGetDirectoryListSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetDirectoryListResponse" />
  </wsdl:message>
  <wsdl:message name="SFDeleteFileSoapIn">
    <wsdl:part name="parameters" element="tns:SFDeleteFile" />
  </wsdl:message>
  <wsdl:message name="SFDeleteFileSoapOut">
    <wsdl:part name="parameters" element="tns:SFDeleteFileResponse" />
  </wsdl:message>
  <wsdl:message name="SFGetConditionCsvFileSoapIn">
    <wsdl:part name="parameters" element="tns:SFGetConditionCsvFile" />
  </wsdl:message>
  <wsdl:message name="SFGetConditionCsvFileSoapOut">
    <wsdl:part name="parameters" element="tns:SFGetConditionCsvFileResponse" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFolderSoapIn">
    <wsdl:part name="parameters" element="tns:SFCreateServerFolder" />
  </wsdl:message>
  <wsdl:message name="SFCreateServerFolderSoapOut">
    <wsdl:part name="parameters" element="tns:SFCreateServerFolderResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateStateTimeMoreSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateStateTimeMore" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateStateTimeMoreSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateStateTimeMoreResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoCustomSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNoCustom" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoCustomSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNoCustomResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoTableSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNoTable" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoTableSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNoTableResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberList" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListNo" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListAll" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeMemberListPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeMemberListPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupList" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListNo" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListAll" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeTimeGroupListPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeTimeGroupListPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClickSwitchSettingTableAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClickSwitchSettingTableAll" />
  </wsdl:message>
  <wsdl:message name="StrGetClickSwitchSettingTableAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClickSwitchSettingTableAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClickSwitchSettingTableSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClickSwitchSettingTable" />
  </wsdl:message>
  <wsdl:message name="StrGetClickSwitchSettingTableSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClickSwitchSettingTableResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataTransformedSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataTransformedSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetDataTransformedSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataTransformedSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataTransformedSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataTransformedSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetDataTransformedSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataTransformedSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDispChangeSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDispChangeSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetDispChangeSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDispChangeSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDispChangeSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDispChangeSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetDispChangeSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDispChangeSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemListAll" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemListAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemList" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemListResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemListNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemListNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemListNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingDataTypeSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingDataType" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingDataTypeSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingDataTypeResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingMenuSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingMenu" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalItemSettingMenuSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalItemSettingMenuResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetMedicalItemSettingMenuSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetMedicalItemSettingMenu" />
  </wsdl:message>
  <wsdl:message name="StrSetMedicalItemSettingMenuSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetMedicalItemSettingMenuResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetFormModeDispAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetFormModeDispAll" />
  </wsdl:message>
  <wsdl:message name="StrGetFormModeDispAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetFormModeDispAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetFormModeDispSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetFormModeDisp" />
  </wsdl:message>
  <wsdl:message name="StrGetFormModeDispSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetFormModeDispResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSettingNo" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSettingNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralDispSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPluralDispSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPluralSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPluralSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPluralSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetPluralSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPluralSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTransformedSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTransformedSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetTransformedSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTransformedSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTransformedSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTransformedSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetTransformedSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTransformedSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRemarksDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientRemarksData" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRemarksDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientRemarksDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetClientRemarksInformationSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetClientRemarksInformation" />
  </wsdl:message>
  <wsdl:message name="StrSetClientRemarksInformationSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetClientRemarksInformationResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetRemarksItemListAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetRemarksItemListAll" />
  </wsdl:message>
  <wsdl:message name="StrGetRemarksItemListAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetRemarksItemListAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectAll" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectIdSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectId" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectIdSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectIdResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectMedicalCheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectMedicalCheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeGroupSelectMedicalCheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeGroupSelectMedicalCheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlAll" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlIdSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlId" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlIdSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlIdResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlMedicalcheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlMedicalcheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeControlMedicalcheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeControlMedicalcheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectAll" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectIdSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectId" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectIdSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectIdResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectMedicalCheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectMedicalCheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetEyeInitialSelectMedicalCheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetEyeInitialSelectMedicalCheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalAll" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalItemNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalItemNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalItemNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalItemNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalMeCodeSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalMeCode" />
  </wsdl:message>
  <wsdl:message name="StrGetMeDataRelationalMeCodeSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeDataRelationalMeCodeResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingFormIdSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingFormId" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingFormIdSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingFormIdResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingFormLabelSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingFormLabel" />
  </wsdl:message>
  <wsdl:message name="StrGetFontSettingFormLabelSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetFontSettingFormLabelResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonSettingAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonSettingAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonSettingAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonSettingAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonDetailAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonDetailAll" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonDetailAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonDetailAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonDetailbyFormIdSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonDetailbyFormId" />
  </wsdl:message>
  <wsdl:message name="StrGetCustomizeButtonDetailbyFormIdSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCustomizeButtonDetailbyFormIdResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetLiteTerminalDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetLiteTerminalData" />
  </wsdl:message>
  <wsdl:message name="StrGetLiteTerminalDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetLiteTerminalDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetLiteTerminalDataAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetLiteTerminalDataAll" />
  </wsdl:message>
  <wsdl:message name="StrGetLiteTerminalDataAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetLiteTerminalDataAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetLiteTerminalDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetLiteTerminalData" />
  </wsdl:message>
  <wsdl:message name="StrSetLiteTerminalDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetLiteTerminalDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientAddInforSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientAddInfor" />
  </wsdl:message>
  <wsdl:message name="StrGetClientAddInforSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientAddInforResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientAddInfor2SoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientAddInfor2" />
  </wsdl:message>
  <wsdl:message name="StrGetClientAddInfor2SoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientAddInfor2Response" />
  </wsdl:message>
  <wsdl:message name="StrSetClientAddInforSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetClientAddInfor" />
  </wsdl:message>
  <wsdl:message name="StrSetClientAddInforSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetClientAddInforResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetClientAddInfor2SoapIn">
    <wsdl:part name="parameters" element="tns:StrSetClientAddInfor2" />
  </wsdl:message>
  <wsdl:message name="StrSetClientAddInfor2SoapOut">
    <wsdl:part name="parameters" element="tns:StrSetClientAddInfor2Response" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckReasonDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckReasonData" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckReasonDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckReasonDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckItemStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckItemState" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckItemStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckItemStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateEndTimeAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateEndTimeAll" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateEndTimeAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateEndTimeAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItemAll" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItemAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItem" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItemResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemItemNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItemItemNo" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupItemItemNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupItemItemNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupItemSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckupItem" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupItemSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckupItemResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetIniGuideMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetIniGuideMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetIniGuideMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetIniGuideMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetIniGuideMasterAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetIniGuideMasterAll" />
  </wsdl:message>
  <wsdl:message name="StrGetIniGuideMasterAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetIniGuideMasterAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTermGuideDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTermGuideData" />
  </wsdl:message>
  <wsdl:message name="StrGetTermGuideDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTermGuideDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTermGuideDataAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTermGuideDataAll" />
  </wsdl:message>
  <wsdl:message name="StrGetTermGuideDataAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTermGuideDataAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetTermGuideDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetTermGuideData" />
  </wsdl:message>
  <wsdl:message name="StrSetTermGuideDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetTermGuideDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientGuideDataWithChildGroupSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientGuideDataWithChildGroup" />
  </wsdl:message>
  <wsdl:message name="StrGetClientGuideDataWithChildGroupSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientGuideDataWithChildGroupResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetChildGroupDetailMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetChildGroupDetailMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetChildGroupDetailMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetChildGroupDetailMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetChildGroupTermMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetChildGroupTermMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetChildGroupTermMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetChildGroupTermMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetExamSettingMasterSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetExamSettingMaster" />
  </wsdl:message>
  <wsdl:message name="StrGetExamSettingMasterSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetExamSettingMasterResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTargetStateNumberSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTargetStateNumber" />
  </wsdl:message>
  <wsdl:message name="StrGetTargetStateNumberSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTargetStateNumberResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetAllStateFromExecDateSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetAllStateFromExecDate" />
  </wsdl:message>
  <wsdl:message name="StrGetAllStateFromExecDateSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetAllStateFromExecDateResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNextGuideDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNextGuideData" />
  </wsdl:message>
  <wsdl:message name="StrGetNextGuideDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNextGuideDataResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteNextGuideDataSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteNextGuideData" />
  </wsdl:message>
  <wsdl:message name="DeleteNextGuideDataSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteNextGuideDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetNextGuideDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetNextGuideData" />
  </wsdl:message>
  <wsdl:message name="StrSetNextGuideDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetNextGuideDataResponse" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapIn">
    <wsdl:part name="parameters" element="tns:HelloWorld" />
  </wsdl:message>
  <wsdl:message name="HelloWorldSoapOut">
    <wsdl:part name="parameters" element="tns:HelloWorldResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetHostDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetHostData" />
  </wsdl:message>
  <wsdl:message name="StrGetHostDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetHostDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMachines" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMachinesResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesAllkeysSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMachinesAllkeys" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesAllkeysSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMachinesAllkeysResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesAllPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMachinesAllPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetMachinesAllPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMachinesAllPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientData" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRegistrationNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientRegistrationNo" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRegistrationNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientRegistrationNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientIDDateSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientIDDate" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientIDDateSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientIDDateResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientData" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRegistrationNoNoDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientRegistrationNoNoDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetClientRegistrationNoNoDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientRegistrationNoNoDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientDataNoDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientDataNoDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeClientDataNoDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeClientDataNoDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralCheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralCheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralAllStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralAllState" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataPluralAllStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataPluralAllStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataNoDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataNoDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataNoDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataNoDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataRegistSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataRegist" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataRegistSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataRegistResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataRegistDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataRegistDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataRegistDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataRegistDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSeachDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataSeachDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSeachDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataSeachDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSeachDivRegistSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetClientDataSeachDivRegist" />
  </wsdl:message>
  <wsdl:message name="StrGetClientDataSeachDivRegistSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetClientDataSeachDivRegistResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetData" />
  </wsdl:message>
  <wsdl:message name="StrGetDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataCheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataCheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetDataCheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataCheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetData" />
  </wsdl:message>
  <wsdl:message name="StrSetDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetDataTimeSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetDataTime" />
  </wsdl:message>
  <wsdl:message name="StrSetDataTimeSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetDataTimeResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetDataPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataAll" />
  </wsdl:message>
  <wsdl:message name="StrGetDataAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetState" />
  </wsdl:message>
  <wsdl:message name="StrGetStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetStateNextSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetStateNext" />
  </wsdl:message>
  <wsdl:message name="StrGetStateNextSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetStateNextResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetState" />
  </wsdl:message>
  <wsdl:message name="StrSetStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetStateTimeSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetStateTime" />
  </wsdl:message>
  <wsdl:message name="StrSetStateTimeSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetStateTimeResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckList" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListAllSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListAll" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListAllSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListAllResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetMedicalCheckListPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMedicalCheckListPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataState" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataState" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTime" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTime" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeMoreSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeMore" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeMoreSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeMoreResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeMoreSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeMore" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeMoreSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeMoreResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeTransSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeTrans" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeTransSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeTransResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeTransSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeTrans" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeTransSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeTransResponse" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeTransMoreSoapIn">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeTransMore" />
  </wsdl:message>
  <wsdl:message name="StrStartUpdateDataStateTimeTransMoreSoapOut">
    <wsdl:part name="parameters" element="tns:StrStartUpdateDataStateTimeTransMoreResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeTransMoreSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeTransMore" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateTimeTransMoreSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateTimeTransMoreResponse" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateNoTimeTransSoapIn">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateNoTimeTrans" />
  </wsdl:message>
  <wsdl:message name="StrEndUpdateDataStateNoTimeTransSoapOut">
    <wsdl:part name="parameters" element="tns:StrEndUpdateDataStateNoTimeTransResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIP" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIPResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIP_PluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIP_Plural" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIP_PluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIP_PluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPallkeysSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIPallkeys" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPallkeysSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIPallkeysResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPallPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIPallPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetPermissionIPallPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetPermissionIPallPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetPermissionIPSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetPermissionIP" />
  </wsdl:message>
  <wsdl:message name="StrSetPermissionIPSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetPermissionIPResponse" />
  </wsdl:message>
  <wsdl:message name="DeletePermissionIPSoapIn">
    <wsdl:part name="parameters" element="tns:DeletePermissionIP" />
  </wsdl:message>
  <wsdl:message name="DeletePermissionIPSoapOut">
    <wsdl:part name="parameters" element="tns:DeletePermissionIPResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTerminalSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTerminalSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetTerminalSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTerminalSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetTerminalSettingPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetTerminalSettingPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetTerminalSettingPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetTerminalSettingPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetTerminalSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetTerminalSetting" />
  </wsdl:message>
  <wsdl:message name="StrSetTerminalSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetTerminalSettingResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteTerminalSettingSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteTerminalSetting" />
  </wsdl:message>
  <wsdl:message name="DeleteTerminalSettingSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteTerminalSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StartUpdateTermStatusSoapIn">
    <wsdl:part name="parameters" element="tns:StartUpdateTermStatus" />
  </wsdl:message>
  <wsdl:message name="StartUpdateTermStatusSoapOut">
    <wsdl:part name="parameters" element="tns:StartUpdateTermStatusResponse" />
  </wsdl:message>
  <wsdl:message name="EndUpdateTermStatusSoapIn">
    <wsdl:part name="parameters" element="tns:EndUpdateTermStatus" />
  </wsdl:message>
  <wsdl:message name="EndUpdateTermStatusSoapOut">
    <wsdl:part name="parameters" element="tns:EndUpdateTermStatusResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldData" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldDataResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldDataNoDivSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldDataNoDiv" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldDataNoDivSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldDataNoDivResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldCheckNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldCheckNo" />
  </wsdl:message>
  <wsdl:message name="StrGetBeforeOldCheckNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetBeforeOldCheckNoResponse" />
  </wsdl:message>
  <wsdl:message name="DatabaseLimitControlSoapIn">
    <wsdl:part name="parameters" element="tns:DatabaseLimitControl" />
  </wsdl:message>
  <wsdl:message name="DatabaseLimitControlSoapOut">
    <wsdl:part name="parameters" element="tns:DatabaseLimitControlResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetDataNameSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetDataName" />
  </wsdl:message>
  <wsdl:message name="StrGetDataNameSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetDataNameResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpName" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpNameResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpNameNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpNameNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameKindNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpNameKindNo" />
  </wsdl:message>
  <wsdl:message name="StrGetMeCheckUpNameKindNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeCheckUpNameKindNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetMeConnectSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetMeConnectSetting" />
  </wsdl:message>
  <wsdl:message name="StrGetMeConnectSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetMeConnectSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetMeConnectSettingSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetMeConnectSetting" />
  </wsdl:message>
  <wsdl:message name="StrSetMeConnectSettingSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetMeConnectSettingResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckup" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckup" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckupResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteNeedCheckupSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteNeedCheckup" />
  </wsdl:message>
  <wsdl:message name="DeleteNeedCheckupSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteNeedCheckupResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupMedicalNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupMedicalNo" />
  </wsdl:message>
  <wsdl:message name="StrGetNeedCheckupMedicalNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetNeedCheckupMedicalNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupMedicalNoSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckupMedicalNo" />
  </wsdl:message>
  <wsdl:message name="StrSetNeedCheckupMedicalNoSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetNeedCheckupMedicalNoResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteNeedCheckupMedicalNoSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteNeedCheckupMedicalNo" />
  </wsdl:message>
  <wsdl:message name="DeleteNeedCheckupMedicalNoSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteNeedCheckupMedicalNoResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetIndividualControlPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetIndividualControlPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetIndividualControlPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetIndividualControlPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetIndividualControlSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetIndividualControl" />
  </wsdl:message>
  <wsdl:message name="StrGetIndividualControlSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetIndividualControlResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetIndividualControlSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetIndividualControl" />
  </wsdl:message>
  <wsdl:message name="StrSetIndividualControlSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetIndividualControlResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteIndividualControlSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteIndividualControl" />
  </wsdl:message>
  <wsdl:message name="DeleteIndividualControlSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteIndividualControlResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCheckItemSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCheckItem" />
  </wsdl:message>
  <wsdl:message name="StrGetCheckItemSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCheckItemResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetCheckItemPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetCheckItemPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetCheckItemPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetCheckItemPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeControlSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeControl" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeControlSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeControlResponse" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeControlPluralSoapIn">
    <wsdl:part name="parameters" element="tns:StrGetConciergeControlPlural" />
  </wsdl:message>
  <wsdl:message name="StrGetConciergeControlPluralSoapOut">
    <wsdl:part name="parameters" element="tns:StrGetConciergeControlPluralResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetConciergeControlSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetConciergeControl" />
  </wsdl:message>
  <wsdl:message name="StrSetConciergeControlSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetConciergeControlResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteConciergeControlSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteConciergeControl" />
  </wsdl:message>
  <wsdl:message name="DeleteConciergeControlSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteConciergeControlResponse" />
  </wsdl:message>
  <wsdl:message name="StrSetClientDataSoapIn">
    <wsdl:part name="parameters" element="tns:StrSetClientData" />
  </wsdl:message>
  <wsdl:message name="StrSetClientDataSoapOut">
    <wsdl:part name="parameters" element="tns:StrSetClientDataResponse" />
  </wsdl:message>
  <wsdl:portType name="PublicServiceSoap">
    <wsdl:operation name="StrSetNextGuideDataHoldTime">
      <wsdl:input message="tns:StrSetNextGuideDataHoldTimeSoapIn" />
      <wsdl:output message="tns:StrSetNextGuideDataHoldTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnly">
      <wsdl:input message="tns:StrSetStateOnlySoapIn" />
      <wsdl:output message="tns:StrSetStateOnlySoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnlyPlural">
      <wsdl:input message="tns:StrSetStateOnlyPluralSoapIn" />
      <wsdl:output message="tns:StrSetStateOnlyPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTimeToNULL">
      <wsdl:input message="tns:StrSetStateTimeToNULLSoapIn" />
      <wsdl:output message="tns:StrSetStateTimeToNULLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientDataAll">
      <wsdl:input message="tns:StrGetCallClientDataAllSoapIn" />
      <wsdl:output message="tns:StrGetCallClientDataAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientData">
      <wsdl:input message="tns:StrGetCallClientDataSoapIn" />
      <wsdl:output message="tns:StrGetCallClientDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetCallClientData">
      <wsdl:input message="tns:StrSetCallClientDataSoapIn" />
      <wsdl:output message="tns:StrSetCallClientDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCallClientData">
      <wsdl:input message="tns:DeleteCallClientDataSoapIn" />
      <wsdl:output message="tns:DeleteCallClientDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMasterAll">
      <wsdl:input message="tns:StrGetCallExamMasterAllSoapIn" />
      <wsdl:output message="tns:StrGetCallExamMasterAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMaster">
      <wsdl:input message="tns:StrGetCallExamMasterSoapIn" />
      <wsdl:output message="tns:StrGetCallExamMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetCallExamMaster">
      <wsdl:input message="tns:StrSetCallExamMasterSoapIn" />
      <wsdl:output message="tns:StrSetCallExamMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCallExamMaster">
      <wsdl:input message="tns:DeleteCallExamMasterSoapIn" />
      <wsdl:output message="tns:DeleteCallExamMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallStatusAll">
      <wsdl:input message="tns:StrGetCallStatusAllSoapIn" />
      <wsdl:output message="tns:StrGetCallStatusAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetCallStatus">
      <wsdl:input message="tns:StrSetCallStatusSoapIn" />
      <wsdl:output message="tns:StrSetCallStatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCallStatus">
      <wsdl:input message="tns:DeleteCallStatusSoapIn" />
      <wsdl:output message="tns:DeleteCallStatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMasterAll">
      <wsdl:input message="tns:StrGetCallTerminalMasterAllSoapIn" />
      <wsdl:output message="tns:StrGetCallTerminalMasterAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMaster">
      <wsdl:input message="tns:StrGetCallTerminalMasterSoapIn" />
      <wsdl:output message="tns:StrGetCallTerminalMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetCallTerminalMaster">
      <wsdl:input message="tns:StrSetCallTerminalMasterSoapIn" />
      <wsdl:output message="tns:StrSetCallTerminalMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteCallTerminalMaster">
      <wsdl:input message="tns:DeleteCallTerminalMasterSoapIn" />
      <wsdl:output message="tns:DeleteCallTerminalMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingPlural">
      <wsdl:input message="tns:GetSoundSettingPluralSoapIn" />
      <wsdl:output message="tns:GetSoundSettingPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingOne">
      <wsdl:input message="tns:GetSoundSettingOneSoapIn" />
      <wsdl:output message="tns:GetSoundSettingOneSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SetSoundSetting">
      <wsdl:input message="tns:SetSoundSettingSoapIn" />
      <wsdl:output message="tns:SetSoundSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteSoundSetting">
      <wsdl:input message="tns:DeleteSoundSettingSoapIn" />
      <wsdl:output message="tns:DeleteSoundSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFile">
      <wsdl:input message="tns:SFCreateServerFileSoapIn" />
      <wsdl:output message="tns:SFCreateServerFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFileForEncode">
      <wsdl:input message="tns:SFCreateServerFileForEncodeSoapIn" />
      <wsdl:output message="tns:SFCreateServerFileForEncodeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetFileExist">
      <wsdl:input message="tns:SFGetFileExistSoapIn" />
      <wsdl:output message="tns:SFGetFileExistSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryExist">
      <wsdl:input message="tns:SFGetDirectoryExistSoapIn" />
      <wsdl:output message="tns:SFGetDirectoryExistSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetFileContent">
      <wsdl:input message="tns:SFGetFileContentSoapIn" />
      <wsdl:output message="tns:SFGetFileContentSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetFileCreateDate">
      <wsdl:input message="tns:SFGetFileCreateDateSoapIn" />
      <wsdl:output message="tns:SFGetFileCreateDateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetFileUpdateDate">
      <wsdl:input message="tns:SFGetFileUpdateDateSoapIn" />
      <wsdl:output message="tns:SFGetFileUpdateDateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetFileList">
      <wsdl:input message="tns:SFGetFileListSoapIn" />
      <wsdl:output message="tns:SFGetFileListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryList">
      <wsdl:input message="tns:SFGetDirectoryListSoapIn" />
      <wsdl:output message="tns:SFGetDirectoryListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFDeleteFile">
      <wsdl:input message="tns:SFDeleteFileSoapIn" />
      <wsdl:output message="tns:SFDeleteFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFGetConditionCsvFile">
      <wsdl:input message="tns:SFGetConditionCsvFileSoapIn" />
      <wsdl:output message="tns:SFGetConditionCsvFileSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFolder">
      <wsdl:input message="tns:SFCreateServerFolderSoapIn" />
      <wsdl:output message="tns:SFCreateServerFolderSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateStateTimeMore">
      <wsdl:input message="tns:StrStartUpdateStateTimeMoreSoapIn" />
      <wsdl:output message="tns:StrStartUpdateStateTimeMoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoCustom">
      <wsdl:input message="tns:StrGetClientDataPluralCheckNoCustomSoapIn" />
      <wsdl:output message="tns:StrGetClientDataPluralCheckNoCustomSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoTable">
      <wsdl:input message="tns:StrGetClientDataPluralCheckNoTableSoapIn" />
      <wsdl:output message="tns:StrGetClientDataPluralCheckNoTableSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberList">
      <wsdl:input message="tns:StrGetConciergeMemberListSoapIn" />
      <wsdl:output message="tns:StrGetConciergeMemberListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListNo">
      <wsdl:input message="tns:StrGetConciergeMemberListNoSoapIn" />
      <wsdl:output message="tns:StrGetConciergeMemberListNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListAll">
      <wsdl:input message="tns:StrGetConciergeMemberListAllSoapIn" />
      <wsdl:output message="tns:StrGetConciergeMemberListAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListPlural">
      <wsdl:input message="tns:StrGetConciergeMemberListPluralSoapIn" />
      <wsdl:output message="tns:StrGetConciergeMemberListPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupList">
      <wsdl:input message="tns:StrGetConciergeTimeGroupListSoapIn" />
      <wsdl:output message="tns:StrGetConciergeTimeGroupListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListNo">
      <wsdl:input message="tns:StrGetConciergeTimeGroupListNoSoapIn" />
      <wsdl:output message="tns:StrGetConciergeTimeGroupListNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListAll">
      <wsdl:input message="tns:StrGetConciergeTimeGroupListAllSoapIn" />
      <wsdl:output message="tns:StrGetConciergeTimeGroupListAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListPlural">
      <wsdl:input message="tns:StrGetConciergeTimeGroupListPluralSoapIn" />
      <wsdl:output message="tns:StrGetConciergeTimeGroupListPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTableAll">
      <wsdl:input message="tns:StrGetClickSwitchSettingTableAllSoapIn" />
      <wsdl:output message="tns:StrGetClickSwitchSettingTableAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTable">
      <wsdl:input message="tns:StrGetClickSwitchSettingTableSoapIn" />
      <wsdl:output message="tns:StrGetClickSwitchSettingTableSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSettingAll">
      <wsdl:input message="tns:StrGetDataTransformedSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetDataTransformedSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSetting">
      <wsdl:input message="tns:StrGetDataTransformedSettingSoapIn" />
      <wsdl:output message="tns:StrGetDataTransformedSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSettingAll">
      <wsdl:input message="tns:StrGetDispChangeSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetDispChangeSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSetting">
      <wsdl:input message="tns:StrGetDispChangeSettingSoapIn" />
      <wsdl:output message="tns:StrGetDispChangeSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListAll">
      <wsdl:input message="tns:StrGetMedicalItemListAllSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemListAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemList">
      <wsdl:input message="tns:StrGetMedicalItemListSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListNo">
      <wsdl:input message="tns:StrGetMedicalItemListNoSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemListNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingAll">
      <wsdl:input message="tns:StrGetMedicalItemSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingNo">
      <wsdl:input message="tns:StrGetMedicalItemSettingNoSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemSettingNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSetting">
      <wsdl:input message="tns:StrGetMedicalItemSettingSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingDataType">
      <wsdl:input message="tns:StrGetMedicalItemSettingDataTypeSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemSettingDataTypeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingMenu">
      <wsdl:input message="tns:StrGetMedicalItemSettingMenuSoapIn" />
      <wsdl:output message="tns:StrGetMedicalItemSettingMenuSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetMedicalItemSettingMenu">
      <wsdl:input message="tns:StrSetMedicalItemSettingMenuSoapIn" />
      <wsdl:output message="tns:StrSetMedicalItemSettingMenuSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDispAll">
      <wsdl:input message="tns:StrGetFormModeDispAllSoapIn" />
      <wsdl:output message="tns:StrGetFormModeDispAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDisp">
      <wsdl:input message="tns:StrGetFormModeDispSoapIn" />
      <wsdl:output message="tns:StrGetFormModeDispSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingAll">
      <wsdl:input message="tns:StrGetPluralDispSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetPluralDispSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingNo">
      <wsdl:input message="tns:StrGetPluralDispSettingNoSoapIn" />
      <wsdl:output message="tns:StrGetPluralDispSettingNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSetting">
      <wsdl:input message="tns:StrGetPluralDispSettingSoapIn" />
      <wsdl:output message="tns:StrGetPluralDispSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSettingAll">
      <wsdl:input message="tns:StrGetPluralSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetPluralSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSetting">
      <wsdl:input message="tns:StrGetPluralSettingSoapIn" />
      <wsdl:output message="tns:StrGetPluralSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSettingAll">
      <wsdl:input message="tns:StrGetTransformedSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetTransformedSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSetting">
      <wsdl:input message="tns:StrGetTransformedSettingSoapIn" />
      <wsdl:output message="tns:StrGetTransformedSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRemarksData">
      <wsdl:input message="tns:StrGetClientRemarksDataSoapIn" />
      <wsdl:output message="tns:StrGetClientRemarksDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetClientRemarksInformation">
      <wsdl:input message="tns:StrSetClientRemarksInformationSoapIn" />
      <wsdl:output message="tns:StrSetClientRemarksInformationSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetRemarksItemListAll">
      <wsdl:input message="tns:StrGetRemarksItemListAllSoapIn" />
      <wsdl:output message="tns:StrGetRemarksItemListAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectAll">
      <wsdl:input message="tns:StrGetEyeGroupSelectAllSoapIn" />
      <wsdl:output message="tns:StrGetEyeGroupSelectAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectId">
      <wsdl:input message="tns:StrGetEyeGroupSelectIdSoapIn" />
      <wsdl:output message="tns:StrGetEyeGroupSelectIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectMedicalCheckNo">
      <wsdl:input message="tns:StrGetEyeGroupSelectMedicalCheckNoSoapIn" />
      <wsdl:output message="tns:StrGetEyeGroupSelectMedicalCheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlAll">
      <wsdl:input message="tns:StrGetEyeControlAllSoapIn" />
      <wsdl:output message="tns:StrGetEyeControlAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlId">
      <wsdl:input message="tns:StrGetEyeControlIdSoapIn" />
      <wsdl:output message="tns:StrGetEyeControlIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlMedicalcheckNo">
      <wsdl:input message="tns:StrGetEyeControlMedicalcheckNoSoapIn" />
      <wsdl:output message="tns:StrGetEyeControlMedicalcheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectAll">
      <wsdl:input message="tns:StrGetEyeInitialSelectAllSoapIn" />
      <wsdl:output message="tns:StrGetEyeInitialSelectAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectId">
      <wsdl:input message="tns:StrGetEyeInitialSelectIdSoapIn" />
      <wsdl:output message="tns:StrGetEyeInitialSelectIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectMedicalCheckNo">
      <wsdl:input message="tns:StrGetEyeInitialSelectMedicalCheckNoSoapIn" />
      <wsdl:output message="tns:StrGetEyeInitialSelectMedicalCheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalAll">
      <wsdl:input message="tns:StrGetMeDataRelationalAllSoapIn" />
      <wsdl:output message="tns:StrGetMeDataRelationalAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalNo">
      <wsdl:input message="tns:StrGetMeDataRelationalNoSoapIn" />
      <wsdl:output message="tns:StrGetMeDataRelationalNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalItemNo">
      <wsdl:input message="tns:StrGetMeDataRelationalItemNoSoapIn" />
      <wsdl:output message="tns:StrGetMeDataRelationalItemNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalMeCode">
      <wsdl:input message="tns:StrGetMeDataRelationalMeCodeSoapIn" />
      <wsdl:output message="tns:StrGetMeDataRelationalMeCodeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingAll">
      <wsdl:input message="tns:StrGetFontSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetFontSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormId">
      <wsdl:input message="tns:StrGetFontSettingFormIdSoapIn" />
      <wsdl:output message="tns:StrGetFontSettingFormIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormLabel">
      <wsdl:input message="tns:StrGetFontSettingFormLabelSoapIn" />
      <wsdl:output message="tns:StrGetFontSettingFormLabelSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonSettingAll">
      <wsdl:input message="tns:StrGetCustomizeButtonSettingAllSoapIn" />
      <wsdl:output message="tns:StrGetCustomizeButtonSettingAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailAll">
      <wsdl:input message="tns:StrGetCustomizeButtonDetailAllSoapIn" />
      <wsdl:output message="tns:StrGetCustomizeButtonDetailAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailbyFormId">
      <wsdl:input message="tns:StrGetCustomizeButtonDetailbyFormIdSoapIn" />
      <wsdl:output message="tns:StrGetCustomizeButtonDetailbyFormIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalData">
      <wsdl:input message="tns:StrGetLiteTerminalDataSoapIn" />
      <wsdl:output message="tns:StrGetLiteTerminalDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalDataAll">
      <wsdl:input message="tns:StrGetLiteTerminalDataAllSoapIn" />
      <wsdl:output message="tns:StrGetLiteTerminalDataAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetLiteTerminalData">
      <wsdl:input message="tns:StrSetLiteTerminalDataSoapIn" />
      <wsdl:output message="tns:StrSetLiteTerminalDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor">
      <wsdl:input message="tns:StrGetClientAddInforSoapIn" />
      <wsdl:output message="tns:StrGetClientAddInforSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor2">
      <wsdl:input message="tns:StrGetClientAddInfor2SoapIn" />
      <wsdl:output message="tns:StrGetClientAddInfor2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor">
      <wsdl:input message="tns:StrSetClientAddInforSoapIn" />
      <wsdl:output message="tns:StrSetClientAddInforSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor2">
      <wsdl:input message="tns:StrSetClientAddInfor2SoapIn" />
      <wsdl:output message="tns:StrSetClientAddInfor2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckReasonData">
      <wsdl:input message="tns:StrGetMedicalCheckReasonDataSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckReasonDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckItemState">
      <wsdl:input message="tns:StrGetMedicalCheckItemStateSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckItemStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateEndTimeAll">
      <wsdl:input message="tns:StrEndUpdateEndTimeAllSoapIn" />
      <wsdl:output message="tns:StrEndUpdateEndTimeAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemAll">
      <wsdl:input message="tns:StrGetNeedCheckupItemAllSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupItemAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItem">
      <wsdl:input message="tns:StrGetNeedCheckupItemSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemItemNo">
      <wsdl:input message="tns:StrGetNeedCheckupItemItemNoSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupItemItemNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupItem">
      <wsdl:input message="tns:StrSetNeedCheckupItemSoapIn" />
      <wsdl:output message="tns:StrSetNeedCheckupItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMaster">
      <wsdl:input message="tns:StrGetIniGuideMasterSoapIn" />
      <wsdl:output message="tns:StrGetIniGuideMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMasterAll">
      <wsdl:input message="tns:StrGetIniGuideMasterAllSoapIn" />
      <wsdl:output message="tns:StrGetIniGuideMasterAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideData">
      <wsdl:input message="tns:StrGetTermGuideDataSoapIn" />
      <wsdl:output message="tns:StrGetTermGuideDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideDataAll">
      <wsdl:input message="tns:StrGetTermGuideDataAllSoapIn" />
      <wsdl:output message="tns:StrGetTermGuideDataAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetTermGuideData">
      <wsdl:input message="tns:StrSetTermGuideDataSoapIn" />
      <wsdl:output message="tns:StrSetTermGuideDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientGuideDataWithChildGroup">
      <wsdl:input message="tns:StrGetClientGuideDataWithChildGroupSoapIn" />
      <wsdl:output message="tns:StrGetClientGuideDataWithChildGroupSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupDetailMaster">
      <wsdl:input message="tns:StrGetChildGroupDetailMasterSoapIn" />
      <wsdl:output message="tns:StrGetChildGroupDetailMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupTermMaster">
      <wsdl:input message="tns:StrGetChildGroupTermMasterSoapIn" />
      <wsdl:output message="tns:StrGetChildGroupTermMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetExamSettingMaster">
      <wsdl:input message="tns:StrGetExamSettingMasterSoapIn" />
      <wsdl:output message="tns:StrGetExamSettingMasterSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTargetStateNumber">
      <wsdl:input message="tns:StrGetTargetStateNumberSoapIn" />
      <wsdl:output message="tns:StrGetTargetStateNumberSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetAllStateFromExecDate">
      <wsdl:input message="tns:StrGetAllStateFromExecDateSoapIn" />
      <wsdl:output message="tns:StrGetAllStateFromExecDateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNextGuideData">
      <wsdl:input message="tns:StrGetNextGuideDataSoapIn" />
      <wsdl:output message="tns:StrGetNextGuideDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteNextGuideData">
      <wsdl:input message="tns:DeleteNextGuideDataSoapIn" />
      <wsdl:output message="tns:DeleteNextGuideDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetNextGuideData">
      <wsdl:input message="tns:StrSetNextGuideDataSoapIn" />
      <wsdl:output message="tns:StrSetNextGuideDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="HelloWorld">
      <wsdl:input message="tns:HelloWorldSoapIn" />
      <wsdl:output message="tns:HelloWorldSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetHostData">
      <wsdl:input message="tns:StrGetHostDataSoapIn" />
      <wsdl:output message="tns:StrGetHostDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMachines">
      <wsdl:input message="tns:StrGetMachinesSoapIn" />
      <wsdl:output message="tns:StrGetMachinesSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllkeys">
      <wsdl:input message="tns:StrGetMachinesAllkeysSoapIn" />
      <wsdl:output message="tns:StrGetMachinesAllkeysSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllPlural">
      <wsdl:input message="tns:StrGetMachinesAllPluralSoapIn" />
      <wsdl:output message="tns:StrGetMachinesAllPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientData">
      <wsdl:input message="tns:StrGetClientDataSoapIn" />
      <wsdl:output message="tns:StrGetClientDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNo">
      <wsdl:input message="tns:StrGetClientRegistrationNoSoapIn" />
      <wsdl:output message="tns:StrGetClientRegistrationNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientIDDate">
      <wsdl:input message="tns:StrGetBeforeClientIDDateSoapIn" />
      <wsdl:output message="tns:StrGetBeforeClientIDDateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientData">
      <wsdl:input message="tns:StrGetBeforeClientDataSoapIn" />
      <wsdl:output message="tns:StrGetBeforeClientDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNoNoDiv">
      <wsdl:input message="tns:StrGetClientRegistrationNoNoDivSoapIn" />
      <wsdl:output message="tns:StrGetClientRegistrationNoNoDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientDataNoDiv">
      <wsdl:input message="tns:StrGetBeforeClientDataNoDivSoapIn" />
      <wsdl:output message="tns:StrGetBeforeClientDataNoDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPlural">
      <wsdl:input message="tns:StrGetClientDataPluralSoapIn" />
      <wsdl:output message="tns:StrGetClientDataPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNo">
      <wsdl:input message="tns:StrGetClientDataPluralCheckNoSoapIn" />
      <wsdl:output message="tns:StrGetClientDataPluralCheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralAllState">
      <wsdl:input message="tns:StrGetClientDataPluralAllStateSoapIn" />
      <wsdl:output message="tns:StrGetClientDataPluralAllStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataNoDiv">
      <wsdl:input message="tns:StrGetClientDataNoDivSoapIn" />
      <wsdl:output message="tns:StrGetClientDataNoDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegist">
      <wsdl:input message="tns:StrGetClientDataRegistSoapIn" />
      <wsdl:output message="tns:StrGetClientDataRegistSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegistDiv">
      <wsdl:input message="tns:StrGetClientDataRegistDivSoapIn" />
      <wsdl:output message="tns:StrGetClientDataRegistDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDiv">
      <wsdl:input message="tns:StrGetClientDataSeachDivSoapIn" />
      <wsdl:output message="tns:StrGetClientDataSeachDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDivRegist">
      <wsdl:input message="tns:StrGetClientDataSeachDivRegistSoapIn" />
      <wsdl:output message="tns:StrGetClientDataSeachDivRegistSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetData">
      <wsdl:input message="tns:StrGetDataSoapIn" />
      <wsdl:output message="tns:StrGetDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataCheckNo">
      <wsdl:input message="tns:StrGetDataCheckNoSoapIn" />
      <wsdl:output message="tns:StrGetDataCheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetData">
      <wsdl:input message="tns:StrSetDataSoapIn" />
      <wsdl:output message="tns:StrSetDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetDataTime">
      <wsdl:input message="tns:StrSetDataTimeSoapIn" />
      <wsdl:output message="tns:StrSetDataTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataPlural">
      <wsdl:input message="tns:StrGetDataPluralSoapIn" />
      <wsdl:output message="tns:StrGetDataPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataAll">
      <wsdl:input message="tns:StrGetDataAllSoapIn" />
      <wsdl:output message="tns:StrGetDataAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetState">
      <wsdl:input message="tns:StrGetStateSoapIn" />
      <wsdl:output message="tns:StrGetStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetStateNext">
      <wsdl:input message="tns:StrGetStateNextSoapIn" />
      <wsdl:output message="tns:StrGetStateNextSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetState">
      <wsdl:input message="tns:StrSetStateSoapIn" />
      <wsdl:output message="tns:StrSetStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTime">
      <wsdl:input message="tns:StrSetStateTimeSoapIn" />
      <wsdl:output message="tns:StrSetStateTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckList">
      <wsdl:input message="tns:StrGetMedicalCheckListSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckListSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListNo">
      <wsdl:input message="tns:StrGetMedicalCheckListNoSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckListNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListAll">
      <wsdl:input message="tns:StrGetMedicalCheckListAllSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckListAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListPlural">
      <wsdl:input message="tns:StrGetMedicalCheckListPluralSoapIn" />
      <wsdl:output message="tns:StrGetMedicalCheckListPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataState">
      <wsdl:input message="tns:StrStartUpdateDataStateSoapIn" />
      <wsdl:output message="tns:StrStartUpdateDataStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataState">
      <wsdl:input message="tns:StrEndUpdateDataStateSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTime">
      <wsdl:input message="tns:StrStartUpdateDataStateTimeSoapIn" />
      <wsdl:output message="tns:StrStartUpdateDataStateTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTime">
      <wsdl:input message="tns:StrEndUpdateDataStateTimeSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateTimeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeMore">
      <wsdl:input message="tns:StrStartUpdateDataStateTimeMoreSoapIn" />
      <wsdl:output message="tns:StrStartUpdateDataStateTimeMoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeMore">
      <wsdl:input message="tns:StrEndUpdateDataStateTimeMoreSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateTimeMoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTrans">
      <wsdl:input message="tns:StrStartUpdateDataStateTimeTransSoapIn" />
      <wsdl:output message="tns:StrStartUpdateDataStateTimeTransSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTrans">
      <wsdl:input message="tns:StrEndUpdateDataStateTimeTransSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateTimeTransSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTransMore">
      <wsdl:input message="tns:StrStartUpdateDataStateTimeTransMoreSoapIn" />
      <wsdl:output message="tns:StrStartUpdateDataStateTimeTransMoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTransMore">
      <wsdl:input message="tns:StrEndUpdateDataStateTimeTransMoreSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateTimeTransMoreSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateNoTimeTrans">
      <wsdl:input message="tns:StrEndUpdateDataStateNoTimeTransSoapIn" />
      <wsdl:output message="tns:StrEndUpdateDataStateNoTimeTransSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP">
      <wsdl:input message="tns:StrGetPermissionIPSoapIn" />
      <wsdl:output message="tns:StrGetPermissionIPSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP_Plural">
      <wsdl:input message="tns:StrGetPermissionIP_PluralSoapIn" />
      <wsdl:output message="tns:StrGetPermissionIP_PluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallkeys">
      <wsdl:input message="tns:StrGetPermissionIPallkeysSoapIn" />
      <wsdl:output message="tns:StrGetPermissionIPallkeysSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallPlural">
      <wsdl:input message="tns:StrGetPermissionIPallPluralSoapIn" />
      <wsdl:output message="tns:StrGetPermissionIPallPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetPermissionIP">
      <wsdl:input message="tns:StrSetPermissionIPSoapIn" />
      <wsdl:output message="tns:StrSetPermissionIPSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeletePermissionIP">
      <wsdl:input message="tns:DeletePermissionIPSoapIn" />
      <wsdl:output message="tns:DeletePermissionIPSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSetting">
      <wsdl:input message="tns:StrGetTerminalSettingSoapIn" />
      <wsdl:output message="tns:StrGetTerminalSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSettingPlural">
      <wsdl:input message="tns:StrGetTerminalSettingPluralSoapIn" />
      <wsdl:output message="tns:StrGetTerminalSettingPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetTerminalSetting">
      <wsdl:input message="tns:StrSetTerminalSettingSoapIn" />
      <wsdl:output message="tns:StrSetTerminalSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteTerminalSetting">
      <wsdl:input message="tns:DeleteTerminalSettingSoapIn" />
      <wsdl:output message="tns:DeleteTerminalSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StartUpdateTermStatus">
      <wsdl:input message="tns:StartUpdateTermStatusSoapIn" />
      <wsdl:output message="tns:StartUpdateTermStatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EndUpdateTermStatus">
      <wsdl:input message="tns:EndUpdateTermStatusSoapIn" />
      <wsdl:output message="tns:EndUpdateTermStatusSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldData">
      <wsdl:input message="tns:StrGetBeforeOldDataSoapIn" />
      <wsdl:output message="tns:StrGetBeforeOldDataSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldDataNoDiv">
      <wsdl:input message="tns:StrGetBeforeOldDataNoDivSoapIn" />
      <wsdl:output message="tns:StrGetBeforeOldDataNoDivSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldCheckNo">
      <wsdl:input message="tns:StrGetBeforeOldCheckNoSoapIn" />
      <wsdl:output message="tns:StrGetBeforeOldCheckNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DatabaseLimitControl">
      <wsdl:input message="tns:DatabaseLimitControlSoapIn" />
      <wsdl:output message="tns:DatabaseLimitControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetDataName">
      <wsdl:input message="tns:StrGetDataNameSoapIn" />
      <wsdl:output message="tns:StrGetDataNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpName">
      <wsdl:input message="tns:StrGetMeCheckUpNameSoapIn" />
      <wsdl:output message="tns:StrGetMeCheckUpNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameNo">
      <wsdl:input message="tns:StrGetMeCheckUpNameNoSoapIn" />
      <wsdl:output message="tns:StrGetMeCheckUpNameNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameKindNo">
      <wsdl:input message="tns:StrGetMeCheckUpNameKindNoSoapIn" />
      <wsdl:output message="tns:StrGetMeCheckUpNameKindNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetMeConnectSetting">
      <wsdl:input message="tns:StrGetMeConnectSettingSoapIn" />
      <wsdl:output message="tns:StrGetMeConnectSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetMeConnectSetting">
      <wsdl:input message="tns:StrSetMeConnectSettingSoapIn" />
      <wsdl:output message="tns:StrSetMeConnectSettingSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckup">
      <wsdl:input message="tns:StrGetNeedCheckupSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupPlural">
      <wsdl:input message="tns:StrGetNeedCheckupPluralSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckup">
      <wsdl:input message="tns:StrSetNeedCheckupSoapIn" />
      <wsdl:output message="tns:StrSetNeedCheckupSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckup">
      <wsdl:input message="tns:DeleteNeedCheckupSoapIn" />
      <wsdl:output message="tns:DeleteNeedCheckupSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupMedicalNo">
      <wsdl:input message="tns:StrGetNeedCheckupMedicalNoSoapIn" />
      <wsdl:output message="tns:StrGetNeedCheckupMedicalNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupMedicalNo">
      <wsdl:input message="tns:StrSetNeedCheckupMedicalNoSoapIn" />
      <wsdl:output message="tns:StrSetNeedCheckupMedicalNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckupMedicalNo">
      <wsdl:input message="tns:DeleteNeedCheckupMedicalNoSoapIn" />
      <wsdl:output message="tns:DeleteNeedCheckupMedicalNoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControlPlural">
      <wsdl:input message="tns:StrGetIndividualControlPluralSoapIn" />
      <wsdl:output message="tns:StrGetIndividualControlPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControl">
      <wsdl:input message="tns:StrGetIndividualControlSoapIn" />
      <wsdl:output message="tns:StrGetIndividualControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetIndividualControl">
      <wsdl:input message="tns:StrSetIndividualControlSoapIn" />
      <wsdl:output message="tns:StrSetIndividualControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteIndividualControl">
      <wsdl:input message="tns:DeleteIndividualControlSoapIn" />
      <wsdl:output message="tns:DeleteIndividualControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItem">
      <wsdl:input message="tns:StrGetCheckItemSoapIn" />
      <wsdl:output message="tns:StrGetCheckItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItemPlural">
      <wsdl:input message="tns:StrGetCheckItemPluralSoapIn" />
      <wsdl:output message="tns:StrGetCheckItemPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControl">
      <wsdl:input message="tns:StrGetConciergeControlSoapIn" />
      <wsdl:output message="tns:StrGetConciergeControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControlPlural">
      <wsdl:input message="tns:StrGetConciergeControlPluralSoapIn" />
      <wsdl:output message="tns:StrGetConciergeControlPluralSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetConciergeControl">
      <wsdl:input message="tns:StrSetConciergeControlSoapIn" />
      <wsdl:output message="tns:StrSetConciergeControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteConciergeControl">
      <wsdl:input message="tns:DeleteConciergeControlSoapIn" />
      <wsdl:output message="tns:DeleteConciergeControlSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="StrSetClientData">
      <wsdl:input message="tns:StrSetClientDataSoapIn" />
      <wsdl:output message="tns:StrSetClientDataSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="PublicServiceSoap" type="tns:PublicServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="StrSetNextGuideDataHoldTime">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetNextGuideDataHoldTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnly">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetStateOnly" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnlyPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetStateOnlyPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTimeToNULL">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetStateTimeToNULL" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientDataAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallClientDataAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetCallClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteCallClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMasterAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallExamMasterAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallExamMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallExamMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetCallExamMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallExamMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteCallExamMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallStatusAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallStatusAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallStatus">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetCallStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallStatus">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteCallStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMasterAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallTerminalMasterAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallTerminalMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallTerminalMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/GetSoundSettingPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingOne">
      <soap:operation soapAction="http://localhost/AR1000Server/GetSoundSettingOne" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSoundSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/SetSoundSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSoundSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteSoundSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFile">
      <soap:operation soapAction="http://localhost/AR1000Server/SFCreateServerFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFileForEncode">
      <soap:operation soapAction="http://localhost/AR1000Server/SFCreateServerFileForEncode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileExist">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetFileExist" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryExist">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetDirectoryExist" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileContent">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetFileContent" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileCreateDate">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetFileCreateDate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileUpdateDate">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetFileUpdateDate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileList">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetFileList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryList">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetDirectoryList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFDeleteFile">
      <soap:operation soapAction="http://localhost/AR1000Server/SFDeleteFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetConditionCsvFile">
      <soap:operation soapAction="http://localhost/AR1000Server/SFGetConditionCsvFile" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFolder">
      <soap:operation soapAction="http://localhost/AR1000Server/SFCreateServerFolder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateStateTimeMore">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateStateTimeMore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoCustom">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoCustom" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoTable">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoTable" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberList">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupList">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTableAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClickSwitchSettingTableAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTable">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClickSwitchSettingTable" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataTransformedSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataTransformedSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDispChangeSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDispChangeSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemListAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemList">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemListNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingDataType">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingDataType" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingMenu">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingMenu" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetMedicalItemSettingMenu">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetMedicalItemSettingMenu" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDispAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetFormModeDispAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDisp">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetFormModeDisp" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSettingNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPluralSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPluralSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTransformedSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTransformedSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRemarksData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientRemarksData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientRemarksInformation">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetClientRemarksInformation" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetRemarksItemListAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetRemarksItemListAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectId">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectMedicalCheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectMedicalCheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlId">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlMedicalcheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlMedicalcheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectId">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectMedicalCheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectMedicalCheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalItemNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalItemNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalMeCode">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalMeCode" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormId">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingFormId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormLabel">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingFormLabel" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonSettingAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonSettingAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonDetailAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailbyFormId">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonDetailbyFormId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetLiteTerminalData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalDataAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetLiteTerminalDataAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetLiteTerminalData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetLiteTerminalData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientAddInfor" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor2">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientAddInfor2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetClientAddInfor" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor2">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetClientAddInfor2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckReasonData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckReasonData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckItemState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckItemState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateEndTimeAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateEndTimeAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItemAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItem">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemItemNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItemItemNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupItem">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckupItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetIniGuideMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMasterAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetIniGuideMasterAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTermGuideData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideDataAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTermGuideDataAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetTermGuideData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetTermGuideData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientGuideDataWithChildGroup">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientGuideDataWithChildGroup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupDetailMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetChildGroupDetailMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupTermMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetChildGroupTermMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetExamSettingMaster">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetExamSettingMaster" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTargetStateNumber">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTargetStateNumber" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetAllStateFromExecDate">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetAllStateFromExecDate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNextGuideData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNextGuideData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNextGuideData">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteNextGuideData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNextGuideData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetNextGuideData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HelloWorld">
      <soap:operation soapAction="http://localhost/AR1000Server/HelloWorld" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetHostData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetHostData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachines">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMachines" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllkeys">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMachinesAllkeys" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMachinesAllPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientRegistrationNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientIDDate">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientIDDate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNoNoDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientRegistrationNoNoDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientDataNoDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientDataNoDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralAllState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralAllState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataNoDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataNoDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegist">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataRegist" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegistDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataRegistDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataSeachDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDivRegist">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetClientDataSeachDivRegist" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataCheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataCheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetDataTime">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetDataTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetStateNext">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetStateNext" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTime">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetStateTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckList">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListAll">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataState">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataState" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTime">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTime">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTime" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeMore">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeMore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeMore">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeMore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTrans">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTrans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTrans">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTrans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTransMore">
      <soap:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTransMore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTransMore">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTransMore" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateNoTimeTrans">
      <soap:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateNoTimeTrans" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIP" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP_Plural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIP_Plural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallkeys">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIPallkeys" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIPallPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetPermissionIP">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetPermissionIP" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeletePermissionIP">
      <soap:operation soapAction="http://localhost/AR1000Server/DeletePermissionIP" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTerminalSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSettingPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetTerminalSettingPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetTerminalSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetTerminalSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteTerminalSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteTerminalSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartUpdateTermStatus">
      <soap:operation soapAction="http://localhost/AR1000Server/StartUpdateTermStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EndUpdateTermStatus">
      <soap:operation soapAction="http://localhost/AR1000Server/EndUpdateTermStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldDataNoDiv">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldDataNoDiv" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldCheckNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldCheckNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DatabaseLimitControl">
      <soap:operation soapAction="http://localhost/AR1000Server/DatabaseLimitControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataName">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetDataName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpName">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpNameNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameKindNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpNameKindNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeConnectSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetMeConnectSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetMeConnectSetting">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetMeConnectSetting" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckup">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckup">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckup">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteNeedCheckup" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupMedicalNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupMedicalNo">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckupMedicalNo">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControlPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetIndividualControlPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControl">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetIndividualControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetIndividualControl">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetIndividualControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteIndividualControl">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteIndividualControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItem">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCheckItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItemPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetCheckItemPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControl">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControlPlural">
      <soap:operation soapAction="http://localhost/AR1000Server/StrGetConciergeControlPlural" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetConciergeControl">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetConciergeControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteConciergeControl">
      <soap:operation soapAction="http://localhost/AR1000Server/DeleteConciergeControl" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientData">
      <soap:operation soapAction="http://localhost/AR1000Server/StrSetClientData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="PublicServiceSoap12" type="tns:PublicServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="StrSetNextGuideDataHoldTime">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetNextGuideDataHoldTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnly">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetStateOnly" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateOnlyPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetStateOnlyPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTimeToNULL">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetStateTimeToNULL" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientDataAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallClientDataAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetCallClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteCallClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMasterAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallExamMasterAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallExamMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallExamMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallExamMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetCallExamMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallExamMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteCallExamMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallStatusAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallStatusAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallStatus">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetCallStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallStatus">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteCallStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMasterAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallTerminalMasterAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCallTerminalMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetCallTerminalMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteCallTerminalMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteCallTerminalMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/GetSoundSettingPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSoundSettingOne">
      <soap12:operation soapAction="http://localhost/AR1000Server/GetSoundSettingOne" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetSoundSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/SetSoundSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteSoundSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteSoundSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFile">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFCreateServerFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFileForEncode">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFCreateServerFileForEncode" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileExist">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetFileExist" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryExist">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetDirectoryExist" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileContent">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetFileContent" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileCreateDate">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetFileCreateDate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileUpdateDate">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetFileUpdateDate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetFileList">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetFileList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetDirectoryList">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetDirectoryList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFDeleteFile">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFDeleteFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFGetConditionCsvFile">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFGetConditionCsvFile" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SFCreateServerFolder">
      <soap12:operation soapAction="http://localhost/AR1000Server/SFCreateServerFolder" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateStateTimeMore">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateStateTimeMore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoCustom">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoCustom" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNoTable">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNoTable" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberList">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeMemberListPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeMemberListPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupList">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeTimeGroupListPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeTimeGroupListPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTableAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClickSwitchSettingTableAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClickSwitchSettingTable">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClickSwitchSettingTable" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataTransformedSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataTransformedSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataTransformedSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDispChangeSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDispChangeSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDispChangeSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemListAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemList">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemListNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemListNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingDataType">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingDataType" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalItemSettingMenu">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalItemSettingMenu" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetMedicalItemSettingMenu">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetMedicalItemSettingMenu" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDispAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetFormModeDispAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFormModeDisp">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetFormModeDisp" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSettingNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSettingNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralDispSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPluralDispSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPluralSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPluralSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPluralSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTransformedSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTransformedSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTransformedSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRemarksData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientRemarksData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientRemarksInformation">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetClientRemarksInformation" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetRemarksItemListAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetRemarksItemListAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectId">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeGroupSelectMedicalCheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeGroupSelectMedicalCheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlId">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeControlMedicalcheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeControlMedicalcheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectId">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetEyeInitialSelectMedicalCheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetEyeInitialSelectMedicalCheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalItemNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalItemNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeDataRelationalMeCode">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeDataRelationalMeCode" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormId">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingFormId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetFontSettingFormLabel">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetFontSettingFormLabel" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonSettingAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonSettingAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonDetailAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCustomizeButtonDetailbyFormId">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCustomizeButtonDetailbyFormId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetLiteTerminalData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetLiteTerminalDataAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetLiteTerminalDataAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetLiteTerminalData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetLiteTerminalData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientAddInfor" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientAddInfor2">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientAddInfor2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetClientAddInfor" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientAddInfor2">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetClientAddInfor2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckReasonData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckReasonData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckItemState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckItemState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateEndTimeAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateEndTimeAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItemAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItem">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupItemItemNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupItemItemNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupItem">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckupItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetIniGuideMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIniGuideMasterAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetIniGuideMasterAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTermGuideData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTermGuideDataAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTermGuideDataAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetTermGuideData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetTermGuideData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientGuideDataWithChildGroup">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientGuideDataWithChildGroup" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupDetailMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetChildGroupDetailMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetChildGroupTermMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetChildGroupTermMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetExamSettingMaster">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetExamSettingMaster" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTargetStateNumber">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTargetStateNumber" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetAllStateFromExecDate">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetAllStateFromExecDate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNextGuideData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNextGuideData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNextGuideData">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteNextGuideData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNextGuideData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetNextGuideData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="HelloWorld">
      <soap12:operation soapAction="http://localhost/AR1000Server/HelloWorld" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetHostData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetHostData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachines">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMachines" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllkeys">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMachinesAllkeys" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMachinesAllPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMachinesAllPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientRegistrationNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientIDDate">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientIDDate" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientRegistrationNoNoDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientRegistrationNoNoDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeClientDataNoDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeClientDataNoDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralCheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralCheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataPluralAllState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataPluralAllState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataNoDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataNoDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegist">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataRegist" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataRegistDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataRegistDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataSeachDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetClientDataSeachDivRegist">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetClientDataSeachDivRegist" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataCheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataCheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetDataTime">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetDataTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetStateNext">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetStateNext" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetStateTime">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetStateTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckList">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListAll">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMedicalCheckListPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMedicalCheckListPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataState">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataState" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTime">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTime">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTime" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeMore">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeMore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeMore">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeMore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTrans">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTrans" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTrans">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTrans" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrStartUpdateDataStateTimeTransMore">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrStartUpdateDataStateTimeTransMore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateTimeTransMore">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateTimeTransMore" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrEndUpdateDataStateNoTimeTrans">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrEndUpdateDataStateNoTimeTrans" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIP" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIP_Plural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIP_Plural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallkeys">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIPallkeys" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetPermissionIPallPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetPermissionIPallPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetPermissionIP">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetPermissionIP" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeletePermissionIP">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeletePermissionIP" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTerminalSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetTerminalSettingPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetTerminalSettingPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetTerminalSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetTerminalSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteTerminalSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteTerminalSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StartUpdateTermStatus">
      <soap12:operation soapAction="http://localhost/AR1000Server/StartUpdateTermStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EndUpdateTermStatus">
      <soap12:operation soapAction="http://localhost/AR1000Server/EndUpdateTermStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldDataNoDiv">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldDataNoDiv" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetBeforeOldCheckNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetBeforeOldCheckNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DatabaseLimitControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/DatabaseLimitControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetDataName">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetDataName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpName">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpNameNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeCheckUpNameKindNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeCheckUpNameKindNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetMeConnectSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetMeConnectSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetMeConnectSetting">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetMeConnectSetting" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckup">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckup" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckup">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckup" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckup">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteNeedCheckup" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetNeedCheckupMedicalNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetNeedCheckupMedicalNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteNeedCheckupMedicalNo">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteNeedCheckupMedicalNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControlPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetIndividualControlPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetIndividualControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetIndividualControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetIndividualControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetIndividualControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteIndividualControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteIndividualControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItem">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCheckItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetCheckItemPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetCheckItemPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrGetConciergeControlPlural">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrGetConciergeControlPlural" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetConciergeControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetConciergeControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteConciergeControl">
      <soap12:operation soapAction="http://localhost/AR1000Server/DeleteConciergeControl" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StrSetClientData">
      <soap12:operation soapAction="http://localhost/AR1000Server/StrSetClientData" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PublicService">
    <wsdl:port name="PublicServiceSoap" binding="tns:PublicServiceSoap">
      <soap:address location="http://localhost:50039/PublicService.asmx" />
    </wsdl:port>
    <wsdl:port name="PublicServiceSoap12" binding="tns:PublicServiceSoap12">
      <soap12:address location="http://localhost:50039/PublicService.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>