﻿using Bogus;

namespace ShinKenShinKunTests.Factories;

public class UserModelFactories
{
    //public static MeanTopHeaderSettingJsonModel GetModel() => new Faker<MeanTopHeaderSettingJsonModel>()
    //    .RuleFor(u => u.DetailsDispMode, f => f.PickRandom<SortOptions>())
    //    .RuleFor(u => u.ClientDispOrder, f => f.PickRandom<SortMode>())
    //    .RuleFor(u => u.ClientsPerPage, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.ColHeaderWidth, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.ClientCheckStateVisible, f => f.Random.Bool())
    //    .RuleFor(u => u.ClientDetailMode, f => f.Random.Bool())
    //    .RuleFor(u => u.AutoUpdating, f => f.Random.Bool())
    //    .RuleFor(u => u.ShowCheckedTypeOnly, f => f.Random.Bool())
    //    .RuleFor(u => u.ShowOnlyPatientsWithUnfinishedChecks, f => f.Random.Bool())
    //    .RuleFor(u => u.TermStateVisible, f => f.Random.Bool())
    //    .RuleFor(u => u.TermUnusedVisible, f => f.Random.Bool())
    //    .RuleFor(u => u.ToolTipVisible, f => f.Random.Bool())
    //    .RuleFor(u => u.Grouping, f => f.Random.Bool())
    //    .RuleFor(u => u.RowHeaderHeight, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.RowHeaderWidth, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.SleepTime, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.ToolTipShowTime, f => f.Random.Number(20, 100))
    //    .RuleFor(u => u.DivisionDispName, f => f.Random.String(20, 100))
    //    .Generate();
}
