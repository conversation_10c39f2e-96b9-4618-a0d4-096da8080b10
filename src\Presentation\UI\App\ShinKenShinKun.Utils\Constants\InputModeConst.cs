﻿namespace ShinKenShinKun.Utils;

/// <summary>
/// 電卓モード定数
/// </summary>
public class InputModeConst
{
    /// <summary>
    ///  入力不可
    /// </summary>
    public static readonly int NOT_INPUT = 0;

    /// <summary>
    /// 標準
    /// </summary>
    public static readonly int BASIC_INPUT = 1;

    /// <summary>
    /// 整数値
    /// </summary>
    public static readonly int INTEGER_INPUT = 2;

    /// <summary>
    /// 視力
    /// </summary>
    public static readonly int EYE_INPUT = 3;

    /// <summary>
    /// 定性値
    /// </summary>
    public static readonly int QUALITATIVE_INPUT = 4;

    /// <summary>
    /// 押下切り替え
    /// </summary>
    public static readonly int PUSH_SWITCH_INPUT = 5;
}