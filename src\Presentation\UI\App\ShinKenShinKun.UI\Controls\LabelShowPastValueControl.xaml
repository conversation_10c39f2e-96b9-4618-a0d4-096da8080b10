<?xml version="1.0" encoding="utf-8" ?>
<Grid
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.UI.LabelShowPastValueControl"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    RowSpacing="{x:Static ui:Dimens.SpacingXs}"
    RowDefinitions="*, Auto"
    x:Name="this">
    <Grid
        Grid.Row="0"
        ColumnDefinitions="Auto, *"
        VerticalOptions="End"
        Margin="{ui:EdgeInsets
                Horizontal={x:Static ui:Dimens.SpacingS}}">
        <Label
            Grid.Column="0"
            Text="{Binding Title, Source={x:Reference this}}"
            Style="{x:Static ui:Styles.LabelPastValueStyle}" />
        <Label
            Grid.Column="1"
            Text="{Binding Value, Source={x:Reference this}}"
            Style="{x:Static ui:Styles.LabelPastValueStyle}" />
    </Grid>
    <BoxView
        Grid.Row="1"
        Style="{x:Static ui:Styles.BoxViewGrayBackStyle}" />
</Grid>