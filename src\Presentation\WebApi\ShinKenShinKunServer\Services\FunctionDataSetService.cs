﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.FunctionDataSetTableAdapters;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// FunctionDataSetService の概要の説明です
    /// </summary>
    public class FunctionDataSetService
    {
        // データセットクラス
        private FunctionDataSet FunctionDs = new FunctionDataSet();

        #region TableAdapterクラス
        private ClickSettingTableAdapter clickSettingTableAdapter
            = new ClickSettingTableAdapter();
        #endregion
        public FunctionDataSetService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します
            //
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public FunctionDataSet GetClickSettingAll()
        {
            int count = clickSettingTableAdapter.Fill(FunctionDs.ClickSetting);
            return FunctionDs;
        }

        public FunctionDataSet GetClickSetting(short medicalCheckNo, short itemNo)
        {
            int count = clickSettingTableAdapter.FillBy(FunctionDs.ClickSetting, medicalCheckNo, itemNo);
            return FunctionDs;

        }
    }
}