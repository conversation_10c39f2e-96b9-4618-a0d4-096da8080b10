namespace ShinKenShinKun;

public partial class BulkReleaseHoldPatientsButtonView : RadBorder
{
    public BulkReleaseHoldPatientsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkReleaseHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkReleaseHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(BulkReleaseHoldPatientsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkReleaseHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkReleaseHoldPatientsCommandProperty);
        set => SetValue(BulkReleaseHoldPatientsCommandProperty, value);
    }
}