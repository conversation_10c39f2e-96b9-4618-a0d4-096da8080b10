<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualTableView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:ffimageloading="clr-namespace:FFImageLoading.Maui;assembly=FFImageLoading.Maui"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:DataType="app:IndividualProgressViewModel"
    x:Name="this">

    <Grid>
        <telerik:RadDataGrid
            IsVisible="{Binding ItemsSource, 
                                Converter={x:Static ui:AppConverters.IsListNotNullOrEmpty},
                                Source={x:Reference this}}"
            Style="{x:Static ui:Styles.PatientListDataGridStyle}"
            SelectionChanged="PatientRowTableSelectionChanged"
            SelectedItem="{Binding ItemSelected, 
                                Source={x:Reference this}, 
                                Mode=TwoWay}"
            ItemsSource="{Binding ItemsSource, 
                                Source={x:Reference this}}">
            <telerik:RadDataGrid.Columns>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Id}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding ClientId}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width320}"
                    SizeMode="Stretch">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                MinimumWidthRequest="{x:Static ui:Dimens.Width320}"
                                Text="{x:Static resources:AppResources.KanaName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding Name}">
                                <Label.Triggers>
                                    <DataTrigger
                                        TargetType="Label"
                                        Binding="{Binding IsMaskMode, 
                                            Source={x:Reference this}}"
                                        Value="True">
                                        <Setter
                                            Property="Text"
                                            Value="{x:Static resources:AppResources.MaskText}" />
                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width120}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Age}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding Age}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width120}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Gender}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding SexDisplay.DisplayName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width150}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <HorizontalStackLayout>
                                <Label
                                    Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                    Text="{x:Static resources:AppResources.Status}" />
                                <ffimageloading:CachedImage
                                    WidthRequest="16"
                                    Source="direction_down.png" />
                            </HorizontalStackLayout>
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding IndividualState}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

            </telerik:RadDataGrid.Columns>
        </telerik:RadDataGrid>
        <ScrollView
            IsVisible="{Binding ItemsSource, 
        Converter={x:Static ui:AppConverters.IsListNullOrEmpty},
        Source={x:Reference this}}"
            Orientation="Horizontal">
            <HorizontalStackLayout
                VerticalOptions="Start">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Id}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width320}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.KanaName}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Age}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Gender}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <HorizontalStackLayout
                        HorizontalOptions="Center">
                        <Label
                            Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                            Text="{x:Static resources:AppResources.Status}" />
                        <Image
                            WidthRequest="16"
                            Source="direction_down.png" />
                    </HorizontalStackLayout>
                </telerik:RadBorder>

            </HorizontalStackLayout>
        </ScrollView>
    </Grid>

</ContentView>
