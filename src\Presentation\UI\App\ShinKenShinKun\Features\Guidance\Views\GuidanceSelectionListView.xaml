<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.GuidanceSelectionListView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:<PERSON>KenShinKun"
    x:Name="this">
    <ScrollView
        Orientation="Vertical">
        <FlexLayout
            Grid.Row="1"
            Style="{x:Static ui:Styles.FlexLayoutStyle}"
            Padding="{ui:EdgeInsets 
                Horizontal={x:Static ui:Dimens.SpacingMd},
                Vertical={x:Static ui:Dimens.Spacing10}}"
            BindableLayout.ItemsSource="{Binding GuideItems, Source={x:Reference this}}">
            <BindableLayout.ItemTemplate>
                <DataTemplate
                    x:DataType="app:GuideItem">
                    <telerik:RadBorder
                        FlexLayout.Basis="20%"
                        Style="{x:Static ui:Styles.CustomRadBorderStyle}"
                        BackgroundColor="{Binding BackgroundColor}">
                        <telerik:RadBorder.GestureRecognizers>
                            <TapGestureRecognizer
                                Command="{Binding 
                                    Source={RelativeSource AncestorType={x:Type app:GuidanceSelectionListView}}, 
                                    Path=SelectionChangedCommand}"
                                CommandParameter="{Binding BindingContext, 
                                    Source={RelativeSource AncestorType={x:Type telerik:RadBorder}}}" />
                        </telerik:RadBorder.GestureRecognizers>

                        <Grid
                            ColumnDefinitions="2*,*">
                            <Label
                                Style="{x:Static ui:Styles.CenterLabel}"
                                Text="{Binding MedicalCheckName}"
                                TextColor="{Binding TextColor}"
                                FontSize="{Binding MedicalCheckName,
                                    Converter={x:Static ui:AppConverters.ResponsiveFontSizeConverter},
                                    ConverterParameter={ui:FontSizeParameters
                                        BaseFontSize={x:Static ui:Dimens.FontSize28},
                                        Ratio=0.9}}" />
                            <Label
                                Grid.Column="1"
                                Margin="{ui:EdgeInsets 
                                    Vertical={x:Static ui:Dimens.Spacing3},
                                    Right={x:Static ui:Dimens.Spacing3}}"
                                Text="{Binding Amount}"
                                FontSize="{Binding Source={x:Static ui:Dimens.FontSizeT2}}"
                                Style="{x:Static ui:Styles.WhiteHeaderCenterLabel}" />
                        </Grid>
                    </telerik:RadBorder>
                </DataTemplate>
            </BindableLayout.ItemTemplate>
        </FlexLayout>
    </ScrollView>
</ContentView>
