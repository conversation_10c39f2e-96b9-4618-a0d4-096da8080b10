﻿#if WINDOWS
using CommunityToolkit.Mvvm.Messaging;
using Microsoft.UI.Xaml.Input;
using Windows.System;

namespace ShinKenShinKun.UI;

public class WindowKeyBoardHandleServices : IKeyboardHandlerServices
{
    private Window? mauiWindow;

    public event Action<string> KeyDown;

    public void StartListening()
    {
        mauiWindow = Application.Current.Windows[0];
        if (mauiWindow?.Handler?.PlatformView is Microsoft.UI.Xaml.Window nativeWindow && nativeWindow.Content != null)
        {
            nativeWindow.Content.KeyDown += OnKeyDown;
        }
    }

    private void OnKeyDown(object sender, KeyRoutedEventArgs args)
    {
        var key = VirtualKeyToChar(args.Key);
        KeyDown?.Invoke(key);
    }

    public void StopListening()
    {
        if (mauiWindow?.Handler?.PlatformView is Microsoft.UI.Xaml.Window nativeWindow && nativeWindow.Content != null)
        {
            nativeWindow.Content.KeyDown -= OnKeyDown;
        }
        WeakReferenceMessenger.Default.Unregister<object>(this);
    }

    private static string VirtualKeyToChar(VirtualKey key)
    {
        return key switch
        {
            (VirtualKey)187 or VirtualKey.Add => AppConstants.Add,
            (VirtualKey)189 or VirtualKey.Subtract => AppConstants.Subtract,
            (VirtualKey)190 or VirtualKey.Decimal => AppConstants.Decimal,
            >= VirtualKey.Number0 and <= VirtualKey.Number9 => ((int)key - (int)VirtualKey.Number0).ToString(),
            >= VirtualKey.NumberPad0 and <= VirtualKey.NumberPad9 => ((int)key - (int)VirtualKey.NumberPad0).ToString(),
            VirtualKey.Enter => AppConstants.Enter,
            VirtualKey.Back => AppConstants.Backspace,
            VirtualKey.Delete => AppConstants.Delete,
            _ => string.Empty
        };
    }

}
#endif