namespace ShinKenShinKun;

public partial class PatientGuidanceFilterView : ContentView
{
    public static readonly BindableProperty MedicalCheckDivisionsProperty = BindableProperty.Create(
        nameof(MedicalCheckDivisions),
        typeof(ObservableCollection<MedicalCheckModel>),
        typeof(PatientGuidanceFilterView));

    public static readonly BindableProperty MedicalCheckSelectedProperty = BindableProperty.Create(
        nameof(MedicalCheckSelected),
        typeof(MedicalCheckModel),
        typeof(PatientGuidanceFilterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty ClientIdSearchedProperty = BindableProperty.Create(
        nameof(ClientIdSearched),
        typeof(string),
        typeof(PatientGuidanceFilterView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty SearchCommandProperty = BindableProperty.Create(
        nameof(SearchCommand),
        typeof(IRelayCommand),
        typeof(PatientGuidanceFilterView));

    public PatientGuidanceFilterView()
    {
        InitializeComponent();
    }

    public ObservableCollection<MedicalCheckModel> MedicalCheckDivisions
    {
        get => (ObservableCollection<MedicalCheckModel>)GetValue(MedicalCheckDivisionsProperty);
        set => SetValue(MedicalCheckDivisionsProperty, value);
    }

    public MedicalCheckModel MedicalCheckSelected
    {
        get => (MedicalCheckModel)GetValue(MedicalCheckSelectedProperty);
        set => SetValue(MedicalCheckSelectedProperty, value);
    }

    public string ClientIdSearched
    {
        get => (string)GetValue(ClientIdSearchedProperty);
        set => SetValue(ClientIdSearchedProperty, value);
    }

    public IRelayCommand SearchCommand
    {
        get => (IRelayCommand)GetValue(SearchCommandProperty);
        set => SetValue(SearchCommandProperty, value);
    }

    private void SearchTextChanged(object sender, TextChangedEventArgs e)
    {
        if(sender is RadEntry entry)
        {
            if(!string.IsNullOrEmpty(e.NewTextValue))
            {
                entry.Text = Regex.Replace(e.NewTextValue, "[^0-9]", "");
            }
        }
    }
}