<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.IndividualStateDisplayView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:app="clr-namespace:ShinKenShinKun"
    x:Name="this">
    <HorizontalStackLayout
        Spacing="20">
        <app:IndividualStateCounterView
            Label="{x:Static resources:AppResources.Done}"
            Value="{Binding DoneCount, Source = {x:Reference this}}"
            LabelColor="{x:Static ui:AppColors.DesaturatedBlue}"
            ValueColor="{x:Static ui:AppColors.LightBlack}" />
        <app:IndividualStateCounterView
            Label="{x:Static resources:AppResources.NotYet}"
            Value="{Binding RemainCount, Source = {x:Reference this}}"
            LabelColor="{x:Static ui:AppColors.Black}"
            ValueColor="{x:Static ui:AppColors.LightBlack}" />
        <app:IndividualStateCounterView
            Label="{x:Static resources:AppResources.StopButton}"
            Value="{Binding StopCount, Source = {x:Reference this}}"
            LabelColor="{x:Static ui:AppColors.LightRed}"
            ValueColor="{x:Static ui:AppColors.LightBlack}" />
        <app:IndividualStateCounterView
            Label="{x:Static resources:AppResources.PendingButton}"
            Value="{Binding PendingCount, Source = {x:Reference this}}"
            LabelColor="{x:Static ui:AppColors.DarkGrey2}"
            ValueColor="{x:Static ui:AppColors.LightBlack}" />
    </HorizontalStackLayout>
</ContentView>