namespace ShinKenShinKun;

public partial class MaskButtonView : RadBorder
{
    public static readonly BindableProperty IsMaskModeProperty = BindableProperty.Create(
        nameof(IsMaskMode),
        typeof(bool),
        typeof(MaskButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty ChangeModeCommandProperty = BindableProperty.Create(
        nameof(ChangeModeCommand),
        typeof(IRelayCommand),
        typeof(MaskButtonView),
        defaultBindingMode: BindingMode.TwoWay);


    public MaskButtonView()
    {
        InitializeComponent();
    }

    public bool IsMaskMode
    {
        get => (bool)GetValue(IsMaskModeProperty);
        set => SetValue(IsMaskModeProperty, value);
    }

    public IRelayCommand ChangeModeCommand
    {
        get => (IRelayCommand)GetValue(ChangeModeCommandProperty);
        set => SetValue(ChangeModeCommandProperty, value);
    }
}