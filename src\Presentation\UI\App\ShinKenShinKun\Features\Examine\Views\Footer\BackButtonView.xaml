<?xml version="1.0" encoding="utf-8" ?>
<telerik:RadBorder
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.BackButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    Style="{x:Static ui:Styles.GrayBackWidth120ButtonBorderStyle}"
    x:Name="this">
    <telerik:RadBorder.Triggers>
        <DataTrigger
            TargetType="telerik:RadBorder"
            Binding="{Binding IsEnabled, Source={x:Reference this}}"
            Value="False">
            <Setter
                Property="BackgroundColor"
                Value="{x:Static ui:AppColors.LightGrayColor}" />
        </DataTrigger>
        <DataTrigger
            TargetType="telerik:RadBorder"
            Binding="{Binding IsEnabled, Source={x:Reference this}}"
            Value="True">
            <Setter
                Property="BackgroundColor"
                Value="{x:Static ui:AppColors.GrayBack}" />
        </DataTrigger>
    </telerik:RadBorder.Triggers>
    <telerik:RadBorder.GestureRecognizers>
        <TapGestureRecognizer
            Command="{Binding BackCommand, Source={x:Reference this}}" />
    </telerik:RadBorder.GestureRecognizers>
    <VerticalStackLayout
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <Image
            Source="{x:Static ui:Icons.BackIcon}"
            Style="{x:Static ui:Styles.ImageFooterXlSizeButtonStyle}" />
        <Label
            Style="{x:Static ui:Styles.BackLabelStyle}" />
    </VerticalStackLayout>
</telerik:RadBorder>
