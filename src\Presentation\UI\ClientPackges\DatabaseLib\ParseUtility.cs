﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace System.Arctec.Ar1000k.DataBase
{
    public class ParseUtility
    {
        public static int? TryParseInt(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return int.Parse(data.ToString());
                }
            }
            catch (Exception) { }

            return null;
        }

        public static double? TryParseDouble(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return double.Parse(data.ToString());
                }
            }
            catch (Exception) { }

            return null;
        }

        public static decimal? TryParseDecimal(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return decimal.Parse(data.ToString());
                }
            }
            catch (Exception) { }

            return null;
        }

        public static long? TryParseLong(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return long.Parse(data.ToString());
                }
            }
            catch (Exception) { }

            return null;
        }

        public static bool? TryParseBool(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return Convert.ToBoolean(int.Parse(data.ToString()));
                }
            }
            catch (Exception) { }

            return null;
        }

        public static string TryParseString(object data)
        {
            try
            {
                if (data != DBNull.Value && !string.IsNullOrEmpty(data.ToString()))
                {
                    return data.ToString().Trim();
                }
            }
            catch (Exception) { }

            return null;
        }

        public static DataTable ToDataTable(XmlElement[] any_schema, XmlElement any1_diffGram)
        {
            var dataTable = new DataTable();

            try
            {
                using (var schemaStream = new MemoryStream())
                {
                    using (var writer = XmlWriter.Create(schemaStream))
                    {
                        foreach (var item in any_schema)
                        {
                            item.WriteTo(writer);
                        }
                    }
                    schemaStream.Position = 0;
                    dataTable.ReadXmlSchema(schemaStream);
                }

                using (var dataStream = new MemoryStream())
                {
                    using (var writer = XmlWriter.Create(dataStream))
                    {
                        any1_diffGram.WriteTo(writer);
                    }
                    dataStream.Position = 0;
                    dataTable.ReadXml(dataStream);
                }

                return dataTable;
            }
            catch (XmlException ex)
            {
                Console.WriteLine($"XML Exception: {ex.Message}");
            }

            return dataTable;
        }

        public static void FromDataTable(
            DataTable table,
            out XmlElement[] any_schema,
            out XmlElement any1_diffGram
        )
        {
            var doc = new XmlDocument();

            // Write schema and data into a MemoryStream
            using var stream = new MemoryStream();
            // Write both schema and diffGram into a single XML document
            table.WriteXml(stream, XmlWriteMode.DiffGram);
            stream.Position = 0;

            // Load the full XML into XmlDocument
            doc.Load(stream);

            var root = doc.DocumentElement;

            // Extract schema elements
            var schemaList = new List<XmlElement>();
            foreach (XmlNode node in root.ChildNodes)
            {
                if (node.LocalName == "schema" || node.LocalName == "xs:schema")
                {
                    schemaList.Add((XmlElement)node);
                }
            }

            any_schema = [.. schemaList];

            // Extract diffGram node (usually <diffgr:diffgram>)
            XmlElement diffGramElement = null;
            foreach (XmlNode node in root.ChildNodes)
            {
                if (node.LocalName == "diffgram" || node.Name.Contains("diffgr:diffgram"))
                {
                    diffGramElement = (XmlElement)node;
                    break;
                }
            }

            any1_diffGram = diffGramElement;
        }

        public static List<T> MapDataTableToModel<T>(DataTable table) where T : new()
        {
            var properties = typeof(T).GetProperties();
            var list = new List<T>();

            foreach (DataRow row in table.Rows)
            {
                var obj = new T();

                foreach (var prop in properties)
                {
                    if (!table.Columns.Contains(prop.Name))
                        continue;

                    var value = row[prop.Name];

                    if (value == DBNull.Value)
                    {
                        if (IsNullable(prop.PropertyType))
                            prop.SetValue(obj, null);

                        continue;
                    }

                    var targetType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                    var safeValue = Convert.ChangeType(value, targetType);
                    prop.SetValue(obj, safeValue);
                }

                list.Add(obj);
            }

            return list;
        }

        private static bool IsNullable(Type type)
        {
            if (!type.IsValueType) return true;

            return Nullable.GetUnderlyingType(type) != null;
        }
    }
}