﻿using ShinKenShinKun.Utils.Resources;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ShinKenShinKun.Utils;

public static class DictionaryHelpers
{
    public static readonly Dictionary<string, KeyAction> KeyActionDic = new()
    {
        { "Input&Enter", KeyAction.InputAndEnter },
        { "Delete&Enter", KeyAction.DeleteAndEnter },
        { "Input", KeyAction.Input },
        { "Delete", KeyAction.Delete },
        { "BackSpace", KeyAction.BackSpace },
        { "Enter", KeyAction.Enter },
        { "Cancel", KeyAction.Cancel },
    };

    public static readonly Dictionary<string, MessageType> MessageTypeDic = new()
    {
        { "I", MessageType.Information },
        { "C", MessageType.Confirm },
        { "W", MessageType.Warning },
        { "E", MessageType.Error },
    };

    public static readonly Dictionary<MessageType, Color> MessageBaseColorDic = new()
    {
        { MessageType.Information, Color.FromArgb("#3163fc")},
        { MessageType.Confirm, Color.FromArgb("#3163fc")},
        { MessageType.Warning, Color.FromArgb("#fcc135")},
        { MessageType.Error, Color.FromArgb("#fa444d")},
    };

    public static readonly Dictionary<string, MessageButtonType> ButtonTypeDic = new()
    {
        { "OK", MessageButtonType.Ok },
        { "Yes_No", MessageButtonType.YesNo },
        { "No_Yes", MessageButtonType.NoYes },
        { "OK_Cancel", MessageButtonType.OkCancel },
        { "Yes_No_Cancel", MessageButtonType.YesNoCancel },
    };

    public static readonly Dictionary<string, int> ButtonResult = new()
    {
        { AppResources.Ok, 1 },
        { AppResources.Yes, 1 },
        { AppResources.No, 2 },
        { AppResources.Cancel, 0 },
    };

    public static readonly Dictionary<MessageButtonType, List<MessageButton>> MessageButtonDic =
        new()
        {
            {
                MessageButtonType.Ok,
                [new() { Text = AppResources.Ok, Result = ButtonResult[AppResources.Ok] }]
            },
            {
                MessageButtonType.YesNo,
                [
                    new() { Text = AppResources.Yes, Result = ButtonResult[AppResources.Yes] },
                    new() { Text = AppResources.No, Result = ButtonResult[AppResources.No] },
                ]
            },
            {
                MessageButtonType.NoYes,
                [
                    new() { Text = AppResources.No, Result = ButtonResult[AppResources.No] },
                    new() { Text = AppResources.Yes, Result = ButtonResult[AppResources.Yes] },
                ]
            },
            {
                MessageButtonType.OkCancel,
                [
                    new() { Text = AppResources.Ok, Result = ButtonResult[AppResources.Ok] },
                    new()
                    {
                        Text = AppResources.Cancel,
                        Result = ButtonResult[AppResources.Cancel],
                    },
                ]
            },
            {
                MessageButtonType.YesNoCancel,
                [
                    new() { Text = AppResources.Yes, Result = ButtonResult[AppResources.Yes] },
                    new() { Text = AppResources.No, Result = ButtonResult[AppResources.No] },
                    new()
                    {
                        Text = AppResources.Cancel,
                        Result = ButtonResult[AppResources.Cancel],
                    },
                ]
            },
        };

    public static readonly Dictionary<MessageType, string> MessageIconDic = new()
    {
        { MessageType.Information, "infomation.png" },
        { MessageType.Confirm, "confirm.png" },
        { MessageType.Warning, "warning.png" },
        { MessageType.Error, "error.png" },
    };
}