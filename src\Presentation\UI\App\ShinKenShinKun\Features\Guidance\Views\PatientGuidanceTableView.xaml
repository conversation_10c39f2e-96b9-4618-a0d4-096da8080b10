<?xml version="1.0" encoding="utf-8"?>

<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.PatientGuidanceTableView"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    x:Name="this">
    <Grid>
        <telerik:RadDataGrid
            x:Name="PatientList"
            IsVisible="{Binding ItemsSource, 
                Converter={x:Static ui:AppConverters.IsListNotNullOrEmpty},
                Source={x:Reference this}}"
            Style="{x:Static ui:Styles.PatientListDataGridStyle}"
            SelectionChanged="PatientRowTableSelectionChanged"
            EmptyContentDisplayMode="ItemsSourceNullOrEmpty"
            SelectedItem="{Binding ItemSelected, 
                Source={x:Reference this}, 
                Mode=TwoWay}"
            ItemsSource="{Binding ItemsSource, 
                Source={x:Reference this}}">
            <telerik:RadDataGrid.Columns>
                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Id}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding ClientId}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width320}"
                    SizeMode="Stretch">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                MinimumWidthRequest="{x:Static ui:Dimens.Width320}"
                                Text="{x:Static resources:AppResources.KanaName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding Name}">
                                <Label.Triggers>
                                    <DataTrigger
                                        TargetType="Label"
                                        Binding="{Binding IsMaskMode, 
                                            Source={x:Reference this}}"
                                        Value="True">
                                        <Setter
                                            Property="Text"
                                            Value="{x:Static resources:AppResources.MaskText}" />
                                    </DataTrigger>
                                </Label.Triggers>
                            </Label>
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Gender}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding SexDisplay.DisplayName}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.WaitingTime}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding WaitTimeSpan, 
                                    Converter={x:Static ui:AppConverters.TimeSpanToStringConverter}}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.Reserved}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding ReserveState}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.GuidanceDestination}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding GuideState}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>

                <telerik:DataGridTextColumn
                    HeaderStyle="{x:Static ui:Styles.HeaderExaminationFrameStyle}"
                    MinimumWidth="{x:Static ui:Dimens.Width160}"
                    SizeMode="Fixed">
                    <telerik:DataGridTextColumn.HeaderContentTemplate>
                        <DataTemplate>
                            <Label
                                Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                                Text="{x:Static resources:AppResources.InExamination}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.HeaderContentTemplate>
                    <telerik:DataGridTextColumn.CellContentTemplate>
                        <DataTemplate>
                            <Label
                                x:DataType="app:ClientInformationModel"
                                Style="{x:Static ui:Styles.ContentCellLabelStyle}"
                                TextColor="{Binding ContentCellColor}"
                                Text="{Binding Checking}" />
                        </DataTemplate>
                    </telerik:DataGridTextColumn.CellContentTemplate>
                </telerik:DataGridTextColumn>
            </telerik:RadDataGrid.Columns>
        </telerik:RadDataGrid>
        <ScrollView
            IsVisible="{Binding ItemsSource, 
                Converter={x:Static ui:AppConverters.IsListNullOrEmpty},
                Source={x:Reference this}}"
            Orientation="Horizontal">
            <FlexLayout
                VerticalOptions="Start">
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Id}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    FlexLayout.Grow="1"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width320}"
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.KanaName}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Gender}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.WaitingTime}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.Reserved}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.GuidanceDestination}" />
                </telerik:RadBorder>
                <telerik:RadBorder
                    Style="{x:Static ui:Styles.HeaderEmptyContentBorderStyle}"
                    MinimumWidthRequest="{x:Static ui:Dimens.Width160}">
                    <Label
                        Style="{x:Static ui:Styles.HeaderExaminationLabelStyle}"
                        Text="{x:Static resources:AppResources.InExamination}" />
                </telerik:RadBorder>
            </FlexLayout>
        </ScrollView>
    </Grid>
</ContentView>