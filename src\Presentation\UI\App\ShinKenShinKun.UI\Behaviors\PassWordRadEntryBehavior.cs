﻿namespace ShinKenShinKun.UI;

public class PassWordRadRadEntryBehavior : Behavior<RadEntry>
{
    private static readonly Regex PasswordRegex = new(@"^[a-zA-Z0-9!@#$%^&*()\-_+=\[\]{}|;:'"",.<>?/]*$", RegexOptions.Compiled);

    protected override void OnAttachedTo(RadEntry radEntry)
    {
        base.OnAttachedTo(radEntry);
        radEntry.TextChanged += OnTextChanged;
        radEntry.IsPassword = true; // Ensure password masking
    }

    protected override void OnDetachingFrom(RadEntry radEntry)
    {
        base.OnDetachingFrom(radEntry);
        radEntry.TextChanged -= OnTextChanged;
    }

    private void OnTextChanged(object sender, TextChangedEventArgs e)
    {
        if (sender is RadEntry radEntry && !string.IsNullOrEmpty(e.NewTextValue))
        {
            // Filter text: only valid password characters allowed
            string filteredText = new string(e.NewTextValue.Where(c => PasswordRegex.IsMatch(c.ToString())).ToArray());

            if (radEntry.Text != filteredText)
            {
                radEntry.Text = filteredText;
            }
        }
    }
}
