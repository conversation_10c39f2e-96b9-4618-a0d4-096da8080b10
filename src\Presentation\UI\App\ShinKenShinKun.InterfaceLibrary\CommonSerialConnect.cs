﻿namespace ShinKenShinKun.InterfaceLibrary;

public class CommonSerialConnect : MeConnectDefine
{
    #region 変数
    // シリアルポートクラス用変数
    public System.IO.Ports.SerialPort serialPort1;
    // 受信中止用フラグ
    public bool StopFlg = false;
    // 標準やり取り時のリトライカウント
    private const int RETRYCOUNT = 3;
    #endregion

    #region コンストラクタ
    public CommonSerialConnect(int[] ConnectConfig)
    {
        // シリアルポートクラスの生成
        serialPort1 = new System.IO.Ports.SerialPort("COM1");
        // 通信速度の設定
        serialPort1.BaudRate = ConnectConfig[BaudRate];
        // データ長の設定
        serialPort1.DataBits = ConnectConfig[DataBits];
        // ストップビットの設定
        serialPort1.StopBits = (System.IO.Ports.StopBits)ConnectConfig[StopBits];
        // パリティの設定
        serialPort1.Parity = (System.IO.Ports.Parity)ConnectConfig[Parity];
        //serialPort1.Handshake = System.IO.Ports.Handshake.RequestToSend;
        // ハンドシェイクの設定
        if (ConnectConfig[HandShake] == 0)
        {
            serialPort1.Handshake = System.IO.Ports.Handshake.None;
        }
        else if (ConnectConfig[HandShake] == 1)
        {
            serialPort1.Handshake = System.IO.Ports.Handshake.RequestToSend;
        }
        else if (ConnectConfig[HandShake] == 2)
        {
            serialPort1.Handshake = System.IO.Ports.Handshake.RequestToSendXOnXOff;
        }
        else if (ConnectConfig[HandShake] == 3)
        {
            serialPort1.Handshake = System.IO.Ports.Handshake.XOnXOff;
        }
        // RTS信号を有効にする。
        serialPort1.RtsEnable = true;
        // DTR信号を有効にする。
        serialPort1.DtrEnable = true;

        // シリアルポートの受信タイムアウトを100mm秒に設定する
        serialPort1.ReadTimeout = 100;
    }
    #endregion

    #region Com_Port_Open
    public bool Com_Port_Open(int portNumber)
    {
        // パラメータのポート番号を取得する
        string newPortName = "COM" + portNumber.ToString();
        // ポート番号が"COM1"で無かった場合は、ポート番号を変更する
        if (serialPort1.PortName != newPortName)
        {
            serialPort1.PortName = newPortName;
        }

        // ポートが閉じていれば、開く
        if (!serialPort1.IsOpen)
        {
            serialPort1.Open();
        }

        // ポートが開いていれば、成功を返す
        if (serialPort1.IsOpen)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    #endregion

    #region Com_Port_Open_Custom
    /// <remarks>
    /// リトライ在り
    /// デフォルトは3回　それ以上はConfigの”PortOpenReTryCou”で設定
    /// </remarks>
    public bool Com_Port_Open_Custom(int portNumber)
    {
        // パラメータのポート番号を取得する
        string newPortName = "COM" + portNumber.ToString();
        // ポート番号が"COM1"で無かった場合は、ポート番号を変更する
        if (serialPort1.PortName != newPortName)
        {
            serialPort1.PortName = newPortName;
        }
        // ポートが閉じていれば、開く
        if (!serialPort1.IsOpen)
        {
            int ReTryCou = 3;
            string PortOpenReTryCou = ConfigReadUtility.GetConfigData("PortOpenReTryCou");
            if (PortOpenReTryCou != null)
            {
                try
                {
                    ReTryCou = Convert.ToInt16(PortOpenReTryCou);
                }
                catch { }
            }
            int i = 0;
            ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Open:" + serialPort1.PortName + "は閉じられている為、Open処理開始");
            while (true)
            {
                try
                {
                    serialPort1.Open();
                    ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Open:" + serialPort1.PortName + "のOpen処理成功");
                    break;
                }
                catch (Exception e)
                {
                    if (ReTryCou-- == 0)
                    {
                        ClientLogger.GetInstance().Write(e.StackTrace.ToString());
                        throw e;
                    }
                    else
                    {
                        i++;
                        ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Open:" + serialPort1.PortName + "失敗" + i + "回目");
                        Thread.Sleep(500);
                    }
                }
            }
        }
        else
        {
            ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Open:" + serialPort1.PortName + "は既に開かれています");
        }
        // ポートが開いていれば、成功を返す
        if (serialPort1.IsOpen)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    #endregion

    #region Com_Port_Close
    public bool Com_Port_Close(int portNumber)
    {
        // パラメータのポート番号を取得する
        string newPortName = "COM" + portNumber.ToString();

        // ポート番号が"COM1"で無かった場合は、ポート番号を変更する
        if (serialPort1.PortName != newPortName)
        {
            serialPort1.PortName = newPortName;
        }
        // ポートが開いていれば、閉じる
        if (serialPort1.IsOpen)
        {
            try
            {
                ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Close:" + serialPort1.PortName + "は開かれている為、Close処理開始");
                //(new System.Threading.Thread(AccessTimeout)).Start();
                serialPort1.Close();
                if (!serialPort1.IsOpen)
                {
                    ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Close:" + serialPort1.PortName + "のClose処理成功");
                }
                else
                {
                    ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Close:" + serialPort1.PortName + "のClose処理失敗");
                }
            }
            catch (Exception e)
            {
                ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Close:" + serialPort1.PortName + "のClose処理失敗_2");
                ClientLogger.GetInstance().Write(e.StackTrace.ToString());
                throw e;
            }
        }
        else
        {
            ClientLogger.GetInstance().Write("【SerialConnect】", "Port_Close:" + serialPort1.PortName + "は既に閉じられています");
        }
        if (serialPort1.IsOpen)
        {
            return false;
        }
        else
        {
            return true;
        }
    }
    #endregion

    #region Com_RcvData
    /// <summary>
    /// ★232C 通信でデータの受信待ちを行う
    /// 設定されたデータ最大長にまで達したら、受信終了する。
    /// </summary>
    /// <remarks>
    /// 設定値に基づき、データの受信を行う。
    /// 受信中止関数が呼ばれた場合、CancelExceptionを発生させる。
    /// 転送ボタン関数が呼ばれた場合、nullを返却する。
    /// </remarks>
    public string Com_RcvData(int[] receiveSetting)
    {
        ArrayList Al = new ArrayList();
        Al.Clear();
        // データ最大長を取得
        int DataMaxLength = receiveSetting[RECEIVE_DATALENGTH];
        // シリアルポートのゴミバッファを削除
        serialPort1.DiscardInBuffer();
        serialPort1.ReadExisting();
        // シリアルポートの受信タイムアウトを100mm秒に設定する
        serialPort1.ReadTimeout = 100;
        StopFlg = false;
        bool bDataStart = false;

        while (true)
        {
            try
            {
                if (bDataStart == false)
                {
                    // 「STX」を受信したら開始
                    if (receiveSetting[RECEIVE_STARTCODE] == RECEIVE_START_STX)
                    {
                        if (Convert.ToByte(serialPort1.ReadByte()) == STX)
                        {
                            Al.Add(STX);
                            bDataStart = true;
                        }
                    }
                    else
                    {
                        bDataStart = true;
                    }
                }
                // データ受信フラグがTRUEの場合なら、受信する
                if (bDataStart == true)
                {
                    // Al.Add(Convert.ToByte(serialPort1.ReadChar()));
                    Al.Add(Convert.ToByte(serialPort1.ReadByte()));

                    // 受信データ最大長をオーバーしていたら、エラーを返却する
                    if (DataMaxLength < Al.Count)
                    {
                        throw new ErrorDataException("受信データ長オーバー");
                    }
                    // 「指定受信長」受信したら終了
                    if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_MAXSIZE)
                    {
                        if (DataMaxLength == Al.Count)
                        {
                            break;
                        }
                    }
                    // 「ETX」 を受信したら終了
                    else if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_GET_ETX)
                    {
                        if ((byte)Al[Al.Count - 1] == ETX)
                        {
                            break;
                        }
                    }
                    // 「CRLF」を受信したら終了
                    else if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_GET_CRLF)
                    {
                        if ((byte)Al[Al.Count - 1] == LF && (byte)Al[Al.Count - 2] == CR)
                        {
                            break;
                        }
                    }
                    // 「CR」を受信したら終了
                    else if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_GET_CR)
                    {
                        if ((byte)Al[Al.Count - 1] == CR)
                        {
                            break;
                        }
                    }
                    // 「LF」を受信したら終了
                    else if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_GET_LF)
                    {
                        if ((byte)Al[Al.Count - 1] == LF)
                        {
                            break;
                        }
                    }
                    // 「EOT」を受信したら終了
                    else if (receiveSetting[RECEIVE_ENDCODE] == RECEIVE_GET_EOT)
                    {
                        if ((byte)Al[Al.Count - 1] == EOT)
                        {
                            break;
                        }
                    }
                }
            }
            catch (TimeoutException)
            {
                // 中止ボタン押下時、CancelExceptionを発生させる。
                if (StopFlg == true)
                {
                    throw new CancelException();
                }
                continue;
            }
        }
        // ArrayListからbyte配列に変換
        byte[] bCmd = new byte[Al.Count];
        Array.Copy(Al.ToArray(), bCmd, Al.Count);
        // byte配列をString文字列に変換
        return Encoding.ASCII.GetString(bCmd, 0, bCmd.Length);
    }
    #endregion

    #region 受信を中止する
    /// <remarks>
    /// 受信中止フラグをONにする。
    /// 受信ロジック側で、CancelExceptionが発生する。
    /// </remarks>
    public void Com_ReceiveStop()
    {
        serialPort1.ReadTimeout = 1;
        StopFlg = true;
    }
    #endregion

    #region Com_SendCmd
    /// <summary>
    /// ★232C 通信でデータの送信を行う
    /// 設定されたデータ最大長にまで達したら、受信終了する。
    /// byte型の配列を送信する。
    /// </summary>
    public bool Com_SendCmd(byte[] SendData)
    {
        try
        {
            serialPort1.DiscardOutBuffer();
            serialPort1.Write(SendData, 0, SendData.Length);
            return true;
        }
        catch (Exception)
        {
            throw new SendErrorException(encSjis.GetString(SendData, 0, SendData.Length));
        }
    }
    #endregion

    #region Com_SendCmd
    /// <summary>
    /// ★232C 通信でデータの送信を行う
    /// 設定されたデータ最大長にまで達したら、受信終了する。
    /// string型の文字列を送信する。
    /// </summary>
    /// <param name="Data">値格納用配列</param>
    public bool Com_SendCmd(string SendData)
    {
        try
        {
            serialPort1.DiscardOutBuffer();
            serialPort1.Write(SendData);
            return true;
        }
        catch (Exception)
        {
            throw new SendErrorException(SendData);
        }
    }
    #endregion

    #region Com_SendCmdBasic
    /// <summary>
    /// ★232C 通信でデータの送信を行う
    /// ARCTEC標準仕様のやり取りを行う。
    /// string型の文字列を送信する。
    /// </summary>
    /// <param name="Data">値格納用配列</param>
    public bool Com_SendCmdBasic(byte[] SendData)
    {
        byte[] reply = new byte[1];　// ACK,EOT返答用配列
        ArrayList Al = new ArrayList();
        int sendCount = 0;

        try
        {
            Al.Clear();
            serialPort1.ReadTimeout = 2000;

            serialPort1.BaudRate = 9600;
            serialPort1.DataBits = 8;
            serialPort1.StopBits = (System.IO.Ports.StopBits)1;
            serialPort1.Parity = (System.IO.Ports.Parity)0;

            serialPort1.DiscardInBuffer();
            serialPort1.DiscardOutBuffer();

            StopFlg = false;
            sendCount = 0;
            serialPort1.Write(SendData, 0, SendData.Length);
            sendCount++;
            while (true)
            {
                try
                {
                    Al.Add(Convert.ToByte(serialPort1.ReadChar()));
                    // ACK を受信したらEOTを返信して終了
                    if ((byte)Al[Al.Count - 1] == ACK)
                    {
                        reply[0] = EOT;
                        Thread.Sleep(1000);
                        serialPort1.Write(encSjis.GetString(reply, 0, reply.Length));
                        return true;
                    }
                    else if ((byte)Al[Al.Count - 1] == NAK)
                    {
                        // 20091228 不具合修正 Add Start
                        if (sendCount == RETRYCOUNT)
                        {
                            break;
                        }
                        // 20091228 不具合修正 Add End
                        serialPort1.DiscardOutBuffer();
                        Thread.Sleep(1000);
                        serialPort1.Write(SendData, 0, SendData.Length);
                        sendCount++;
                        serialPort1.ReadTimeout = 2000;
                        continue;
                    }
                }
                catch (TimeoutException)
                {
                    if (StopFlg == true)
                    {
                        throw new CancelException();
                    }
                    if (sendCount == RETRYCOUNT)
                    {
                        break;
                    }
                    serialPort1.DiscardOutBuffer();
                    Thread.Sleep(1000);
                    serialPort1.Write(SendData, 0, SendData.Length);
                    sendCount++;
                    continue;
                }
            }
            throw new TimeOutException();
        }
        catch (CancelException)
        {
            throw new CancelException();
        }
        catch (TimeOutException)
        {
            throw new TimeOutException();
        }
        catch (Exception)
        {
            throw new SendErrorException(encSjis.GetString(SendData, 0, SendData.Length));
        }
    }
    #endregion
}
