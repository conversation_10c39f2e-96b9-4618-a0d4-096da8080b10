namespace ShinKenShinKun.UI;

public partial class AreaControl : Grid
{
    #region BindableProperty

    public static readonly BindableProperty AreaProperty = BindableProperty.Create(
        nameof(Area),
        typeof(AreaModel),
        typeof(AreaControl));

    #endregion BindableProperty

    public AreaControl()
    {
        Loaded += OnLoaded;
    }

    #region Property

    public AreaModel Area
    {
        get => (AreaModel)GetValue(AreaProperty);
        set => SetValue(AreaProperty, value);
    }

    #endregion Property

    #region Private Method

    private void OnLoaded(object? sender, EventArgs e)
    {
        Margin = Area.MarginDisplay;
        if(Area.Width != null)
        {
            WidthRequest = (double)Area.Width;
        }

        HeightRequest = Area.Height;
        VerticalOptions = Area.VerticalOptions == LayoutOptions.Start
            ? Area.VerticalOptions
            : new LayoutOptions(Area.VerticalOptions.Alignment, true);
        HorizontalOptions = Area.HorizontalOptions;

        foreach(var ratio in Area.RowRatiosDisplay)
        {
            RowDefinitions.Add(new RowDefinition { Height = new GridLength(ratio, GridUnitType.Star) });
        }

        foreach(var ratio in Area.ColumnRatiosDisplay)
        {
            ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(ratio, GridUnitType.Star) });
        }

        for(var i = 0; i < Area.ComponentLayouts.Count; i++)
        {
            var view = GetComponent(Area.ComponentLayouts[i], i);
            if(view == null)
            {
                continue;
            }

            view.Margin = Area.ComponentLayouts[i].MarginDisplay;
            view.VerticalOptions = Area.ComponentLayouts[i].VerticalOptions;
            view.HorizontalOptions = Area.ComponentLayouts[i].HorizontalOptions;

            Grid.SetRow(view, Area.ComponentLayouts[i].RowIndex);
            Grid.SetColumn(view, Area.ComponentLayouts[i].ColumnIndex);

            if(Area.ComponentLayouts[i].RowSpan > 1)
            {
                Grid.SetRowSpan(view, Area.ComponentLayouts[i].RowSpan);
            }

            if(Area.ComponentLayouts[i].ColumnSpan > 1)
            {
                Grid.SetColumnSpan(view, Area.ComponentLayouts[i].ColumnSpan);
            }

            Children.Add(view);
        }
    }

    private View? GetComponent(ComponentLayoutModel component, int index)
    {
        var data = (ComponentLayoutCombineModel)component;
        switch(data.ComponentType)
        {
            case ComponentType.StaticLabel:
                return new StaticLabelControl
                {
                    Text = data.Text
                };

            case ComponentType.TestInputResultLabel:
            {
                var view = new LabelInputResultControl
                {
                    Title = data.Title,
                    DisplayMode = data.DisplayMode,
                    Type = data.LabelResultType,
                    Categories = data.CategoryDisplays,
                    SettingNumberPadId = data.SettingNumberPadId,
                    InputFormat = data.InputFormat,
                    LabelColor = data.LabelColor,
                    IsReadOnly = data.IsReadOnly,
                    TextColor = data.TextColor,
                    UseDirectInput = data.UseDirectInput,
                };
                view.SetBinding(LabelInputResultControl.ValueProperty,
                    new Binding($"Area.ComponentLayouts[{index}].Value", source: this));

                return view;
            }
            case ComponentType.TestInputMultiLabel:
            {
                var view = new MultiLineLabelControl();
                view.SetBinding(MultiLineLabelControl.TextProperty,
                    new Binding($"Area.ComponentLayouts[{index}].Text", source: this));
                return view;
            }
            case ComponentType.PastValueLabel:
            {
                var view = new LabelShowPastValueControl
                {
                    DisplayMode = data.DisplayMode,
                    Title = data.Title
                };
                view.SetBinding(LabelShowPastValueControl.ValueProperty,
                    new Binding($"Area.ComponentLayouts[{index}].Value", source: this));
                return view;
            }
            case ComponentType.StateButton:
            {
                var view = new StateButtonControl
                {
                    TextStyle = Styles.TextStateButtonStyle,
                    BorderStyle = Styles.BorderStateButtonStyle,
                    StateStyle = Styles.StateButtonStyle,
                };
                view.SetBinding(StateButtonControl.TextProperty,
                    new Binding($"Area.ComponentLayouts[{index}].Text", source: this));
                view.SetBinding(StateButtonControl.StatusListProperty,
                    new Binding($"Area.ComponentLayouts[{index}].StatusList", source: this));
                view.SetBinding(StateButtonControl.CurrentStateProperty,
                    new Binding($"Area.ComponentLayouts[{index}].CurrentState", source: this));
                return view;
            }
            case ComponentType.FunctionButton:
                return new FunctionButtonControl
                {
                    Text = data.Text,
                };

            case ComponentType.MultiSelectTable:
            {
                var view = new MultiSelectTableControl
                {
                    TableConfig = new MultiSelectTableConfigModel
                    {
                        NumberOfMeasurements = data.NumberOfMeasurements,
                        NumberOfSelection = data.NumberOfSelection,
                        IsAverageResultDisplay = data.IsAverageResultDisplay,
                        IsOldResultDisplay = data.IsOldResultDisplay,
                        IsHorizontalLayout = data.IsHorizontalLayout,
                    },
                    SettingNumberPadId = data.SettingNumberPadId,
                    InputFormat = data.InputFormat,
                };
                view.SetBinding(MultiSelectTableControl.TestItemDtoProperty,
                    new Binding($"Area.ComponentLayouts[{index}].Items", source: this));

                return view;
            }
            default:
                return null;
        }
    }

    #endregion Private Method
}