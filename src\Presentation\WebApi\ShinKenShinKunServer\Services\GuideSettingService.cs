﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.GuideSupportDataSetTableAdapters;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Text;
using System.Transactions;

namespace ShinKenShinKunServer.Services
{
    public class GuideSettingService
    {
        GuideSupportDataSet GuideDataSet = new GuideSupportDataSet();

        private GuideSupportService srv;
        private Logger logger = null;

        public GuideSettingService()
        {
            srv = new GuideSupportService();
        }

        #region IniGuideMaster　１レコード取得
        /// <summary>
        /// IniGuideMaster　１レコード取得
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        public string[] StrGetIniGuideMaster(string sIp)
        {
            try
            {
                //動的配列を用意する。
                ArrayList ipArray = new ArrayList();
                //IP再作成用
                StringBuilder sb = new StringBuilder();
                //IPを配列二分割する
                string[] sIpArr = sIp.Split('.');  // "aaa", " bbb", " ccc", " ddd"

                for (int i = 0; i < 4; i++)
                {
                    if (sIpArr[i].Length != 3)
                    {
                        sb.Append(sIpArr[i].PadLeft(3, '0').ToString());
                    }
                    else
                    {
                        sb.Append(sIpArr[i].ToString());
                    }
                    if (i < 3)
                    {
                        sb.Append('.');
                    }
                }
                //完成したIPを設定する
                sIp = sb.ToString();
                //データセットの関数をコールする。

                GuideDataSet = srv.GetIniGuideMaster(sIp);

                //日付・当日ID・区分をキーに検索し、その行を取得する。
                if (GuideDataSet.Tables["IniGuideMaster"].Rows.Count != 0)
                {
                    DataRow row = GuideDataSet.Tables["IniGuideMaster"].Rows[0];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        ipArray.Add(row["IPAddress"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["IniGuideFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["LastUpdate"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
                // ArrayListからString配列に変換
                string[] ipList = new string[ipArray.Count];
                Array.Copy(ipArray.ToArray(), ipList, ipArray.Count);
                return ipList;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region IniGuideMaster　全レコード取得
        /// <summary>
        /// IniGuideMaster　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetIniGuideMasterAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList ipArray = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetIniGuideMasterAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["IniGuideMaster"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["IniGuideMaster"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        ipArray.Add(row["IPAddress"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["IniGuideFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["LastUpdate"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[ipArray.Count];
                    Array.Copy(ipArray.ToArray(), ipList, ipArray.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    ipArray.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region TermGuideData　１レコード取得
        /// <summary>
        /// TermGuideData　１レコード取得
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        public string[] StrGetTermGuideData(string sIp)
        {
            try
            {
                //動的配列を用意する。
                ArrayList ipArray = new ArrayList();
                //IP再作成用
                StringBuilder sb = new StringBuilder();
                //IPを配列二分割する
                string[] sIpArr = sIp.Split('.');  // "aaa", " bbb", " ccc", " ddd"

                for (int i = 0; i < 4; i++)
                {
                    if (sIpArr[i].Length != 3)
                    {
                        sb.Append(sIpArr[i].PadLeft(3, '0').ToString());
                    }
                    else
                    {
                        sb.Append(sIpArr[i].ToString());
                    }
                    if (i < 3)
                    {
                        sb.Append('.');
                    }
                }
                //完成したIPを設定する
                sIp = sb.ToString();
                //データセットの関数をコールする。

                GuideDataSet = srv.GetTermGuideData(sIp);

                //日付・当日ID・区分をキーに検索し、その行を取得する。
                if (GuideDataSet.Tables["TermGuideData"].Rows.Count != 0)
                {
                    DataRow row = GuideDataSet.Tables["TermGuideData"].Rows[0];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        ipArray.Add(row["IPAddress"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["GuideFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["TermFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["LastUpdate"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
                // ArrayListからString配列に変換
                string[] ipList = new string[ipArray.Count];
                Array.Copy(ipArray.ToArray(), ipList, ipArray.Count);
                return ipList;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region TermGuideData　全レコード取得
        /// <summary>
        /// TermGuideData　全レコード取得
        /// </summary>
        /// <returns></returns>
        public PluralRecord[] StrGetTermGuideDataAll()
        {
            try
            {
                //動的配列を用意する。
                ArrayList ipArray = new ArrayList();
                //データセットの関数をコールする。
                GuideDataSet = srv.GetTermGuideDataAll();

                // 取得したレコード数をチェックする。
                int recordCount = GuideDataSet.Tables["TermGuideData"].Rows.Count;

                // 複数レコード返却クラスの配列を作成する
                PluralRecord[] pRecord = new PluralRecord[recordCount];

                // レコード数分、処理を繰り返す
                for (int j = 0; j < recordCount; j++)
                {
                    //日付・当日ID・区分をキーに検索し、その行を取得する。

                    DataRow row = GuideDataSet.Tables["TermGuideData"].Rows[j];
                    if (row != null)
                    {
                        // 検索した情報を配列に追加していく
                        ipArray.Add(row["IPAddress"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["GuideFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["TermFlg"].ToString().PadLeft(1, ' '));
                        ipArray.Add(row["LastUpdate"].ToString().PadLeft(1, ' '));
                    }
                    else
                    {
                        return null;
                    }
                    // ArrayListからString配列に変換
                    string[] ipList = new string[ipArray.Count];
                    Array.Copy(ipArray.ToArray(), ipList, ipArray.Count);
                    pRecord[j] = new PluralRecord(ipList);
                    ipArray.Clear();
                }
                return pRecord;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion

        #region TermGuideData　更新
        /// <summary>
        /// TermGuideData　更新
        /// </summary>
        /// <param name="sIp"></param>
        /// <param name="termID"></param>
        /// <param name="beforeTermID"></param>
        /// <returns></returns>
        public bool StrSetTermGuideData(string sIp, string gidFlg, string termFlg)
        {
            bool retryFlag = false;
            int retryCount = 0;

            try
            {
                //動的配列を用意する。
                ArrayList ipArray = new ArrayList();
                //IP再作成用
                StringBuilder sb = new StringBuilder();
                //IPを配列二分割する
                string[] sIpArr = sIp.Split('.');  // "aaa", " bbb", " ccc", " ddd"

                for (int i = 0; i < 4; i++)
                {
                    if (sIpArr[i].Length != 3)
                    {
                        sb.Append(sIpArr[i].PadLeft(3, '0').ToString());
                    }
                    else
                    {
                        sb.Append(sIpArr[i].ToString());
                    }
                    if (i < 3)
                    {
                        sb.Append('.');
                    }

                }
                //完成したIPを設定する
                sIp = sb.ToString();

                do
                {
                    try
                    {
                        // トランザクションの開始処理を行う
                        using (TransactionScope tx = new TransactionScope())
                        {
                            DataRow row = null;

                            //日付(yyyymmddHHmmss形式)をDateTime型に変換する
                            string strNowTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                            DateTime nowTime = DateTime.ParseExact(strNowTime.Substring(0, 14), "yyyyMMddHHmmss", DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None);

                            //各データを、更新する行に上書きしていく。行がない場合は新規作成する。
                            GuideDataSet = srv.GetTermGuideData(sIp);

                            if (GuideDataSet.Tables["TermGuideData"].Rows.Count == 1)
                            {
                                row = GuideDataSet.Tables["TermGuideData"].Rows[0];

                                // 検査開始時刻により()更新処理をスキップする
                                if (!row["LastUpdate"].Equals(DBNull.Value) && DateTime.Compare((DateTime)row["LastUpdate"], nowTime) > 0)
                                {
                                    //throw new ControllerSkipExceptioncs();
                                }
                            }
                            else if (GuideDataSet.Tables["TermGuideData"].Rows.Count == 0)
                            {
                                row = GuideDataSet.Tables["TermGuideData"].NewRow();
                            }
                            row["IPAddress"] = sIp;
                            row["GuideFlg"] = gidFlg;
                            if (termFlg != null)
                            {
                                row["TermFlg"] = termFlg;
                            }
                            row["LastUpdate"] = nowTime;

                            if (GuideDataSet.Tables["TermGuideData"].Rows.Count == 0)
                            {
                                GuideDataSet.Tables["TermGuideData"].Rows.Add(row);
                            }
                            //健診状況の更新を行う。

                            TermGuideDataTableAdapter adpt
                                = new TermGuideDataTableAdapter();
                            adpt.Update(GuideDataSet.TermGuideData);
                            // リトライ判断フラグをOFFにする
                            retryFlag = false;
                            // トランザクションのコミットを行う
                            tx.Complete();
                            return true;
                        }
                    }
                    catch (ControllerDatabaseExceptioncs dbexp)
                    {
                        // リトライカウントを1UPさせる
                        retryCount++;
                        System.Threading.Thread.Sleep(200);
                        // リトライ判断フラグをONにする
                        retryFlag = true;
                        logger = Logger.GetInstance();
                        logger.logWrite(dbexp.ToString());
                    }
                    // リトライフラグがOFF(=更新成功)もしくは3回更新処理のリトライに失敗すると終了する
                } while (retryFlag && retryCount < 3);
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception ex)
            {
                logger = Logger.GetInstance();
                logger.logWrite(ex.ToString());
            }
            return false;
        }
        #endregion

        #region ExamSettingMaster　１レコード取得
        /// <summary>
        /// ExamSettingMaster　１レコード取得
        /// </summary>
        /// <param name="sIp"></param>
        /// <returns></returns>
        public string[] StrGetExamSettingMaster(short medicalCheckNo)
        {
            try
            {
                //動的配列を用意する。
                ArrayList array = new ArrayList();

                GuideDataSet = srv.GetExamSettingMaster(medicalCheckNo);

                DataRow row = GuideDataSet.Tables["ExamSettingMaster"].Rows[0];

                //日付・当日ID・区分をキーに検索し、その行を取得する。
                if (row != null)
                {
                    // 検索した情報を配列に追加していく
                    array.Add(row["ExamSettingId"].ToString().PadLeft(1, ' '));
                    array.Add(row["MedicalCheckNo"].ToString().PadLeft(1, ' '));
                    array.Add(row["ExamTime"].ToString().PadLeft(1, ' '));
                    array.Add(row["WaitTime"].ToString().PadLeft(1, ' '));
                    array.Add(row["WaitPeople"].ToString().PadLeft(1, ' '));
                    array.Add(row["LastUpdate"].ToString().PadLeft(1, ' '));
                }
                else
                {
                    return null;
                }

                // ArrayListからString配列に変換
                string[] datalist = new string[array.Count];
                Array.Copy(array.ToArray(), datalist, array.Count);
                return datalist;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
        #endregion
    }
}