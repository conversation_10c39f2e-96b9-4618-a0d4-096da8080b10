﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35521.163
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun", "src\Presentation\UI\App\ShinKenShinKun\ShinKenShinKun.csproj", "{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.CoreMVVM", "src\Presentation\UI\App\ShinKenShinKun.CoreMVVM\ShinKenShinKun.CoreMVVM.csproj", "{630EE80F-4784-4E95-B6A9-9206C0657999}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.DataAccess", "src\Presentation\UI\App\ShinKenShinKun.DataAccess\ShinKenShinKun.DataAccess.csproj", "{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.UI", "src\Presentation\UI\App\ShinKenShinKun.UI\ShinKenShinKun.UI.csproj", "{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.Utils", "src\Presentation\UI\App\ShinKenShinKun.Utils\ShinKenShinKun.Utils.csproj", "{4A5FA1EC-75CB-418C-A840-02C4454F61CE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{89A5890B-FB64-4C2B-8C1D-A50BB3881B2C}"
	ProjectSection(SolutionItems) = preProject
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "App", "App", "{05B4D7D9-02CA-47C3-98F9-E7737097F515}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{BA8C135C-E30C-4BE8-847C-33863AB158D0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{1F13749E-20D3-4E3A-82D3-44F927025F7E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Shared", "src\Common\Shared\Shared.csproj", "{EB2C75CE-644F-B126-4C74-73364D1B4D4C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Application", "src\Core\Application\Application.csproj", "{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Domain", "src\Core\Domain\Domain.csproj", "{73A0E841-8070-20B3-947F-EC7DDAB1B405}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{6014FFE4-1446-46C5-A1F5-74DB5B6BAA7B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKunTests", "src\Tests\ShinKenShinKunTests\ShinKenShinKunTests.csproj", "{D27E2310-198F-1CDE-1779-259E08C974C0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{82929887-DA7B-41D7-A45A-79DCAB090E75}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure", "src\Infrastructure\Infrastructure.csproj", "{3D745DFE-AF9A-E111-755D-FD3FFF280728}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ClientPackages", "ClientPackages", "{1DB454C3-A9F6-4058-B25C-EEF542BD327E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{95A1A143-4942-4B6F-B7A1-B6B3A0E9EBD5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebApi", "WebApi", "{B6784336-78F5-488E-B264-0B9D332CA98A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKunServer", "src\Presentation\WebApi\ShinKenShinKunServer\ShinKenShinKunServer.csproj", "{2B88F456-8E47-42AE-98E8-97AF81599801}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.InterfaceLibrary", "src\Presentation\UI\App\ShinKenShinKun.InterfaceLibrary\ShinKenShinKun.InterfaceLibrary.csproj", "{8572B43C-4F7E-427F-9782-DD8A50A98152}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShinKenShinKun.Common", "src\Presentation\UI\App\ShinKenShinKun.Common\ShinKenShinKun.Common.csproj", "{D33A3541-158D-414A-BE25-38A1C0CA7678}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DatabaseLib", "src\Presentation\UI\ClientPackges\DatabaseLib\DatabaseLib.csproj", "{5EB3695B-71D3-4506-AB69-0C951902AF04}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{630EE80F-4784-4E95-B6A9-9206C0657999}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{630EE80F-4784-4E95-B6A9-9206C0657999}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{630EE80F-4784-4E95-B6A9-9206C0657999}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{630EE80F-4784-4E95-B6A9-9206C0657999}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53}.Release|Any CPU.Build.0 = Release|Any CPU
		{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A5FA1EC-75CB-418C-A840-02C4454F61CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A5FA1EC-75CB-418C-A840-02C4454F61CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A5FA1EC-75CB-418C-A840-02C4454F61CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A5FA1EC-75CB-418C-A840-02C4454F61CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB2C75CE-644F-B126-4C74-73364D1B4D4C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB2C75CE-644F-B126-4C74-73364D1B4D4C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB2C75CE-644F-B126-4C74-73364D1B4D4C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB2C75CE-644F-B126-4C74-73364D1B4D4C}.Release|Any CPU.Build.0 = Release|Any CPU
		{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{73A0E841-8070-20B3-947F-EC7DDAB1B405}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{73A0E841-8070-20B3-947F-EC7DDAB1B405}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{73A0E841-8070-20B3-947F-EC7DDAB1B405}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{73A0E841-8070-20B3-947F-EC7DDAB1B405}.Release|Any CPU.Build.0 = Release|Any CPU
		{D27E2310-198F-1CDE-1779-259E08C974C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D27E2310-198F-1CDE-1779-259E08C974C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D27E2310-198F-1CDE-1779-259E08C974C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D27E2310-198F-1CDE-1779-259E08C974C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D745DFE-AF9A-E111-755D-FD3FFF280728}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D745DFE-AF9A-E111-755D-FD3FFF280728}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D745DFE-AF9A-E111-755D-FD3FFF280728}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D745DFE-AF9A-E111-755D-FD3FFF280728}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B88F456-8E47-42AE-98E8-97AF81599801}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B88F456-8E47-42AE-98E8-97AF81599801}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B88F456-8E47-42AE-98E8-97AF81599801}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B88F456-8E47-42AE-98E8-97AF81599801}.Release|Any CPU.Build.0 = Release|Any CPU
		{8572B43C-4F7E-427F-9782-DD8A50A98152}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8572B43C-4F7E-427F-9782-DD8A50A98152}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8572B43C-4F7E-427F-9782-DD8A50A98152}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8572B43C-4F7E-427F-9782-DD8A50A98152}.Release|Any CPU.Build.0 = Release|Any CPU
		{D33A3541-158D-414A-BE25-38A1C0CA7678}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D33A3541-158D-414A-BE25-38A1C0CA7678}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D33A3541-158D-414A-BE25-38A1C0CA7678}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D33A3541-158D-414A-BE25-38A1C0CA7678}.Release|Any CPU.Build.0 = Release|Any CPU
		{5EB3695B-71D3-4506-AB69-0C951902AF04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5EB3695B-71D3-4506-AB69-0C951902AF04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5EB3695B-71D3-4506-AB69-0C951902AF04}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5EB3695B-71D3-4506-AB69-0C951902AF04}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{76B3A8A3-662B-42B2-AC0B-C19BB3CEC3A2} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{630EE80F-4784-4E95-B6A9-9206C0657999} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{C7A81BF5-ABF7-49A8-A9C9-735EF0AE5D53} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{D63B7A63-BB1B-41FF-8A53-DCA0301A48A2} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{4A5FA1EC-75CB-418C-A840-02C4454F61CE} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{05B4D7D9-02CA-47C3-98F9-E7737097F515} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{EB2C75CE-644F-B126-4C74-73364D1B4D4C} = {BA8C135C-E30C-4BE8-847C-33863AB158D0}
		{018D89FD-2F13-DA0D-EA7A-4C78767AEC3C} = {1F13749E-20D3-4E3A-82D3-44F927025F7E}
		{73A0E841-8070-20B3-947F-EC7DDAB1B405} = {1F13749E-20D3-4E3A-82D3-44F927025F7E}
		{D27E2310-198F-1CDE-1779-259E08C974C0} = {6014FFE4-1446-46C5-A1F5-74DB5B6BAA7B}
		{3D745DFE-AF9A-E111-755D-FD3FFF280728} = {82929887-DA7B-41D7-A45A-79DCAB090E75}
		{1DB454C3-A9F6-4058-B25C-EEF542BD327E} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{95A1A143-4942-4B6F-B7A1-B6B3A0E9EBD5} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{B6784336-78F5-488E-B264-0B9D332CA98A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{2B88F456-8E47-42AE-98E8-97AF81599801} = {B6784336-78F5-488E-B264-0B9D332CA98A}
		{8572B43C-4F7E-427F-9782-DD8A50A98152} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{D33A3541-158D-414A-BE25-38A1C0CA7678} = {05B4D7D9-02CA-47C3-98F9-E7737097F515}
		{5EB3695B-71D3-4506-AB69-0C951902AF04} = {1DB454C3-A9F6-4058-B25C-EEF542BD327E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2869DB92-48F1-435B-8BEA-28D30F1A0934}
	EndGlobalSection
EndGlobal
