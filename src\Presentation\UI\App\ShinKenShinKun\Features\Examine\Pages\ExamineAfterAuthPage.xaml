<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExamineAfterAuthPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:mtk="clr-namespace:MemoryToolkit.Maui;assembly=MemoryToolkit.Maui"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:converter="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    mtk:LeakMonitorBehavior.Cascade="True"
    mtk:TearDownBehavior.Cascade="False"
    x:DataType="app:ExamineAfterAuthViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.HeaderColor}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.ExaminationScreen}"
            MedicalMachineName="{Binding MedicalMachineName}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <Grid
                Grid.Column="1"
                Padding="{x:Static ui:Dimens.SpacingSm2}"
                RowSpacing="{x:Static ui:Dimens.SpacingSm2}"
                RowDefinitions="*,auto">
                <telerik:RadBorder
                    Grid.Row="0"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <ScrollView
                        x:Name="AreaLayout"
                        Orientation="Vertical" />
                </telerik:RadBorder>

                <telerik:RadBorder
                    Grid.Row="1"
                    Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingMd}}"
                    Style="{x:Static ui:Styles.FrameBorderStyle}">
                    <app:PatientListView
                        ItemsSource="{Binding ClientSelected.MedicalCheckStateDisplays}" />
                </telerik:RadBorder>
            </Grid>

        </Grid>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            BackCommand="{Binding BackCommand}"
            HoldCommand="{Binding GoToPendingTestSelectionCommand}"
            PauseCommand="{Binding GoToStopTestSelectionCommand}"
            ClientSelected="{Binding ClientSelected}"
            RegisterCommand="{Binding GoToGuidanceSelectionCommand}" />
    </Grid>
</mvvm:BasePage>