namespace ShinKenShinKun;

public partial class PauseButtonView : RadBorder
{
    public PauseButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty PauseCommandProperty = BindableProperty.Create(
        nameof(PauseCommand),
        typeof(IRelayCommand),
        typeof(PauseButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand PauseCommand
    {
        get => (IRelayCommand)GetValue(PauseCommandProperty);
        set => SetValue(PauseCommandProperty, value);
    }
}