﻿namespace ShinKenShinKun.InterfaceLibrary;

public class ErrorDataException : Exception
{
    #region 変数
    private int FldErrorCode;
    private string FldMsg;
    #endregion

    #region ErrorDataException
    public ErrorDataException()
    {
    }
    #endregion
    #region ErrorDataException
    public ErrorDataException(string msg)
    {
        FldMsg = msg;
    }
    #endregion

    #region Msg
    public string Msg
    {
        get
        {
            return FldMsg;
        }
    }
    #endregion

    #region ErrorDataException
    public ErrorDataException(int errorCode)
    {
        ErrorCode = errorCode;
    }
    #endregion

    #region ErrorCode
    public int ErrorCode
    {
        set
        {
            FldErrorCode = value;
        }
        get
        {
            return FldErrorCode;
        }
    }
    #endregion
}
