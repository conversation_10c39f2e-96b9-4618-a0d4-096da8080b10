﻿namespace ShinKenShinKun;

public class AppSettings
{
    public bool IsLogin { get; init; }
    public string DefaultScreen { get; init; } = string.Empty;
    public string DefaultMedicalCheck { get; init; } = string.Empty;
    public string MonitoringSystemFolderPath { get; init; } = string.Empty;
    public string MonitoringSystemWorkingFolderPath { get; init; } = string.Empty;
    public List<MedicalCheckProgressSetting> MedicalCheckProgressSetting { get; init; } = [];
    public List<AreaModel> AreaSettings { get; init; } = [];
}