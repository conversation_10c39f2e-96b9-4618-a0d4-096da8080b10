﻿namespace ShinKenShinKun.Common;

/// <summary>
/// 画面表示の定数クラス
/// </summary>
public class FormDispModeConst
{
    /// <summary>
    /// [0]標準：6項目
    /// </remarks>
    public static readonly int BASIC_MODE = 0;

    /// <summary>
    /// [20]標準：7項目
    /// </remarks>
    public static readonly int BASIC_MODE7 = 20;

    /// <summary>
    /// [1]標準2列
    /// </summary>
    public static readonly int BASIC_TWO_ROW_MODE = 1;

    /// <summary>
    /// [21]標準2列7項目
    /// </summary>
    public static readonly int BASIC_TWO_ROW_MODE7 = 21;

    /// <summary>
    /// [22]カスタム30
    /// </summary>
    public static readonly int BASIC_CUSTOM30 = 22;

    /// <summary>
    /// [3]回数選択
    /// </summary>
    public static readonly int SELECT_MODE = 3;

    /// <summary>
    /// [4]通過管理
    /// </summary>
    public static readonly int PASS_MODE = 4;

    /// <summary>
    /// [5]視力画面
    /// </summary>
    public static readonly int EYE_MODE = 5;

    /// <summary>
    /// [6]視力画面2列
    /// </summary>
    public static readonly int EYE_TWO_ROW_MODE = 6;

    /// <summary>
    /// [7]一面表示
    /// </summary>
    public static readonly int DOUBLE_MODE = 7;

    /// <summary>
    /// [8]拡大3行
    /// </summary>
    public static readonly int ENLARGE_3_MODE = 8;

    /// <summary>
    /// [9]拡大4行
    /// </summary>
    public static readonly int ENLARGE_4_MODE = 9;

    /// <summary>
    /// [10]拡大5行
    /// </summary>
    public static readonly int ENLARGE_5_MODE = 10;

    /// <summary>
    /// [11]回数選択3行
    /// </summary>
    public static readonly int SELECT_3_MODE = 11;

    /// <summary>
    /// [12]回数選択4行
    /// </summary>
    public static readonly int SELECT_4_MODE = 12;

    /// <summary>
    /// [13]回数選択5行
    /// </summary>
    public static readonly int SELECT_5_MODE = 13;

    /// <summary>
    /// [14]回数選択個別（大）
    /// </summary>
    public static readonly int SELECT_SEPARATE_L_MODE = 14;

    /// <summary>
    /// [15]回数選択個別（小）
    /// </summary>
    public static readonly int SELECT_SEPARATE_S_MODE = 15;

    // TODO: No need to porting
    ///// <summary>
    ///// [16]拡大5行
    ///// </summary>
    //public static readonly int DOUBLE_5_MODE = 16;
    ///// <summary>
    ///// [17]拡大4行
    ///// </summary>
    //public static readonly int DOUBLE_4_MODE = 17;
    ///// <summary>
    ///// [18]拡大3行
    ///// </summary>
    //public static readonly int DOUBLE_3_MODE = 18;
    ///// <summary>
    ///// [19]マルチ
    ///// </summary>
    //public static readonly int BASIC_ARRANGE_MODE = 19;
    /// <summary>
    /// [23]終了確認
    /// </summary>
    public static readonly int END_CHECK_MODE = 23;
}