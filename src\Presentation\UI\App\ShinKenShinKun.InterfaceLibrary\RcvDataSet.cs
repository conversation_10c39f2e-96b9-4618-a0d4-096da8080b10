﻿namespace ShinKenShinKun.InterfaceLibrary;
public class RcvDataSet
{
    #region 変数
    /// <summary>
    /// 必要項目のデータ
    /// </summary>
    private string[] FldData;
    /// <summary>
    /// 必要項目のデータテーブル
    /// </summary>
    private Hashtable FldHashTable;
    /// <summary>
    /// 受信した全データ
    /// </summary>
    private string FldReceiveData;
    #endregion

    #region RcvDataSet
    public RcvDataSet(string[] recordData, string allData)
    {
        Data = recordData;
        ReceiveData = allData;
    }
    #endregion

    #region RcvDataSet
    public RcvDataSet(Hashtable recordData, string allData)
    {
        if (recordData != null)
        {
            FldHashTable = recordData;
        }
        else
        {
            FldHashTable = new Hashtable();
        }
        ReceiveData = allData;
    }
    #endregion

    #region 解析済みデータ用
    public string[] Data
    {
        set
        {
            FldData = value;
        }
        get
        {
            return FldData;
        }
    }
    #endregion

    #region 解析済みデータ用
    public Hashtable TableData
    {
        set
        {
            FldHashTable = value;
        }
        get
        {
            return FldHashTable;
        }
    }
    #endregion

    #region 受信全データ
    public string ReceiveData
    {
        set
        {
            FldReceiveData = value;
        }
        get
        {
            return FldReceiveData;
        }
    }
    #endregion

}
