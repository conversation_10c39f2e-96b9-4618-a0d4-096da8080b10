namespace ShinKenShinKun;

public partial class LogoutButtonView : RadBorder
{
    public LogoutButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty LogoutCommandProperty = BindableProperty.Create(
        nameof(LogoutCommand),
        typeof(IRelayCommand),
        typeof(LogoutButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand LogoutCommand
    {
        get => (IRelayCommand)GetValue(LogoutCommandProperty);
        set => SetValue(LogoutCommandProperty, value);
    }
}