﻿namespace ShinKenShinKun;

public partial class ClientInformationModel : BaseModel
{
    [ObservableProperty] private byte age;

    [ObservableProperty] private DateTime birthDay;

    [ObservableProperty] private string clientId;

    [ObservableProperty] private Color contentCellColor = AppColors.Black;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(MedicalChecklists))]
    private string medicalChecklist;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(MedicalCheckStates))]
    private string medicalCheckState;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(MedicalCheckProgresses))]
    private string medicalCheckProgress;

    [ObservableProperty] private string name;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(Notes))]
    private string note;

    [ObservableProperty]
    [NotifyPropertyChangedFor(nameof(PersonalNotes))]
    private string personalNote;

    [ObservableProperty] private string reserveState;

    [ObservableProperty] private string sex;

    [ObservableProperty] private string waitTime;

    [ObservableProperty] private string guideState;

    [ObservableProperty] private string checking;

    [ObservableProperty] private string individualState;

    public List<string> MedicalChecklists => MedicalChecklist.SplitToList();

    public ObservableCollection<MedicalCheckStateModel> MedicalCheckStateDisplays =>
    [
        ..SplitToMedicalCheckStateList(AppConstants.MedicalCheckNames, MedicalChecklists, MedicalCheckStates)
    ];

    public ObservableCollection<MedicalCheckStateModel> MedicalCheckProgressesDisplays =>
    [
        ..SplitToMedicalCheckStateList(AppConstants.MedicalCheckProgressNames, MedicalChecklists,
            MedicalCheckProgresses)
    ];

    //TODO: Need to refactor less using readonly property
    public ObservableCollection<MedicalCheckStateModel> MedicalCheckProgressesSubDisplays { get; set; }

    public ObservableCollection<string> MedicalCheckStates => [.. MedicalCheckState.SplitToList()];
    public ObservableCollection<string> MedicalCheckProgresses => [.. MedicalCheckProgress.SplitToList()];
    public ObservableCollection<string>? Notes => [.. Note.SplitToList()];
    public ObservableCollection<string>? PersonalNotes => [.. PersonalNote.SplitToList()];
    public SexModel SexDisplay => AppDictionaryHelpers.SexDictionary.GetValueOrNull(Sex) ?? new SexModel();
    public TimeSpan? WaitTimeSpan => TimeSpan.TryParse(WaitTime, out var date) ? date : null;

    private static IEnumerable<MedicalCheckStateModel> SplitToMedicalCheckStateList(
        List<string> medicalCheckNames,
        List<string> medicalChecklists,
        ObservableCollection<string> medicalCheckStates)
    {
        return medicalCheckNames.Select(x =>
        {
            var index = medicalChecklists.IndexOf(x);
            var testStatusKey = (index >= 0 && index < medicalCheckNames.Count && index < medicalChecklists.Count &&
                                 index < medicalCheckStates.Count)
                ? medicalCheckStates[index]
                : "外";

            var testStatus = AppDictionaryHelpers.TestStatusDictionary[testStatusKey];

            return new MedicalCheckStateModel
            {
                MedicalCheckName = x,
                TestStatus = new TestStatusModel
                {
                    MedicalState = testStatus.MedicalState,
                    DisplayName = testStatus.DisplayName,
                    StatusColor = testStatus.StatusColor,
                    ProgressColor = testStatus.ProgressColor,
                    TextColor = testStatus.TextColor
                }
            };
        });
    }
}