<?xml version="1.0" encoding="utf-8"?>

<mvvm:BasePage
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExamineBeforeAuthPage"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:DataType="app:ExamineBeforeAuthViewModel"
    xmlns:app="clr-namespace:ShinKenShinKun">
    <Shell.BackButtonBehavior>
        <BackButtonBehavior
            IsVisible="False"
            IsEnabled="False" />
    </Shell.BackButtonBehavior>
    <Grid
        BackgroundColor="{x:Static ui:AppColors.LightCobaltBlue}"
        RowDefinitions="80, *, Auto">
        <!--Header-->
        <app:HeaderView
            Grid.Row="0"
            CompomentStatus="{Binding CompomentStatus}"
            Title="{x:Static resources:AppResources.ExaminationScreen}"
            MedicalMachineName="{Binding MedicalMachineName}"
            GoHomeCommand="{Binding GoHomeCommand}"
            ChangeModeCommand="{Binding ChangeModeCommand}"
            IsMaskMode="{Binding IsMaskMode}"
            LoginUser="{Binding UserName}" />        

        <!--Content-->
        <Grid
            Grid.Row="1"
            ColumnDefinitions="3*,7*"
            BackgroundColor="{x:Static ui:AppColors.ContentBackgroud}">
            <app:PatientInfoView
                NoteScroll="{Binding NoteScroll}"
                PersonalNoteScroll="{Binding PersonalNoteScroll}"
                ShowNoteEditPopupCommand="{Binding ShowNoteEditPopupCommand}"
                IsMaskMode="{Binding IsMaskMode}"
                IsItemSelected="{Binding ClientSelected,
                    Converter={x:Static ui:AppConverters.ObjectToBoolConverter}}"
                ItemSelected="{Binding ClientSelected}"
                Grid.Column="0" />
            <app:PatientListMonitorView
                Grid.Column="1" />
        </Grid>

        <!--Footer-->
        <app:FooterView
            Grid.Row="2"
            CompomentStatus="{Binding CompomentStatus}"
            BackCommand="{Binding BackCommand}"
            ClientSelected="{Binding ClientSelected}"
            EnterIdCommand="{Binding ShowInputClientIdPopupCommand}"
            HoldCommand="{Binding GoToPendingTestSelectionCommand}"
            PauseCommand="{Binding GoToStopTestSelectionCommand}" />
    </Grid>
</mvvm:BasePage>