﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// MedicalItemListService の概要の説明です

    /// </summary>
    public class RemarksItemListService
    {
        private RemarksService rSrv;
        private Logger logger = null;
        public RemarksItemListService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            rSrv = new RemarksService();
        }
        public PluralRecord[] GetRemarksItemListAll()
        {
            try
            {
                RemarksDataSet.RemarksItemListDataTable tmpTable =
                    rSrv.GetRemarksItemListAll().RemarksItemList;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (RemarksDataSet.RemarksItemListRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.RemarksItemNo.ToString());
                        rList.Add(row.RemarksItemName.ToString());
                        rList.Add(row.RemarksItemDetail.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetMedicalItemListNo(string medicalCheckNo)
        {
            try
            {
                short medicalCheckNoShr = Convert.ToInt16(medicalCheckNo);
                RemarksDataSet.RemarksItemListDataTable tmpTable =
                    rSrv.GetRemarksItemList(medicalCheckNoShr).RemarksItemList;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    foreach (RemarksDataSet.RemarksItemListRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.RemarksItemNo.ToString());
                        rList.Add(row.RemarksItemName.ToString());
                        rList.Add(row.RemarksItemDetail.ToString());
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}