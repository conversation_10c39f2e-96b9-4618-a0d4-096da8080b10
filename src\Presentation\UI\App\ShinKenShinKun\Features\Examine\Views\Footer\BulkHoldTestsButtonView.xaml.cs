namespace ShinKenShinKun;

public partial class BulkHoldTestsButtonView : RadBorder
{
    public BulkHoldTestsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkHoldTestsCommandProperty = BindableProperty.Create(
        nameof(BulkHoldTestsCommand),
        typeof(IRelayCommand),
        typeof(BulkHoldTestsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkHoldTestsCommand
    {
        get => (IRelayCommand)GetValue(BulkHoldTestsCommandProperty);
        set => SetValue(BulkHoldTestsCommandProperty, value);
    }
}