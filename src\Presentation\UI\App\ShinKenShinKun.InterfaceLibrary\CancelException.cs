﻿namespace ShinKenShinKun.InterfaceLibrary;
public class CancelException : Exception
{
    private int FldErrorCode;

    public CancelException()
    {
    }

    public CancelException(int errorCode)
    {
        ErrorCode = errorCode;
    }

    public int ErrorCode
    {
        set
        {
            FldErrorCode = value;
        }
        get
        {
            return FldErrorCode;
        }
    }
}
