﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.DataSets.GuideSupportDataSetTableAdapters;
using System;

namespace ShinKenShinKunServer.Services
{
    public class CallMonitorService
    {
        #region データセット、テーブルアダプターのインスタンス化

        /// <summary>
        /// データセットクラス
        /// </summary>
        private GuideSupportDataSet GuideDataSet = new GuideSupportDataSet();

        /// <summary>
        /// TableAdapterクラス
        /// </summary>
        //private CallClientDataTableAdapter adapterCallClientData
        //    = new CallClientDataTableAdapter();
        //private CallExamMasterTableAdapter adapterCallExamMaster
        //    = new CallExamMasterTableAdapter();
        //private CallStatusTableAdapter adapterCallStatus
        //    = new CallStatusTableAdapter();
        //private CallTerminalMasterTableAdapter adapterCallTerminalMaster
        //    = new CallTerminalMasterTableAdapter();

        #endregion

        /// <summary>
        /// コンストラクタ
        /// </summary>
        public CallMonitorService()
        {
        }

        public GuideSupportDataSet GetCallClientDataAll()
        {
            CallClientDataTableAdapter adapterCallClientData
                = new CallClientDataTableAdapter();
            int count = adapterCallClientData.Fill(GuideDataSet.CallClientData);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallClientData(DateTime dMedicalCheckDate, string clientID, string division, int examID)
        {
            CallClientDataTableAdapter adapterCallClientData
                = new CallClientDataTableAdapter();
            adapterCallClientData.FillBy(GuideDataSet.CallClientData, dMedicalCheckDate, clientID, division, examID);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallExamMasterAll()
        {
            CallExamMasterTableAdapter adapterCallExamMaster
                = new CallExamMasterTableAdapter();
            int count = adapterCallExamMaster.Fill(GuideDataSet.CallExamMaster);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallExamMaster(short examID)
        {
            CallExamMasterTableAdapter adapterCallExamMaster
                = new CallExamMasterTableAdapter();
            adapterCallExamMaster.FillBy(GuideDataSet.CallExamMaster, examID);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallStatusAll()
        {
            CallStatusTableAdapter adapterCallStatus
                = new CallStatusTableAdapter();
            int count = adapterCallStatus.Fill(GuideDataSet.CallStatus);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallStatus(int examID)
        {
            CallStatusTableAdapter adapterCallStatus
                = new CallStatusTableAdapter();
            int count = adapterCallStatus.FillBy(GuideDataSet.CallStatus, examID);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallTerminalMasterAll()
        {
            CallTerminalMasterTableAdapter adapterCallTerminalMaster
                = new CallTerminalMasterTableAdapter();
            int count = adapterCallTerminalMaster.Fill(GuideDataSet.CallTerminalMaster);
            return GuideDataSet;
        }

        public GuideSupportDataSet GetCallTerminalMaster(string termID)
        {
            CallTerminalMasterTableAdapter adapterCallTerminalMaster
                = new CallTerminalMasterTableAdapter();
            int count = adapterCallTerminalMaster.FillBy(GuideDataSet.CallTerminalMaster, termID);
            return GuideDataSet;
        }
    }
}