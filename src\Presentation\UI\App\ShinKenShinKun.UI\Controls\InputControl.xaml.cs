namespace ShinKenShinKun.UI;

public partial class InputControl : ContentView
{
    public static readonly BindableProperty TextProperty = BindableProperty.Create(
        nameof(Text),
        typeof(string),
        typeof(InputControl),
        default(string),
        BindingMode.TwoWay);

    public static readonly BindableProperty BackgroundColorInputControlProperty = BindableProperty.Create(
        nameof(BackgroundColorInputControl),
        typeof(Color),
        typeof(InputControl));

    public static readonly BindableProperty BorderThicknessProperty = BindableProperty.Create(
        nameof(BorderThickness),
        typeof(Thickness),
        typeof(InputControl));

    public static readonly BindableProperty CornerRadiusProperty = BindableProperty.Create(
        nameof(CornerRadius),
        typeof(Thickness),
        typeof(InputControl));

    public static readonly BindableProperty BorderColorProperty = BindableProperty.Create(
        nameof(BorderColor),
        typeof(Color),
        typeof(InputControl),
        default);

    public static readonly BindableProperty KeyboardProperty = BindableProperty.Create(
        nameof(Keyboard),
        typeof(Keyboard),
        typeof(InputControl),
        default);

    public static readonly BindableProperty MaxLengthProperty = BindableProperty.Create(
        nameof(MaxLength),
        typeof(int),
        typeof(InputControl),
        default);

    public InputControl()
    {
        InitializeComponent();
    }

    public event EventHandler<TextChangedEventArgs>? TextChanged;

    public event EventHandler<FocusEventArgs>? Unfocused;

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    public Color BackgroundColorInputControl
    {
        get => (Color)GetValue(BackgroundColorInputControlProperty);
        set => SetValue(BackgroundColorInputControlProperty, value);
    }

    public Thickness BorderThickness
    {
        get => (Thickness)GetValue(BorderThicknessProperty);
        set => SetValue(BorderThicknessProperty, value);
    }

    public Thickness CornerRadius
    {
        get => (Thickness)GetValue(CornerRadiusProperty);
        set => SetValue(CornerRadiusProperty, value);
    }

    public Color BorderColor
    {
        get => (Color)GetValue(BorderColorProperty);
        set => SetValue(BorderColorProperty, value);
    }

    public Keyboard Keyboard
    {
        get => (Keyboard)GetValue(KeyboardProperty);
        set => SetValue(KeyboardProperty, value);
    }

    public int MaxLength
    {
        get => (int)GetValue(MaxLengthProperty);
        set => SetValue(MaxLengthProperty, value);
    }

    private void RadEntryTextChanged(object sender, TextChangedEventArgs e)
    {
        TextChanged?.Invoke(sender, e);
    }

    private void RadEntryUnfocused(object sender, FocusEventArgs e)
    {
        Unfocused?.Invoke(sender, e);
    }
}