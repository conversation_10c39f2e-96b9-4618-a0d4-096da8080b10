namespace ShinKenShinKun;

public partial class HeaderView : Grid
{
    public HeaderView()
    {
        InitializeComponent();
    }

    #region Bindable Properties
    public static readonly BindableProperty CompomentStatusProperty = BindableProperty.Create(
        nameof(CompomentStatus),
        typeof(CompomentStatusModel),
        typeof(HeaderView),
        null,
        BindingMode.TwoWay);

    public static readonly BindableProperty TitleProperty = BindableProperty.Create(
        nameof(Title),
        typeof(string),
        typeof(HeaderView),
        string.Empty,
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty MedicalMachineNameProperty = BindableProperty.Create(
        nameof(MedicalMachineName),
        typeof(string),
        typeof(HeaderView),
        string.Empty,
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty GoHomeCommandProperty = BindableProperty.Create(
        nameof(GoHomeCommand),
        typeof(IRelayCommand),
        typeof(HeaderView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty IsMaskModeProperty = BindableProperty.Create(
        nameof(IsMaskMode),
        typeof(bool),
        typeof(HeaderView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty IsHomeButtonEnabledProperty = BindableProperty.Create(
        nameof(IsHomeButtonEnabled),
        typeof(bool),
        typeof(HeaderView),
        true,
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty ChangeModeCommandProperty = BindableProperty.Create(
        nameof(ChangeModeCommand),
        typeof(IRelayCommand),
        typeof(HeaderView),
        defaultBindingMode: BindingMode.TwoWay);

    public static readonly BindableProperty LoginUserProperty = BindableProperty.Create(
        nameof(LoginUser),
        typeof(string),
        typeof(HeaderView),
        string.Empty,
        BindingMode.TwoWay);
    #endregion

    #region Properties
    public CompomentStatusModel CompomentStatus
    {
        get => (CompomentStatusModel)GetValue(CompomentStatusProperty);
        set => SetValue(CompomentStatusProperty, value);
    }

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public string MedicalMachineName
    {
        get => (string)GetValue(MedicalMachineNameProperty);
        set => SetValue(MedicalMachineNameProperty, value);
    }

    public IRelayCommand GoHomeCommand
    {
        get => (IRelayCommand)GetValue(GoHomeCommandProperty);
        set => SetValue(GoHomeCommandProperty, value);
    }

    public bool IsMaskMode
    {
        get => (bool)GetValue(IsMaskModeProperty);
        set => SetValue(IsMaskModeProperty, value);
    }

    public bool IsHomeButtonEnabled
    {
        get => (bool)GetValue(IsHomeButtonEnabledProperty);
        set => SetValue(IsHomeButtonEnabledProperty, value);
    }

    public IRelayCommand ChangeModeCommand
    {
        get => (IRelayCommand)GetValue(ChangeModeCommandProperty);
        set => SetValue(ChangeModeCommandProperty, value);
    }

    public string LoginUser
    {
        get => (string)GetValue(LoginUserProperty);
        set => SetValue(LoginUserProperty, value);
    }
    #endregion
}