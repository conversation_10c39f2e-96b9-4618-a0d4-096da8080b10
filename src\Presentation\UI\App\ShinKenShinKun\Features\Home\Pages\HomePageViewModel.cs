

using System.Reflection.PortableExecutable;
using NPOI.HSSF.Record;

namespace ShinKenShinKun;

public partial class HomePageViewModel(
    IAppNavigator appNavigator,
    ISessionServices sessionServices,
    IMedicalCheckItemServices medicalCheckItemServices,
    IAppSettingServices appSettingServices) : NavigationAwareBaseViewModel(appNavigator)
{
    [ObservableProperty] private ObservableCollection<MenuItem> menus;

    [ObservableProperty] private bool isLogin = appSettingServices.AppSettings.IsLogin;

    [ObservableProperty] private string medicalCheck = appSettingServices.AppSettings.DefaultMedicalCheck;

    [ObservableProperty] private string userName;

    [ObservableProperty] private bool isMaskMode;

    internal Action hiddenEntryFocus;

    [RelayCommand]
    private async Task SelectButton(int id)
    {
        hiddenEntryFocus.Invoke();
        var selectedMenuItem = Menus.FirstOrDefault(b => b.Id == id);
        if (selectedMenuItem != null)
        {
            selectedMenuItem.IsSelected = true;
            await Task.Delay(100);
            if (selectedMenuItem.TaskAction != null)
            {
                await selectedMenuItem.TaskAction();
            }
            selectedMenuItem.IsSelected = false;
        }
    }

    [RelayCommand]
    private async Task OpenLogoutPopup()
    {
        if (await ShowDialog(AppResources.LogoutMessage))
        {
            await AppNavigator.GoBackAsync();
        }
    }

    [RelayCommand]
    private async Task OpenQuitAppPopup()
    {
        if (await ShowDialog(AppResources.QuitAppTitle))
        {
            if (await ShowDialog(AppResources.ShutdownDeviceTitle))
                ShutdownDevice();
            else
                QuitApp();
        }
    }

    [RelayCommand]
    private void ChangeMode()
    {
        IsMaskMode = !IsMaskMode;
        sessionServices.IsMaskMode = IsMaskMode;
    }

    private static void QuitApp()
    {
        Application.Current?.Quit();
    }

    private static void ShutdownDevice()
    {
        QuitApp();
#if WINDOWS
        Process.Start("shutdown", "/s /t 0");
#endif
    }

    private static async Task<bool> ShowDialog(string title)
    {
        return await Shell.Current.DisplayAlert("", title, AppResources.Yes, AppResources.No);
    }

    public override Task OnAppearingAsync()
    {
        Menus =
        [
            new MenuItem { Id = 1, Text = "検査画面", IconSource = Icons.MedicalCheckImage, TaskAction = async() => await NavigateToPage(RouterName.TestSelectionPage )},
            new MenuItem { Id = 2, Text = "全体進歩", IconSource = Icons.ProgressIcon, TaskAction = async() => await RouteHelpers.RunMonitoringSystem(appSettingServices.AppSettings)},
            new MenuItem { Id = 3, Text = "個別進歩", IconSource = Icons.IndividualIcon, TaskAction = async() => await NavigateToPage(RouterName.IndividualProgressPage)},
            new MenuItem { Id = 4, Text = "誘導調整", IconSource = Icons.GaidancesScreen, TaskAction = async() =>await NavigateToPage(RouterName.PatientGuidanceAdjustmentPage)},
            new MenuItem { Id = 5, Text = "一括保留", IconSource = Icons.BulkHoldScreen, TaskAction = async() => await NavigateToPage(RouterName.PatientPendingPage) },
            new MenuItem { Id = 6, Text = "各種設定", IconSource = Icons.VariousSetting },
            new MenuItem { Id = 7, Text = "サブシステムA", IconSource = Icons.MedicalCheckImage },
            new MenuItem { Id = 8, Text = "サブシステムB", IconSource = Icons.MedicalCheckImage },
            new MenuItem { Id = 9, Text = "サブシステムC", IconSource = Icons.MedicalCheckImage }
        ];
        UserName = sessionServices.UserName;
        IsMaskMode = sessionServices.IsMaskMode;
        return base.OnAppearingAsync();
    }

    private async Task NavigateToPage(string pageName)
    {
        if (pageName == RouterName.ExamineBeforeAuthPage)
        {
            MedicalCheck = sessionServices.SeletedMedicalMachine.MedicalMachineName ?? appSettingServices.AppSettings.DefaultMedicalCheck;
            await AppNavigator.NavigateAsync(pageName);
            return;
        }
        else if (pageName == RouterName.TestSelectionPage)
        {
            var machines = new MedicalMachinesDataRecord[0];
                machines = await medicalCheckItemServices.GetMedicalCheckItems();
            await AppNavigator.NavigateAsync(pageName, false, machines);
            return;
        }
        await AppNavigator.NavigateAsync(pageName);
    }
}