﻿using System.Diagnostics.CodeAnalysis;
using CommunityToolkit.Maui.Views;

namespace ShinKenShinKun.CoreMVVM;

[ExcludeFromCodeCoverage]
public class AppNavigator : IAppNavigator
{
    private readonly IServiceProvider serviceProvider;

    public AppNavigator(
        IServiceProvider serviceProvider)
    {
        this.serviceProvider = serviceProvider;
    }

    public Task GoBackAsync(bool animated = false, object data = default)
    {
        return NavigateAsync(UriHelper.GoBackSegment, animated, data);
    }

    public Task NavigateAsync(string target, bool animated = false, object args = default)
    {
        var navArgs = new Dictionary<string, object>()
        {
            { "source", Shell.Current.CurrentState.Location.OriginalString },
            { nameof(target), target },
            { nameof(animated), animated }
        };

        if (args is not null)
        {
            navArgs.Add(UriHelper.DataQueryParameterName, args);
        }

        if (target == UriHelper.GoBackSegment || target == $"{UriHelper.GoBackSegment}/{UriHelper.GoBackSegment}")
        {
            navArgs.Add(UriHelper.GoBackQueryParameterName, true);
        }

        return MainThread.InvokeOnMainThreadAsync(() => Shell.Current.GoToAsync(
            target,
            animated,
            navArgs
        ).ContinueWith(x =>
        {
            Log.Error(x.Exception?.Message ?? "", x.Exception);
        }));
    }

    public Task<bool> OpenUrlAsync(string url)
    {
        return Launcher.OpenAsync(url);
    }

    public Task ShareAsync(string text, string title = default)
    {
        var request = new ShareTextRequest(text, title);

        return Share.Default.RequestAsync(request);
    }

    public async Task ShowNotificationDialog(string message, string title = default) =>
        await Shell.Current.DisplayAlert(
        title,
        message,
        AppResources.Yes
    );
    public async Task<bool> ShowConfirmationDialog(string message, string title = default) =>
        await Shell.Current.DisplayAlert(
        title,
        message,
        AppResources.Yes,
        AppResources.No
    );

    public async Task ShowPopupAsync(Popup view)
    {
        await Shell.Current.ShowPopupAsync(view);
    }
}
