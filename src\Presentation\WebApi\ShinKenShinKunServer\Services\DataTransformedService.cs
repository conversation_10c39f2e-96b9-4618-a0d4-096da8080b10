﻿using ShinKenShinKunServer.DataSets;
using ShinKenShinKunServer.Dtos;
using ShinKenShinKunServer.Utils;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace ShinKenShinKunServer.Services
{

    /// <summary>
    /// DataTransformedService の概要の説明です

    /// </summary>
    public class DataTransformedService
    {
        private DispSettingService service;
        private Logger logger = null;

        public DataTransformedService()
        {
            //
            // TODO: コンストラクタ ロジックをここに追加します

            //
            service = new DispSettingService();
        }

        public PluralRecord[] GetDataTransformedSettingAll()
        {
            try
            {
                //DispSettingDataSet.DataTransformedSettingDataTable tmpTable
                //= this.service.GetDataTranseformedSettingAll().DataTransformedSetting;
                DispSettingDataSet.DataTransformedDataDataTable tmpTable
                 = service.GetDataTranseformedSettingAll().DataTransformedData;

                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    //foreach (DispSettingDataSet.DataTransformedSettingRow row in tmpTable)
                    foreach (DispSettingDataSet.DataTransformedDataRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        rList.Add(row.TransId.ToString());
                        rList.Add(row.DispData);
                        rList.Add(row.TransformedData);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }

        public PluralRecord[] GetDataTransformedSetting(
            string medicalCheckNo,
            string itemNo)
        {
            try
            {
                short medicalCheckNoSht = Convert.ToInt16(medicalCheckNo);
                short itemNoSht = Convert.ToInt16(itemNo);
                //            DispSettingDataSet.DataTransformedSettingDataTable tmpTable
                //                = this.service.GetDataTransformedSetteing(medicalCheckNoSht, itemNoSht).DataTransformedSetting
                //;
                DispSettingDataSet.DataTransformedDataDataTable tmpTable
                    = service.GetDataTransformedSetteing(medicalCheckNoSht, itemNoSht).DataTransformedData
    ;
                if (tmpTable != null)
                {
                    List<PluralRecord> pRecordList = new List<PluralRecord>();
                    //foreach (DispSettingDataSet.DataTransformedSettingRow row in tmpTable)
                    foreach (DispSettingDataSet.DataTransformedDataRow row in tmpTable)
                    {
                        List<string> rList = new List<string>();
                        rList.Add(row.MedicalCheckNo.ToString());
                        rList.Add(row.ItemNo.ToString());
                        //rList.Add(row.TransNo.ToString());
                        rList.Add(row.TransId.ToString());
                        rList.Add(row.DispData);
                        rList.Add(row.TransformedData);
                        PluralRecord pRec = new PluralRecord(rList.ToArray());
                        pRecordList.Add(pRec);
                    }
                    if (pRecordList.Count != 0)
                    {
                        return pRecordList.ToArray();
                    }
                }
                return null;
            }
            catch (SqlException se)
            {
                throw se;
            }
            catch (FormatException fe)
            {
                throw fe;
            }
            catch (Exception exp)
            {
                logger = Logger.GetInstance();
                logger.logWrite(exp.ToString());
                throw exp;
            }
        }
    }
}