﻿namespace ShinKenShinKun.UI;

public class FontSizeParametersExtension : IMarkupExtension
{
    public double BaseFontSize { get; set; } = Dimens.FontSizeT6;
    public double MinFontSize { get; set; } = Dimens.FontSizeT9;
    public double MaxFontSize { get; set; } = Dimens.FontSizeT1;
    public double Ratio { get; set; }

    public object ProvideValue(IServiceProvider serviceProvider)
    {
        return new FontSizeModel { BaseFontSize = BaseFontSize, MinFontSize = MinFontSize, MaxFontSize = MaxFontSize, Ratio = Ratio };
    }
}
public class FontSizeModel
{
    public double BaseFontSize { get; set; }
    public double MinFontSize { get; set; }
    public double MaxFontSize { get; set; }
    public double Ratio { get; set; }
}