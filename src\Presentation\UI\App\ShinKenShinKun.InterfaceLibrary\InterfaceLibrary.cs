﻿
namespace ShinKenShinKun.InterfaceLibrary;

#region 定義クラス
public class MeConnectDefine
{
    /// <summary>
    /// 定数部分(文字コード) 
    /// </summary>
    public const byte NULL = 0x00;
    public const byte SOH = 0x01;
    public const byte STX = 0x02;
    public const byte ETX = 0x03;
    public const byte EOT = 0x04;
    public const byte ENQ = 0x05;
    public const byte ACK = 0x06;
    public const byte TAB = 0x09;
    public const byte LF = 0x0A;
    public const byte CR = 0x0D;
    public const byte SO = 0x0E;
    public const byte SI = 0x0F;
    public const byte NAK = 0x15;
    public const byte ETB = 0x17;
    public const byte CAN = 0x18;
    public const byte SPACE = 0x20;
    public const byte ZERO = 0x30;
    /// <summary>
    /// 検査種別コード
    /// </summary>
    public const int BODY = 1;
    public const int BLOODPRE = 2;
    public const int EYE = 3;
    public const int RUNGS = 4;
    public const int BONE = 5;
    public const int EAR = 6;
    public const int EYEPRE = 7;
    public const int FUNDUS = 8;
    public const int HEART = 9;
    public const int GYNECOLOGY = 10;
    public const int BLOOD = 11;
    public const int HEART_ECHO = 14;
    public const int STOMACH_ECHO = 15;
    public const int BREAST_X = 17;
    public const int STOMACH_X = 18;
    public const int CT = 20;
    public const int MR = 21;
    public const int ENDSCOPE = 25;
    /// <summary>
    /// 定数部分(DLL内で必要な情報) 
    /// </summary>
    // 通信設定に関する部分
    public const int BaudRate = 0;    // 通信速度
    public const int DataBits = 1;    // データ長
    public const int StopBits = 2;    // ストップビット
    public const int Parity = 3;    // パリティ
    public const int HandShake = 4;   // ハンドシェイク
    // 受信データに関する部分
    public const int RECEIVE_DATALENGTH = 0;    // 受信データの最大長
    public const int RECEIVE_STARTCODE = 1;    // 受信データの開始文字コード
    public const int RECEIVE_ENDCODE = 2;    // 受信データの終了文字コード
    public const int RECEIVE_DATACOUNT = 3;    // 解析結果のデータ数
    public const int RECEIVE_DATADIVIDETYPE = 4;    // データの区切り方
    public const int RECEIVE_DATASTARTPOS = 0;    // データ受信開始位置
    public const int RECEIVE_DATASIZE = 1; 　 // データ長
    // ・・・今後、以下に項目が追記される事が予想される
    // 設定項目が増えた場合には、上記に追加すると共に、下記の値を変更すること
    public const int RECEIVE_DATACONFIGSIZE = 2; 　 // 1データあたりの設定項目
    // データ受信の開始・終了方法
    public const int RECEIVE_MAXSIZE = 0;    // 指定データ長を受信したら終了
    public const int RECEIVE_START_STX = 1;    // STXを受信したら開始する
    public const int RECEIVE_GET_ETX = 1;    // ETXを受信したら終了する
    public const int RECEIVE_GET_CRLF = 2;    // CRLFを受信したら終了する
    public const int RECEIVE_GET_CR = 3;      // CRを受信したら終了する
    public const int RECEIVE_GET_LF = 4;      // LFを受信したら終了する
    public const int RECEIVE_GET_EOT = 5;      // EOTを受信したら終了する
    // データの区切り方
    public const int RECEIVE_FIXEDSIZE = 0;    // 指定データ位置
    public const int RECEIVE_COMMASIZE = 1;    // カンマ区切り
    public const int RECEIVE_CRLFSIZE = 2;    // CRLF区切り
    // 送信データに関する部分
    public const int SEND_DATALENGTH = 0;    // 送信データの最大長
    public const int SEND_STARTCODE = 1;    // 送信データの開始文字コード
    public const int SEND_ENDCODE = 2;    // 送信データの終了文字コード
    public const int SEND_DATACOUNT = 3;    // 解析結果のデータ数
    public const int SEND_DATAAFTER_ADD = 4; 　 // 項目区切り文字
    public const int SEND_DATASTARTPOS = 0;  　// データ設定開始位置
    public const int SEND_DATASIZE = 1; 　 // データ長
    public const int SEND_DATACONVERT = 2; 　 // データ変換形式
    public const int SEND_DATACOVER = 3; 　 // 空白埋め/0埋め
    public const int SEND_DATAPAD = 4; 　 // 左/右寄せ
    // ・・・今後、以下に項目が追記される事が予想される
    // 設定項目が増えた場合には、上記に追加すると共に、下記の値を変更すること
    public const int SEND_DATACONFIGSIZE = 5; 　 // 1データあたりの設定項目
    // 変換方法の指定
    public const int NOCHANGE = 0;    // 変換なし
    public const int CHANGE_NAME = 1;    // 名前変換
    public const int CHANGE_NAME_SOSI = 2;    // 名前変換（SO,SI付加）
    public const int CHANGE_SEX_12 = 3;    // 性別変換（男/女　→　1/2）
    public const int CHANGE_SEX_MF = 4;    // 性別変換（男/女　→　M/F）
    public const int CHANGE_BIRTHDAY = 5;    // 年月日変換（YYYY/MM/DD → yyyymmdd）
    public const int CHANGE_KANATOROMA = 6;   // ローマ字変換（半角カナ → ローマ字）
    public const int CHANGE_YYMMDD = 7;   // 年月日変換（YYYY/MM/DD → yymmdd）
    public const int CHANGE_YYMMDD_SLASH = 8; // 年月日変換（YYYY年MM月DD → YYYY/MM/DD）
    public const int CHANGE_BEFORE_DOT = 9;  // 小数点の前まで取得する。
    // 先頭コード/終了コード
    public const int CODE_STX = 1;    // STX 
    public const int CODE_ETX = 1;    // ETX 
    public const int CODE_CRLF = 2;    // CRLF 
    public const int CODE_CR = 3;    // CR 
    public const int CODE_LF = 4;    // LF 
    public const int CODE_EOT = 5;    // EOT
    public const int CODE_ETX_BCC = 6; // ETX + BCC
    // スペース登録用ポジションNo
    public const int ADD_SPACE = 888; // スペース埋め
    // 左/右寄せ
    public const int PADLEFT = 0;   // 右寄せ 
    public const int PADRIGHT = 1;  // 左寄せ
    // 空白埋め/0埋め
    public const int SPACECOVER = 0;     // ０埋め 
    public const int ZEROCOVER = 1;    // スペース埋め 
    public const int NULLCOVER = 2;    　// NULL埋め 
    // CRLF付加/カンマ付加
    public const int ADD_CRLF = 1;  // CRLF付加 
    public const int ADD_COMMA = 2; // カンマ付加 
    /// <summary>
    /// 文字エンコード
    /// </summary>
    public Encoding encSjis = Encoding.GetEncoding(932);
}
#endregion

#region ME機器DLLのベースとなる抽象クラス
public abstract class MeConnect : MeConnectDefine
{
    #region 変数
    // シリアル通信用
    public CommonSerialConnect comSC;
    // データ解析用基本クラス
    public CommonDataAnalyze Ana;
    // 属性情報作成用基本クラス
    public CommonCreateAttribute comCA;
    public int[] ConnectSetting;
    public int[] ReceiveSetting;
    public int[] ReceiveDataConfig;
    public int[] SendSetting;
    public int[] SendDataConfig;
    private ClientLogger Logger;
    private const string LOG_TITTLE = "【InterfaceLibrary】";
    private System.Arctec.Ar1000k.DataBase.StateData[] fState = null;
    /// <summary>
    /// 検査項目番号を返却する。
    /// </summary>
    /// 検査項目に応じた種別番号が返却される。
    /// (身長体重：1、血圧：2・・・etc)
    public abstract int GetMeKindNo();
    public object ObjectFromResult;
    #endregion

    #region MeConnect
    public MeConnect()
    {
        Logger = ClientLogger.GetInstance();
    }
    #endregion

    #region SetMeConnectInfo
    public void SetMeConnectInfo(int[] meConnectSetting)
    {
        ConnectSetting = meConnectSetting;
        if (comSC == null)
        {
            comSC = new CommonSerialConnect(ConnectSetting);
        }
        else
        {
            comSC.serialPort1.BaudRate = ConnectSetting[BaudRate];
            comSC.serialPort1.DataBits = ConnectSetting[DataBits];
            comSC.serialPort1.StopBits = (System.IO.Ports.StopBits)ConnectSetting[StopBits];
            comSC.serialPort1.Parity = (System.IO.Ports.Parity)ConnectSetting[Parity];
            if (ConnectSetting[HandShake] == 0)
            {
                comSC.serialPort1.Handshake = System.IO.Ports.Handshake.None;
            }
            else if (ConnectSetting[HandShake] == 1)
            {
                comSC.serialPort1.Handshake = System.IO.Ports.Handshake.RequestToSend;
            }
            else if (ConnectSetting[HandShake] == 2)
            {
                comSC.serialPort1.Handshake = System.IO.Ports.Handshake.RequestToSendXOnXOff;
            }
            else if (ConnectSetting[HandShake] == 3)
            {
                comSC.serialPort1.Handshake = System.IO.Ports.Handshake.XOnXOff;
            }
        }
    }
    #endregion

    #region SetMeReceiveInfo
    public void SetMeReceiveInfo(int[] meReceiveSets, int[] meReceiveDatas)
    {
        ReceiveSetting = meReceiveSets;
        ReceiveDataConfig = meReceiveDatas;
        if (Ana == null)
        {
            Ana = new CommonDataAnalyze();
        }
    }
    #endregion

    #region GetMeReceiveSets
    public int[] GetMeReceiveSets()
    {
        return ReceiveSetting;
    }
    #endregion

    #region GetMeReceiveDatas
    public int[] GetMeReceiveDatas()
    {
        return ReceiveDataConfig;
    }
    #endregion

    #region SetMeSendInfo
    public void SetMeSendInfo(int[] meSendSets, int[] meSendDatas)
    {
        SendSetting = meSendSets;
        SendDataConfig = meSendDatas;
        if (comCA == null)
        {
            comCA = new CommonCreateAttribute();
        }
    }
    #endregion

    #region GetMeSendSets
    public int[] GetMeSendSets()
    {
        return SendSetting;
    }
    #endregion

    #region GetMeSendDatas
    public int[] GetMeSendDatas()
    {
        return SendDataConfig;
    }
    #endregion

    #region Port_Open
    public bool Port_Open(int portNumber)
    {
        Logger.Write(LOG_TITTLE, "Port_Open:COM" + portNumber);
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(1);
        }
        return comSC.Com_Port_Open(portNumber);
    }
    #endregion

    #region Port_Open_Customリトライバージョン
    public bool Port_Open_Custom(int portNumber)
    {
        Logger.Write(LOG_TITTLE, "Port_Open_Custom:COM" + portNumber);
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(1);
        }
        return comSC.Com_Port_Open_Custom(portNumber);
    }
    #endregion

    #region Port_Close
    public bool Port_Close(int portNumber)
    {
        Logger.Write(LOG_TITTLE, "Port_Close:COM" + portNumber);
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(1);
        }
        return comSC.Com_Port_Close(portNumber);
    }
    #endregion

    #region Port_Test
    public bool Port_Test(int portNumber)
    {
        bool result = false;
        try
        {
            if (ConnectSetting == null)
            {
                ConnectSetting = new int[] { 9600, 8, 1, 0, 0 };
            }
            if (comSC == null)
            {
                comSC = new CommonSerialConnect(ConnectSetting);
            }
            result = comSC.Com_Port_Open(portNumber);
            if (result == false)
            {
                return false;
            }
            return comSC.Com_Port_Close(portNumber);
        }
        catch (Exception)
        {
            return false;
        }
    }
    #endregion

    #region RcvMedicalData
    public virtual string RcvMedicalData()
    {
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(2);
        }
        return comSC.Com_RcvData(ReceiveSetting);
    }
    #endregion

    #region AnalyzeMedicalData
    public virtual RcvDataSet AnalyzeMedicalData(string medicalData)
    {
        if (Ana == null)
        {
            throw new MeConnectNonSettingException(2);
        }
        return new RcvDataSet(Ana.Com_DataAnalyze(medicalData, ReceiveSetting, ReceiveDataConfig), medicalData);
    }
    #endregion

    #region CreateAttribute
    public virtual byte[] CreateAttribute(string[] data)
    {
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(3);
        }
        return comCA.Com_CreateAttribute(data, SendSetting, SendDataConfig);
    }
    #endregion

    #region SendAttribute
    public virtual bool SendAttribute(byte[] data)
    {
        if (comSC == null)
        {
            throw new MeConnectNonSettingException(3);
        }
        return comSC.Com_SendCmd(data);
    }
    #endregion

    #region SendCmdBefore
    public virtual void SendCmdBefore(object obj)
    {
    }
    #endregion

    #region SendCmd
    public bool SendCmd(string[] data)
    {
        Logger.Write(LOG_TITTLE, "【属性送信を行う】");
        byte[] sendData = CreateAttribute(data);
        return SendAttribute(sendData);
    }
    #endregion

    #region SetStatus
    public void SetStatus(System.Arctec.Ar1000k.DataBase.StateData[] state)
    {
        // ステータス情報を格納する。
        fState = state;
    }
    #endregion

    #region SetObject リザルト→Meへの値渡しカスタマイズ用
    public void SetObject(object obj)
    {
        ObjectFromResult = obj;
    }
    #endregion

    #region プロパティ
    /// <summary>
    /// ME接続DLLオブジェクト取得用
    /// </summary>
    protected System.Arctec.Ar1000k.DataBase.StateData[] CheckStatus
    {
        // 読み取り専用の為、SET無し
        get { return fState; }
    }
    #endregion

    #region DataDecide
    /// <summary>
    /// 確定キー用データ受信終了関数
    /// データ受信を終了し、アプリケーションにnullを返却する。
    /// (検査中止との違いを出すための処理)
    /// </summary>=
    public virtual void DataDecide()
    {
    }
    #endregion

    #region RestartSetup
    public virtual void RestartSetup()
    {
    }
    #endregion

    #region RcvData
    /// <remarks>
    /// ME機器からのデータの受信を行い、受信データの解析を行う。
    /// </remarks>
    public RcvDataSet RcvData()
    {
        string medicalData;
        RcvDataSet rcvdt = null;
        // 受信データの取得を行います。
        try
        {
            Logger.Write(LOG_TITTLE, "データ受信待ち開始");
            // データ受信前初期化関数
            ReadInitialize();
            medicalData = RcvMedicalData();
        }
        // データ長エラーが発生した場合
        catch (ErrorDataException ed)
        {
            Logger.Write(LOG_TITTLE, "データ受信待ち終了[データ長エラー]");
            throw ed;
        }
        // 中止処理が発生した場合
        catch (CancelException ce)
        {
            Logger.Write(LOG_TITTLE, "データ受信待ち終了[CancelException]");
            throw ce;
        }
        // その他例外が発生した場合
        catch (Exception e)
        {
            Logger.Write(LOG_TITTLE, "データ受信待ち終了[Exception]" + e.StackTrace);
            throw new ErrorDataException(e.ToString());
        }
        // 受診データがnullだった場合
        if (medicalData == null)
        {
            Logger.Write(LOG_TITTLE, "データ受信待ち終了");
            return null;
        }
        else
        {
            try
            {
                Logger.Write(LOG_TITTLE, "【受信データ】：" + medicalData);
                // 受信データの解析を行います。
                rcvdt = AnalyzeMedicalData(medicalData);
                // 解析データがnullではなかった場合
                if (rcvdt.Data != null)
                {
                    return rcvdt;
                }
                else if (rcvdt.TableData != null)
                {
                    return rcvdt;
                }
                else
                {
                    return null;
                }
            }
            catch (IndexOutOfRangeException e)
            {
                Logger.Write(LOG_TITTLE, e.StackTrace.ToString());
                throw new ErrorDataException(medicalData);
            }
            catch (Exception ex)
            {
                Logger.Write(LOG_TITTLE, ex.StackTrace.ToString());
                if (rcvdt != null)
                {
                    throw new ErrorDataException(rcvdt.ReceiveData);
                }
                else
                {
                    throw new ErrorDataException(ex.Message);
                }
            }
            finally
            {
                Logger.Write(LOG_TITTLE, "データ受信待ち終了");
            }
        }
    }
    #endregion

    #region ReceiveStop
    public virtual void ReceiveStop()
    {
        comSC.Com_ReceiveStop();
    }
    #endregion

    #region GetReceiveStopFlag
    public bool GetReceiveStopFlag()
    {
        return comSC.StopFlg;
    }
    #endregion

    #region FileWrite
    public virtual bool FileWrite(string data, string tempFileName, string outFileName)
    {
        try
        {
            // ファイルの作成を行う。
            FileStream file = new FileStream(tempFileName, FileMode.Create, FileAccess.Write, FileShare.None);
            // ファイル読み書き用のインスタンスを行う。
            StreamWriter writer = new StreamWriter(file, encSjis);
            // ファイルへの書き込みを行う。
            writer.Write(data);
            // ファイルストリームを閉じる。
            writer.Close();
            // ファイルを閉じる。
            file.Close();
            writer = null;
            file = null;
            // ファイルのコピーを行う。
            File.Move(tempFileName, outFileName);
            return true;
        }
        catch (DirectoryNotFoundException dex)
        {
            throw dex;
        }
        catch (IOException iex)
        {
            throw iex;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
    #endregion

    #region SendFile
    public bool SendFile(string[] data, string tempFileName, string outFileName)
    {
        byte[] bSendData = CreateAttribute(data);
        string sendData = encSjis.GetString(bSendData, 0, bSendData.Length);
        return FileWrite(sendData, tempFileName, outFileName);
    }
    #endregion

    #region IsOriginalMeProcess
    public virtual bool IsOriginalMeProcess()
    {
        return false;
    }
    #endregion

    #region OriginalMeProcess
    public virtual void OriginalMeProcess()
    {
    }
    #endregion

    #region OriginalMeProcess
    public virtual object OriginalMeProcess(object obj)
    {
        return obj;
    }
    #endregion

    #region 受信初期化関数
    public virtual void ReadInitialize()
    {
        comSC.StopFlg = false;
    }
    #endregion

    #endregion

}
