﻿namespace ShinKenShinKun.DataAccess;

public static class ConfigInMemoryDb
{
    public static MauiAppBuilder AddImMemDb(this MauiAppBuilder builder)
    {
        builder.Services.AddDbContext<InMemoryDbContext>(options =>
            options.UseInMemoryDatabase("InMemoryDb")
        );
        return builder;
    }

    public static void SeedData(IServiceProvider serviceProvider)
    {
        var dbContext = serviceProvider.GetRequiredService<InMemoryDbContext>();
        if (!dbContext.SettingNumberPads.Any())
        {
            var items = ExcelHelper.CreateEntity<SettingNumberPad>(
                AppConstants.NumberPadExcelFile,
                AppConstants.SettingNumberPadSheetName
            );
            dbContext.SettingNumberPads.AddRange(items);
        }

        if (!dbContext.StandardMasterKeys.Any())
        {
            var items = ExcelHelper.CreateEntity<StandardMasterKey>(
                AppConstants.NumberPadExcelFile,
                AppConstants.StandardMasterKeySheetName,
                ConfigureMappings
            );
            dbContext.StandardMasterKeys.AddRange(items);
        }

        if (!dbContext.CustomMasterKeys.Any())
        {
            var items = ExcelHelper.CreateEntity<CustomMasterKey>(
                AppConstants.NumberPadExcelFile,
                AppConstants.CustomMasterKeySheetName,
                ConfigureMappings
            );
            dbContext.CustomMasterKeys.AddRange(items);
        }

        if (!dbContext.StandardMasterKeySettings.Any())
        {
            var items = ExcelHelper.CreateEntity<StandardMasterKeySetting>(
                AppConstants.NumberPadExcelFile,
                AppConstants.StandardMasterKeySettingSheetName
            );
            dbContext.StandardMasterKeySettings.AddRange(items);
        }

        if (!dbContext.CustomMasterKeySettings.Any())
        {
            var items = ExcelHelper.CreateEntity<CustomMasterKeySetting>(
                AppConstants.NumberPadExcelFile,
                AppConstants.CustomMasterKeySettingSheetName
            );
            dbContext.CustomMasterKeySettings.AddRange(items);
        }

        dbContext.SaveChanges();
    }

    private static void ConfigureMappings(ExcelMapper excel)
    {
        excel
            .AddMapping<StandardMasterKey>("Action", p => p.Action)
            .SetPropertyUsing(cellvalue =>
            {
                var value = cellvalue.ToString();
                return DictionaryHelpers.KeyActionDic.TryGetValue(value, out var action)
                    ? action
                    : throw new Exception($"Unknown action: {value}");
            });
        excel
            .AddMapping<CustomMasterKey>("Action", p => p.Action)
            .SetPropertyUsing(cellvalue =>
            {
                var value = cellvalue.ToString();
                return DictionaryHelpers.KeyActionDic.TryGetValue(value, out var action)
                    ? action
                    : throw new Exception($"Unknown action: {value}");
            });
    }
}