﻿using Microsoft.Maui.Controls.Shapes;

namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style HeaderExaminationLabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPMedium)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingXs, 0))
        .Set(VisualElement.HeightRequestProperty, Dimens.Height50);

    public static Style HeaderMultiSelectLabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.LightBlue)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPMedium)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingXs, 0))
        .Set(VisualElement.HeightRequestProperty, Dimens.Height50);

    public static Style ContentCellLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingMd, 0))
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center);

    public static Style ContentMultiSelectTableLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingMd, 0))
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center);

    public static Style ContentCellLabelFillStyle => CreateStyle<Label>()
        .BaseOn(ContentCellLabelStyle)
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Fill);

    public static Style BarItemLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5);

    public static Style FunctionButtonsLabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.MarginProperty, new Thickness(Dimens.Spacing24, Dimens.SpacingXxs))
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.End)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.WidthRequestProperty, Dimens.Spacing74)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular);

    public static Style LabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty,
            Application.Current?.RequestedTheme == AppTheme.Dark ? AppColors.White : AppColors.Black)
        .Set(Label.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Label.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.PaddingProperty, new Thickness(8, 10))
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap);

    public static Style LoginHeaderLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(Label)))
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.MarginProperty, new Thickness(0, 0, 0, 5))
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Start)
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Center);

    public static Style LoginSystemNameLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Large, typeof(Label)))
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.TextColorProperty, AppColors.DarkPastelBlue)
        .Set(Label.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style NotoSansBoldStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPBold)
        .Set(Label.FontAttributesProperty, FontAttributes.Bold)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap);

    public static Style NotoSansMediumStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap);

    public static Style NotoSansRegularStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap);

    public static Style NotoSansThinStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPThin)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT6)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap);

    public static Style OnlineOfflineModeLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Title, typeof(Label)))
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.FontAttributesProperty, FontAttributes.Bold)
        .Set(Label.TextTransformProperty, TextTransform.Uppercase);

    public static Style TestSelectionPageHeaderStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(Label)))
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(Label.TextProperty, AppResources.TestSelectionPageHeader)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Start);

    public static Style UserIdPasswordLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Micro, typeof(Label)))
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Start)
        .Set(Label.TextColorProperty, AppColors.DarkPastelBlue);

    public static Style LabelPatientInfo => CreateStyle<Label>()
        .BaseOn(NotoSansMediumStyle)
        .Set(Label.PaddingProperty, 0)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPMedium)
        .Set(Label.TextColorProperty, AppColors.Black);

    public static Style CenterLabel => CreateStyle<Label>()
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style CenterLabelBold => CreateStyle<Label>()
        .BaseOn(CenterLabel)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(Label)))
        .Set(FlexLayout.BasisProperty, new FlexBasis(0.4f, true))
        .Set(Label.TextProperty, AppResources.FirstCandidateTitle)
        .Set(Label.FontAttributesProperty, FontAttributes.Bold);

    public static Style WhiteHeaderCenterLabel => CreateStyle<Label>()
        .BaseOn(CenterLabel)
        .Set(Label.BackgroundColorProperty, AppColors.White)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT2);

    public static Style LabelHomeStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.Home)
        .Set(Label.TextTransformProperty, TextTransform.Uppercase);

    public static Style LabelBackStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.TextProperty, AppResources.Back)
        .Set(Label.TextTransformProperty, TextTransform.Uppercase);

    public static Style LabelMaskedStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.Mask)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.PaddingProperty, Dimens.SpacingSm2);

    public static Style LabelInputClientIdStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.InputClientIdButton)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style LabelPendingStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.PendingButton)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style LabelStopStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.StopButton)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style AppHeaderLabelStyle => CreateStyle<Label>()
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(Label)))
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center);

    public static Style PatientStatTitleLabel => CreateStyle<Label>()
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.HorizontalOptionsProperty, HorizontalAlignment.Center);

    public static Style IndividualProgressPatientStatTitleLabel => CreateStyle<Label>()
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.HorizontalOptionsProperty, HorizontalAlignment.Center);

    public static Style PatientStatDetailLabel => CreateStyle<Label>()
        .BaseOn(NotoSansMediumStyle)
        .Set(Label.PaddingProperty, 0)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.End)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3);

    public static Style InputResultLabel => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.PaddingProperty, 0)
        .Set(Label.TextColorProperty, AppColors.LightBlue)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(Label.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3);

    public static Style IndividualProgressPatientStatDetailLabel => CreateStyle<Label>()
        .BaseOn(NotoSansMediumStyle)
        .Set(Label.PaddingProperty, 0)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.End)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3);

    public static Style SaveButtonLabelStyle => CreateStyle<Label>()
        .Set(Label.TextProperty, AppResources.Save)
        .Set(Label.TextTransformProperty, TextTransform.Uppercase)
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Center);

    public static Style NoteMasterLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.MarginProperty, Dimens.Spacing1)
        .Set(Label.PaddingProperty, Dimens.Spacing7)
        .Set(Label.BackgroundColorProperty, AppColors.White)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4);

    public static Style NoteLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(VisualElement.HeightRequestProperty, Dimens.Height32)
        .Set(VisualElement.BackgroundColorProperty, AppColors.White)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4);

    public static Style OrderNoteLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4);

    public static Style DropDownComboboxLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(VisualStateManager.VisualStateGroupsProperty, DropDownComboboxLabelVisualStateGroups());

    public static Style IdInputDisplayLabelStyle => CreateStyle<Label>()
        .Set(Label.FontSizeProperty, Dimens.FontSize35)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.End)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style IdInputTitleLabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPSemiBold)
        .Set(Label.VerticalOptionsProperty, LayoutOptions.Start)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Start)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3);

    public static Style HeaderPatientPendingLabelStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPMedium)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingMd, 0))
        .Set(VisualElement.HeightRequestProperty, Dimens.Spacing60);

    public static Style PendingAllLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.PendingAll)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style ChangeGuideLabelStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.ChangeGuide)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style LabelPendingTestAllStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.PendingAll)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style LabeCancelAllStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.CancelAll)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style LabelStopTestAllStyle => CreateStyle<Label>()
        .BaseOn(NotoSansRegularStyle)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.TextProperty, AppResources.StopAll)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style MaskOnOffSwitchModeLabel => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style MaskOnSwitchModeLabel => CreateStyle<Label>()
        .BaseOn(MaskOnOffSwitchModeLabel)
        .Set(Label.TextColorProperty, AppColors.White);
    public static Style MaskOffSwitchModeLabel => CreateStyle<Label>()
        .BaseOn(MaskOnOffSwitchModeLabel)
        .Set(Label.TextColorProperty, AppColors.Black);

    public static Style ExamineEditLabel => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.TextProperty, AppResources.EDIT)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT4)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style WrappedTextLabelStyle => CreateStyle<Label>()
        .Set(Label.BackgroundColorProperty, AppColors.White)
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(View.MarginProperty, Dimens.Spacing7)
        .Set(Label.LineBreakModeProperty, LineBreakMode.WordWrap);

    public static Style LabelPastValueStyle => CreateStyle<Label>()
        .Set(View.HorizontalOptionsProperty, LayoutOptions.Center)
        .Set(View.VerticalOptionsProperty, LayoutOptions.Center)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT3)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.TextColorProperty, AppColors.SlateGray);

    public static Style CenteredLabelStyle => CreateStyle<Label>()
        .Set(Label.BackgroundColorProperty, AppColors.MediumSlateBlue)
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT2)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.VerticalOptionsProperty, LayoutOptions.Fill)
        .Set(Label.HorizontalOptionsProperty, LayoutOptions.Fill)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center);

    public static Style StateButtonStyle => CreateStyle<Label>()
        .Set(Label.FontSizeProperty, Dimens.FontSizeT2)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.PaddingProperty, new Thickness(Dimens.SpacingXs, 0));

    public static Style TextStateButtonStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.BackgroundColorProperty, AppColors.White)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT2)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center);

    public static Style NumpadHeaderLabelStyle => Styles.CreateStyle<Label>()
       .Set(Label.FontAttributesProperty, FontAttributes.Bold)
       .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Large, typeof(Label)))
       .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Start)
       .Set(Label.TextColorProperty, Colors.Black)
       .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style NumericPadLabelStyle => CreateStyle<Label>()
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Small, typeof(Label)))
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.MaxLinesProperty, 2)
        .Set(Label.TextColorProperty, AppColors.White);

    public static Style NumericPadTextStyle => CreateStyle<Label>()
        .Set(Label.PaddingProperty, new Thickness(0, 15, 5, 15))
        .Set(VisualElement.BackgroundColorProperty, Colors.White)
        .Set(Label.FontSizeProperty, Device.GetNamedSize(NamedSize.Header, typeof(Label)))
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.End)
        .Set(Label.TextColorProperty, Colors.Black)
        .Set(Label.FontAttributesProperty, FontAttributes.Bold)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Start);

    public static Style CommonLabelForButtonStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.White)
        .Set(VisualElement.BackgroundColorProperty, AppColors.Transparent)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.LineHeightProperty, 1.3)
        .Set(Label.LineBreakModeProperty, LineBreakMode.CharacterWrap)
        .Set(Label.FontSizeProperty, Dimens.FontSizeFooterButton)
        .Set(Label.TextProperty, AppResources.CancelAll)
        .Set(Label.HorizontalTextAlignmentProperty, TextAlignment.Center)
        .Set(Label.VerticalTextAlignmentProperty, TextAlignment.Center);

    public static Style BackLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.TurnBack);

    public static Style EnterIdLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.InputClientIdButton);

    public static Style FinishLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.Shutdown);

    public static Style LogoutLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.Logout);

    public static Style RegisterLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.Register);

    public static Style ChangeGuidanceLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.ChangeGuide);

    public static Style BulkReleaseHoldPatientsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.CancelPending);

    public static Style BulkCancelPauseTestsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.CancelAll);

    public static Style BulkReleaseHoldTestsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.CancelAll);

    public static Style BulkHoldPatientsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.PendingAll);

    public static Style BulkHoldTestsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.PendingAll);

    public static Style HoldLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.PendingButton);

    public static Style BulkRegisterHoldPatientLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.PendingButton);

    public static Style PauseLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.StopButton);

    public static Style BulkPauseTestsLabelStyle => CreateStyle<Label>()
        .BaseOn(CommonLabelForButtonStyle)
        .Set(Label.TextProperty, AppResources.StopAll);
    public static Style MessageBodyStyle => CreateStyle<Label>()
        .Set(Label.TextColorProperty, AppColors.Black)
        .Set(Label.FontSizeProperty, Dimens.FontSizeT5)
        .Set(Label.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(Label.LineBreakModeProperty,LineBreakMode.WordWrap);

    public static Style MessageTitleStyle => CreateStyle<Label>()
        .BaseOn(MessageBodyStyle)
        .Set(Label.FontAttributesProperty, FontAttributes.Bold);
}