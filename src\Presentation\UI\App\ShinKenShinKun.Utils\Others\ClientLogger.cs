﻿using static NPOI.HSSF.Util.HSSFColor;
using FileSystem = Microsoft.Maui.Storage.FileSystem;

namespace ShinKenShinKun.Utils;

/// <summary>
/// .NET 9 MAUI Migrated
/// </summary>
public class ClientLogger
{
    private DataAccessControl DbCtl;
    public bool MODE_IIS = false;
    public string Collect_Log_Path = string.Empty;
    private static ClientLogger? Instance;
    private static readonly object Lock = new();
    private static int WriteCount = 999;
    private readonly ILogger Logger;
    private bool delFlg = false;

    private ClientLogger(int logOutputCount)
    {
        WriteCount = logOutputCount;

        try
        {
            DbCtl = DataAccessControl.GetInstance();

            Logger = Log.Logger;

            Logger.Information("ClientLogger initialized with WriteCount = {WriteCount}", WriteCount);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ClientLogger initialization failed: {ex.Message}");
        }
    }

    public static ClientLogger GetInstance(int logOutputCount)
    {
        lock (Lock)
        {
            return Instance ??= new ClientLogger(logOutputCount);
        }
    }

    public static ClientLogger GetInstance()
    {
        return GetInstance(WriteCount);
    }

    public void Write(string message)
    {
        Logger.Information(message);
    }

    public void Write(string title, string data)
    {
        Logger.Information("{Title}: {Data}", title, data);
    }

    public void Write(string[] messages)
    {
        Logger.Information("Array: {Messages}", string.Join(", ", messages));
    }

    public void Write(string title, string[] messages)
    {
        Logger.Information("{Title}: {Messages}", title, string.Join(", ", messages));
    }

    public void WriteTestMode(string title, string data)
    {
        Write(title, data);
    }

    private string GetIp()
    {
        string result = string.Empty;
        IPHostEntry hostEntry = Dns.GetHostEntry(Dns.GetHostName());
        foreach (IPAddress ip in hostEntry.AddressList)
        {
            if (ip.AddressFamily == AddressFamily.InterNetwork)
            {
                result = ip.ToString();
                break;
            }
        }
        return result;
    }

    public void Close()
    {
        try
        {
            if (!MODE_IIS)
            {
                GetCollectLogSetting();

                if (!string.IsNullOrEmpty(Collect_Log_Path))
                {
                    var logFiles = Directory.GetFiles(FileSystem.AppDataDirectory, "*.log");
                    var collectFiles = Directory.GetFiles(Collect_Log_Path);

                    if (collectFiles.Length == 0)
                    {
                        foreach (var file in logFiles)
                        {
                            File.Copy(file, Path.Combine(Collect_Log_Path, Path.GetFileName(file)), true);
                        }
                    }
                    else
                    {
                        var latestLog = logFiles.OrderByDescending(f => f).FirstOrDefault();
                        if (latestLog != null)
                        {
                            var latestName = Path.GetFileName(latestLog);
                            File.Copy(latestLog, Path.Combine(Collect_Log_Path, latestName), true);

                            var firstCollectFile = collectFiles.FirstOrDefault();
                            var collectName = Path.GetFileName(firstCollectFile);

                            if (!string.Equals(latestName, collectName, StringComparison.OrdinalIgnoreCase))
                            {
                                File.Delete(firstCollectFile);
                            }
                        }
                    }
                }
            }

            Log.CloseAndFlush();

            if (!string.IsNullOrEmpty(Collect_Log_Path))
            {
                var sortedFiles = SortLogFiles(Directory.GetFiles(FileSystem.AppDataDirectory, "*.log"));
                if (sortedFiles.Length > 0)
                {
                    var latestLog = sortedFiles[0];
                    var fileName = Path.GetFileName(latestLog);
                    var content = File.ReadAllText(latestLog, Encoding.UTF8);

                    Logger?.Information("サーバー側フォルダへログコピー: {FileName}", fileName);
                    DbCtl.SFCreateServerFileForEncode(Collect_Log_Path, fileName, content, true, "utf-8");
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Error(ex, "ログクローズ時のエラー");
        }
    }

    public void Flush()
    {
        Log.CloseAndFlush();
    }

    private void DeleteOldLogs(string logDirectory, int maxCount)
    {
        if (delFlg) return;
        delFlg = true;

        try
        {
            if (!Directory.Exists(logDirectory)) return;

            var logFiles = Directory.GetFiles(logDirectory, "*.log")
                                    .OrderByDescending(f => f)
                                    .ToList();

            for (int i = maxCount; i < logFiles.Count; i++)
            {
                File.Delete(logFiles[i]);
            }
        }
        catch (Exception ex)
        {
            Logger?.Error(ex, "古いログの削除中にエラーが発生しました。");
        }
    }

    private string[] SortLogFiles(string[] files)
    {
        try
        {
            return files.OrderByDescending(f => f).ToArray();
        }
        catch (Exception ex)
        {
            Logger?.Warning(ex, "ログファイルのソートに失敗しました。");
            return files;
        }
    }

    private void GetCollectLogSetting()
    {
        try
        {
            string ip = GetIp();
            string basePath = ConfigReadUtility.GetConfigData("CollectLogPath");

            if (!string.IsNullOrEmpty(basePath))
            {
                Collect_Log_Path = Path.Combine(basePath, $"{ip}_Log");
            }

            Logger?.Information("サーバー側フォルダパス: {Path}", Collect_Log_Path);

            if (!MODE_IIS)
            {
                if (!Directory.Exists(Collect_Log_Path))
                {
                    Directory.CreateDirectory(Collect_Log_Path);
                    Logger?.Information("サーバー側フォルダ作成完了: {Path}", Collect_Log_Path);
                }
                else
                {
                    Logger?.Information("サーバー側フォルダ存在: {Path}", Collect_Log_Path);
                }
            }
            else
            {
                if (!DbCtl.SFGetDirectoryExist(Collect_Log_Path))
                {
                    DbCtl.SFCreateServerFolder(Collect_Log_Path);
                    Logger?.Information("IISモード：サーバー側フォルダ作成完了: {Path}", Collect_Log_Path);
                }
                else
                {
                    Logger?.Information("IISモード：サーバー側フォルダ存在: {Path}", Collect_Log_Path);
                }
            }
        }
        catch (Exception ex)
        {
            Logger?.Error(ex, "GetCollectLogSetting 実行中にエラーが発生しました。");
        }
    }
}