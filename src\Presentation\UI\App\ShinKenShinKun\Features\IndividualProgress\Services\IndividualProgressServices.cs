﻿namespace ShinKenShinKun;

public class IndividualProgressServices(
IFileSystemService fileSystemService,
IMapper mapper) : IIndividualProgressServices
{
    public ObservableCollection<MedicalCheckModel> GetMedicalCheckClients()
    {
        var data = AppConstants.MedicalCheckFilters
            .Select(x => new MedicalCheckModel { DisplayName = x, Value = x });
        return data.ToObservableCollection();
    }

    public ObservableCollection<ClientInformationModel> LoadDataClientInformations(MedicalCheckModel medicalCheck)
    {
        var data = fileSystemService.ReadClientExcelFile()
            .Where(c =>
                string.IsNullOrEmpty(medicalCheck.Value) || c.MedicalChecklist.Contains(medicalCheck.Value))
            .OrderBy(c => c.WaitTime);

        if(data == null || !data.Any())
            return [];
        return mapper.Map<ObservableCollection<ClientInformationModel>>(data);
    }

    public IndividualPatientInfoDto PatientInfoGenerate()
    {
        return new IndividualPatientInfoDto
        {
            IndividualStats = new ObservableCollection<IndividualPatientModel>
        {
            new IndividualPatientModel { Id = 1, Name = AppResources.Height, Value = "172.3"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Weight, Value = "65.3"},
            new IndividualPatientModel { Id = 1, Name = AppResources.BodyFat, Value = "12.3"},
            new IndividualPatientModel { Id = 1, Name = AppResources.BodyMassIndex, Value = "23.2"},
        },
            IndividualSight = new ObservableCollection<IndividualPatientModel>
        {
            new IndividualPatientModel { Id = 1, Name = AppResources.RightNakedEye, Value = "0.5"},
            new IndividualPatientModel { Id = 1, Name = AppResources.LeftNakedEye, Value = "0.5"},
            new IndividualPatientModel { Id = 1, Name = AppResources.RightEye, Value = "1.2"},
            new IndividualPatientModel { Id = 1, Name = AppResources.LeftEye, Value = "1.2"},
        },
            IndividualBloodPressure = new ObservableCollection<IndividualPatientModel>
        {
            new IndividualPatientModel { Id = 1, Name = AppResources.HighestBloodPressure1, Value = "120"},
            new IndividualPatientModel { Id = 1, Name = AppResources.LowestBloodPressure1, Value = "60"},
            new IndividualPatientModel { Id = 1, Name = AppResources.HighestBloodPressure1, Value = "118"},
            new IndividualPatientModel { Id = 1, Name = AppResources.LowestBloodPressure2, Value = "61"},
        },
            IndividualHearing = new ObservableCollection<IndividualPatientModel>
        {
            new IndividualPatientModel { Id = 1, Name = AppResources.Right1000Hz, Value = "〇"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left1000Hz, Value = "〇"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right4000Hz, Value = "〇"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left4000Hz, Value = "〇"},
        },
            IndividualHearingThreshold = new ObservableCollection<IndividualPatientModel>
        {
            new IndividualPatientModel { Id = 1, Name = AppResources.Right250Hz, Value = "10"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left250Hz, Value = "10"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right500Hz, Value = "15"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left500Hz, Value = "15"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right1000Hz, Value = "10"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left1000Hz, Value = "10"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right2000Hz, Value = "20"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left2000Hz, Value = "25"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right4000Hz, Value = "30"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left4000Hz, Value = "35"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Right8000Hz, Value = "40"},
            new IndividualPatientModel { Id = 1, Name = AppResources.Left8000Hz, Value = "45"},
        }
        };
    }
}
