namespace ShinKenShinKun;

public partial class BulkRegisterHoldPatientsButtonView : RadBorder
{
    public BulkRegisterHoldPatientsButtonView()
    {
        InitializeComponent();
    }

    public static readonly BindableProperty BulkRegisterHoldPatientsCommandProperty = BindableProperty.Create(
        nameof(BulkRegisterHoldPatientsCommand),
        typeof(IRelayCommand),
        typeof(BulkRegisterHoldPatientsButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand BulkRegisterHoldPatientsCommand
    {
        get => (IRelayCommand)GetValue(BulkRegisterHoldPatientsCommandProperty);
        set => SetValue(BulkRegisterHoldPatientsCommandProperty, value);
    }
}