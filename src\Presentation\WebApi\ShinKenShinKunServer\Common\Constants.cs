﻿namespace ShinKenShinKunServer.Common
{
    internal static class Constants
    {
        // LoginId
        internal const int LoginIdMinLength = 5;

        internal const int LoginIdMaxLength = 50;

        // Password
        internal const int PasswordMinLength = 8;

        internal const int PasswordMaxLength = 50;

        // UserName
        internal const int UserNameMinLength = 5;

        internal const int UserNameMaxLength = 100;

        // UserName
        internal const int EmailMinLength = 5;

        internal const int EmailMaxLength = 256;

        internal const int LoginFailureCountMax = 5;

        // Regex patterns
        internal const string PasswordPattern = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,}$";

        internal const string LoginidPattern = @"^[a-zA-Z0-9.]+$";
    }
}