﻿namespace ShinKenShinKun.UI;

public partial class Styles
{
    public static Style ComboboxStyle => CreateStyle<RadComboBox>()
        .Set(RadBorder.CornerRadiusProperty, Dimens.RadBorderCornerRadius0)
        .Set(RadBorder.BorderThicknessProperty, Dimens.RadBorderThickness0)
        .Set(RadComboBox.IsClearButtonVisibleProperty, false)
        .Set(RadComboBox.SearchModeProperty, SearchMode.Contains)
        .Set(RadComboBox.SelectionModeProperty, SelectionMode.Single)
        .Set(RadComboBox.TextColorProperty, AppColors.Black)
        .Set(RadComboBox.FontFamilyProperty, FontNames.NotoSansJPRegular)
        .Set(RadComboBox.DropDownCornerRadiusProperty, Dimens.RadBorderCornerRadius5)
        .Set(RadComboBox.DropDownBorderThicknessProperty, Dimens.RadBorderThickness1)
        .Set(RadComboBox.FontSizeProperty, Dimens.FontSizeT4);
}
