namespace ShinKenShinKun;

public partial class LoginUserView : ContentView
{
    public static readonly BindableProperty LoginUserProperty = BindableProperty.Create(
        nameof(LoginUser),
        typeof(string),
        typeof(LoginUserView),
        string.Empty,
        BindingMode.TwoWay);

	public LoginUserView()
	{
		InitializeComponent();
	}

    public string LoginUser
    {
        get => (string)GetValue(LoginUserProperty);
        set => SetValue(LoginUserProperty, value);
    }
}