namespace ShinKenShinKun;

public partial class FinishButtonView : RadBorder
{
	public FinishButtonView()
	{
		InitializeComponent();
	}

    public static readonly BindableProperty FinishCommandProperty = BindableProperty.Create(
        nameof(FinishCommand),
        typeof(IRelayCommand),
        typeof(FinishButtonView),
        defaultBindingMode: BindingMode.TwoWay);

    public IRelayCommand FinishCommand
    {
        get => (IRelayCommand)GetValue(FinishCommandProperty);
        set => SetValue(FinishCommandProperty, value);
    }
}