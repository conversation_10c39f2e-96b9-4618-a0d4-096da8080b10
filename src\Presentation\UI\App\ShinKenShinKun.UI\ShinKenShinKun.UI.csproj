﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0;net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>
	<PropertyGroup Condition="$(Configuration.Contains('Release')) ">
		<DebugType>none</DebugType>
	</PropertyGroup>

	<ItemGroup>	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Black.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Bold.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-ExtraBold.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-ExtraLight.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Light.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Medium.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Regular.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-SemiBold.ttf" />
	  <None Remove="Fonts\NotoSanJP\Fonts\NotoSansJP-Thin.ttf" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Maui" />
		<PackageReference Include="CommunityToolkit.Mvvm" />
		<PackageReference Include="Microsoft.Maui.Controls" />
		<PackageReference Include="Syncfusion.Maui.Toolkit" />
		<PackageReference Include="Telerik.UI.for.Maui" />
	</ItemGroup>

	<ItemGroup>
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Black.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Bold.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-ExtraBold.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-ExtraLight.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Light.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Medium.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Regular.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-SemiBold.ttf" />
	  <MauiFont Include="Fonts\NotoSanJP\Fonts\NotoSansJP-Thin.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\ShinKenShinKun.CoreMVVM\ShinKenShinKun.CoreMVVM.csproj" />
	  <ProjectReference Include="..\ShinKenShinKun.DataAccess\ShinKenShinKun.DataAccess.csproj" />
	  <ProjectReference Include="..\ShinKenShinKun.Utils\ShinKenShinKun.Utils.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Controls\FunctionButtonControl.xaml.cs">
	    <DependentUpon>FunctionButtonControl.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Controls\MultiSelectTableControl.xaml.cs">
	    <DependentUpon>MultiSelectTableControl.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Controls\StateButtonControl.xaml.cs">
	    <DependentUpon>StateButtonControl.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Views\QuitAndShutdownPopup.xaml.cs">
	    <DependentUpon>QuitAndShutdownPopup.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Update="Controls\DialogMessage.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\DropDownControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\FunctionButtonControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\InputControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\LabelInputResultControl.xaml">
		<Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\LoadingIndicator.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\StaticLabelControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\LabelShowPastValueControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\MultiLineLabelControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\MultiSelectTableControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\StateButtonControl.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Controls\NumberPad.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	  <MauiXaml Update="Views\QuitAndShutdownPopup.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </MauiXaml>
	</ItemGroup>

</Project>