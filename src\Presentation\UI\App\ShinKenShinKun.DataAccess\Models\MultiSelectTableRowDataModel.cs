﻿namespace ShinKenShinKun.DataAccess;

public partial class MultiSelectTableRowDataModel : BaseModel
{
    [ObservableProperty] private int testTypeNumber;
    [ObservableProperty] private int testItemNumber;
    [ObservableProperty] private string title;
    [ObservableProperty] private int noOrderDisplay;
    [ObservableProperty] private string pressEvent;
    [ObservableProperty] private string numbericKeypadSettingID;
    [ObservableProperty] private string pressSwitchingSettingID;
    [ObservableProperty] private string[] data;
    [ObservableProperty] private string average;
    [ObservableProperty] private string oldValue;
    [ObservableProperty] private bool isSelected;
}