﻿namespace ShinKenShinKun.UI;

public partial class MultiSelectTableControl : ContentView
{
    public MultiSelectTableControl()
    {
        InitializeComponent();
    }

    #region BindableProperties

    public static readonly BindableProperty TableConfigProperty =
        BindableProperty.Create(nameof(TableConfig), typeof(MultiSelectTableConfigModel),
            typeof(MultiSelectTableControl), propertyChanged: OnDataChanged);

    public static readonly BindableProperty TestItemDtoProperty =
        BindableProperty.Create(nameof(TestItemDto), typeof(ObservableCollection<MultiSelectTableRowDataModel>),
            typeof(MultiSelectTableControl), propertyChanged: OnDataChanged);

    #endregion BindableProperties

    #region Properties

    public MultiSelectTableConfigModel TableConfig
    {
        get => (MultiSelectTableConfigModel)GetValue(TableConfigProperty);
        set => SetValue(TableConfigProperty, value);
    }

    public ObservableCollection<MultiSelectTableRowDataModel> TestItemDto
    {
        get => (ObservableCollection<MultiSelectTableRowDataModel>)GetValue(TestItemDtoProperty);
        set => SetValue(TestItemDtoProperty, value);
    }

    public int SettingNumberPadId { get; set; }

    private List<RadCheckBox> checkBoxes = [];
    public string InputFormat { get; set; }

    #endregion Properties

    #region Functions

    /// <summary>
    /// Called when data properties change and rebuilds the grid if needed.
    /// </summary>
    /// <param name="bindable">The bindable object.</param>
    /// <param name="oldValue">The old value.</param>
    /// <param name="newValue">The new value.</param>
    private static void OnDataChanged(BindableObject bindable, object oldValue, object newValue)
    {
        var control = (MultiSelectTableControl)bindable;
        if (control.TableConfig != null && control.TestItemDto != null)
        {
            control.BuildGrid();
        }
    }

    /// <summary>
    /// Update the Checkbox when check or uncheck
    /// </summary>
    private void UpdateCheckboxStates()
    {
        if (checkBoxes == null || TableConfig == null)
            return;

        int maxSelection = TableConfig.NumberOfSelection;

        int selectedCount = checkBoxes.Count(cb => cb.IsChecked == true);

        foreach (var checkBox in checkBoxes)
        {
            checkBox.IsEnabled = selectedCount < maxSelection || checkBox.IsChecked == true;
        }
    }

    /// <summary>
    /// Calculates the average value across measurement properties for a data item.
    /// </summary>
    /// <param name="item">The data item.</param>
    /// <returns>The average value.</returns>
    private string CalculateAverage(MultiSelectTableRowDataModel item)
    {
        if (item.Data == null || item.Data.Length == 0) return "0";

        double sum = 0;
        int count = 0;

        foreach (var value in item.Data)
        {
            if (!string.IsNullOrEmpty(value) && double.TryParse(value, out double d))
            {
                sum += d;
                count++;
            }
        }

        return count > 0 ? (sum / count).ToString("0.##") : "0";
    }

    /// <summary>
    /// Gets the text representation of a property value.
    /// </summary>
    /// <param name="item">The data item.</param>
    /// <param name="propName">The property name.</param>
    /// <returns>The property value as text.</returns>
    private static string GetPropValueText(object item, string propName)
    {
        var prop = item.GetType().GetProperty(propName);
        var val = prop?.GetValue(item);
        return val != null && double.TryParse(val.ToString(), out double d) ? d.ToString("0.##") : "";
    }

    #endregion Functions

    #region Grid Build

    /// <summary>
    /// Builds the grid layout based on the current configuration.
    /// </summary>
    private void BuildGrid()
    {
        DynamicGrid.Children.Clear();
        DynamicGrid.RowDefinitions.Clear();
        DynamicGrid.ColumnDefinitions.Clear();
        if (TableConfig.IsHorizontalLayout)
            BuildDefaultLayout();
        else
            BuildRotatedLayout();
    }

    /// <summary>
    /// Sets up the grid with the specified number of rows and columns.
    /// </summary>
    /// <param name="rows">Number of rows.</param>
    /// <param name="cols">Number of columns.</param>
    private void SetupGrid(int rows, int cols)
    {
        DynamicGrid.RowDefinitions.Clear();
        DynamicGrid.ColumnDefinitions.Clear();
        DynamicGrid.Children.Clear();
        
        var radDataGrid = new RadDataGrid() { WidthRequest = 0, HeightRequest = 0, Opacity = 0};
        radDataGrid.Commands.Add(new KeyDownCommand());
        DynamicGrid.Children.Add(radDataGrid);
        for (int i = 0; i < rows; i++)
            DynamicGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });

        for (int i = 0; i < cols; i++)
            DynamicGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Star });
    }

    #endregion Grid Build

    #region Table Build

    /// <summary>
    /// Builds the default (horizontal) table layout.
    /// </summary>
    private void BuildDefaultLayout()
    {
        if (TestItemDto == null || TableConfig == null)
            return;

        int rows = TestItemDto.Count + 1;

        // - Columns:
        //   1 for title column
        //   + NumberOfMeasurements columns for data inputs
        //   + 1 optional column for average (if enabled)
        //   + 1 optional column for old value (if enabled)
        int cols = 1 + TableConfig.NumberOfMeasurements +
                   (TableConfig.IsAverageResultDisplay ? 1 : 0) +
                   (TableConfig.IsOldResultDisplay ? 1 : 0);

        SetupGrid(rows, cols);

        int col = 0;

        AddHeader("", 0, col++);

        for (int i = 0; i < TableConfig.NumberOfMeasurements; i++)
        {
            AddHeader($"{string.Format(AppResources.NumberOf, i + 1)}", 0, col++, includeCheckbox: true);
        }

        if (TableConfig.IsAverageResultDisplay)
            AddHeader(AppResources.AverageValue, 0, col++);

        if (TableConfig.IsOldResultDisplay)
            AddHeader(AppResources.OldValue, 0, col++, isOldValue: true);

        for (int row = 0; row < TestItemDto.Count; row++)
        {
            BuildDefaultRow(TestItemDto[row], row + 1, cols);
        }
    }

    /// <summary>
    /// Builds a row for the default layout.
    /// </summary>
    /// <param name="item">The data item.</param>
    /// <param name="rowIndex">The row index.</param>
    /// <param name="totalCols">Total number of columns.</param>
    private void BuildDefaultRow(MultiSelectTableRowDataModel item, int rowIndex, int totalCols)
    {
        int expectedLength = TableConfig.NumberOfMeasurements;

        if (item.Data == null || item.Data.Length != expectedLength)
        {
            var newData = new string[expectedLength];
            if (item.Data != null)
            {
                for (int i = 0; i < Math.Min(item.Data.Length, expectedLength); i++)
                {
                    newData[i] = item.Data[i];
                }
            }
            item.Data = newData;
        }

        int currentCol = 0;
        AddHeader(item.Title ?? "", rowIndex, currentCol++);

        string avg = CalculateAverage(item);
        item.Average = avg;

        Label? avgLabel = null;

        for (int i = 0; i < item.Data.Length; i++)
        {
            int index = i;

            AddEntryCell(
                item.Title,
                item.Data[index] ?? "",
                rowIndex,
                currentCol++,
                newText =>
                {
                    item.Data[index] = newText;

                    string newAvg = CalculateAverage(item);
                    item.Average = newAvg;
                    if (avgLabel != null)
                        avgLabel.Text = newAvg;
                }
            );
        }

        // If the table is configured to display the average value,
        // place the average in the appropriate column.
        // - If "OldValue" is also displayed, average goes 2 columns from the end.
        // - If not, average goes in the last column.
        if (TableConfig.IsAverageResultDisplay)
            avgLabel = AddCell(avg, rowIndex, totalCols - (TableConfig.IsOldResultDisplay ? 2 : 1));

        // If the table is configured to display the old value,
        // place it in the last column.
        if (TableConfig.IsOldResultDisplay)
            AddCell(item.OldValue, rowIndex, totalCols - 1, isOldValue: true);
    }

    /// <summary>
    /// Builds the rotated (vertical) table layout.
    /// </summary>
    private void BuildRotatedLayout()
    {
        if (TestItemDto == null || TableConfig == null)
            return;

        int expectedLength = TableConfig.NumberOfMeasurements;

        foreach (var item in TestItemDto)
        {
            if (item.Data == null || item.Data.Length != expectedLength)
            {
                var newData = new string[expectedLength];
                if (item.Data != null)
                {
                    for (int i = 0; i < Math.Min(item.Data.Length, expectedLength); i++)
                    {
                        newData[i] = item.Data[i];
                    }
                }
                item.Data = newData;
            }
        }
        // Calculate the total number of rows in the table:
        // - 1 row for the header
        // - 'expectedLength' rows for the measurement data (e.g., Data1, Data2, ..., DataN)
        // - 1 additional row if the average value should be displayed
        // - 1 additional row if the old value should be displayed
        int rows = 1 + expectedLength + (TableConfig.IsAverageResultDisplay ? 1 : 0) +
                   (TableConfig.IsOldResultDisplay ? 1 : 0);
        int cols = TestItemDto.Count + 1;
        SetupGrid(rows, cols);

        AddHeader("", 0, 0);

        for (int i = 0; i < expectedLength; i++)
        {
            AddHeader($"{string.Format(AppResources.NumberOf, i + 1)}", i + 1, 0, true);
        }

        int nextRow = expectedLength + 1;
        if (TableConfig.IsAverageResultDisplay)
            AddHeader(AppResources.AverageValue, nextRow++, 0);
        if (TableConfig.IsOldResultDisplay)
            AddHeader(AppResources.OldValue, nextRow, 0, isOldValue: true);

        for (int col = 0; col < TestItemDto.Count; col++)
            BuildRotatedColumn(TestItemDto[col], col + 1);
    }

    /// <summary>
    /// Builds a column for the rotated layout.
    /// </summary>
    /// <param name="item">The data item.</param>
    /// <param name="colIndex">The column index.</param>
    private void BuildRotatedColumn(MultiSelectTableRowDataModel item, int colIndex)
    {
        AddHeader(item.Title ?? "", 0, colIndex);

        string avg = CalculateAverage(item);
        item.Average = avg;

        Label? avgLabel = null;

        for (int i = 0; i < item.Data.Length; i++)
        {
            int index = i; // local copy to safely capture the correct index

            AddEntryCell(
                item.Title,
                item.Data[i] ?? "",
                i + 1,
                colIndex,
                newText =>
                {
                    item.Data[index] = newText;

                    string newAvg = CalculateAverage(item);
                    item.Average = newAvg;
                    if (avgLabel != null)
                        avgLabel.Text = newAvg;
                }
            );
        }

        int row = item.Data.Length + 1;
        if (TableConfig.IsAverageResultDisplay)
            avgLabel = AddCell(avg, row++, colIndex);
        if (TableConfig.IsOldResultDisplay)
            AddCell(item.OldValue, row, colIndex, isOldValue: true);
    }

    #endregion Table Build

    #region Cell Generate

    /// <summary>
    /// Adds an Entry cell to the dynamic grid at the specified row and column.
    /// </summary>
    /// <param name="text">Initial text for the Entry.</param>
    /// <param name="row">Row index in the grid.</param>
    /// <param name="column">Column index in the grid.</param>
    /// <param name="onTextChanged">Callback invoked when the Entry text changes.</param>
    private void AddEntryCell(
    string title,
    string text,
    int row,
    int column,
    Action<string> onTextChanged)
    {
        var label = CreateStyledLabel(title, text, onTextChanged);

        var cell = new Grid
        {
            Style = Styles.MultiSelectTableCellStyle
        };
        cell.Children.Add(label);

        AddProperBorders(cell, row, column);

        DynamicGrid.Add(cell, column, row);
    }

    /// <summary>
    /// Creates a styled Entry with read-only behavior and a tap gesture to open a number pad popup.
    /// </summary>
    /// <param name="text">Initial text for the Entry.</param>
    /// <param name="onTextChanged">Callback invoked when the text is updated via the number pad.</param>
    /// <returns>A configured Entry control.</returns>
    private Label CreateStyledLabel(
        string title,
        string text,
        Action<string> onTextChanged)
    {
        var label = new Label
        {
            Text = text,
            TextColor = AppColors.LightBlue,
            Style = Styles.ContentMultiSelectTableLabelStyle
        };

        var tapGesture = new TapGestureRecognizer();
        tapGesture.Tapped += async (s, e) =>
        {
            var popupService = Application.Current?.Handler.MauiContext.Services.GetService<IPopupService>();
            await popupService.ShowPopupAsync<NumberPadViewModel>(vm =>
            {
                vm.Text = label.Text;
                vm.IsStandard = false;
                vm.InputFormat = InputFormat;
                vm.SettingNumberPadId = SettingNumberPadId;
                vm.Comfirm = new RelayCommand<string>((newValue) =>
                {
                    label.Text = newValue;
                    onTextChanged?.Invoke(newValue);
                });
                vm.Title = title;
            });
        };

        label.GestureRecognizers.Add(tapGesture);

        return label;
    }

    /// <summary>
    /// Adds appropriate borders to a grid cell based on its row and column position.
    /// </summary>
    /// <param name="cell">Grid cell to add borders to.</param>
    /// <param name="row">Row index of the cell.</param>
    /// <param name="column">Column index of the cell.</param>
    private static void AddProperBorders(Grid cell, int row, int column)
    {
        bool isFirstRow = row == 1;
        bool isFirstCol = column == 0;

        if (isFirstRow && isFirstCol)
            AddBorders(cell, right: true, bottom: true, left: true);
        else if (isFirstCol)
            AddBorders(cell, bottom: true, right: true, left: true);
        else
            AddBorders(cell, bottom: true, right: true);
    }

    /// <summary>
    /// Adds a StaticLabel cell to the dynamic grid at the specified row and column.
    /// </summary>
    /// <param name="text">Text content of the StaticLabel.</param>
    /// <param name="row">Row index in the grid.</param>
    /// <param name="column">Column index in the grid.</param>
    /// <returns>The created Label instance, so its content can be updated later.</returns>
    private Label AddCell(string text, int row, int column, bool isOldValue = false)
    {
        var label = new Label
        {
            Text = text,
            TextColor = isOldValue ? AppColors.SlateGray : AppColors.LightBlue,
            Style = Styles.ContentMultiSelectTableLabelStyle,
        };

        var cell = new Grid
        {
            Padding = 0,
            Margin = 0,
            HorizontalOptions = LayoutOptions.Fill,
            VerticalOptions = LayoutOptions.Fill,
        };

        if (row == 1 && column == 0)
        {
            AddBorders(cell, right: true, bottom: true, left: true);
        }
        else if (column == 0)
        {
            AddBorders(cell, bottom: true, right: true, left: true);
        }
        else
        {
            AddBorders(cell, bottom: true, right: true);
        }

        cell.Children.Add(label);
        DynamicGrid.Add(cell, column, row);
        return label;
    }

    /// <summary>
    /// Adds a header cell with optional checkbox to the dynamic grid.
    /// </summary>
    /// <param name="text">Text content of the header.</param>
    /// <param name="row">Row index in the grid.</param>
    /// <param name="column">Column index in the grid.</param>
    /// <param name="includeCheckbox">Whether to include a CheckBox next to the header text.</param>
    private void AddHeader(string text, int row, int column, bool includeCheckbox = false, bool isOldValue = false)
    {
        HorizontalStackLayout headerContent;

        if (includeCheckbox)
        {
            var checkBox = new RadCheckBox
            {
                VerticalOptions = LayoutOptions.Center
            };

            checkBoxes.Add(checkBox);

            checkBox.IsCheckedChanged += (s, e) =>
            {
                UpdateCheckboxStates();
            };

            headerContent = new HorizontalStackLayout
            {
                Style = Styles.MultiSelectTableCheckboxHeaderStyle,
                Children =
            {
                checkBox,
                new Label
                {
                    Text = text,
                    Style = Styles.HeaderMultiSelectLabelStyle,
                }
            }
            };
        }
        else
        {
            headerContent = new HorizontalStackLayout
            {
                HorizontalOptions = LayoutOptions.Center,
                Spacing = 5,
                Children =
            {
                new Label
                {
                    Text = text,
                    TextColor = isOldValue ? AppColors.SlateGray : AppColors.LightBlue,
                    Style = Styles.HeaderMultiSelectLabelStyle,
                }
            }
            };
        }

        var cell = new Grid();

        if (row == 0 && column == 0)
        {
            AddBorders(cell, right: true, bottom: true);
        }
        else if (column == 0)
        {
            AddBorders(cell, left: true, right: true, bottom: true);
        }
        else if (row == 0)
        {
            AddBorders(cell, top: true, right: true, bottom: true);
        }

        cell.Children.Add(headerContent);
        DynamicGrid.Add(cell, column, row);
    }

    /// <summary>
    /// Adds borders (top, right, bottom, left) to a grid cell.
    /// </summary>
    /// <param name="grid">Grid to which borders will be added.</param>
    /// <param name="top">Whether to add a top border.</param>
    /// <param name="right">Whether to add a right border.</param>
    /// <param name="bottom">Whether to add a bottom border.</param>
    /// <param name="left">Whether to add a left border.</param>
    private static void AddBorders(Grid grid, bool top = false, bool right = false, bool bottom = false, bool left = false)
    {
        const int borderThickness = 1;
        var color = Colors.LightGray;
        if (top)
        {
            grid.Children.Add(new BoxView
            {
                BackgroundColor = color,
                HeightRequest = borderThickness,
                VerticalOptions = LayoutOptions.Start,
                HorizontalOptions = LayoutOptions.Fill
            });
        }

        if (right)
        {
            var boxview = new BoxView
            {
                BackgroundColor = color,
                WidthRequest = borderThickness,
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.End
            };
            grid.Children.Add(boxview);
        }

        if (bottom)
        {
            grid.Children.Add(new BoxView
            {
                BackgroundColor = color,
                HeightRequest = borderThickness,
                VerticalOptions = LayoutOptions.End,
                HorizontalOptions = LayoutOptions.Fill
            });
        }

        if (left)
        {
            grid.Children.Add(new BoxView
            {
                BackgroundColor = color,
                WidthRequest = borderThickness,
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Start
            });
        }
    }

    #endregion Cell Generate
}