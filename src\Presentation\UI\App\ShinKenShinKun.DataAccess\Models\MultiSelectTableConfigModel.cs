﻿namespace ShinKenShinKun.DataAccess;

public partial class MultiSelectTableConfigModel : ComponentLayoutModel
{
    [ObservableProperty] private int numberOfMeasurements;
    [ObservableProperty] private int numberOfSelection;
    [ObservableProperty] private bool isAverageResultDisplay;
    [ObservableProperty] private bool isOldResultDisplay;
    [ObservableProperty] private bool isHorizontalLayout;
    [ObservableProperty] private ObservableCollection<MultiSelectTableRowDataModel> items;
}