﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <RootNamespace>ShinKenShinKun.Infrastructure</RootNamespace>
        <AssemblyName>ShinKenShinKun.Infrastructure</AssemblyName>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper" />
        <PackageReference Include="Azure.Identity" />
        <PackageReference Include="CsvHelper" />
        <PackageReference Include="Microsoft.AspNetCore.ApiAuthorization.IdentityServer" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Core\Application\Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Persistence\Migrations\" />
    </ItemGroup>

</Project>
