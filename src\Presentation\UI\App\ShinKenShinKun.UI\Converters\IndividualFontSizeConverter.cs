﻿namespace ShinKenShinKun.UI;

public class IndividualFontSizeConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double number)
        {

            if (value == null) return Dimens.FontSizeT3; // Default font size

            string text = value.ToString();

            int length = text.Length;

            var screenWidth = DeviceDisplay.MainDisplayInfo.Width / DeviceDisplay.MainDisplayInfo.Density;
            var screenHeight = DeviceDisplay.MainDisplayInfo.Height / DeviceDisplay.MainDisplayInfo.Density;

            bool isSmallScreen = screenWidth <= 1280 && screenHeight <= 800;
            return isSmallScreen ? Dimens.FontSizeT4 : Dimens.FontSizeT3;
        }
        return Dimens.FontSizeT3; // Default size if value is not a double
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
