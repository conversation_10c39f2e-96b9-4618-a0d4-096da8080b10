﻿<?xml version="1.0" encoding="utf-8" ?>
<mvvm:BasePopup
    x:Class="ShinKenShinKun.UI.NumberPad"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    x:Name="this"
    x:DataType="ui:NumberPadViewModel"
    CanBeDismissedByTappingOutsideOfPopup="False">
    <telerik:RadBorder
        BackgroundColor="{x:Static ui:AppColors.WhisperGray}"
        CornerRadius="{Static ui:Dimens.Thickness4}"
        HorizontalOptions="Center"
        VerticalOptions="Center">
        <VerticalStackLayout
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingXxs2}}">
            <Grid
                Padding="{ui:EdgeInsets Right={x:Static ui:Dimens.SpacingXxs2}, Left={x:Static ui:Dimens.Spacing10}, Vertical={x:Static ui:Dimens.Spacing10}}"
                ColumnDefinitions="*,auto">
                <Label
                    Style="{x:Static ui:Styles.IdInputTitleLabelStyle}"
                    Text="{Binding Title}" />
                <Image
                    Grid.Column="1"
                    Source="{x:Static ui:Icons.XIcon}"
                    Style="{x:Static ui:Styles.IdInputCloseButtonStyle}">
                    <Image.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding CancelCommand, Source={Reference this}}" />
                    </Image.GestureRecognizers>
                </Image>
            </Grid>
            <Grid
                x:Name="numberPad"
                Grid.Row="1"
                Padding="{Static ui:Dimens.Spacing5}"
                HeightRequest="400"
                IsClippedToBounds="False"
                RowDefinitions="auto,*"
                RowSpacing="{Static ui:Dimens.Spacing5}"
                WidthRequest="400">
                <telerik:RadBorder
                    CornerRadius="{Static ui:Dimens.Thickness4}"
                    Style="{x:Static ui:Styles.IdInputDisplayBorderStyle}">
                    <Label
                        Style="{x:Static ui:Styles.IdInputDisplayLabelStyle}"
                        Text="{Binding Text}" />
                </telerik:RadBorder>
            </Grid>
        </VerticalStackLayout>
    </telerik:RadBorder>
</mvvm:BasePopup>