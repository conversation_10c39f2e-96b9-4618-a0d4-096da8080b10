﻿namespace ShinKenShinKun.Utils;

public class TimeUtility
{
    public static string TimeSet()
    {
        string rtTime = null;

        try
        {
            // UDP生成
            UdpClient objSck;
            System.Net.IPEndPoint ipAny =
                new System.Net.IPEndPoint(
                System.Net.IPAddress.Any, 0);
            objSck = new UdpClient(ipAny);

            objSck.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReceiveTimeout, 4000);

            // UDP送信
            byte[] sdat = new byte[48];
            sdat[0] = 0xB;
            //                objSck.Send(sdat, sdat.GetLength(0),
            //                    "time.windows.com", 123);
            string timeServerAddress = ConfigReadUtility.GetConfigData("TimeServerAddress");

            objSck.Send(sdat, sdat.GetLength(0),
                timeServerAddress, 123);

            // UDP受信
            byte[] rdat = objSck.Receive(ref ipAny);

            // 1900年1月1日からの経過時間(日時分秒)
            long lngAllS; // 1900年1月1日からの経過秒数
            long lngD;    // 日
            long lngH;    // 時
            long lngM;    // 分
            long lngS;    // 秒

            // 1900年1月1日からの経過秒数
            lngAllS = (long)(
                      rdat[40] * Math.Pow(2, 8 * 3) +
                      rdat[41] * Math.Pow(2, 8 * 2) +
                      rdat[42] * Math.Pow(2, 8 * 1) +
                      rdat[43]);

            lngD = lngAllS / (24 * 60 * 60); // 日
            lngS = lngAllS % (24 * 60 * 60); // 残りの秒数
            lngH = lngS / (60 * 60);         // 時
            lngS = lngS % (60 * 60);         // 残りの秒数
            lngM = lngS / 60;                // 分
            lngS = lngS % 60;                // 秒

            // DateTime型への変換
            DateTime dtTime = new DateTime(1900, 1, 1);
            dtTime = dtTime.AddDays(lngD);
            dtTime = dtTime.AddHours(lngH);
            dtTime = dtTime.AddMinutes(lngM);
            dtTime = dtTime.AddSeconds(lngS);
            // グリニッジ標準時から日本時間への変更
            dtTime = dtTime.AddHours(9);

            DateAndTime.Today = dtTime.Date;
            DateAndTime.TimeOfDay = dtTime;

            rtTime = dtTime.ToString();
        }
        catch (SocketException)
        {
            rtTime = null;
        }

        return rtTime;
    }
}