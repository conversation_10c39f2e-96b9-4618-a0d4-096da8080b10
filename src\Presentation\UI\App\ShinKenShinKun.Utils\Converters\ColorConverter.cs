﻿namespace ShinKenShinKun.Utils;

public static class ColorConverter
{
    //public static System.Windows.Media.Color ToWindowsMedia(System.Drawing.Color input)
    //{
    //    System.Windows.Media.Color output = System.Windows.Media.Color.FromArgb(input.A, input.R, input.G, input.B);
    //    return output;
    //}

    //public static System.Drawing.Color ToSystemDrawing(System.Windows.Media.Color input)
    //{
    //    System.Drawing.Color output = System.Drawing.Color.FromArgb(input.A, input.R, input.G, input.B);
    //    return output;
    //}

    /// <summary>
    /// .NET 9 MAUI Migrated
    /// </summary>
    public static System.Drawing.Color ToWindowsMedia(System.Drawing.Color input)
    {
        System.Drawing.Color output = System.Drawing.Color.FromArgb(input.A, input.R, input.G, input.B);
        return output;
    }

    /// <summary>
    /// .NET 9 MAUI Migrated
    /// </summary>
    public static System.Drawing.Color ToSystemDrawing(System.Drawing.Color input)
    {
        System.Drawing.Color output = System.Drawing.Color.FromArgb(input.A, input.R, input.G, input.B);
        return output;
    }
}