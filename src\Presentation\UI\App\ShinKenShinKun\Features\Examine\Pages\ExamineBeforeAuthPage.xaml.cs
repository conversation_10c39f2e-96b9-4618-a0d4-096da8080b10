namespace ShinKenShinKun;

public partial class ExamineBeforeAuthPage : BasePage
{
    private readonly ExamineBeforeAuthViewModel vm;

    public ExamineBeforeAuthPage(ExamineBeforeAuthViewModel vm)
    {
        InitializeComponent();
        BindingContext = vm;
        this.vm = vm;
    }

    protected override void OnNavigatedTo(NavigatedToEventArgs args)
    {
        vm.StartListening();
        base.OnNavigatedTo(args);
    }

    protected override void OnNavigatedFrom(NavigatedFromEventArgs args)
    {
        vm.StopListening();
        base.OnNavigatedFrom(args);
    }
}