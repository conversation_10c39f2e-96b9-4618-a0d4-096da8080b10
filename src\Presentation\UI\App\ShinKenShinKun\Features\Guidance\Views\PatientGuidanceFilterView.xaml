<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.PatientGuidanceFilterView"
    xmlns:app="clr-namespace:ShinKenShinKun"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    x:Name="this">
    <Grid
        ColumnSpacing="{x:Static ui:Dimens.SpacingMd}"
        ColumnDefinitions="*, *, 1.4*">
        <telerik:RadBorder
            Grid.Column="1"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingSm}}"
            Style="{x:Static ui:Styles.ExamineFilterBorderStyle}">
            <Grid
                ColumnDefinitions="auto,*">
                <Label
                    Grid.Column="0"
                    VerticalOptions="Center"
                    Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                    FontSize="{x:Static ui:Dimens.FontSizeT5}"
                    Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingXs}}"
                    Text="{x:Static resources:AppResources.NameMedicalCategory}" />
                <ui:DropDownControl
                    Grid.Column="1"
                    BackgroundColorDropDownControl="{x:Static ui:AppColors.White}"
                    BorderThickness="{x:Static ui:Dimens.Thickness1}"
                    BorderColor="{x:Static ui:AppColors.DarkLightBlueGray}"
                    ItemsSource="{Binding MedicalCheckDivisions, Source={x:Reference this}}"
                    SelectedItem="{Binding MedicalCheckSelected, Source={x:Reference this}}" />
            </Grid>
        </telerik:RadBorder>
        <telerik:RadBorder
            Grid.Column="2"
            Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingSm}}"
            Style="{x:Static ui:Styles.ExamineFilterBorderStyle}">
            <Grid
                ColumnDefinitions="auto, *, 100">
                <Label
                    Grid.Column="0"
                    VerticalOptions="Center"
                    Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                    FontSize="{x:Static ui:Dimens.FontSizeT5}"
                    Margin="{ui:EdgeInsets Horizontal={x:Static ui:Dimens.SpacingXs}}"
                    Text="{x:Static resources:AppResources.ReceiptNumber}" />
                <ui:InputControl
                    Grid.Column="1"
                    Keyboard="Numeric"
                    MaxLength="10"
                    TextChanged="SearchTextChanged"
                    Text="{Binding ClientIdSearched, Source={x:Reference this}}"
                    BackgroundColorInputControl="{x:Static ui:AppColors.White}"
                    BorderThickness="{x:Static ui:Dimens.Thickness1}"
                    HeightRequest="{Binding Source={Reference Search}, Path=Height}"
                    BorderColor="{x:Static ui:AppColors.DarkLightBlueGray}" />
                <telerik:RadBorder
                    x:Name="Search"
                    Margin="{ui:EdgeInsets
                        Left={x:Static ui:Dimens.SpacingXs}}"
                    Grid.Column="2"
                    Style="{x:Static ui:Styles.SearchBorderStyle}">
                    <VerticalStackLayout
                        Padding="{ui:EdgeInsets 
                            All={x:Static ui:Dimens.Spacing2}}"
                        VerticalOptions="End">
                        <Image
                            Aspect="Fill"
                            HorizontalOptions="Center"
                            HeightRequest="{x:Static ui:Dimens.Spacing18}"
                            WidthRequest="{x:Static ui:Dimens.Spacing18}"
                            Source="{x:Static ui:Icons.SearchIcon}" />
                        <Label
                            Style="{x:Static ui:Styles.NotoSansRegularStyle}"
                            HorizontalTextAlignment="Center"
                            Text="{x:Static resources:AppResources.Search}" />
                    </VerticalStackLayout>
                    <telerik:RadBorder.GestureRecognizers>
                        <TapGestureRecognizer
                            Command="{Binding SearchCommand, Source={x:Reference this}}" />
                    </telerik:RadBorder.GestureRecognizers>
                </telerik:RadBorder>
            </Grid>
        </telerik:RadBorder>
    </Grid>
</ContentView>
