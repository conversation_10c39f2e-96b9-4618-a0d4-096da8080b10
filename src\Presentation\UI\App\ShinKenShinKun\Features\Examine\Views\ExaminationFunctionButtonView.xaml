<?xml version="1.0" encoding="utf-8" ?>
<ContentView
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    x:Class="ShinKenShinKun.ExaminationFunctionButtonView"
    xmlns:ui="clr-namespace:ShinKenShinKun.UI;assembly=ShinKenShinKun.UI"
    xmlns:mvvm="clr-namespace:ShinKenShinKun.CoreMVVM;assembly=ShinKenShinKun.CoreMVVM"
    xmlns:resources="clr-namespace:ShinKenShinKun.Utils.Resources;assembly=ShinKenShinKun.Utils"
    xmlns:telerik="http://schemas.telerik.com/2022/xaml/maui"
    x:Name="this">
    <ContentView.Resources>
        <ControlTemplate
            x:Key="ItemBarItemTemplate">
            <Grid
                BackgroundColor="{TemplateBinding BackgroundColor}">
                <ContentPresenter />
            </Grid>
        </ControlTemplate>
    </ContentView.Resources>
    <telerik:RadBorder
        Padding="{ui:EdgeInsets All={x:Static ui:Dimens.SpacingSm}}"
        Style="{x:Static ui:Styles.FunctionButtonsBorderStyle}">
        <telerik:RadCollectionView
            SelectionMode="None"
            ItemsSource="{Binding MenuButtons, Source={x:Reference this}}">
            <telerik:RadCollectionView.ItemViewStyle>
                <Style
                    BasedOn="{x:Static ui:Styles.FunctionButtonsItemStyle}"
                    TargetType="telerik:RadCollectionViewItemView">
                    <Setter
                        Property="VisualStateManager.VisualStateGroups">
                        <VisualStateGroupList />
                    </Setter>
                </Style>
            </telerik:RadCollectionView.ItemViewStyle>
            <telerik:RadCollectionView.ItemsLayout>
                <telerik:CollectionViewLinearLayout
                    Orientation="Horizontal"
                    ItemSpacing="{x:Static ui:Dimens.SpacingXxs}" />
            </telerik:RadCollectionView.ItemsLayout>
            <telerik:RadCollectionView.ItemTemplate>
                <DataTemplate
                    x:DataType="x:String">
                    <Label
                        Style="{x:Static ui:Styles.FunctionButtonsLabelStyle}"
                        Text="{Binding .}" />
                </DataTemplate>
            </telerik:RadCollectionView.ItemTemplate>
        </telerik:RadCollectionView>
    </telerik:RadBorder>
</ContentView>
