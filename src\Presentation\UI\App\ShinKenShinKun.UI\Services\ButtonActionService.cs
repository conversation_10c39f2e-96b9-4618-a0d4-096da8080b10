using Serilog;
using System.Collections.Concurrent;
using System.ComponentModel;

namespace ShinKenShinKun.UI;

public class ButtonActionService : IButtonActionService
{
    private readonly ConcurrentDictionary<string, AreaControl> _areaControls = new();

    public void RegisterAreaControl(string areaId, AreaControl areaControl)
    {
        _areaControls.AddOrUpdate(areaId, areaControl, (key, oldValue) => areaControl);
    }

    public void UnregisterAreaControl(string areaId)
    {
        _areaControls.TryRemove(areaId, out _);
    }

    public async Task<bool> CanExecuteActionAsync(ButtonActionModel action)
    {
        if (action == null) return false;

        return action.ActionType switch
        {
            ButtonActionType.FillEmptyValues => CanExecuteFillEmptyValues(action),
            ButtonActionType.ClearAllValues => CanExecuteClearAllValues(action),
            ButtonActionType.NavigateToPage => CanExecuteNavigateToPage(action),
            ButtonActionType.ShowDialog => true,
            ButtonActionType.ExecuteCommand => true,
            ButtonActionType.ToggleVisibility => CanExecuteToggleVisibility(action),
            ButtonActionType.ValidateForm => CanExecuteValidateForm(action),
            _ => false
        };
    }

    public async Task ExecuteActionAsync(ButtonActionModel action, string sourceAreaId = "")
    {
        if (action == null || !await CanExecuteActionAsync(action))
            return;

        try
        {
            switch (action.ActionType)
            {
                case ButtonActionType.FillEmptyValues:
                    await ExecuteFillEmptyValuesAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.ClearAllValues:
                    await ExecuteClearAllValuesAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.NavigateToPage:
                    await ExecuteNavigateToPageAsync(action);
                    break;
                case ButtonActionType.ShowDialog:
                    await ExecuteShowDialogAsync(action);
                    break;
                case ButtonActionType.ExecuteCommand:
                    await ExecuteCommandAsync(action);
                    break;
                case ButtonActionType.ToggleVisibility:
                    await ExecuteToggleVisibilityAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.ValidateForm:
                    await ExecuteValidateFormAsync(action, sourceAreaId);
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Error(nameof(ButtonActionService), $"{nameof(ExecuteActionAsync)}: Error executing button action: {ex.Message}");
        }
    }

    private async Task ExecuteFillEmptyValuesAsync(ButtonActionModel action, string sourceAreaId)
    {
        var fillValue = action.ActionValue;

        List<View> targetComponents;

        if (string.IsNullOrEmpty(action.TargetAreaId))
        {
            targetComponents = GetTargetComponentsAcrossAllAreas(action.TargetComponentIds);
        }
        else
        {
            // Use specific area
            var targetAreaId = action.TargetAreaId;
            if (!_areaControls.TryGetValue(targetAreaId, out var areaControl))
            {
                Log.Error(nameof(ButtonActionService), $"{nameof(ExecuteFillEmptyValuesAsync)}: Area control not found for AreaId: {targetAreaId}");
                return;
            }

            targetComponents = GetTargetComponents(areaControl, action.TargetComponentIds);
        }

        foreach (var component in targetComponents)
        {
            string componentId = "";
            string currentValue = "";
            bool processed = false;

            if (component is LabelInputResultControl inputControl)
            {
                componentId = inputControl.ComponentId;
                currentValue = inputControl.Value ?? string.Empty;

                // Check if value is empty or whitespace based on action configuration
                bool shouldFill = string.IsNullOrEmpty(currentValue) || string.IsNullOrWhiteSpace(currentValue);

                if (shouldFill)
                {
                    inputControl.Value = fillValue;
                }
                processed = true;
            }
            else if (component is LabelShowPastValueControl pastValueControl)
            {
                componentId = pastValueControl.ComponentId;
                currentValue = pastValueControl.Value ?? string.Empty;

                // Check if value is empty or whitespace based on action configuration
                bool shouldFill = string.IsNullOrEmpty(currentValue) || string.IsNullOrWhiteSpace(currentValue);

                if (shouldFill)
                {
                    pastValueControl.Value = fillValue;
                }
                processed = true;
            }

            if (!processed)
            {
                Log.Error(nameof(ButtonActionService), $"{nameof(ExecuteFillEmptyValuesAsync)}: Unsupported component type: {component.GetType().Name}");
            }
        }
    }

    private async Task ExecuteClearAllValuesAsync(ButtonActionModel action, string sourceAreaId)
    {
        List<View> targetComponents;

        if (string.IsNullOrEmpty(action.TargetAreaId))
        {
            // Search across all registered area controls for the target components
            targetComponents = GetTargetComponentsAcrossAllAreas(action.TargetComponentIds);
        }
        else
        {
            // Use specific area
            var targetAreaId = action.TargetAreaId;

            if (!_areaControls.TryGetValue(targetAreaId, out var areaControl))
                return;

            targetComponents = GetTargetComponents(areaControl, action.TargetComponentIds);
        }

        foreach (var component in targetComponents)
        {
            if (component is LabelInputResultControl inputControl)
            {
                inputControl.Value = string.Empty;
            }
            else if (component is LabelShowPastValueControl pastValueControl)
            {
                pastValueControl.Value = string.Empty;
            }
        }
    }

    private async Task ExecuteNavigateToPageAsync(ButtonActionModel action)
    {
        if (action is NavigateActionModel navAction && !string.IsNullOrEmpty(navAction.PageRoute))
        {
            // Implement navigation logic here
            await Shell.Current.GoToAsync(navAction.PageRoute);
        }
    }

    private async Task ExecuteShowDialogAsync(ButtonActionModel action)
    {
        if (action is ShowDialogActionModel dialogAction)
        {
            // Implement dialog showing logic here
            await Application.Current.MainPage.DisplayAlert(
                dialogAction.DialogTitle, 
                dialogAction.DialogMessage, 
                "OK");
        }
    }

    private async Task ExecuteCommandAsync(ButtonActionModel action)
    {
        // Implement custom command execution logic here
        await Task.CompletedTask;
    }

    private async Task ExecuteToggleVisibilityAsync(ButtonActionModel action, string sourceAreaId)
    {
        // Implement visibility toggle logic here
        await Task.CompletedTask;
    }

    private async Task ExecuteValidateFormAsync(ButtonActionModel action, string sourceAreaId)
    {
        // Implement form validation logic here
        await Task.CompletedTask;
    }

    private List<View> GetTargetComponents(AreaControl areaControl, List<string> targetComponentIds)
    {
        var components = new List<View>();

        if (targetComponentIds == null || targetComponentIds.Count == 0)
        {
            // If no specific targets, get all input controls
            components.AddRange(GetAllInputControls(areaControl));
        }
        else
        {
            // Get specific components by ID
            foreach (var componentId in targetComponentIds)
            {
                var component = FindComponentById(areaControl, componentId);
                if (component != null)
                {
                    components.Add(component);
                }
                else
                {
                    Log.Error(nameof(ButtonActionService), $"{nameof(GetTargetComponents)}: Component not found: {componentId}");
                }
            }
        }

        return components;
    }

    private List<View> GetTargetComponentsAcrossAllAreas(List<string> targetComponentIds)
    {
        var components = new List<View>();

        if (targetComponentIds == null || targetComponentIds.Count == 0)
        {
            // Get all input controls from all areas
            foreach (var areaControl in _areaControls.Values)
            {
                components.AddRange(GetAllInputControls(areaControl));
            }
        }
        else
        {
            // Search for specific components across all areas
            foreach (var componentId in targetComponentIds)
            {
                bool found = false;
                foreach (var kvp in _areaControls)
                {
                    var areaId = kvp.Key;
                    var areaControl = kvp.Value;

                    var component = FindComponentById(areaControl, componentId);
                    if (component != null)
                    {
                        components.Add(component);
                        found = true;
                        break;
                    }
                }

                if (!found)
                {
                    Log.Error(nameof(ButtonActionService), $"{nameof(GetTargetComponentsAcrossAllAreas)}: Component {componentId} not found in any area");
                }
            }
        }
        return components;
    }

    private List<View> GetAllInputControls(AreaControl areaControl)
    {
        var inputControls = new List<View>();

        foreach (var child in areaControl.Children)
        {
            if (child is LabelInputResultControl inputControl)
            {
                inputControls.Add(inputControl);
            }
            else if (child is LabelShowPastValueControl pastValueControl)
            {
                inputControls.Add(pastValueControl);
            }
        }

        return inputControls;
    }

    private View FindComponentById(AreaControl areaControl, string componentId)
    {
        foreach (var child in areaControl.Children)
        {
            if (child is LabelInputResultControl inputControl)
            {
                // First try to match by ComponentId
                if (!string.IsNullOrEmpty(inputControl.ComponentId) && inputControl.ComponentId == componentId)
                {
                    return inputControl;
                }

                // Fallback to Title if ComponentId is not set
                if (string.IsNullOrEmpty(inputControl.ComponentId) && inputControl.Title == componentId)
                {
                    return inputControl;
                }
            }
            else if (child is LabelShowPastValueControl pastValueControl)
            {
                // First try to match by ComponentId
                if (!string.IsNullOrEmpty(pastValueControl.ComponentId) && pastValueControl.ComponentId == componentId)
                {
                    return pastValueControl;
                }

                // Fallback to Title if ComponentId is not set
                if (string.IsNullOrEmpty(pastValueControl.ComponentId) && pastValueControl.Title == componentId)
                {
                    return pastValueControl;
                }
            }
        }
        return null;
    }

    private bool CanExecuteFillEmptyValues(ButtonActionModel action)
    {
        return !string.IsNullOrEmpty(action.ActionValue);
    }

    private bool CanExecuteClearAllValues(ButtonActionModel action)
    {
        return true;
    }

    private bool CanExecuteNavigateToPage(ButtonActionModel action)
    {
        return action is NavigateActionModel navAction && !string.IsNullOrEmpty(navAction.PageRoute);
    }

    private bool CanExecuteToggleVisibility(ButtonActionModel action)
    {
        return action.TargetComponentIds?.Count > 0;
    }

    private bool CanExecuteValidateForm(ButtonActionModel action)
    {
        return true;
    }
}
