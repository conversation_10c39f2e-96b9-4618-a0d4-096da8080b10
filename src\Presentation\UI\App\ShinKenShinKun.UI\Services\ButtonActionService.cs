using System.Collections.Concurrent;

namespace ShinKenShinKun.UI;

public class ButtonActionService : IButtonActionService
{
    private readonly ConcurrentDictionary<string, AreaControl> _areaControls = new();

    public void RegisterAreaControl(string areaId, AreaControl areaControl)
    {
        _areaControls.AddOrUpdate(areaId, areaControl, (key, oldValue) => areaControl);
    }

    public void UnregisterAreaControl(string areaId)
    {
        _areaControls.TryRemove(areaId, out _);
    }

    public async Task<bool> CanExecuteActionAsync(ButtonActionModel action)
    {
        if (action == null) return false;

        return action.ActionType switch
        {
            ButtonActionType.FillEmptyValues => CanExecuteFillEmptyValues(action),
            ButtonActionType.ClearAllValues => CanExecuteClearAllValues(action),
            ButtonActionType.NavigateToPage => CanExecuteNavigateToPage(action),
            ButtonActionType.ShowDialog => true,
            ButtonActionType.ExecuteCommand => true,
            ButtonActionType.ToggleVisibility => CanExecuteToggleVisibility(action),
            ButtonActionType.ValidateForm => CanExecuteValidateForm(action),
            _ => false
        };
    }

    public async Task ExecuteActionAsync(ButtonActionModel action, string sourceAreaId = "")
    {
        if (action == null || !await CanExecuteActionAsync(action))
            return;

        try
        {
            switch (action.ActionType)
            {
                case ButtonActionType.FillEmptyValues:
                    await ExecuteFillEmptyValuesAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.ClearAllValues:
                    await ExecuteClearAllValuesAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.NavigateToPage:
                    await ExecuteNavigateToPageAsync(action);
                    break;
                case ButtonActionType.ShowDialog:
                    await ExecuteShowDialogAsync(action);
                    break;
                case ButtonActionType.ExecuteCommand:
                    await ExecuteCommandAsync(action);
                    break;
                case ButtonActionType.ToggleVisibility:
                    await ExecuteToggleVisibilityAsync(action, sourceAreaId);
                    break;
                case ButtonActionType.ValidateForm:
                    await ExecuteValidateFormAsync(action, sourceAreaId);
                    break;
            }
        }
        catch (Exception ex)
        {
            // Log error and optionally show user-friendly message
            System.Diagnostics.Debug.WriteLine($"Error executing button action: {ex.Message}");
        }
    }

    private async Task ExecuteFillEmptyValuesAsync(ButtonActionModel action, string sourceAreaId)
    {
        var fillValue = action.ActionValue;
        var targetAreaId = string.IsNullOrEmpty(action.TargetAreaId) ? sourceAreaId : action.TargetAreaId;

        System.Diagnostics.Debug.WriteLine($"ExecuteFillEmptyValues - SourceAreaId: {sourceAreaId}, TargetAreaId: {targetAreaId}, FillValue: {fillValue}");
        System.Diagnostics.Debug.WriteLine($"TargetComponentIds: {string.Join(", ", action.TargetComponentIds ?? new List<string>())}");

        if (string.IsNullOrEmpty(targetAreaId) || !_areaControls.TryGetValue(targetAreaId, out var areaControl))
        {
            System.Diagnostics.Debug.WriteLine($"Area control not found for AreaId: {targetAreaId}");
            return;
        }

        var targetComponents = GetTargetComponents(areaControl, action.TargetComponentIds);
        System.Diagnostics.Debug.WriteLine($"Found {targetComponents.Count} target components");

        foreach (var component in targetComponents)
        {
            string componentId = "";
            string currentValue = "";
            bool processed = false;

            if (component is LabelInputResultControl inputControl)
            {
                componentId = inputControl.ComponentId;
                currentValue = inputControl.Value ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"Processing LabelInputResultControl - ComponentId: {componentId}, Title: {inputControl.Title}, CurrentValue: '{currentValue}'");

                // Check if value is empty or whitespace based on action configuration
                bool shouldFill = string.IsNullOrEmpty(currentValue) ||
                                 (action.Parameters.ContainsKey("OnlyIfWhitespace") &&
                                  action.Parameters["OnlyIfWhitespace"].ToString() == "true" &&
                                  string.IsNullOrWhiteSpace(currentValue));

                if (shouldFill)
                {
                    inputControl.Value = fillValue;
                    System.Diagnostics.Debug.WriteLine($"Filled LabelInputResultControl {componentId} with value: {fillValue}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Skipped LabelInputResultControl {componentId} - already has value: '{currentValue}'");
                }
                processed = true;
            }
            else if (component is LabelShowPastValueControl pastValueControl)
            {
                componentId = pastValueControl.ComponentId;
                currentValue = pastValueControl.Value ?? string.Empty;
                System.Diagnostics.Debug.WriteLine($"Processing LabelShowPastValueControl - ComponentId: {componentId}, Title: {pastValueControl.Title}, CurrentValue: '{currentValue}'");

                // Check if value is empty or whitespace based on action configuration
                bool shouldFill = string.IsNullOrEmpty(currentValue) ||
                                 (action.Parameters.ContainsKey("OnlyIfWhitespace") &&
                                  action.Parameters["OnlyIfWhitespace"].ToString() == "true" &&
                                  string.IsNullOrWhiteSpace(currentValue));

                if (shouldFill)
                {
                    pastValueControl.Value = fillValue;
                    System.Diagnostics.Debug.WriteLine($"Filled LabelShowPastValueControl {componentId} with value: {fillValue}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Skipped LabelShowPastValueControl {componentId} - already has value: '{currentValue}'");
                }
                processed = true;
            }

            if (!processed)
            {
                System.Diagnostics.Debug.WriteLine($"Unsupported component type: {component.GetType().Name}");
            }
        }
    }

    private async Task ExecuteClearAllValuesAsync(ButtonActionModel action, string sourceAreaId)
    {
        var targetAreaId = string.IsNullOrEmpty(action.TargetAreaId) ? sourceAreaId : action.TargetAreaId;

        if (string.IsNullOrEmpty(targetAreaId) || !_areaControls.TryGetValue(targetAreaId, out var areaControl))
            return;

        var targetComponents = GetTargetComponents(areaControl, action.TargetComponentIds);

        foreach (var component in targetComponents)
        {
            if (component is LabelInputResultControl inputControl)
            {
                inputControl.Value = string.Empty;
            }
        }
    }

    private async Task ExecuteNavigateToPageAsync(ButtonActionModel action)
    {
        if (action is NavigateActionModel navAction && !string.IsNullOrEmpty(navAction.PageRoute))
        {
            // Implement navigation logic here
            await Shell.Current.GoToAsync(navAction.PageRoute);
        }
    }

    private async Task ExecuteShowDialogAsync(ButtonActionModel action)
    {
        if (action is ShowDialogActionModel dialogAction)
        {
            // Implement dialog showing logic here
            await Application.Current.MainPage.DisplayAlert(
                dialogAction.DialogTitle, 
                dialogAction.DialogMessage, 
                "OK");
        }
    }

    private async Task ExecuteCommandAsync(ButtonActionModel action)
    {
        // Implement custom command execution logic here
        await Task.CompletedTask;
    }

    private async Task ExecuteToggleVisibilityAsync(ButtonActionModel action, string sourceAreaId)
    {
        // Implement visibility toggle logic here
        await Task.CompletedTask;
    }

    private async Task ExecuteValidateFormAsync(ButtonActionModel action, string sourceAreaId)
    {
        // Implement form validation logic here
        await Task.CompletedTask;
    }

    private List<View> GetTargetComponents(AreaControl areaControl, List<string> targetComponentIds)
    {
        var components = new List<View>();

        if (targetComponentIds == null || targetComponentIds.Count == 0)
        {
            // If no specific targets, get all input controls
            System.Diagnostics.Debug.WriteLine("No specific target component IDs - getting all input controls");
            components.AddRange(GetAllInputControls(areaControl));
            System.Diagnostics.Debug.WriteLine($"Found {components.Count} input controls");
        }
        else
        {
            // Get specific components by ID
            System.Diagnostics.Debug.WriteLine($"Looking for specific components: {string.Join(", ", targetComponentIds)}");
            foreach (var componentId in targetComponentIds)
            {
                var component = FindComponentById(areaControl, componentId);
                if (component != null)
                {
                    components.Add(component);
                    System.Diagnostics.Debug.WriteLine($"Added component: {componentId}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Component not found: {componentId}");
                }
            }
        }

        return components;
    }

    private List<View> GetAllInputControls(AreaControl areaControl)
    {
        var inputControls = new List<View>();

        foreach (var child in areaControl.Children)
        {
            if (child is LabelInputResultControl inputControl)
            {
                inputControls.Add(inputControl);
            }
            else if (child is LabelShowPastValueControl pastValueControl)
            {
                inputControls.Add(pastValueControl);
            }
        }

        return inputControls;
    }

    private View FindComponentById(AreaControl areaControl, string componentId)
    {
        System.Diagnostics.Debug.WriteLine($"Looking for component with ID: {componentId}");

        foreach (var child in areaControl.Children)
        {
            System.Diagnostics.Debug.WriteLine($"Checking child: {child.GetType().Name}");

            if (child is LabelInputResultControl inputControl)
            {
                System.Diagnostics.Debug.WriteLine($"  - LabelInputResultControl - ComponentId: '{inputControl.ComponentId}', Title: '{inputControl.Title}'");

                // First try to match by ComponentId
                if (!string.IsNullOrEmpty(inputControl.ComponentId) && inputControl.ComponentId == componentId)
                {
                    System.Diagnostics.Debug.WriteLine($"  - Found match by ComponentId: {componentId}");
                    return inputControl;
                }

                // Fallback to Title if ComponentId is not set
                if (string.IsNullOrEmpty(inputControl.ComponentId) && inputControl.Title == componentId)
                {
                    System.Diagnostics.Debug.WriteLine($"  - Found match by Title: {componentId}");
                    return inputControl;
                }
            }
            else if (child is LabelShowPastValueControl pastValueControl)
            {
                System.Diagnostics.Debug.WriteLine($"  - LabelShowPastValueControl - ComponentId: '{pastValueControl.ComponentId}', Title: '{pastValueControl.Title}'");

                // First try to match by ComponentId
                if (!string.IsNullOrEmpty(pastValueControl.ComponentId) && pastValueControl.ComponentId == componentId)
                {
                    System.Diagnostics.Debug.WriteLine($"  - Found match by ComponentId: {componentId}");
                    return pastValueControl;
                }

                // Fallback to Title if ComponentId is not set
                if (string.IsNullOrEmpty(pastValueControl.ComponentId) && pastValueControl.Title == componentId)
                {
                    System.Diagnostics.Debug.WriteLine($"  - Found match by Title: {componentId}");
                    return pastValueControl;
                }
            }
        }

        System.Diagnostics.Debug.WriteLine($"Component not found: {componentId}");
        return null;
    }

    private bool CanExecuteFillEmptyValues(ButtonActionModel action)
    {
        return !string.IsNullOrEmpty(action.ActionValue);
    }

    private bool CanExecuteClearAllValues(ButtonActionModel action)
    {
        return true;
    }

    private bool CanExecuteNavigateToPage(ButtonActionModel action)
    {
        return action is NavigateActionModel navAction && !string.IsNullOrEmpty(navAction.PageRoute);
    }

    private bool CanExecuteToggleVisibility(ButtonActionModel action)
    {
        return action.TargetComponentIds?.Count > 0;
    }

    private bool CanExecuteValidateForm(ButtonActionModel action)
    {
        return true;
    }
}
